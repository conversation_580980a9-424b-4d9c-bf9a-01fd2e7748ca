package com.trs.police.search.panorama.service.impl.message;

import com.trs.police.search.domain.entity.archive.CaseArchiveEntity;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.service.BaseBusinessArchiveService;
import com.trs.police.search.panorama.service.ISearchFoundationService;
import org.springframework.stereotype.Service;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * CaseArchiveMessageServiceImpl
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/30 15:42
 * @since 1.0
 */
@Service
public class CaseArchiveServiceImpl extends BaseBusinessArchiveService<CaseArchiveEntity> {

    public CaseArchiveServiceImpl(ISearchFoundationService searchService) {
        super(searchService);
    }

    /**
     * archivesEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 14:41
     */
    @Override
    public ArchivesEnum archivesEnum() {
        return ArchivesEnum.CASE;
    }

    /**
     * targetEntity<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 16:11
     */
    @Override
    public Class<CaseArchiveEntity> targetEntity() {
        return CaseArchiveEntity.class;
    }
}
