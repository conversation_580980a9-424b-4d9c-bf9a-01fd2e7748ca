package com.trs.police.search.businessarchives.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.vo.message.BusinessArchivesMessageVO;
import com.trs.police.search.businessarchives.properties.BusinessArchivesConfigProperties;
import com.trs.police.search.businessarchives.properties.DwdRepositoryConfigProperties;
import com.trs.police.search.businessarchives.service.IBusinessArchivesDataLoader;
import com.trs.police.search.businessarchives.service.IDataPersist;
import com.trs.police.search.businessarchives.vo.DbInfoConfigWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 业务档案数据入库
 * *@author:wen.wen
 * *@create 2024-05-07 19:33
 **/
@Service
@Slf4j
public class BusinessArchivesDataLoaderImpl implements IBusinessArchivesDataLoader {

    private List<BusinessArchivesConfigProperties> businessArchivesConfigProperties = new ArrayList<>();

    @Autowired
    private Environment environment;

    @Autowired
    private DwdRepositoryConfigProperties dwdRepositoryConfigProperties;

    /**
     * 初始化 BusinessArchivesConfigProperties
     */
    @PostConstruct
    public void initProperties() {
        try {
            String json = environment.getProperty("business.archives.config.properties");
            if (!StringUtils.isNullOrEmpty(json)) {
                businessArchivesConfigProperties = JSON.parseArray(json, BusinessArchivesConfigProperties.class);
                // 设置数据库连接信息
                businessArchivesConfigProperties.forEach(property -> {
                    switch (property.getDbType()) {
                        case "ES":
                            property.setHost(dwdRepositoryConfigProperties.getEsRepositoryHost());
                            property.setPort(dwdRepositoryConfigProperties.getEsRepositoryPort());
                            property.setUsername(dwdRepositoryConfigProperties.getEsRepositoryUserName());
                            property.setPassword(dwdRepositoryConfigProperties.getEsRepositoryPassword());
                            break;
                        case "Hive":
                            property.setHost(dwdRepositoryConfigProperties.getHiveRepositoryHost());
                            property.setPort(dwdRepositoryConfigProperties.getHiveRepositoryPort());
                            property.setUsername(dwdRepositoryConfigProperties.getHiveRepositoryUserName());
                            property.setPassword(dwdRepositoryConfigProperties.getHiveRepositoryPassword());
                            break;
                        default:
                            log.error("暂不支持{}的dbType", property.getDbType());
                    }
                });
                return;
            }
            log.warn("请注意：business.archives.config.properties 未配置值，会导致业务档案数据入库失败!");
        } catch (Exception e) {
            log.error("businessArchivesConfigProperties 未正常初始化!", e);
        }
    }

    @Override
    public void loadBusinessArchives(List<BusinessArchivesMessageVO> messageVos) {
        if (CollectionUtils.isEmpty(messageVos)) {
            return;
        }
        messageVos = filterMessages(messageVos);
        //根据serviceCode进行分组
        Map<String, List<BusinessArchivesMessageVO>> serviceCodeAndVoMap = messageVos.stream()
                .collect(Collectors.groupingBy(BusinessArchivesMessageVO::getServiceCode));
        serviceCodeAndVoMap.forEach((serviceCode, messageVOList) -> {
            //根据serviceCode获取配置信息，包括入库类型方式等等
            List<BusinessArchivesConfigProperties> currentProperties = businessArchivesConfigProperties.stream()
                    .filter((properties) -> Objects.equals(properties.getServiceCode(), serviceCode)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currentProperties)) {
                log.warn("serviceCode={}的业务未配置入库信息，入库失败!失败的数据为:{}", serviceCode, JSON.toJSONString(messageVOList));
                return;
            }
            //根据不同的数据库类型执行不同的操作
            for (BusinessArchivesConfigProperties currentProperty : currentProperties) {
                Map<String, List<BusinessArchivesMessageVO>> operateTypeMap = messageVOList.stream().collect(
                        Collectors.groupingBy(BusinessArchivesMessageVO::getOperateType));
                operateTypeMap.forEach((operateType, datas) -> {
                    DbInfoConfigWrapper dbInfoConfig = new DbInfoConfigWrapper(currentProperty);
                    String way = environment.getProperty("business.archives.config.persist.way", "DB");
                    switch (operateType) {
                        case "insert":
                        case "update":
                            try {
                                long start = System.currentTimeMillis();
                                IDataPersist.getDataPersistByWay(way).insert(dbInfoConfig, datas);
                                long end = System.currentTimeMillis();
                                log.info("saveOrUpdate操作成功，耗时:{}ms", end - start);
                            } catch (Throwable e) {
                                log.warn("saveOrUpdate操作失败，失败的数据为:{}", JSON.toJSONString(datas));
                                throw new TRSException("saveOrUpdate操作失败：", e);
                            }
                            break;
                        case "delete":
                            try {
                                long start = System.currentTimeMillis();
                                IDataPersist.getDataPersistByWay(way).delete(dbInfoConfig, datas);
                                long end = System.currentTimeMillis();
                                log.info("delete操作成功，耗时:{}ms", end - start);
                            } catch (Throwable e) {
                                log.warn("delete操作失败，失败的数据为:{}", JSON.toJSONString(datas));
                                throw new TRSException("delete操作失败：", e);
                            }
                            break;
                        default:
                            log.warn("消息中的OperateType有误{}", operateType);
                            break;
                    }
                });
            }
        });
    }

    private List<BusinessArchivesMessageVO> filterMessages(List<BusinessArchivesMessageVO> messageVos) {
        // 过滤掉serviceCode或者operateType为空的数据
        List<BusinessArchivesMessageVO> filterMessages = messageVos.stream().filter(vo -> StringUtils.isEmpty(vo.getServiceCode()) || StringUtils.isEmpty(vo.getOperateType()))
                .collect(Collectors.toList());
        log.info("过滤掉serviceCode和operateType为空的数据：{}", JSON.toJSONString(filterMessages));
        List<BusinessArchivesMessageVO> result = messageVos.stream().filter(vo -> !StringUtils.isEmpty(vo.getServiceCode()) && !StringUtils.isEmpty(vo.getOperateType()))
                .collect(Collectors.toList());
        return result;
    }

}
