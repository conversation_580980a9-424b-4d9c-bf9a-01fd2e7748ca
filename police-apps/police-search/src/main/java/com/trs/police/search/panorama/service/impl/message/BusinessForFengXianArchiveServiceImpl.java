package com.trs.police.search.panorama.service.impl.message;

import com.trs.police.search.domain.entity.archive.BusinessForFengXianArchiveEntity;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.service.BaseBusinessArchiveService;
import com.trs.police.search.panorama.service.ISearchFoundationService;
import org.springframework.stereotype.Service;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/16 22:50
 * @since 1.0
 */
@Service
public class BusinessForFengXianArchiveServiceImpl extends BaseBusinessArchiveService<BusinessForFengXianArchiveEntity> {

    public BusinessForFengXianArchiveServiceImpl(ISearchFoundationService searchService) {
        super(searchService);
    }

    /**
     * targetEntity<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 16:11
     */
    @Override
    public Class<BusinessForFengXianArchiveEntity> targetEntity() {
        return BusinessForFengXianArchiveEntity.class;
    }

    /**
     * archivesEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 14:41
     */
    @Override
    public ArchivesEnum archivesEnum() {
        return ArchivesEnum.BUSINESS_FOR_FENG_XIAN;
    }
}
