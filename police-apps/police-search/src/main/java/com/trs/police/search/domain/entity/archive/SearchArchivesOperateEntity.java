package com.trs.police.search.domain.entity.archive;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName SearchArchivesOperate
 * @Description 档案被操作记录实体
 * <AUTHOR>
 * @Date 2024/12/18 17:19
 **/
@Data
@TableName("tb_search_archives_operate")
public class SearchArchivesOperateEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private LocalDateTime crTime;

    private String crUser;

    private Long crUserId;

    private Long crDeptId;

    private Integer type;

    private String archivesType;

    private String recordId;

    private String secondRecordId;

    /**
     * archivesType_record + secondRecordId构成，person_aaa_bbb
     */
    private String uniqueId;

    private String archivesInfo;

}
