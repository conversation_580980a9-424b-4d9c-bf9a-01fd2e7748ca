package com.trs.police.search.panorama.service.impl.parse.ai;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.service.BaseAiLabelParseService;
import com.trs.police.search.panorama.vo.AiLabelVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/2/27 10:48
 * @since 1.0
 */
@Service
public class XiangGuanCheLiangAiLabelParseServiceImpl extends BaseAiLabelParseService {

    /**
     * makeCenter<BR>
     *
     * @param label  参数
     * @param labels 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/27 10:30
     */
    @Override
    protected List<String> makeCenter(AiLabelVo label, List<AiLabelVo> labels) {
        List<String> list = new ArrayList<>(2);
        list.add(label.getRelationObj());
        Optional.ofNullable(label.getHpzl())
                .filter(StringUtils::isNotEmpty)
                .ifPresent(list::add);
        final List<String> time = new ArrayList<>(2);
        time.add("zxzpsj");
        switch (StringUtils.showEmpty(label.getRelation())) {
            case SearchConstant.AI_LABEL_CHEZHU:
            case SearchConstant.AI_LABEL_CHANGJIASHICHELIANG:
                time.add("djsj");
                break;
            case SearchConstant.AI_LABEL_WZCL:
                time.add("zhjksj");
            case SearchConstant.AI_LABEL_WZJL:
                time.add("wfsj");
                break;
            case SearchConstant.AI_LABEL_JSCL:
            case SearchConstant.AI_LABEL_CZCL:
            default:
                time.add("sj");
                break;
        }
        Optional.ofNullable(label.getExt())
                .map(it -> {
                    for (String s : time) {
                        String tt = it.getString(s);
                        if (TimeUtils.isValid(tt)) {
                            return tt;
                        }
                    }
                    return null;
                })
                .filter(TimeUtils::isValid)
                .map(it -> "最新时间：" + TimeUtils.stringToString(it, TimeUtils.YYYYMMDD_HHMMSS))
                .ifPresent(list::add);
        return list;
    }

    /**
     * makeLine<BR>
     *
     * @param label  参数
     * @param labels 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/27 10:30
     */
    @Override
    protected String makeLine(AiLabelVo label, List<AiLabelVo> labels) {
        return label.getRelation();
    }

    @Override
    public String relationCatalog() {
        return SearchConstant.AI_LABEL_CATALOG_XGCL;
    }

    /**
     * archivesEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 14:41
     */
    @Override
    public ArchivesEnum archivesEnum() {
        return ArchivesEnum.PERSON;
    }
}
