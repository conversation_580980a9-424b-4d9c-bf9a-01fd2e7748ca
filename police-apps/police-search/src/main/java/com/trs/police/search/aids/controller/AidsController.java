package com.trs.police.search.aids.controller;

import com.trs.police.search.aids.dto.AidsSearchDTO;
import com.trs.police.search.aids.service.IAidsService;
import com.trs.police.search.panorama.vo.PanoramaVO;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/17 16:32
 * @since 1.0
 */
@RestController
@RequestMapping("aids")
@AllArgsConstructor
public class AidsController {

    private final IAidsService service;

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/534/interface/api/153">查询人员信息</a><BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/17 16:33
     */
    @RequestMapping(
            value = "findPerson",
            method = {
                    RequestMethod.GET,
                    RequestMethod.POST
            }
    )
    public RestfulResultsV2<PanoramaVO> findPerson(AidsSearchDTO dto) {
        return RestfulResultsV2.checkedBuild(() -> service.findPerson(dto));
    }

    /**
     * <a href="https://trsyapi.trscd.com.cn/project/534/interface/api/188">最新数据时间获取</a><BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/18 16:21
     */
    @RequestMapping(
            value = "getLastDataTime",
            method = {
                    RequestMethod.GET,
                    RequestMethod.POST
            }
    )
    public RestfulResultsV2<String> getLastDataTime() {
        return RestfulResultsV2.checkedBuild(() -> service.getLastDataTime());
    }
}
