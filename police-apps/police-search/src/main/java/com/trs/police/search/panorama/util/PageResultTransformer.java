package com.trs.police.search.panorama.util;


import com.trs.db.sdk.pojo.RecordInfo;
import com.trs.web.entity.PageList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.db.sdk.util.EntityUtils.mapToEntity;

/**
 * @ClassName PageResultTransformer
 * @Description  工具类，用于将 PageList对象转换为包含特定类型数据对象的 PageList 对象。
 * <AUTHOR>
 * @Date 2024/5/9
 **/
public class PageResultTransformer {

    /**
     * toDataDO
     *
     * @param pageResult pageResult
     * @param eClass     eClass
     * @param <T>       泛型参数
     * @return {@link PageList}<{@link T}>
     */
    public static <T> PageList<T> toDataDO(PageList<RecordInfo> pageResult, Class<T> eClass) {
        return toDataDO(pageResult, eClass, new HashMap<>());
    }

    /**
     * @param pageResult   pageResult
     * @param eClass       eClass
     * @param fieldMapping fieldMapping
     * @param <T>          泛型参数
     * @return {@link PageList}<{@link T}>
     */
    public static <T> PageList<T> toDataDO(PageList<RecordInfo> pageResult, Class<T> eClass, Map<String, String> fieldMapping) {
        Objects.requireNonNull(pageResult, "PageResult must not be null");
        Objects.requireNonNull(eClass, "Class must not be null");

        PageList<T> pageList = new PageList<>();
        pageList.setPageNum(pageResult.getPageNum());
        pageList.setPageSize(pageResult.getPageSize());
        pageList.setTotal(pageResult.getTotal());

        long totalPage = pageResult.getTotal() / pageResult.getPageSize();
        if (pageResult.getTotal() % pageResult.getPageSize() > 0) {
            totalPage++;
        }
        pageList.setTotalPage(totalPage);
        List<T> contents = pageResult.getContents().stream()
                .map(data -> mapToEntity(data, fieldMapping, eClass))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        pageList.setContents(contents);
        return pageList;
    }
}
