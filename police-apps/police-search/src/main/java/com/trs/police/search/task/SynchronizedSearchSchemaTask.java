package com.trs.police.search.task;

import com.trs.police.search.service.SearchBackstageService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/8/9 16:34
 */
@Component
@ConditionalOnProperty(value = "com.trs.synchronized.search.task.enable", havingValue = "true")
public class SynchronizedSearchSchemaTask {

    @Resource
    private SearchBackstageService searchBackstageService;

    /**
     * 每天凌晨0点同步元数据表
     */
    @Scheduled(cron = "${com.trs.synchronized.search.task.cron}")
    public void synchronizedSearchSchema() {
        searchBackstageService.synchronizedSearchSchema();
    }
}
