package com.trs.police.search.panorama.util;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.operator.IOperator;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.search.panorama.constant.SearchConstant;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName SearchUtils
 * @Description 检索相关工具类
 * <AUTHOR>
 * @Date 2023/10/23 17:11
 **/
public class ExpressionHelper {


    /**
     * 构造关键词的筛选<BR>
     *
     * @param archivesType 参数
     * @param keyword      参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL><br/>
     * @date 创建时间：2023-10-13 09:39
     */
    public static Expression makeKeywordExpression(String archivesType, String keyword) {
        return makeKeywordExpression(archivesType, "", keyword);
    }

    /**
     * 构造关键词的筛选<BR>
     *
     * @param archivesType 参数
     * @param filedNames   参数
     * @param keyword      参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL><br/>
     * @date 创建时间：2023-10-13 09:39
     */
    public static Expression makeKeywordExpression(String archivesType, String filedNames, String keyword) {
        // 根据开会讨论要求，去掉后缀模糊匹配
        String data = Arrays.stream(keyword.split(StringUtils.SEPARATOR_BLANK_SPACE))
                .filter(StringUtils::isNotEmpty)
                .map(it -> modifyKeyword(archivesType, it))
                .collect(Collectors.joining(StringUtils.SEPARATOR_BLANK_SPACE));
        return Condition(StringUtils.showEmpty(filedNames), ExpressionHelper.makeQueryString(), data, 200);
    }

    /**
     * modifyKeyword<BR>
     *
     * @param archivesType 参数
     * @param keyword      参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/24 09:59
     */
    public static String modifyKeyword(String archivesType, String keyword) {
        if (SearchConstant.OR.equals(keyword)
                || SearchConstant.AND.equals(keyword)
                || SearchConstant.NOT.equals(keyword)
                || SearchConstant.TO.equals(keyword)
                || StringUtils.contains(keyword, SearchConstant.SPECIAL_CHARACTERS)) {
            return keyword;
        }
        // 如果是公司库
        if (Objects.equals(archivesType, ArchivesConstants.ARCHIVES_TYPE_COMPANY)) {
            // 如果不符合任何实体的规则就拆词匹配
            if (ArchivesUtils.findAll()
                    .stream()
                    .noneMatch(it -> it.check(keyword))) {
                return keyword;
            }
        }
        return "\"" + keyword + "\"";
    }

    /**
     * makeQueryString<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL><br/>
     * @date 创建时间：2023-10-11 15:57
     */
    public static IOperator makeQueryString() {
        if (isSimpleQueryString()) {
            return EsOperator.SimpleQueryString;
        }
        return EsOperator.QueryString;
    }

    /**
     * 判断是否使用的是simpleQueryString
     *
     * @return true-是，false-否
     */
    public static boolean isSimpleQueryString() {
        return System.getenv().containsKey("useSimpleQueryString");
    }

    /**
     * 替换|符号为or
     *
     * @param value 享有传入value
     * @return 替换后的值
     */
    public static String buildOrConditionValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return value;
        }
        if (isSimpleQueryString()) {
            return value;
        }
        return value.replaceAll("[|]", " OR ");
    }

}
