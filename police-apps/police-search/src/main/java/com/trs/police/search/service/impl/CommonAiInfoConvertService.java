package com.trs.police.search.service.impl;

import com.trs.common.utils.StringUtils;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.vo.AiInfoVo;
import com.trs.police.search.service.BaseAiInfoConvertService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/14 19:20
 * @since 1.0
 */
@Service
public class CommonAiInfoConvertService extends BaseAiInfoConvertService<String, String> {
    /**
     * convertToAiKnowledge<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 19:16
     */
    @Override
    public Optional<String> convertToAiKnowledge(AiInfoVo vo) {
        return Optional.ofNullable(vo)
                .map(AiInfoVo::getContent)
                .filter(StringUtils::isNotEmpty);
    }

    /**
     * convertToReQueryList<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 19:16
     */
    @Override
    public String convertToReQueryData(AiInfoVo vo) {
        return vo.getContent();
    }

    @Override
    public String key() {
        return SearchConstant.AI_INFO_CONVERT_KEY_COMMON;
    }

    /**
     * convertToRedisString<BR>
     *
     * @param s 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/17 10:29
     */
    @Override
    public String convertToRedisString(String s) {
        return s;
    }

    /**
     * convertToRedisUniqueId<BR>
     *
     * @param s 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/17 10:30
     */
    @Override
    public String convertToRedisUniqueId(String s) {
        return StringUtils.convertToSHA256(s);
    }
}
