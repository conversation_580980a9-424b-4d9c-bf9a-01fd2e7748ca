package com.trs.police.search.panorama.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ServiceException;
import com.trs.common.reflect.ClassInfo;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.Operator;
import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.pojo.BaseRecordDO;
import com.trs.db.sdk.pojo.RecordInfo;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.search.message.dto.ArchiveMessageDTO;
import com.trs.police.search.message.dto.ArchiveMessageFieldDTO;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.dto.AiLabelDTO;
import com.trs.police.search.panorama.handler.BaseSendMessage;
import com.trs.police.search.panorama.util.ArchivesUtils;
import com.trs.police.search.panorama.util.LabelUtils;
import com.trs.police.search.panorama.vo.AiLabelVo;
import com.trs.police.search.panorama.vo.ExtPageList;
import com.trs.web.builder.util.KeyMgrFactory;
import com.trs.web.entity.PageInfo;
import com.trs.web.entity.PageList;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.Tuple3;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseArchiveMessageService
 *
 * @param <Target> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/1/30 14:42
 * @since 1.0
 */
@Slf4j
public abstract class BaseArchiveService<Target extends BaseRecordDO>
        implements IArchiveMessageService, IArchiveAiLabelService {

    private final ISearchFoundationService searchService;

    private final Map<String, TableField> fieldMap;

    public BaseArchiveService(ISearchFoundationService searchService) {
        this.searchService = searchService;
        fieldMap = new HashMap<>(1);
    }

    /**
     * findAiLabelVo<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/19 18:33
     */
    @Override
    public ExtPageList<AiLabelVo> findAiLabelVo(AiLabelDTO dto) throws ServiceException {
        var table = dto.getRelationTableName();
        Expression expression = makeCondition(dto, table);
        return makeExtPageList(dto, table, expression);
    }

    /**
     * makeCondition<BR>
     *
     * @param dto   参数
     * @param table 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/2/7 16:17
     */
    public Expression makeCondition(AiLabelDTO dto, String table) {
        Expression expression;
        if (Objects.equals(SearchConstant.DEFAULT_ARCHIVE_RELATION_TABLE, table)) {
            expression = And(
                    ArchivesUtils.makeTargetExpression("obj", dto.getRecordId()),
                    ArchivesUtils.makeTargetExpression("obj_type", archivesEnum().getArchiveRelationObjType())
            );
        } else {
            expression = ArchivesUtils.makeTargetExpression(archivesEnum().getDetailField(), dto.getRecordId());
        }
        return expression;
    }

    /**
     * convertAiLabel<BR>
     *
     * @param dto    参数
     * @param labels 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/16 23:04
     */
    @Override
    public Tuple2<Map<String, List<AiLabelVo>>, String> convertAiLabel(AiLabelDTO dto, List<AiLabelVo> labels) throws ServiceException {
        Map<String, List<AiLabelVo>> map = new HashMap<>(labels.size());
        for (AiLabelVo label : labels) {
            String relationCatalogClass1 = label
                    .getRelationCatalog()
                    .split(StringUtils.STRING_DIVIDE_FLAG, 2)[0];
            var tmp = map.getOrDefault(relationCatalogClass1, new ArrayList<>(1));
            Optional<BaseAiLabelParseService> aiLabelParseService = LabelUtils.findAiLabelParseService(archivesEnum(), label.getRelationCatalog());
            aiLabelParseService
                    .map(parse -> Try.of(() -> parse.parseLabel(label, labels))
                            .onFailure(e -> log.warn("标签[{}]解析异常", label, e)).getOrNull())
                    .ifPresent(tmp::add);
            if (aiLabelParseService.isPresent()) {
                BaseAiLabelParseService service = aiLabelParseService.get();
                map.put(relationCatalogClass1, service.sort(tmp));
            } else {
                map.put(relationCatalogClass1, tmp);
            }
        }
        return Tuple.of(map, null);
    }

    /**
     * megreExtPageList<BR>
     *
     * @param dto  参数
     * @param data 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/20 21:23
     */
    protected ExtPageList<AiLabelVo> megreExtPageList(AiLabelDTO dto, List<ExtPageList<AiLabelVo>> data) {
        if (CollectionUtils.isEmpty(data)) {
            return getNoDataPageList(dto.getPageNum(), dto.getPageSize(), "", 0L);
        } else if (data.size() == 1) {
            return data.get(0);
        } else {
            int len = data.size();
            List<String> condition = new ArrayList<>(len);
            Long took = 0L;
            Long totalNum = 0L;
            List<AiLabelVo> records = new ArrayList<>(len);
            for (ExtPageList<AiLabelVo> list : data) {
                took += Optional.ofNullable(list.getTook()).orElse(0L);
                totalNum += Optional.ofNullable(list.getTotal()).orElse(0L);
                condition.add(StringUtils.showEmpty(list.getCondition()));
                Optional.ofNullable(list.getContents())
                        .ifPresent(records::addAll);
            }
            return getDataPageList(
                    records,
                    dto.getPageNum(),
                    dto.getPageSize(),
                    totalNum,
                    JSON.toJSONString(condition),
                    took
            );
        }
    }

    /**
     * makeExtPageList<BR>
     *
     * @param dto        参数
     * @param tableName  参数
     * @param expression 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/20 21:10
     */
    protected ExtPageList<AiLabelVo> makeExtPageList(AiLabelDTO dto, String tableName, Expression expression) throws ServiceException {
        Expression merge = makeExpression(expression, dto);
        final Tuple3<PageList<RecordInfo>, String, Long> pageResult = searchService.findPageList(
                tableName,
                merge,
                PageInfo.newPage(dto.getPageNum(), 9999)
        );
        if (pageResult._1.isEmpty()) {
            return getNoDataPageList(dto.getPageNum(), dto.getPageSize(), pageResult._2, pageResult._3);
        }
        final boolean notCarArchives = !Objects.equals(archivesEnum(), ArchivesEnum.CAR);
        final var hpzl = ArchivesUtils.parseHpzl(dto).orElse("");
        var labels = pageResult._1.getContents()
                .stream()
                .map(it -> convertToAiLabelVo(dto, it))
                .filter(it -> notCarArchives
                        || StringUtils.isEmpty(hpzl)
                        || StringUtils.isEmpty(it.getHpzl())
                        || Objects.equals(hpzl, it.getHpzl())
                ).collect(Collectors.toList());
        return getDataPageList(
                labels,
                dto.getPageNum(),
                dto.getPageSize(),
                pageResult._1.getTotal(),
                pageResult._2,
                pageResult._3
        );
    }

    /**
     * 查询数据为空，返回自己得空PageList<BR>
     *
     * @param pageNum   参数
     * @param pageSize  参数
     * @param condition 参数
     * @param took      参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/7 16:53
     */
    protected ExtPageList getNoDataPageList(Integer pageNum, Integer pageSize, String condition, Long took) {
        ExtPageList resultPageList = new ExtPageList();
        resultPageList.setPageNum(pageNum);
        resultPageList.setPageSize(pageSize);
        resultPageList.setTotal(0L);
        resultPageList.setContents(Collections.emptyList());
        resultPageList.setCondition(StringUtils.showEmpty(condition));
        resultPageList.setTook(Optional.ofNullable(took).orElse(0L));
        return resultPageList;
    }

    /**
     * 查询数据为空，返回自己得空PageList
     *
     * @param vList     参数
     * @param pageNum   参数
     * @param pageSize  参数
     * @param totalNum  参数
     * @param condition 参数
     * @param took      参数
     * @param <RESULT>  结果
     * @return 结果
     */
    protected <RESULT> ExtPageList getDataPageList(
            List<RESULT> vList,
            Integer pageNum,
            Integer pageSize,
            Long totalNum,
            String condition,
            Long took
    ) {
        ExtPageList pageList = new ExtPageList();
        pageList.setPageNum(pageNum);
        pageList.setPageSize(pageSize);
        pageList.setTotal(totalNum);
        pageList.setContents(vList);
        pageList.setCondition(StringUtils.showEmpty(condition));
        pageList.setTook(Optional.ofNullable(took).orElse(0L));
        return pageList;
    }

    /**
     * makeExpression<BR>
     *
     * @param expression 参数
     * @param dto        参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/7 17:04
     */
    protected Expression makeExpression(Expression expression, AiLabelDTO dto) {
        final String objTypes = StringUtils.getList(dto.getObjTypes(), StringUtils.SEPARATOR_COMMA_OR_SEMICOLON, true, true)
                .stream()
                .map(it -> String.format("\"%s\"", it))
                .collect(Collectors.joining("|"));
        final String relationObjTypes = StringUtils.getList(dto.getRelationObjTypes(), StringUtils.SEPARATOR_COMMA_OR_SEMICOLON, true, true)
                .stream()
                .map(it -> String.format("\"%s\"", it))
                .collect(Collectors.joining("|"));
        final String relations = StringUtils.getList(dto.getRelations(), StringUtils.SEPARATOR_COMMA_OR_SEMICOLON, true, true)
                .stream()
                .map(it -> String.format("\"%s\"", it))
                .collect(Collectors.joining("|"));
        return And(
                expression,
                Or(
                        StringUtils.isNotEmpty(dto.getTimeliness()),
                        Condition(
                                StringUtils.isNotEmpty(dto.getTimeliness()),
                                "timeliness",
                                Operator.EqualWholeWord,
                                dto.getTimeliness()
                        ),
                        Condition(
                                StringUtils.isNotEmpty(dto.getTimeliness()),
                                "timeliness",
                                Operator.isNull,
                                dto.getTimeliness()
                        ),
                        Condition(
                                "timeliness",
                                Operator.Equal,
                                ""
                        )
                ),
                Condition(StringUtils.isNotEmpty(relations), "relation", EsOperator.SimpleQueryString, relations),
                Condition(StringUtils.isNotEmpty(objTypes), "obj_type", EsOperator.SimpleQueryString, objTypes),
                Condition(StringUtils.isNotEmpty(relationObjTypes), "relation_obj_type", EsOperator.SimpleQueryString, relationObjTypes)
        );
    }

    @Override
    public Expression makeTargetExpression(ArchiveMessageDTO dto) {
        return ArchivesUtils.makeTargetExpression(
                archivesEnum().getDetailField(),
                StringUtils.showEmpty(dto.getRecordId())
                        + StringUtils.SEPARATOR_SEMICOLON
                        + StringUtils.showEmpty(dto.getSecondRecordId())
        );
    }

    /**
     * convertToAiLabelVo<BR>
     *
     * @param dto        参数
     * @param recordInfo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/7 17:04
     */
    protected AiLabelVo convertToAiLabelVo(AiLabelDTO dto, RecordInfo recordInfo) {
        AiLabelVo vo = new AiLabelVo();
        vo.setArchivesType(dto.getArchivesType());
        vo.setEsDocId(StringUtils.showEmpty(recordInfo.getUid()));
        vo.setRecordId(Optional.ofNullable(ArchivesUtils.getDetailFieldByType(dto.getArchivesType()))
                .filter(StringUtils::isNotEmpty)
                .map(recordInfo::getFieldValue)
                .map(Object::toString)
                .filter(StringUtils::isNotEmpty)
                .orElse(dto.getRecordId()));
        vo.setObj(
                Optional.ofNullable(recordInfo.getFieldValue("obj"))
                        .map(Object::toString)
                        .orElse("")
        );
        vo.setObjType(
                Optional.ofNullable(recordInfo.getFieldValue("obj_type"))
                        .map(Object::toString)
                        .orElse("")
        );
        vo.setRelationCatalog(
                Optional.ofNullable(recordInfo.getFieldValue("relation_catalog"))
                        .map(Object::toString)
                        .orElse("")
        );
        vo.setRelation(
                Optional.ofNullable(recordInfo.getFieldValue("relation"))
                        .map(Object::toString)
                        .orElse("")
        );
        vo.setRelationObjType(
                Optional.ofNullable(recordInfo.getFieldValue("relation_obj_type"))
                        .map(Object::toString)
                        .orElse("")
        );
        vo.setRelationObj(
                Optional.ofNullable(recordInfo.getFieldValue("relation_obj"))
                        .map(Object::toString)
                        .orElse("")
        );
        vo.setTimeliness(
                Optional.ofNullable(recordInfo.getFieldValue("timeliness"))
                        .map(Object::toString)
                        .orElse("")
        );
        vo.setExt(
                Optional.ofNullable(recordInfo.getFieldValue("ext"))
                        .map(a -> a.toString().replaceAll("\\\\", "/"))
                        .filter(JsonUtils::isValidObject)
                        .map(JSONObject::parseObject)
                        .orElse(new JSONObject())
        );
        vo.setObjExt(
                Optional.ofNullable(recordInfo.getFieldValue("obj_ext"))
                        .map(a -> a.toString().replaceAll("\\\\", "/"))
                        .filter(JsonUtils::isValidObject)
                        .map(JSONObject::parseObject)
                        .orElse(new JSONObject())
        );
        vo.setRelationObjExt(
                Optional.ofNullable(recordInfo.getFieldValue("relation_obj_ext"))
                        .map(a -> a.toString().replaceAll("\\\\", "/"))
                        .filter(JsonUtils::isValidObject)
                        .map(JSONObject::parseObject)
                        .orElse(new JSONObject())
        );
        vo.setHpzl(StringUtils.showEmpty(
                Optional.ofNullable(recordInfo.getFieldValue("hpzl"))
                        .map(Object::toString)
                        .filter(StringUtils::isNotEmpty)
                        .orElseGet(() -> vo.getExt().getString("hpzl")),
                StringUtils.showEmpty(vo.getObjExt().getString("hpzl"),
                        StringUtils.showEmpty(vo.getRelationObjExt().getString("hpzl")))
        ));
        vo.setObjHpzl(StringUtils.showEmpty(StringUtils.showEmpty(vo.getObjExt().getString("hpzl"),
                vo.getExt().getString("hpzl"))));
        vo.setRelationObjHpzl(StringUtils.showEmpty(StringUtils.showEmpty(vo.getRelationObjExt().getString("hpzl"),
                vo.getExt().getString("hpzl"))));
        // 补充对象所属档案类型
        vo.setObjArchivesType(ArchivesUtils.convertObjTypeToArchivesType(vo.getObjType()));
        vo.setRelationObjArchivesType(ArchivesUtils.convertObjTypeToArchivesType(vo.getRelationObjType()));
        return vo;
    }

    /**
     * injectOnInsert<BR>
     *
     * @param recordInfo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/11 13:48
     */
    protected RecordInfo injectOnInsert(RecordInfo recordInfo) {
        return recordInfo;
    }

    /**
     * injectOnUpdate<BR>
     *
     * @param recordInfo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/11 13:48
     */
    protected RecordInfo injectOnUpdate(RecordInfo recordInfo) {
        return recordInfo;
    }

    /**
     * consumerMessage<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 15:10
     */
    @Override
    public Try<Void> consumerMessage(ArchiveMessageDTO dto) {
        return Try.run(() -> {
            Expression expression = makeTargetExpression(dto);
            RecordInfo recordInfo = makeRecordInfo(dto);
            String tableName = archivesEnum().getTableName();
            RecordInfo singleResult = searchService.searchArchivesDetail(
                    tableName,
                    archivesEnum().getDetailField(),
                    dto.makeRealRecordId()
            );
            switch (StringUtils.showEmpty(dto.getUpdateStrategy())) {
                case ArchivesConstants.FULL_UPDATE:
                    // 先删除数据
                    searchService.deleteByExpression(tableName, expression);
                    searchService.insert(tableName, injectOnInsert(recordInfo));
                    break;
                case ArchivesConstants.DELETE:
                    // 删除数据
                    searchService.deleteByExpression(tableName, expression);
                    break;
                case ArchivesConstants.INCREMENTAL_UPDATE:
                default:
                    var existData = Optional.ofNullable(singleResult)
                            .filter(it -> StringUtils.isNotEmpty(it.getUid()));
                    if (existData.isPresent()) {
                        recordInfo.setUid(existData.get().getUid());
                        recordInfo.setUidName(existData.get().getUidName());
                        searchService.update(tableName, injectOnUpdate(recordInfo));
                    } else {
                        searchService.insert(tableName, injectOnInsert(recordInfo));
                    }
                    break;
            }
            Try.run(() -> {
                var opt = BaseSendMessage.findByKey(ArchivesConstants.ZHONG_TAI);
                if (opt.isPresent()) {
                    opt.get().sendData(dto);
                } else {
                    log.info(
                            "[{}]数据[{}:{}]在[{}]策略下跳过向中台发送消息",
                            desc(),
                            dto.getRecordId(),
                            StringUtils.showEmpty(dto.getSecondRecordId()),
                            dto.getUpdateStrategy()
                    );
                }
            }).onFailure(e -> log.warn(
                    "[{}]数据[{}:{}]在[{}]策略下向中台发送消息异常",
                    desc(),
                    dto.getRecordId(),
                    StringUtils.showEmpty(dto.getSecondRecordId()),
                    dto.getUpdateStrategy(),
                    e
            ));
        }).onFailure(e -> log.error(
                "[{}]数据[{}:{}]在[{}]策略下处理异常",
                desc(),
                dto.getRecordId(),
                StringUtils.showEmpty(dto.getSecondRecordId()),
                dto.getUpdateStrategy(),
                e
        ));
    }

    /**
     * makeRecordInfo<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 15:06
     */
    @Override
    public RecordInfo makeRecordInfo(ArchiveMessageDTO dto) {
        var fieldName = archivesEnum().getDetailField();
        var fields = fieldName.split(StringUtils.SEPARATOR_SEMICOLON);
        RecordInfo recordInfo = new RecordInfo();
        recordInfo.setTableId(dto.makeRealRecordId());
        recordInfo.setTableIdName(fields[0]);
        recordInfo.setIndexName(archivesEnum().getTableName());
        var map = makeFieldValueMap(dto);
        map.put(fields[0], dto.getRecordId());
        if (fields.length > 1 && StringUtils.isNotEmpty(dto.getSecondRecordId())) {
            var data = StringUtils.getList(dto.getSecondRecordId(), StringUtils.SEPARATOR_SEMICOLON, false, false);
            for (int i = 1; i < fields.length; i++) {
                if (data.size() >= i) {
                    var value = data.get(i - 1);
                    if (StringUtils.isNotEmpty(value)) {
                        map.put(fields[i], value);
                    }
                }
            }
        }
        recordInfo.setFieldValueMap(map);
        return recordInfo;
    }

    /**
     * makeFieldValueMap<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 15:06
     */
    @Override
    public Map<String, Object> makeFieldValueMap(ArchiveMessageDTO dto) {
        Map<String, Object> map = new HashMap<>(2);
        var fieldMapping = makeFieldMapping();
        map.put(SearchConstant.DEFAULT_TIME_FIELD, TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS));
        for (ArchiveMessageFieldDTO field : dto.getFields()) {
            if (fieldMapping.containsKey(field.getKey())) {
                TableField tableField = fieldMapping.get(field.getKey());
                String key = StringUtils.showEmpty(tableField.value(), field.getKey());
                if (tableField.objectValue() || tableField.multivalue()) {
                    String value = StringUtils.showEmpty(
                            field.getValue(),
                            tableField.multivalue() ? "[]" : "{}"
                    );
                    if (JsonUtils.isValidObject(value)) {
                        var obj = JSONObject.parseObject(value);
                        if (obj.isEmpty()) {
                            map.put(key, null);
                        } else {
                            map.put(key, obj);
                        }
                    } else if (JsonUtils.isValidArray(value)) {
                        var array = JSONArray.parseArray(value);
                        if (array.isEmpty()) {
                            map.put(key, null);
                        } else {
                            map.put(key, array);
                        }
                    }
                } else {
                    map.put(key, field.getValue());
                }
            }
        }
        return map;
    }

    @Override
    public String key() {
        return archivesEnum().getType();
    }

    @Override
    public String desc() {
        return archivesEnum().getTableDesc();
    }

    /**
     * makeFieldMapping<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 16:50
     */
    public Map<String, TableField> makeFieldMapping() {
        if (fieldMap.isEmpty()) {
            synchronized (BaseArchiveService.class) {
                if (fieldMap.isEmpty()) {
                    Map<String, TableField> values = new HashMap<>(1);
                    ClassInfo classInfo = ClassInfo.forName(targetEntity());
                    classInfo.getFieldByAnnotation(TableField.class)
                            .forEach(fieldName -> {
                                TableField tableField = classInfo
                                        .getSpecifiedFieldAnnotationAs(fieldName, TableField.class)
                                        .get(0);
                                values.put(fieldName, tableField);
                                if (StringUtils.isNotEmpty(tableField.value())) {
                                    values.put(tableField.value(), tableField);
                                }
                            });
                    fieldMap.putAll(values);
                }
            }
        }
        return fieldMap;
    }

    /**
     * targetEntity<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 16:11
     */
    public abstract Class<Target> targetEntity();

    /**
     * findService<BR>
     *
     * @param archiveType 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/19 19:12
     */
    public static BaseArchiveService findService(String archiveType)
            throws ServiceException {
        return KeyMgrFactory.findMgrByKey(
                BaseArchiveService.class,
                archiveType,
                true
        );
    }
}
