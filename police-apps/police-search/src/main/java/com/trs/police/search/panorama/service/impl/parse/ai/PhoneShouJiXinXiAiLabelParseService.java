package com.trs.police.search.panorama.service.impl.parse.ai;

import com.trs.common.utils.StringUtils;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.service.BasePhoneAiLabelParseService;
import com.trs.police.search.panorama.vo.AiLabelVo;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * PhoneShiMingXinXiAiLabelParseService
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/20 21:36
 * @since 1.0
 */
@Service
public class PhoneShouJiXinXiAiLabelParseService extends BasePhoneAiLabelParseService {
    /**
     * relationCatalog<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/20 19:39
     */
    @Override
    public String relationCatalog() {
        return SearchConstant.AI_LABEL_CATALOG_SJXX;
    }

    /**
     * makeCenter<BR>
     *
     * @param label  参数
     * @param labels 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/27 10:30
     */
    @Override
    protected List<String> makeCenter(AiLabelVo label, List<AiLabelVo> labels) {
        List<String> list = new ArrayList<>(3);
        String sjpp = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("sjpp"))
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        String sjxh = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("sjxh"))
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        String first = Arrays.stream(new String[]{sjpp, sjxh})
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("："));
        if (StringUtils.isNotEmpty(first)) {
            list.add(first);
        }
        String two = Arrays.stream(new String[]{
                        label.getRelationObjType(),
                        label.getRelationObj()
                }).filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("："));
        if (StringUtils.isNotEmpty(two)) {
            list.add(two);
        }
        Optional.ofNullable(label.getExt())
                .map(it -> it.getString("time"))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(it -> list.add("最新采集时间：" + it));
        return list;
    }

    /**
     * makeTopRight<BR>
     *
     * @param label  参数
     * @param labels 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/27 10:30
     */
    @Override
    protected List<String> makeTopRight(AiLabelVo label, List<AiLabelVo> labels) {
        return Collections.singletonList(label.getRelation());
    }
}
