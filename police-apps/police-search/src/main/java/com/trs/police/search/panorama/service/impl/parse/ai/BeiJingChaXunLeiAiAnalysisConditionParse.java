package com.trs.police.search.panorama.service.impl.parse.ai;

import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.Nested;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.vo.search.AiAnalysisVo;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.search.panorama.service.BaseAiAnalysisConditionParse;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import org.springframework.stereotype.Service;

import static com.trs.common.utils.expression.ExpressionBuilder.And;
import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/12/26 21:10
 * @since 1.0
 */
@Service
public class BeiJingChaXunLeiAiAnalysisConditionParse extends BaseAiAnalysisConditionParse {
    /**
     * parseCondition<BR>
     *
     * @param dto        参数
     * @param analysisVo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/26 21:03
     */
    @Override
    public Tuple2<Expression, Long> doParseCondition(BaseArchivesSearchDTO dto, AiAnalysisVo analysisVo) {
        return Tuple.of(
                And(
                        makeRecordIdsExpression(analysisVo.getRecordId()),
                        new Nested(Condition("tags.tag_name", Operator.Equal, analysisVo.getAnalysisValue()), "tags")
                ),
                (long) analysisVo.getRecordId().size()
        );
    }

    @Override
    public String key() {
        return "背景类查询";
    }

    @Override
    public String desc() {
        return "背景类查询";
    }
}
