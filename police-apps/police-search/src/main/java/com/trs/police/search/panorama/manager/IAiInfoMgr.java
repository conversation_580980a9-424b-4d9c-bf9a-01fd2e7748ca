package com.trs.police.search.panorama.manager;

import com.trs.common.utils.StringUtils;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.vo.AiInfoVo;
import com.trs.web.builder.base.IKey;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/12/2 17:54
 * @since 1.0
 */
public interface IAiInfoMgr extends IKey {

    /**
     * clearInfo<BR>
     *
     * @param uuid  参数
     * @param types 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/2/28 14:45
     */
    void clearInfo(String uuid, String... types);

    /**
     * findInfo<BR>
     *
     * @param type     参数
     * @param subTypes 参数
     * @param uuid     参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/2 18:19
     */
    String findInfo(String uuid, String type, String... subTypes);

    /**
     * findRawInfo<BR>
     *
     * @param uuid     参数
     * @param type     参数
     * @param subTypes 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 18:15
     */
    Map<Object, List<AiInfoVo>> findRawInfo(String uuid, String type, String... subTypes);

    /**
     * putInfo<BR>
     *
     * @param type    参数
     * @param subType 参数
     * @param uuid    参数
     * @param content 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/2 17:55
     */
    default void putInfo(String type, String subType, String uuid, String content) {
        putInfo(
                type,
                subType,
                uuid,
                List.of(
                        AiInfoVo.builder()
                                .key(StringUtils.convertToSHA256(content))
                                .convertKey(SearchConstant.AI_INFO_CONVERT_KEY_COMMON)
                                .content(content)
                                .build()
                )
        );
    }

    /**
     * putInfo<BR>
     *
     * @param type    参数
     * @param subType 参数
     * @param uuid    参数
     * @param content 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/10 15:18
     */
    default void putInfo(String type, String subType, String uuid, AiInfoVo content) {
        putInfo(
                type,
                subType,
                uuid,
                List.of(content)
        );
    }

    /**
     * putInfo<BR>
     *
     * @param type    参数
     * @param subType 参数
     * @param uuid    参数
     * @param content 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/10 15:02
     */
    void putInfo(String type, String subType, String uuid, Collection<AiInfoVo> content);

    /**
     * putInfo<BR>
     *
     * @param type    参数
     * @param subType 参数
     * @param uuid    参数
     * @param content 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/2 17:55
     */
    void putInfoWithMerge(String type, String subType, String uuid, List<AiInfoVo> content);

    /**
     * markHaveAiFlag<BR>
     *
     * @param uuid 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/6 18:53
     */
    void markHaveAiFlag(String uuid);

    /**
     * haveAiFlag<BR>
     *
     * @param uuid 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/6 18:53
     */
    Boolean haveAiFlag(String uuid);

    /**
     * markUseAiAnalysisCondition<BR>
     *
     * @param uuid 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/4 18:34
     */
    void markUseAiAnalysisCondition(String uuid);

    /**
     * markUseAiAnalysisCondition<BR>
     *
     * @param uuid  参数
     * @param key   参数
     * @param value 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/4 18:34
     */
    void markUseAiAnalysisCondition(String uuid, String key, String value);

    /**
     * useAiAnalysisCondition<BR>
     *
     * @param uuid 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/6 18:53
     */
    Boolean useAiAnalysisCondition(String uuid);

    /**
     * useAiAnalysisCondition<BR>
     *
     * @param uuid 参数
     * @param key  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/6 18:53
     */
    Boolean useAiAnalysisCondition(String uuid, String key);

    /**
     * findAiAnalysisCondition<BR>
     *
     * @param uuid 参数
     * @param key  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/17 18:35
     */
    String findAiAnalysisCondition(String uuid, String key);
}
