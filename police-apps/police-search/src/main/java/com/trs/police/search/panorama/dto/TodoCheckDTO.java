package com.trs.police.search.panorama.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询数据待办情况
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-17 10:30:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TodoCheckDTO extends BaseDTO {
    /**
     * 档案类型
     */
    private String type;
    /**
     * 证件号码，车牌号、手机号等
     */
    private String recordId;
    /**
     * 号牌种类等
     */
    private String secondRecordId;

    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(getType(),"档案类型不能为空");
        PreConditionCheck.checkNotEmpty(getRecordId(),"证件号码不能为空");
        return true;
    }
}
