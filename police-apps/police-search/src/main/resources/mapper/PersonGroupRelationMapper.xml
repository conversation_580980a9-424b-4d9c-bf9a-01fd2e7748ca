<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.search.mapper.PersonGroupRelationMapper">

    <select id="groupInfoBySfzh" resultType="com.trs.police.search.domain.vo.GroupInfoVO">
        SELECT
        tpg.name as name,
        tpg.create_time as createTime,
        count(*) as personCount
        from t_profile_person_group_relation tppgr
        left join t_profile_group tpg on tpg.id = tppgr.group_id
        <where>
            tpg.deleted = 0
            <if test="dto.sfzh != null and dto.sfzh != ''">
                AND tppgr.group_id IN
                (select tmp.group_id from t_profile_person_group_relation tmp left join t_profile_person tpp on
                tmp.person_id = tpp.id
                where tpp.deleted = 0 and tpp.id_number = #{dto.sfzh})
            </if>
        </where>
        GROUP BY tppgr.group_id
        ORDER BY tpg.create_time desc
    </select>

    <select id="basicSourceInfo" resultType="com.trs.police.search.domain.vo.BasicSourceVO">
        SELECT
        `type`,
        `name`,
        ST_AsText(ST_SwapXY(`point`)) as point,
        `code`,
        `address`,
        ST_Distance_Sphere(point(ST_Y(`point`),ST_X(`point`)),point(#{dto.longitude},#{dto.latitude})) AS distance
        FROM `t_control_warning_source`
        <where>
            `is_legal` = 1
            <if test="typeList != null and typeList.size() > 0">
                AND `type` IN
                <foreach collection="typeList" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="dto.radiusSize == null">
                AND ST_Y(`point`) = #{dto.longitude}
                AND ST_X(`point`) = #{dto.latitude}
            </if>
        </where>
        <if test="dto.radiusSize != null">
            HAVING distance <![CDATA[<=]]> #{dto.radiusSize}
            ORDER BY `create_time` desc
        </if>
    </select>

    <select id="querySource" resultType="com.trs.police.search.domain.vo.BasicSourceVO">
        SELECT tcws.type,
        tcws.name,
        ST_AsText(ST_SwapXY(tcws.point)) as point,
        tcws.code,
        tcws.address
        FROM t_control_warning_source tcws
        <where>
            tcws.is_legal = 1
            <if test="typeList != null and typeList.size() > 0">
                AND tcws.type IN
                <foreach collection="typeList" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="dto.address != null and dto.address != ''">
                AND tcws.address like concat('%',#{dto.address},'%')
            </if>
        </where>
        ORDER BY tcws.create_time desc
    </select>
</mapper>