<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.search.mapper.SearchFootholdMapper">

    <select id="countList" resultType="com.trs.police.search.panorama.vo.PersonFootholdCountVO">
        SELECT
        `xxdz`,
        `jd`,
        `wd`,
        `zhcxsj`,
        SUM(`jszqcs`) AS num
        FROM `tb_search_luo_jiao_dian`
        <where>
            `ysbsf` = #{dto.factorType}
            AND `ysz` = #{dto.factorValue}
            <if test="dto.startTime != null and dto.startTime != ''">
                AND `zhcxsj` >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND `zhcxsj` <![CDATA[<=]]> #{dto.endTime}
            </if>
            <if test="dto.jslx != null and dto.jslx != ''">
                AND `jslx` = #{dto.jslx}
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND `xxdz` LIKE CONCAT('%',#{dto.keyword},'%')
            </if>
        </where>
        GROUP BY `xxdz`
    </select>

    <select id="dateCountList" resultType="com.trs.police.search.panorama.vo.PersonFootholdDateCountVO">
        SELECT
            DATE_FORMAT(`zhcxsj`, '%Y-%m-%d') AS date,
            SUM( CASE WHEN `jslx` = 'sojourn' THEN `jszqcs` ELSE 0 END ) AS sojournNum,
            SUM( CASE WHEN `jslx` = 'locus' THEN `jszqcs` ELSE 0 END ) AS locusNum
        FROM `tb_search_luo_jiao_dian`
        <where>
            `ysbsf` = #{dto.factorType}
            AND `ysz` = #{dto.factorValue}
            AND `xxdz` = #{dto.xxdz}
        </where>
        GROUP BY date
    </select>
</mapper>