package com.trs.police.search.test;

import com.trs.police.search.PoliceSearchApplication;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceSearchApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DemoTest {

    @Test
    void testNacos() {
        System.out.println(BeanFactoryHolder.getEnv().getProperty("test.order"));
    }
}
