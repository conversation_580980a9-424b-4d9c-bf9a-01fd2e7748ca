package com.trs.police.subject.domain.entity;

import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.annotations.TableName;
import com.trs.db.sdk.pojo.BaseRecordDO;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: dingkeyu
 * @date: 2024/07/04
 * @description:
 */
@Data
@NoArgsConstructor
@TableName("${archive.person.relation.index:archive_person_relation}")
public class ArchivePersonRelationEsDO extends BaseRecordDO {

    private static final long serialVersionUID = 1L;

    /**
     * 关联关系,枚举
     */
    @TableField("relation")
    private String relation;

    /**
     * 关联关系分类，枚举
     */
    @TableField("relation_catalog")
    private String relationCatalog;

    /**
     * 关系对象值，身份证、车牌、地址等，具体值由关系对象类型决定
     */
    @TableField("relation_obj")
    private String relationObj;

    /**
     * 关联对象类型，枚举
     */
    @TableField("relation_obj_type")
    private String relationObjType;

    /**
     * 证件号码
     */
    @TableField("zjhm")
    private String zjhm;

}
