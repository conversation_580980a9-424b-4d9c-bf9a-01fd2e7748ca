package com.trs.police.subject.sa.controller;

import com.trs.police.subject.domain.dto.ClueExcavateDTO;
import com.trs.police.subject.domain.vo.sa.*;
import com.trs.police.subject.sa.service.SaClueExcavateService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: dingkeyu
 * @date: 2024/11/08
 * @description: 涉案大屏-线索挖掘
 */
@Slf4j
@RestController
@RequestMapping("/sa/clue")
public class SaClueExcavateController {

    @Autowired
    private SaClueExcavateService excavateService;

    /**
     * 集群线索列表
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link JqxsVO}>
     */
    @GetMapping("/jqxsList")
    public RestfulResultsV2<JqxsVO> jqxsList(ClueExcavateDTO dto){
        return excavateService.jqxsList(dto);
    }

    /**
     * 可疑暗娼场所
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link KyaccsVO}>
     */
    @GetMapping("/kyaccsList")
    public RestfulResultsV2<KyaccsVO> kyaccsList(ClueExcavateDTO dto) {
        return excavateService.kyaccsList(dto);
    }

    /**
     * 可疑阳性暗娼人员
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link KyyxacryVO}>
     */
    @GetMapping("/kyyxacryList")
    public RestfulResultsV2<KyyxacryVO> kyyxacryList(ClueExcavateDTO dto) {
        return excavateService.kyyxacryList(dto);
    }

    /**
     * 可追诉人员线索
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link KzsryxsVO}>
     */
    @GetMapping("/kzsryxsList")
    public RestfulResultsV2<KzsryxsVO> kzsryxsList(ClueExcavateDTO dto) {
        return excavateService.kzsryxsList(dto);
    }

    /**
     * 跨区域预警
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link KqyyjVO}>
     */
    @GetMapping("/kqyyjList")
    public RestfulResultsV2<KqyyjVO> kqyyjList(ClueExcavateDTO dto) {
        return excavateService.kqyyjList(dto);
    }
}
