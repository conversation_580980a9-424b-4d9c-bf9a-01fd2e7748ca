package com.trs.police.subject.sw.controller;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonActivityTrendsVO;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.common.service.scene.ISubjectSceneSearch;
import com.trs.police.subject.common.service.scene.SubjectSceneSearchFactory;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人员管控控制器
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Slf4j
@RestController
@RequestMapping(value = {"/sw/person", "/public/sw/person"})
public class SwPersonControl {

    /**
     * 人员区域分布
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/areaDistribution")
    public RestfulResultsV2<PersonalStatisticsVO> areaDistribution(@RequestBody SubjectSceneContext<PersonDTO> context) {
        ISubjectSceneSearch subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("sw-personAreaDistribution");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 活跃人员趋势
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link JSONObject}>
     */
    @PostMapping("/activeStatistic")
    public RestfulResultsV2<PersonActivityTrendsVO> activeStatistic(@RequestBody SubjectSceneContext<PersonDTO> context) {
        ISubjectSceneSearch subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("sw-personActiveStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 布控人员列表
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonVO}>
     */
    @PostMapping("/bkPersonList")
    public RestfulResultsV2<PersonVO> bkPersonList(@RequestBody SubjectSceneContext<PersonDTO> context) {
        AbstractPersonControlAnalysisImpl subjectSceneSearchImpl = (AbstractPersonControlAnalysisImpl) SubjectSceneSearchFactory.getSubjectSceneSearchImpl("sw-personBkPersonList");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context))
                .addTotalCount(subjectSceneSearchImpl.getTotal())
                .addPageNum(context.getPageNum())
                .addPageSize(context.getPageSize());
    }

    /**
     * 顶部统计
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/topStatistics")
    public RestfulResultsV2<PersonalStatisticsVO> topStatistics(@RequestBody SubjectSceneContext<PersonDTO> context) {
        ISubjectSceneSearch subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("sw-personTopStatistics");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 人员轨迹列表
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonTrackVO}>
     */
    @PostMapping("/trackList")
    public RestfulResultsV2<PersonTrackVO> trackList(@RequestBody SubjectSceneContext<PersonDTO> context) {
        ISubjectSceneSearch subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("sw-personTrackList");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }
}
