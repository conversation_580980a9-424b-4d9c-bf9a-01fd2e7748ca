package com.trs.police.subject.jz.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.http2.HttpRequest;
import com.trs.police.subject.jz.config.ModelArsenalTaskConfig;
import com.trs.police.subject.domain.entity.JbGwryyjEntity;
import com.trs.police.subject.domain.entity.RiskyBusinesEntity;
import com.trs.police.subject.common.mapper.JbGwryyjMapper;
import com.trs.police.subject.common.mapper.RiskyBusinessMapper;
import com.trs.police.subject.jz.service.ModelArsenalTaskService;
import com.trs.police.subject.jz.service.impl.mybatis.JbGwryyjMybatisServiceImpl;
import com.trs.police.subject.jz.service.impl.mybatis.RiskyBusinessMybatisServiceImpl;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: gaoyuan
 * @date: 2024/08/07
 * @description: 模型武库模块定时任务业务层
 */
@Slf4j
@Component
public class ModelArsenalTaskServiceImpl implements ModelArsenalTaskService {

    @Autowired
    private RiskyBusinessMapper riskyBusinessMapper;

    @Autowired
    private RiskyBusinessMybatisServiceImpl riskyBusinessMybatisService;

    @Autowired
    private JbGwryyjMapper jbGwryyjMapper;

    @Autowired
    private JbGwryyjMybatisServiceImpl jbGwryyjMybatisService;

    @Autowired
    private ModelArsenalTaskConfig modelArsenalTaskConfig;

    // 请求接口没有其他参数，一次性拿的全部数据，放大连接等待时间
    private final HttpRequest request = new HttpRequest.Builder(
            new OkHttpClient.Builder()
                    .connectTimeout(4, TimeUnit.MINUTES)
                    .readTimeout(4, TimeUnit.MINUTES)
                    .writeTimeout(4, TimeUnit.MINUTES))
            .build();

    @Override
    public void synRiskyBusiness() {
        JSONObject result = doRequest(modelArsenalTaskConfig.riskyBusinessPath);
//        JSONObject result1 = JSONObject.parseObject("{\"status\":200,\"message\":\"success\",\"data\":{\"result\":{\"total_size\":83361,\"cost\":\"12690ms\",\"file_list\":[\"xydm\",\"qymc\",\"qylx\",\"hy\",\"fr\",\"zycyxm\",\"zycysfzh\",\"clrq\",\"djjg\",\"gwhyfz\",\"qyfxlxfz\",\"wnsdjfz\",\"nsrztfz\",\"tdhbtfrfz\",\"dzxjdf\",\"lxdhxtsfz\",\"jcrzfz\",\"nsdjlrsjwtyrfz\",\"tyggsrnzcdjqyjf\",\"nlfz\",\"gwdqfz\",\"flzjryfz\",\"sabjjf\",\"sydjfz\",\"gxrqkbjjf\",\"gxrygszcjf\",\"gjjf\",\"bjzf\"],\"values\":[[\"sidnsaiudnisadnsaifdas\",\"泸州nsdusa有限公司\",\"有限责任公司(dsdsfs)\",\"批发和零售业\",\"xx\",\"xx\",\"510503198410301234\",\"2020-01-03 10:36:03\",\"泸州市江阳区市场监督管理局\",\"2.5\",\"2.5\",\"0\",\"0\",\"0\",\"0\",\"0\",\"5\",\"5\",\"0\",\"2\",\"0\",\"3\",\"0\",\"0\",\"0\",\"0\",\"20\",\"40\"],[\"dcsadsahdbsadja\",\"生猪养殖场\",\"个人独资企业\",\"农、林、牧、渔业\",\"王某某\",\"王某某\",\"51052519830101322X\",\"2008-11-24 00:00:00\",\"市场监督管理局\",\"0\",\"0\",\"5\",\"0\",\"0\",\"0\",\"0\",\"5\",\"0\",\"0\",\"2\",\"0\",\"0\",\"5\",\"3\",\"0\",\"0\",\"20\",\"40\"],[\"nsadnasdnsadna\",\"种植专业合作社\",\"农民专业合作社\",\"农、林、牧、渔业\",\"aasd\",\"dsdaas\",\"510521197111262321\",\"2016-02-19 00:00:00\",\"泸州市江阳区市场监督管理局\",\"0\",\"0\",\"5\",\"0\",\"0\",\"0\",\"0\",\"5\",\"0\",\"0\",\"2\",\"0\",\"3\",\"0\",\"3\",\"0\",\"0\",\"20\",\"38\"],[\"noxzncixucna\",\"物流有限公司\",\"有限责任公司(自然人投资或控股)\",\"交通运输、仓储和邮政业\",\"dd\",\"ff\",\"512901195710090909\",\"2009-12-11 00:00:00\",\"泸州市龙马潭区市场监督管理局\",\"0\",\"2.5\",\"5\",\"0\",\"0\",\"0\",\"0\",\"5\",\"0\",\"0\",\"2\",\"0\",\"3\",\"5\",\"5\",\"0\",\"0\",\"20\",\"47.5\"]]}}}");
//        JSONObject result = result1.getJSONObject("data").getJSONObject("result");
        if (null == result) {
            return;
        }
        JSONArray fileList = result.getJSONArray("file_list");
        JSONArray allValues = result.getJSONArray("values");
        List<RiskyBusinesEntity> entities = new ArrayList<>();
        for (int i = 0; i < allValues.size(); i++) {
            RiskyBusinesEntity entity = buildRiskyBusinessEntity(fileList, allValues.getJSONArray(i));
            Optional.ofNullable(entity).ifPresent(entities::add);
            if (entities.size() >= modelArsenalTaskConfig.batchSize) {
                saveOrUpdateRiskyBusinesEntity(entities);
                entities.clear();
            }
        }
        if (!entities.isEmpty()) {
            saveOrUpdateRiskyBusinesEntity(entities);
        }
    }

    private RiskyBusinesEntity buildRiskyBusinessEntity(JSONArray fileList, JSONArray jsonArray) {
        RiskyBusinesEntity entity = new RiskyBusinesEntity();
        for (int i = 0; i < fileList.size(); i++) {
            String field = fileList.getString(i);
            fillRiskyBusinessFieldValue(entity, field, i, jsonArray);
        }
        return entity;
    }

    private void fillRiskyBusinessFieldValue(RiskyBusinesEntity entity, String field, Integer index, JSONArray jsonArray) {
        switch (field) {
            case "xydm": entity.setXydm(jsonArray.getString(index)); break;
            case "qymc": entity.setQymc(jsonArray.getString(index)); break;
            case "qylx": entity.setQylx(jsonArray.getString(index)); break;
            case "hy": entity.setHy(jsonArray.getString(index)); break;
            case "fr": entity.setFr(jsonArray.getString(index)); break;
            case "zycyxm": entity.setZycyxm(jsonArray.getString(index)); break;
            case "zycysfzh": entity.setZycysfzh(jsonArray.getString(index)); break;
            case "clrq": entity.setClrq(jsonArray.getString(index)); break;
            case "djjg": entity.setDjjg(jsonArray.getString(index)); break;
            case "gwhyfz": entity.setGwhyfz(jsonArray.getString(index)); break;
            case "qyfxlxfz": entity.setQyfxlxfz(jsonArray.getString(index)); break;
            case "wnsdjfz": entity.setWnsdjfz(jsonArray.getString(index)); break;
            case "nsrztfz": entity.setNsrztfz(jsonArray.getString(index)); break;
            case "tdhbtfrfz": entity.setTdhbtfrfz(jsonArray.getString(index)); break;
            case "dzxjdf": entity.setDzxjdf(jsonArray.getString(index)); break;
            case "lxdhxtsfz": entity.setLxdhxtsfz(jsonArray.getString(index)); break;
            case "jcrzfz": entity.setJcrzfz(jsonArray.getString(index)); break;
            case "nsdjlrsjwtyrfz": entity.setNsdjlrsjwtyrfz(jsonArray.getString(index)); break;
            case "tyggsrnzcdjqyjf": entity.setTyggsrnzcdjqyjf(jsonArray.getString(index)); break;
            case "nlfz": entity.setNlfz(jsonArray.getString(index)); break;
            case "gwdqfz": entity.setGwdqfz(jsonArray.getString(index)); break;
            case "flzjryfz": entity.setFlzjryfz(jsonArray.getString(index)); break;
            case "sabjjf": entity.setSabjjf(jsonArray.getString(index)); break;
            case "sydjfz": entity.setSydjfz(jsonArray.getString(index)); break;
            case "gxrqkbjjf": entity.setGxrqkbjjf(jsonArray.getString(index)); break;
            case "gxrygszcjf": entity.setGxrygszcjf(jsonArray.getString(index)); break;
            case "gjjf": entity.setGjjf(jsonArray.getString(index)); break;
            case "bjzf": entity.setBjzf(jsonArray.getDoubleValue(index)); break;
            default: break;
        }
    }

    @Override
    public void synJbGwryyj() {
        JSONObject result = doRequest(modelArsenalTaskConfig.jbGwryyjPath);
//        JSONObject object = JSONObject.parseObject("{\"status\":200,\"message\":\"success\",\"data\":{\"result\":{\"total_size\":77553,\"cost\":\"5576ms\",\"file_list\":[\"xyrxm\",\"xyrcyzjzjhm\",\"xyrlxdh\",\"jg_dmzh\",\"xb\",\"nl\",\"s\",\"x\",\"s\",\"facs\",\"jgfs_bzh\",\"nlfs_bzh\",\"xbfs_bzh\",\"qkfs_bzh\",\"zdqyfs_bzh\",\"lcplfs_bzh\",\"thfs_bzh\",\"zf\"],\"values\":[[\"王xx\",\"510521194901015218\",null,\"510502\",\"男\",\"75\",\"四川省\",\"江阳区\",\"泸州市\",\"1\",\"0\",\"0\",\"3\",\"4\",\"0\",\"0\",\"0\",\"7\"],[\"王x\",\"510521198902224072\",null,\"510521\",\"男\",\"35\",\"四川省\",\"泸县\",\"泸州市\",\"1\",\"0\",\"3\",\"3\",\"4\",\"0\",\"0\",\"0\",\"10\"],[\"邓xx\",\"510521198601011411\",null,\"510521\",\"男\",\"38\",\"四川省\",\"泸县\",\"泸州市\",\"17\",\"0\",\"3\",\"3\",\"6\",\"0\",\"0\",\"0\",\"12\"],[\"邱xx\",\"510502195601141111\",null,\"510502\",\"男\",\"68\",\"四川省\",\"江阳区\",\"泸州市\",\"1\",\"0\",\"0\",\"3\",\"4\",\"0\",\"0\",\"0\",\"7\"],[\"张xx\",\"510502196807111111\",null,\"510502\",\"男\",\"56\",\"四川省\",\"江阳区\",\"泸州市\",\"6\",\"0\",\"3\",\"3\",\"6\",\"0\",\"0\",\"0\",\"12\"]]}}}");
//        JSONObject result = object.getJSONObject("data").getJSONObject("result");
        if (null == result) {
            return;
        }
        JSONArray fileList = result.getJSONArray("file_list");
        JSONArray allValues = result.getJSONArray("values");
        List<JbGwryyjEntity> entities = new ArrayList<>();
        for (int i = 0; i < allValues.size(); i++) {
            JbGwryyjEntity entity = buildJbGwryyjEntity(fileList, allValues.getJSONArray(i));
            Optional.ofNullable(entity).ifPresent(entities::add);
            if (entities.size() >= modelArsenalTaskConfig.batchSize) {
                saveOrUpdateRiskyJbGwryyjEntity(entities);
                entities.clear();
            }
        }
        if (!entities.isEmpty()) {
            saveOrUpdateRiskyJbGwryyjEntity(entities);
        }
    }

    private JbGwryyjEntity buildJbGwryyjEntity(JSONArray fileList, JSONArray jsonArray) {
        JbGwryyjEntity entity = new JbGwryyjEntity();
        for (int i = 0; i < fileList.size(); i++) {
            String file = fileList.getString(i);
            fillJbGwryyjFieldValue(entity, file, i, jsonArray);
        }
        return entity;
    }

    private JbGwryyjEntity fillJbGwryyjFieldValue(JbGwryyjEntity entity, String file, Integer index, JSONArray jsonArray) {
        switch (file) {
            case "xyrxm": entity.setXyrxm(jsonArray.getString(index)); break;
            case "xyrcyzjzjhm": entity.setXyrcyzjzjhm(jsonArray.getString(index)); break;
            case "xyrlxdh": entity.setXyrlxdh(jsonArray.getString(index)); break;
            case "jg_dmzh": entity.setJgDmzh(jsonArray.getString(index)); break;
            case "xb": entity.setXb(jsonArray.getString(index)); break;
            case "nl": entity.setNl(jsonArray.getIntValue(index)); break;
            case "sf": entity.setSf(jsonArray.getString(index)); break;
            case "s": entity.setS(jsonArray.getString(index)); break;
            case "x": entity.setX(jsonArray.getString(index)); break;
            case "facs": entity.setFacs(jsonArray.getIntValue(index)); break;
            case "jgfs_bzh": entity.setJgfsBzh(jsonArray.getIntValue(index)); break;
            case "nlfs_bzh": entity.setNlfsBzh(jsonArray.getIntValue(index)); break;
            case "xbfs_bzh": entity.setXbfsBzh(jsonArray.getIntValue(index)); break;
            case "qkfs_bzh": entity.setQkfsBzh(jsonArray.getIntValue(index)); break;
            case "zdqyfs_bzh": entity.setZdqyfsBzh(jsonArray.getIntValue(index)); break;
            case "lcplfs_bzh": entity.setLcplfsBzh(jsonArray.getIntValue(index)); break;
            case "thfs_bzh": entity.setThfsBzh(jsonArray.getIntValue(index)); break;
            case "zf": entity.setZf(jsonArray.getIntValue(index)); break;
            default: break;
        }
        return entity;
    }

    private JSONObject doRequest(String path) {
        try {
            String requestUrl = modelArsenalTaskConfig.url + path;
            Headers headers = new Headers.Builder()
                    .add("Authorization", modelArsenalTaskConfig.bearerToken).build();
            String string = request.doGet(requestUrl, headers);
            JSONObject object = JSONObject.parseObject(string);
            int status = object.getIntValue("status");
            if (status != 200) {
                throw new Exception(String.format("查询接口%s异常，错误信息%s", requestUrl, object.toJSONString()));
            }
            return object.getJSONObject("data")
                    .getJSONObject("result");
        } catch (Exception e) {
            log.error("发送http请求异常：" + e);
            return null;
        }
    }

    private void saveOrUpdateRiskyBusinesEntity(List<RiskyBusinesEntity> entityList) {
        List<String> xydmList = entityList.stream()
                .map(RiskyBusinesEntity::getXydm)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (xydmList.isEmpty()) {
            return;
        }
        Map<String, Long> xydmAndIdMap = riskyBusinessMapper.selectExistXydm(xydmList).stream()
                .collect(Collectors.toMap(RiskyBusinesEntity::getXydm, RiskyBusinesEntity::getId, (x1, x2) -> x1));
        Map<Boolean, List<RiskyBusinesEntity>> booleanListMap = entityList.stream()
                .filter(entity -> Objects.nonNull(entity.getXydm()))
                .peek(entity -> entity.setId(xydmAndIdMap.get(entity.getXydm())))
                .collect(Collectors.groupingBy(entity -> Objects.nonNull(entity.getId())));
        List<RiskyBusinesEntity> trueEntityList = booleanListMap.get(Boolean.TRUE);
        Optional.ofNullable(trueEntityList).ifPresent(riskyBusinessMybatisService::updateBatchById);
        List<RiskyBusinesEntity> falseEntityList = booleanListMap.get(Boolean.FALSE);
        Optional.ofNullable(falseEntityList).ifPresent(riskyBusinessMybatisService::saveBatch);
    }

    private void saveOrUpdateRiskyJbGwryyjEntity(List<JbGwryyjEntity> entityList) {
        List<String> zjhmList = entityList.stream()
                .map(JbGwryyjEntity::getXyrcyzjzjhm)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, Long> zjhmAndIdMap = jbGwryyjMapper.selectExistZjhm(zjhmList).stream()
                .collect(Collectors.toMap(JbGwryyjEntity::getXyrcyzjzjhm, JbGwryyjEntity::getId, (x1, x2) -> x1));
        Map<Boolean, List<JbGwryyjEntity>> booleanListMap = entityList.stream()
                .filter(entity -> Objects.nonNull(entity.getXyrcyzjzjhm()))
                .peek(entity -> entity.setId(zjhmAndIdMap.get(entity.getXyrcyzjzjhm())))
                .collect(Collectors.groupingBy(entity -> Objects.nonNull(entity.getId())));
        List<JbGwryyjEntity> trueEntityList = booleanListMap.get(Boolean.TRUE);
        Optional.ofNullable(trueEntityList).ifPresent(jbGwryyjMybatisService::updateBatchById);
        List<JbGwryyjEntity> falseEntityList = booleanListMap.get(Boolean.FALSE);
        Optional.ofNullable(falseEntityList).ifPresent(jbGwryyjMybatisService::saveBatch);
    }

}
