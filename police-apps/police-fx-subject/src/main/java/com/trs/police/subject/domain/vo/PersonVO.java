package com.trs.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 管控人员
 *
 * <AUTHOR>
 * @date 2024/4/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
public class PersonVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 真实姓名
     */
    @ExcelProperty("姓名")
    private String realName;

    /**
     * 性别
     */
    @ExcelProperty("性别")
    private String gender;

    /**
     * 年龄
     */
    @ExcelProperty("年龄")
    private Integer age;

    /**
     * 民族
     */
    @ExcelProperty("民族")
    private String nation;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate birthday;

    /**
     * 身份证号
     */
    @ExcelProperty("身份证号")
    private String idCard;

    /**
     * 人员编号
     */
    @ExcelProperty("人员编号")
    private String personNumber;

    /**
     * 组织职务
     */
    @ExcelProperty("组织职务")
    private String orgPost;

    /**
     * 加入组织时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ExcelProperty("加入组织时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date orgJoinTime;

    /**
     * 人员类型
     */
    @ExcelProperty("人员类型")
    private String personType;

    /**
     * 模型id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> modelId;

    /**
     * 人员状态
     */
    @ExcelProperty("人员状态")
    private String personStatus;

    /**
     * 列控状态
     */
    @ExcelProperty("列控状态")
    private String lkStatus;

    /**
     * 列控时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ExcelProperty("列控时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date lkTime;

    /**
     * 思想状况
     */
    @ExcelProperty("思想状况")
    private String thought;

    /**
     * 积分
     */
    @ExcelProperty("积分情况")
    private Integer score;

    /**
     * 地域编码
     */
    private String areaCode;

    /**
     * 地域名称
     */
    @ExcelProperty("所属区县")
    private String areaName;

    /**
     * 流动状态
     */
    private String flowStatus;

    /**
     * 活跃状态
     */
    private String activeStatus;

    /**
     * 证件照
     */
    private List<FileInfoVO> photo;

    /**
     * 管控派出所
     */
    private String controlPoliceStation;

    /**
     * 管控单位
     */
    @ExcelProperty("稳控单位")
    private String controlUnit;

    /**
     * 管控地域
     */
    private String controlAreaCode;

    /**
     * 管控地域名称
     */
    private String controlAreaName;

    /**
     * 管控责任人
     */
    @ExcelProperty("稳控联系人")
    private String controlZrr;

    /**
     * 责任人电话
     */
    @ExcelProperty("稳控联系人电话")
    private String zrrPhone;

    /**
     * 人员类别
     */
    @ExcelProperty("人员类别")
    private String personCategory;

    /**
     * 人员标签
     */
    private List<Long> personLabelIds;

    /**
     * 是否关注
     */
    private Integer careStatus;

    /**
     * 户籍地
     */
    @ExcelProperty("户籍地")
    private String hjd;

    /**
     * 联系电话
     */
    @ExcelProperty("联系电话")
    private String lxdh;

    /**
     * 群体
     */
    @ExcelProperty("群体")
    private String groupName;

    /**
     * 主要诉求
     */
    @ExcelProperty("主要诉求")
    private String zysq;

    /**
     * 稳控状态
     */
    @ExcelProperty("稳控状态")
    private String wkStatus;

    /**
     * 历史上访人员频次
     */
    @ExcelProperty("历史上访人员频次")
    private Integer lssfrypc;

    /**
     * 历史集访人员频次
     */
    @ExcelProperty("历史集访人员频次")
    private Integer lsjfrypc;

    /**
     * 标签
     */
    private String tag;

    /**
     * 诊断日期
     */
    private String zdrq;

    /**
     * 报告单位
     */
    private String bgdw;

    /**
     * 预警id
     */
    private String warningIds;

    /**
     * 预警级别
     */
    private String warningLevel;

    /**
     * 抓拍时间
     */
    private String captureTime;

    /**
     * 活动地址
     */
    private String activityAddress;

    /**
     * 抓拍图片
     */
    private String capturePhoto;

    /**
     * 是否建档：0为未建档，1为已建档
     */
    private Integer onRecord;
}
