package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.entity.WarningProcessEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/04/26
 * @description:
 */
@Mapper
public interface WarningProcessMapper extends BaseMapper<WarningProcessEntity> {

    /**
     * 获取用户预警预警流程
     *
     * @param warningIds 预警id
     * @param userId    用户id
     * @param deptId    部门id
     * @return {@link  WarningProcessEntity}
     */
    List<WarningProcessEntity> getUserProcessByWarningId(@Param("warningIds") List<Long> warningIds, @Param("userId") Long userId,
                                                   @Param("deptId") Long deptId);

    /**
     * 获取用户预警预警流程
     *
     * @param warningIds 预警ids
     * @return {@link List}<{@link WarningProcessEntity}>
     */
    List<WarningProcessEntity> getProcessByWarningId(@Param("warningIds") List<Long> warningIds);
}