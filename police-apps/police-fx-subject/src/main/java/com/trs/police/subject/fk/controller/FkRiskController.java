package com.trs.police.subject.fk.controller;

import com.trs.police.subject.common.service.scene.SubjectSceneSearchFactory;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.ImportantAreaVO;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.sw.service.scene.AbstractSwSituationAnalysisImpl;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 高新反恐大屏-风险防控
 *
 * <AUTHOR>
 * @date 2424/06/26
 */
@RequestMapping("/fk/riskControl")
@RestController
public class FkRiskController {

    /**
     * 高新FK专题大屏-风险防控-重点目标单位
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/importantTargetUnits")
    public RestfulResultsV2<ImportantAreaVO> personTrend(@RequestBody SubjectSceneContext<StatisticsDTO> context) {
        AbstractSwSituationAnalysisImpl subjectSceneSearchImpl = (AbstractSwSituationAnalysisImpl) SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-importantTargetUnits");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context))
                .addTotalCount(subjectSceneSearchImpl.getTotal())
                .addPageNum(context.getPageNum())
                .addPageSize(context.getPageSize());
    }

    /**
     * 高新FK专题大屏-风险防控-区域人员轨迹
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/areaPersonTrackList")
    public RestfulResultsV2<PersonTrackVO> areaPersonTrackList(@RequestBody SubjectSceneContext<PersonDTO> context) {
        AbstractSwSituationAnalysisImpl subjectSceneSearchImpl = (AbstractSwSituationAnalysisImpl) SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-areaPersonTrackList");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context))
                .addTotalCount(subjectSceneSearchImpl.getTotal())
                .addPageNum(context.getPageNum())
                .addPageSize(context.getPageSize());
    }

    /**
     * 高新FK专题大屏-风险防控-人员异常行为
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/abnormalBehavior")
    public RestfulResultsV2<PersonTrackVO> abnormalBehavior(@RequestBody SubjectSceneContext<PersonDTO> context) {
        AbstractSwSituationAnalysisImpl subjectSceneSearchImpl = (AbstractSwSituationAnalysisImpl) SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-abnormalBehavior");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context))
                .addTotalCount(subjectSceneSearchImpl.getTotal())
                .addPageNum(context.getPageNum())
                .addPageSize(context.getPageSize());
    }

}
