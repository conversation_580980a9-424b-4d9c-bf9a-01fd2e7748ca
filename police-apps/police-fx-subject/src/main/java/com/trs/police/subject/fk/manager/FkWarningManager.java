package com.trs.police.subject.fk.manager;

import com.trs.police.common.core.constant.enums.WarningFeedbackTypeEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.params.DeptRequestParams;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.subject.fk.config.FkWarningModalConfig;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.common.mapper.WarningFeedbackFkrxyjMapper;
import com.trs.police.subject.common.mapper.WarningFkrxyjMapper;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 高新反馈预警 管理层
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Slf4j
@Component
public class FkWarningManager {

    @Resource
    private WarningFkrxyjMapper warningFkrxyjMapper;

    @Resource
    private WarningFeedbackFkrxyjMapper warningFeedbackFkrxyjMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private DictService dictService;

    @Resource
    private FkWarningModalConfig fkWarningModalConfig;

    /**
     * 预警统计-高新派出所
     *
     * @param dto dto
     * @return 各派出所的统计数据
     */
    public RestfulResultsV2<PersonalStatisticsVO> warningStatistics(StatisticsDTO dto) {
        DeptRequestParams params = new DeptRequestParams();
        params.setPid(1L);
        DictDto dict = dictService.getDictByTypeAndName("dept_type", "派出所");
        params.setType(dict.getCode().intValue());
        final List<DeptDto> deptDtos = permissionService.getDeptByParams(params);
        List<PersonalStatisticsVO> warningStatisticsVos = warningFkrxyjMapper.warningStatistics(dto);
        List<PersonalStatisticsVO> signStatisticsVos = warningFeedbackFkrxyjMapper.signWarningStatistics(dto, WarningFeedbackTypeEnum.SIGN.getCode().longValue());
        List<PersonalStatisticsVO> feedbackStatisticsVos = warningFeedbackFkrxyjMapper.feedbackWarningStatistics(dto, WarningFeedbackTypeEnum.FEEDBACK.getCode().longValue());
        Map<String, Long> waringMap = warningStatisticsVos.stream().collect(Collectors.toMap(PersonalStatisticsVO::getAreaName, PersonalStatisticsVO::getWarningCount, (c1, c2) -> c1));
        Map<String, Long> signMap = signStatisticsVos.stream().collect(Collectors.toMap(PersonalStatisticsVO::getAreaName, PersonalStatisticsVO::getSignCount, (c1, c2) -> c1));
        Map<String, Long> feedbackMap = feedbackStatisticsVos.stream().collect(Collectors.toMap(PersonalStatisticsVO::getAreaName, PersonalStatisticsVO::getFeedbackCount, (c1, c2) -> c1));
        List<PersonalStatisticsVO> statisticsVos = deptDtos.stream().map(deptDto -> {
                    String shortName = deptDto.getShortName();
                    return buildStatisticsVO(deptDto, waringMap.get(shortName), signMap.get(shortName), feedbackMap.get(shortName));
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparingLong((PersonalStatisticsVO vo) -> vo.getWarningCount() + vo.getSignCount() + vo.getFeedbackCount()).reversed())
                .collect(Collectors.toList());
        return RestfulResultsV2.ok(statisticsVos);
    }

    private PersonalStatisticsVO buildStatisticsVO(DeptDto deptDto, Long warningCount, Long signCount, Long feedbackCount) {
        if (warningCount == null && signCount == null && feedbackCount == null) {
            return null;
        }
        PersonalStatisticsVO vo = new PersonalStatisticsVO();
        vo.setAreaCode(deptDto.getCode());
        vo.setAreaName(deptDto.getShortName());
        vo.setWarningCount(warningCount == null ? 0 : warningCount);
        vo.setSignCount(signCount == null ? 0 : signCount);
        vo.setFeedbackCount(feedbackCount == null ? 0 : feedbackCount);
        return vo;
    }

    /**
     * 顶部统计
     *
     * @param dto dto
     * @return 统计数据
     */
    public RestfulResultsV2<PersonalStatisticsVO> topStatistics(StatisticsDTO dto) {
        PersonalStatisticsVO vo = new PersonalStatisticsVO();
        // 1、常控：人员数 （人档fk人数）
        Long totalCount = warningFkrxyjMapper.profilePersonFkgzStatistic(dto, fkWarningModalConfig.fkLabelIds);
        vo.setTotalCount(totalCount);
        // 2、首入：人员数 (首次入区：收入预警的人数)
        Long activeCount = warningFkrxyjMapper.firstInfoPersonStatistic(dto);
        vo.setActiveCount(activeCount);
        // 3、预警：人员数（所有预警人数）
        Long warningCount = warningFkrxyjMapper.warningPersonStatistic(dto, null, null);
        vo.setWarningCount(warningCount);
        // 4、线索：=除开蓝色预警的人员数
        List<Long> monitorLevelList = Arrays.stream(fkWarningModalConfig.fkWarningLevel.split(",")).map(Long::valueOf).collect(Collectors.toList());
        Long clueCount = warningFkrxyjMapper.warningPersonStatistic(dto, null, monitorLevelList);
        vo.setClueCount(clueCount);
        // 5、专家数：3个
        vo.setZaCount(fkWarningModalConfig.fkZjCount);
        // 6、战果数：写死-5个
        vo.setFightResultCount(fkWarningModalConfig.fkFightResultCount);
        return RestfulResultsV2.ok(vo);
    }
}
