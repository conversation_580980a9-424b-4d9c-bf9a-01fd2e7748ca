package com.trs.police.subject.fk.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员管控-人员动向
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class FkPersonTrendServiceImpl extends AbstractPersonControlAnalysisImpl<PersonVO> {

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Override
    protected List<PersonVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            Page<Object> page = new Page<>(context.getPageNum(), context.getPageSize());
            Page<PersonVO> pageList = personMapper.fkPersonLocalTrend(dto,page);
            // 设置总数
            this.total = pageList.getTotal();
            if (CollectionUtils.isEmpty(pageList.getRecords())){
                return new ArrayList<>();
            }
            getPersonLabels(pageList.getRecords());
            return pageList.getRecords();
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }

    private void getPersonLabels(List<PersonVO> records) {
        // 获取人员标签名称
        Set<Long> personLabelIds = records.stream().flatMap(o -> o.getPersonLabelIds().stream()).collect(Collectors.toSet());
        Map<String, String> personLabelMap = fkPersonMapper.getPersonLabelName(personLabelIds).stream()
                .collect(Collectors.toMap(KeyValueVO::getKey, KeyValueVO::getValue));
        // 设置人员标签名称和预警级别名称
        records.forEach(vo -> {
            vo.setPersonType(vo.getPersonLabelIds().stream()
                    .map(v -> personLabelMap.get(v.toString()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(",")));
        });
    }

    @Override
    public String key() {
        return "fk-personTrend";
    }

    @Override
    public String desc() {
        return "人员动向";
    }
}
