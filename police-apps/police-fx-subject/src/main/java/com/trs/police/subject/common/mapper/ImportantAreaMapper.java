package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.vo.ImportantAreaVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 重点管控区域(ImportantArea)持久层
 *
 * <AUTHOR>
 * @date 2022-06-01 15:46:21
 */
@Mapper
public interface ImportantAreaMapper extends BaseMapper<ImportantAreaEntity> {

    /**
     * 获取风险防控-重点目标单位
     *
     * @param dto dto
     * @param page page
     * @return {@link Page}<{@link ImportantAreaVO}>
     */
    Page<ImportantAreaVO> fkImportantTargetUnits(StatisticsDTO dto, Page<Object> page);

}