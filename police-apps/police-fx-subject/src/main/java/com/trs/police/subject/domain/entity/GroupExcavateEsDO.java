package com.trs.police.subject.domain.entity;

import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.annotations.TableName;
import com.trs.db.sdk.pojo.BaseRecordDO;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: dingkeyu
 * @date: 2024/07/05
 * @description: 群体线索挖掘
 */
@Data
@NoArgsConstructor
@TableName("${theme.fx.qtwj.index:theme_fx_xswj_qtwj}")
public class GroupExcavateEsDO extends BaseRecordDO {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableField(value = "trs_id")
    private String trsId;

    /**
     * 群体名称
     */
    @TableField(value = "qtmc")
    private String qtmc;

    /**
     * 入库时间
     */
    @TableField(value = "trs_in_time")
    private String trsInTime;

    /**
     * 来源时间
     */
    @TableField(value = "trs_source_time")
    private String trsSourceTime;

    /**
     * 来源
     */
    @TableField(value = "trs_source_from")
    private String trsSourceFrom;

    /**
     * 姓名
     */
    @TableField(value = "xm")
    private String xm;

    /**
     * 证件号码
     */
    @TableField(value = "zjhm")
    private String zjhm;

}
