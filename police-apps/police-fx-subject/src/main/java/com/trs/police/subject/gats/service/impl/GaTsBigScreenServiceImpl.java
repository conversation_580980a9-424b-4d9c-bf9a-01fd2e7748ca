package com.trs.police.subject.gats.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.StringUtils;
import com.trs.log.exception.RecordableException;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.subject.gats.constant.GaTsConstant;
import com.trs.police.subject.domain.dto.SituationStatisticDto;
import com.trs.police.subject.domain.entity.GaTsBigScreenMapInfo;
import com.trs.police.subject.domain.vo.GaTsCommonVO;
import com.trs.police.subject.common.mapper.GaTsBigScreenMapinfoMapper;
import com.trs.police.subject.common.mapper.GaTsBigScreenMapper;
import com.trs.police.subject.common.mapper.ProfileCaseEntityMapper;
import com.trs.police.subject.gats.service.GaTsBigScreenService;
import com.trs.police.subject.sa.service.gaTsBigscreen.GaSituationStatisticService;
import com.trs.police.subject.sa.service.gaTsBigscreen.scoreCompute.ScoreComputeService;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import com.trs.police.subject.domain.dto.MajorSensitiveCasesListDTO;
import com.trs.police.subject.domain.vo.MajorSensitiveCasesListVO;
import com.trs.police.subject.common.mapper.ProfileSthyMapper;

/**
 *
 */
@Service
@Slf4j
public class GaTsBigScreenServiceImpl implements GaTsBigScreenService {

    private final HttpRequest httpRequest = new HttpRequest.Builder().build();

    @Autowired
    private DictService dictService;

    @Autowired
    private List<GaSituationStatisticService> situationStatisticServices;

    @Autowired
    private List<ScoreComputeService> scoreComputeServices;

    @Autowired
    private GaTsBigScreenMapper gaTsBigScreenMapper;

    @Autowired
    private GaTsBigScreenMapinfoMapper mapinfoMapper;

    @Autowired
    private ProfileSthyMapper profileSthyMapper;

    @Autowired
    private ProfileCaseEntityMapper profileCaseMapper;
    @Override
    public RestfulResultsV2<GaTsCommonVO> groupGather(SituationStatisticDto dto) {
        Integer groupGatherType = BeanFactoryHolder.getEnv().getProperty("com.trs.fxSubject.gaTsBigScreen.groupGatherType"
                , Integer.class, 2);
        IPage<GaTsCommonVO> pageResult = Page.of(dto.getPageNum(),dto.getPageSize());
        String areaCodePrefix = getCurrentUserDistrict();
        pageResult = gaTsBigScreenMapper.selectYaoQingPage(pageResult, dto, groupGatherType, areaCodePrefix);
        if (CollectionUtils.isEmpty(pageResult.getRecords())){
            return RestfulResultsV2.ok(new ArrayList<>()).addTotalCount(0L).addPageSize(dto.getPageSize())
                    .addPageNum(dto.getPageNum());
        }
        for (GaTsCommonVO record : pageResult.getRecords()) {
            JSONObject jsonObject = JSONObject.parseObject(record.getAreaInfo(), JSONObject.class);
            record.setAreaName(Objects.nonNull(jsonObject) ? jsonObject.getString("name") : "");
            record.setAreaObj(jsonObject);
        }
        return RestfulResultsV2.ok(pageResult.getRecords()).addTotalCount(pageResult.getTotal()).addPageSize(dto.getPageSize())
                .addPageNum(dto.getPageNum());
    }

    @Override
    public List<GaTsCommonVO> majorSecurityEvents(SituationStatisticDto dto) {
        //todo 调用第三方勤务系统获取数据
        String url = BeanFactoryHolder.getEnv().getProperty("com.trs.fxSubject.gaTsBigScreen.majorSecurityEvents.url"
                , "");
        if (StringUtils.isEmpty(url)){
            return new ArrayList<>();
        }
        String response;
        try {
            response = httpRequest.doGet(url);
        } catch (RecordableException e) {
            log.info("调用第三方勤务系统获取数据失败");
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }

    @Override
    public RestfulResultsV2<CountItem> doStatistic(SituationStatisticDto dto, String key) {
        GaSituationStatisticService gaSituationStatisticService = situationStatisticServices.stream()
                .filter(e -> key.equals(e.getServiceKey()))
                .findFirst().orElse(null);
        if (Objects.isNull(gaSituationStatisticService)){
            return RestfulResultsV2.ok(new CountItem());
        }
        List<CountItem> list = gaSituationStatisticService.doStatistic(dto);
        return RestfulResultsV2.ok(list.get(0));
    }

    @Override
    public RestfulResultsV2<CountItem> abnormalPersonYj(SituationStatisticDto dto){
        GaSituationStatisticService gaSituationStatisticService = situationStatisticServices.stream()
                .filter(e -> GaTsConstant.ABNORMAL_PERSON_YJ_STATISTICS.equals(e.getServiceKey()))
                .findFirst().orElse(null);
        if (Objects.isNull(gaSituationStatisticService)){
            return RestfulResultsV2.ok(new CountItem());
        }
        List<CountItem> list = gaSituationStatisticService.doStatistic(dto);
        return RestfulResultsV2.ok(list);
    }

    @Override
    public List<GaTsCommonVO> centerMapInfo(SituationStatisticDto dto) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        DeptDto dept = currentUser.getDept();
        Preconditions.checkNotNull(dept, "当前用户未分配部门");
        //根据当前登录用户部门获取可见区域
        List<DistrictDto> districtList = getDistrictList(dept);
        if (CollectionUtils.isEmpty(districtList)){
            return new ArrayList<>();
        }
        List<String> districtCodeList = districtList.stream().map(DistrictDto::getCode).collect(Collectors.toList());
        districtCodeList = districtCodeList.stream().sorted().collect(Collectors.toList());
        //获取区域代码map
        Map<String, String> districtMap = CollectionUtils.isEmpty(districtList) ? new HashMap<>()
                : districtList.stream().collect(Collectors.toMap(DistrictDto::getCode, DistrictDto::getName));
        //是否计算分数
        List<GaTsBigScreenMapInfo> gaTsBigScreenMapInfos = mapinfoMapper.selectList(new QueryWrapper<GaTsBigScreenMapInfo>()
                .eq("parent_district", dept.getDistrictCode())
                .le(StringUtils.isNotEmpty(dto.getEndTime()), "statistic_date", dto.getEndTime())
                .ge(StringUtils.isNotEmpty(dto.getStartTime()), "statistic_date", dto.getStartTime())
                .orderByDesc("statistic_date"));
        if (CollectionUtils.isEmpty(gaTsBigScreenMapInfos)){
            computeScore();
            gaTsBigScreenMapInfos = mapinfoMapper.selectList(new QueryWrapper<GaTsBigScreenMapInfo>()
                    .eq("parent_district", dept.getDistrictCode())
                    .le(StringUtils.isNotEmpty(dto.getEndTime()), "statistic_date", dto.getEndTime())
                    .ge(StringUtils.isNotEmpty(dto.getStartTime()), "statistic_date", dto.getStartTime())
                    .orderByDesc("statistic_date"));
        }
        Map<String, List<GaTsBigScreenMapInfo>> mapInfo = CollectionUtils.isNotEmpty(gaTsBigScreenMapInfos)
                ? gaTsBigScreenMapInfos.stream().collect(Collectors.groupingBy(GaTsBigScreenMapInfo::getDistrictCode))
                : new HashMap<>();
        List<GaTsCommonVO> resultList = new ArrayList<>();
        log.info("区域信息：{}",districtCodeList);
        for (String code : districtCodeList) {
            GaTsCommonVO vo = new GaTsCommonVO();
            List<JqCommonVO> currentJqList = gaTsBigScreenMapper.selectJqListByDto(dto, code);
            vo.setCurrentJqList(currentJqList);
            vo.setDistrictCode(code);
            vo.setDistrictName(districtMap.get(code));
            List<GaTsBigScreenMapInfo> scoreInfoList = mapInfo.get(code);
            Double score = CollectionUtils.isEmpty(scoreInfoList) ? 0.0
                    : scoreInfoList.get(0).getScore();
            vo.setSmoothScore(score);
            vo.setSmoothStatus(getStatusByScore(score));
            resultList.add(vo);
        }
        return resultList;
    }

    @Override
    public void computeScore() {
        log.info("安稳分数计算开始");
        String parentAreaCode = BeanFactoryHolder.getEnv().getProperty("com.trs.fxSubject.gaTsBigScreen.parentCode", "511600");
        DistrictDto dto = new DistrictDto();
        dto.setCode(parentAreaCode);
        dto.setName("广安市");
        List<DistrictDto> districtList = dictService.getOneLayerDistrictList(parentAreaCode);
        districtList.add(dto);
        scoreComputeServices = scoreComputeServices.stream()
                .filter(e -> e.execute()).collect(Collectors.toList());
        for (DistrictDto districtDto : districtList) {
            GaTsBigScreenMapInfo info = new GaTsBigScreenMapInfo();
            JSONObject jsonObject = new JSONObject();
            Double score = 0.0;
            for (ScoreComputeService e : scoreComputeServices) {
                score += e.compute(districtDto,info,jsonObject);
            }
            info.setScore(score > Double.MAX_VALUE ? 100.0 : score);
            info.setScoreDetail(JSONObject.toJSONString(jsonObject));
            info.setCreateTime(LocalDateTime.now());
            info.setUpdateTime(LocalDateTime.now());
            info.setDistrictCode(districtDto.getCode());
            info.setDistrictName(districtDto.getName());
            info.setParentDistrict(parentAreaCode);
            info.setStatisticDate(LocalDateTime.now());
            info.setDeleted(false);
            mapinfoMapper.insert(info);
        }
        log.info("安稳分数计算结束");
    }

    @Override
    public RestfulResultsV2<MajorSensitiveCasesListVO> getMajorSensitiveCasesList(MajorSensitiveCasesListDTO dto) {
        // 获取当前用户所在区域代码
        String districtCode = getCurrentUserDistrict();

        // 根据type判断查询警情还是案件
        if ("jq".equals(dto.getType())) {
            // 查询警情
            return queryPoliceIncidents(dto, districtCode);
        } else if ("aj".equals(dto.getType())) {
            // 查询案件
            return queryCases(dto, districtCode);
        } else {
            return RestfulResultsV2.error("无效的查询类型");
        }
    }

    /**
     * 警情列表
     *
     * @param dto dto
     * @param districtCodePrefix 区域代码前缀
     * @return 结果
     */
    private RestfulResultsV2<MajorSensitiveCasesListVO> queryPoliceIncidents(MajorSensitiveCasesListDTO dto, String districtCodePrefix) {
        // 获取关键词配置
        String keyWord = BeanFactoryHolder.getEnv().getProperty("com.trs.fxSubject.gaTsBigScreen.keyword","");
        List<String> keywordList = StringUtils.isEmpty(keyWord) ? new ArrayList<>()
                : Arrays.asList(keyWord.split(","));
        // 分页查询
        IPage<MajorSensitiveCasesListVO> page;
        if (Boolean.TRUE.equals(dto.getIsAll())) {
            // 查询全部，不分页
            List<MajorSensitiveCasesListVO> list = profileSthyMapper.selectList(dto,keywordList,districtCodePrefix);
            page = new Page<>(1, list.size());
            page.setRecords(list);
            page.setTotal(list.size());
        } else {
            // 分页查询
            page = profileSthyMapper.selectList(new Page<>(dto.getPageNum(), dto.getPageSize()), dto,
                    keywordList,districtCodePrefix);
        }
        for (MajorSensitiveCasesListVO record : page.getRecords()) {
            record.setBriefly(record.getTitle());
        }
        return RestfulResultsV2.ok(page.getRecords())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(page.getTotal());
    }

    /**
     * 案件列表
     *
     * @param dto dto
     * @param districtCodePrefix 区域代码前缀
     * @return 结果
     */
    private RestfulResultsV2<MajorSensitiveCasesListVO> queryCases(MajorSensitiveCasesListDTO dto, String districtCodePrefix) {
        // 获取关键词配置
        String keyWord = BeanFactoryHolder.getEnv().getProperty("com.trs.fxSubject.gaTsBigScreen.keyword", "");
        List<String> keywordList = StringUtils.isEmpty(keyWord) ? new ArrayList<>()
                : Arrays.asList(keyWord.split(","));
        // 分页查询
        IPage<MajorSensitiveCasesListVO> page;
        if (Boolean.TRUE.equals(dto.getIsAll())) {
            // 查询全部，不分页
            List<MajorSensitiveCasesListVO> list = profileCaseMapper.selectList(dto,keywordList,districtCodePrefix);
            page = new Page<>(1, list.size());
            page.setRecords(list);
            page.setTotal(list.size());
        } else {
            // 分页查询
            page = profileCaseMapper.selectList(new Page<>(dto.getPageNum(), dto.getPageSize()), dto,keywordList,districtCodePrefix);
        }
        List<MajorSensitiveCasesListVO> resultList = page.getRecords();

        if (CollectionUtils.isEmpty(resultList)){
            return RestfulResultsV2.ok(new ArrayList<>())
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(0L);
        }
        //获取案事件编号
        List<String> ajsbhList = resultList.stream().map(MajorSensitiveCasesListVO::getUniqueKey)
                .collect(Collectors.toList());
        List<KeyValueVO> suspectList = profileCaseMapper.selectSuspectPerson(ajsbhList);
        Map<String, List<KeyValueVO>> suspectMap = CollectionUtils.isEmpty(suspectList) ? new HashMap<>()
                : suspectList.stream().collect(Collectors.groupingBy(KeyValueVO::getKey));
        for (MajorSensitiveCasesListVO vo : resultList) {
            List<KeyValueVO> list = suspectMap.get(vo.getUniqueKey());
            vo.setSuspectPerson(CollectionUtils.isEmpty(list) ? new ArrayList<>()
                    : list.stream().map(KeyValueVO::getValue).collect(Collectors.toList()));
        }
        return RestfulResultsV2.ok(resultList)
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(page.getTotal());
    }

    private String getStatusByScore(Double score) {
        if (score >= 0.0 && score < 60.0){
            return "平稳";
        } else if (score >= 60.0 && score < 70.0) {
            return "低风险";
        } else if (score >= 70.0 && score < 80.0) {
            return "中风险";
        } else if (score >= 80.0){
            return "高风险";
        } else {
            return "平稳";
        }
    }

    private List<DistrictDto> getDistrictList(DeptDto dept){
        String parentAreaCode = BeanFactoryHolder.getEnv().getProperty("com.trs.fxSubject.gaTsBigScreen.parentCode", "511600");
        if (parentAreaCode.equals(dept.getDistrictCode())){
            List<DistrictDto> districtList = dictService.getOneLayerDistrictList(parentAreaCode);
            if (CollectionUtils.isEmpty(districtList)){
                return new ArrayList<>();
            }
            DistrictDto dto = new DistrictDto();
            dto.setCode(dept.getDistrictCode());
            dto.setName(dept.getDistrictCode());
            districtList.add(dto);
            return districtList;
        }else {
            DistrictListDto districtByCode = dictService.getDistrictByCode(dept.getDistrictCode());
            DistrictDto dto = new DistrictDto();
            dto.setCode(districtByCode.getCode());
            dto.setName(districtByCode.getName());
            return Arrays.asList(dto);
        }
    }

    private String getCurrentUserDistrict() {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        if (Objects.isNull(currentUser.getDept())){
            return "";
        }
        if (StringUtils.isEmpty(currentUser.getDept().getDistrictCode())){
            return "";
        }
        return AreaUtils.areaPrefix(currentUser.getDept().getDistrictCode());
    }


}
