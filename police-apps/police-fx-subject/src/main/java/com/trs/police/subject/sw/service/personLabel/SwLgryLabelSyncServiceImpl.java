package com.trs.police.subject.sw.service.personLabel;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.trs.common.utils.TimeUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.entity.BusTicketPurchaseEntity;
import com.trs.police.common.core.entity.CivilAviationBookingEntity;
import com.trs.police.common.core.entity.PassengerBusTicketingEntity;
import com.trs.police.common.core.entity.RailwayTicketBookingEntity;
import com.trs.police.common.core.fx.entity.Person;
import com.trs.police.subject.common.mapper.FxWarningMapper;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.police.subject.common.repository.*;
import com.trs.police.subject.domain.entity.FxSubjectLabelEsDO;
import com.trs.police.subject.domain.entity.FxWarningEntity;
import com.trs.police.subject.fx.manager.FxActionExcavateManager;
import com.trs.police.subject.fx.manager.FxLabelSourceCalculationManager;
import com.trs.police.subject.fx.service.impl.FxWarningServiceImpl;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: tang.shuai
 * @date: 2025/05/09
 * @description: sw离广人员标签同步
 */
@Service
@Slf4j
public class SwLgryLabelSyncServiceImpl extends AbstractLabelSyncServiceImpl {

    @Resource
    private PersonMapper personMapper;
    @Resource
    private ThemeGjxxbRepository themeGjxxbRepository;
    @Resource
    private BusTicketPurchaseRepository busTicketPurchaseRepository;
    @Resource
    private CivilAviationBookingRepository civilAviationBookingRepository;
    @Resource
    private PassengerBusTicketingRepository passengerBusTicketingRepository;
    @Resource
    private RailwayTicketBookingRepository railwayTicketBookingRepository;
    @Resource
    private FxWarningMapper fxWarningMapper;
    @Resource
    private FxActionExcavateManager fxActionExcavateManager;
    @Resource
    private FxLabelSourceCalculationManager fxLabelSourceCalculationManager;
    @Resource
    private FxWarningServiceImpl fxWarningService;

    @Override
    protected Consumer<List<Person>> action() {
        String lgry = BeanFactoryHolder.getEnv().getProperty("fx.subject.label.lgry", "离广");
        // 执行分批次捞取并入库
        Consumer<List<Person>> action = (personList) -> {
            if (CollectionUtils.isEmpty(personList)) {
                log.info("没有找到需要处理的人员数据");
                return;
            }

            try {
                // 获取所有人员的身份证号
                List<String> idCards = personList.stream()
                        .map(Person::getIdCard)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if (CollectionUtils.isEmpty(idCards)) {
                    log.info("没有有效的身份证号码");
                    return;
                }

                log.info("开始处理{}个人员的离广标签", idCards.size());

                // 获取当前日期和30天前的日期
                String endDate = TimeUtils.getCurrentDate("yyyy-MM-dd HH:mm:ss");
                String startDate = TimeUtils.dateBefOrAft(endDate, -30, "yyyy-MM-dd HH:mm:ss");
                Expression expression = ExpressionBuilder.And(
                        ExpressionBuilder.Condition("dxbh", Operator.In, idCards),
                        ExpressionBuilder.Condition("hdfssj", Operator.GreaterThanOrEqual, startDate),
                        ExpressionBuilder.Condition("hdfssj", Operator.LessThanOrEqual, endDate)
                );
                List<String> railwayIdCards = queryEsIdCards("dws_gzyq_hz_railway_ticket_booking", expression);
                List<String> aviationIdCards = queryEsIdCards("dws_gzyq_hz_civil_aviation_booking", expression);
                List<String> busIdCards1 = queryEsIdCards("dws_gzyq_hz_passenger_bus_ticketing", expression);
                List<String> busIdCards2 = queryEsIdCards("dws_gzyq_hz_bus_passenger_transport_ticket_purchase", expression);
                // 合并所有符合条件的身份证
                Set<String> allMatchedIdCards = new HashSet<>();
                allMatchedIdCards.addAll(railwayIdCards);
                allMatchedIdCards.addAll(aviationIdCards);
                allMatchedIdCards.addAll(busIdCards1);
                allMatchedIdCards.addAll(busIdCards2);

                if (CollectionUtils.isEmpty(allMatchedIdCards)) {
                    log.info("没有找到符合离广条件的人员");
                    return;
                }

                // 构建结果Map
                Map<String, List<String>> personTagsMap = new HashMap<>();
                for (String idCard : allMatchedIdCards) {
                    personTagsMap.put(idCard, Collections.singletonList(lgry));
                }

                // 保存到预警表
                save2FxWarning(personTagsMap);

                log.info("离广标签处理完成，共找到{}个符合条件的人员", allMatchedIdCards.size());
            } catch (Exception e) {
                log.error("处理离广标签时发生错误: {}", e.getMessage(), e);
            }
        };

        return action;
    }

    /**
     * 从ES查询符合条件的身份证号列表
     *
     * @param indexName ES索引名称
     * @param expression 查询条件表达式
     * @return 符合条件的身份证号列表
     */
    private List<String> queryEsIdCards(String indexName, Expression expression) {
        try {
            log.debug("开始从索引[{}]查询数据，查询条件: {}", indexName, expression);

            List<String> idCards = new ArrayList<>();

            // 根据不同的索引名称选择不同的查询方式
            switch (indexName) {
                case "dws_gzyq_hz_railway_ticket_booking":
                    // 火车预定查询
                    List<RailwayTicketBookingEntity> railwayEntities = railwayTicketBookingRepository.findList(expression);
                    if (!CollectionUtils.isEmpty(railwayEntities)) {
                        idCards = railwayEntities.stream()
                                .map(RailwayTicketBookingEntity::getDxbh)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.toList());
                        log.debug("从火车预定索引查询到{}条数据，提取出{}个不同的身份证号", railwayEntities.size(), idCards.size());
                    }
                    break;

                case "dws_gzyq_hz_civil_aviation_booking":
                    // 民航预定查询
                    List<CivilAviationBookingEntity> aviationEntities = civilAviationBookingRepository.findList(expression);
                    if (!CollectionUtils.isEmpty(aviationEntities)) {
                        idCards = aviationEntities.stream()
                                .map(CivilAviationBookingEntity::getDxbh)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.toList());
                        log.debug("从民航预定索引查询到{}条数据，提取出{}个不同的身份证号", aviationEntities.size(), idCards.size());
                    }
                    break;

                case "dws_gzyq_hz_passenger_bus_ticketing":
                    // 客运汽车售票查询
                    List<PassengerBusTicketingEntity> busTicketingEntities = passengerBusTicketingRepository.findList(expression);
                    if (!CollectionUtils.isEmpty(busTicketingEntities)) {
                        idCards = busTicketingEntities.stream()
                                .map(PassengerBusTicketingEntity::getDxbh)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.toList());
                        log.debug("从客运汽车售票索引查询到{}条数据，提取出{}个不同的身份证号", busTicketingEntities.size(), idCards.size());
                    }
                    break;

                case "dws_gzyq_hz_bus_passenger_transport_ticket_purchase":
                    // 汽车购票查询
                    List<BusTicketPurchaseEntity> busTicketPurchaseEntities = busTicketPurchaseRepository.findList(expression);
                    if (!CollectionUtils.isEmpty(busTicketPurchaseEntities)) {
                        idCards = busTicketPurchaseEntities.stream()
                                .map(BusTicketPurchaseEntity::getDxbh)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.toList());
                        log.debug("从汽车购票索引查询到{}条数据，提取出{}个不同的身份证号", busTicketPurchaseEntities.size(), idCards.size());
                    }
                    break;
                default:
                    log.warn("索引[{}]暂不支持直接查询，请实现相应的查询逻辑", indexName);
                    break;
            }
            if (CollectionUtils.isEmpty(idCards)) {
                log.debug("从索引[{}]查询数据结果为空", indexName);
            }
            return idCards;
        } catch (Exception e) {
            log.error("从索引[{}]查询数据时发生错误: {}", indexName, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    private void save2FxWarning(Map<String, List<String>> personTagsMap) {
        if (CollectionUtils.isEmpty(personTagsMap)) {
            return;
        }
        List<String> idCards = new ArrayList<>(personTagsMap.keySet());
        List<FxWarningEntity> fxWarningEntities = fxWarningMapper.selectList(new QueryWrapper<FxWarningEntity>().in("id_card", idCards));
        Map<String, FxWarningEntity> map = fxWarningEntities.stream().collect(Collectors.toMap(FxWarningEntity::getIdCard, Function.identity(), (o1, o2) -> o1));
        List<FxWarningEntity> list = new ArrayList<>();
        idCards.forEach(idCard -> {
            FxWarningEntity entity = map.get(idCard);
            List<String> tags = personTagsMap.get(idCard);
            if (CollectionUtils.isEmpty(tags)) {
                return;
            }
            if (Objects.nonNull(entity)) {
                // 标签取并集
                Set<String> set = new HashSet<>(entity.getWarningTags());
                set.addAll(tags);
                entity.setWarningTags(new ArrayList<>(set));
                List<FxSubjectLabelEsDO.FxTag> fxTags = JSON.parseArray(JSON.toJSONString(entity.getFxTags()), FxSubjectLabelEsDO.FxTag.class);
                List<String> tagNames = fxTags.stream().map(FxSubjectLabelEsDO.FxTag::getTag).collect(Collectors.toList());
                Set<String> set1 = new HashSet<>(tagNames);
                set1.addAll(tags);
                List<String> tagList = new ArrayList<>(set1);
                List<FxSubjectLabelEsDO.FxTag> fxTagList = tagList.stream().map(tag -> {
                    FxSubjectLabelEsDO.FxTag fxTag = new FxSubjectLabelEsDO.FxTag();
                    fxTag.setTag(tag);
                    fxTag.setTimes(1);
                    fxTag.setHot(false);
                    return fxTag;
                }).collect(Collectors.toList());
                entity.setFxTags(fxTagList);
            } else {
                entity = new FxWarningEntity();
                entity.setIdCard(idCard);
                entity.setWarningTags(tags);
                List<FxSubjectLabelEsDO.FxTag> fxTagList = tags.stream().map(tag -> {
                    FxSubjectLabelEsDO.FxTag fxTag = new FxSubjectLabelEsDO.FxTag();
                    fxTag.setTag(tag);
                    fxTag.setTimes(1);
                    fxTag.setHot(false);
                    return fxTag;
                }).collect(Collectors.toList());
                entity.setFxTags(fxTagList);
            }
            entity.setActivityTime(LocalDateTime.now());
            entity.setWarningLevel(MonitorLevelEnum.ORANGE);
            entity.setContent("标签预警");
            entity.setWarningTime(LocalDateTime.now());
            entity.setModelId(Lists.newArrayList(0L));
            entity.setMonitorId(0L);
            entity.setWarningScore(fxLabelSourceCalculationManager.calculateScore(entity.getFxTags(), getAllFxDicts())._1());
            list.add(entity);
        });
        fxWarningService.saveOrUpdateBatch(list);
    }

    @Override
    protected String getSubjectType() {
        return "sw";
    }
}
