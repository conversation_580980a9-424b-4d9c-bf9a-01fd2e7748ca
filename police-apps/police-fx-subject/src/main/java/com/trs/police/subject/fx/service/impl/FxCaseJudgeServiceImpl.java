package com.trs.police.subject.fx.service.impl;

import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.subject.domain.dto.PersonRelationAtlasDTO;
import com.trs.police.subject.domain.dto.SpecialCaseJudgeDTO;
import com.trs.police.subject.domain.vo.SpecialCaseJudgeVO;
import com.trs.police.subject.fx.manager.FxCaseJudgeManager;
import com.trs.police.subject.fx.service.FxCaseJudgeService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: tang.shuai
 * @date: 2024/04/22
 * @description: fx专题服务层实现
 */
@Slf4j
@Service
public class FxCaseJudgeServiceImpl implements FxCaseJudgeService {

    @Autowired
    private FxCaseJudgeManager fxCaseJudgeManager;

    /**
     * 专案研判列表
     *
     * @param dto dto
     * @return {@link PageResult}<{@link SpecialCaseJudgeVO}>
     */
    @Override
    public PageResult<SpecialCaseJudgeVO> specialCaseJudgeList(SpecialCaseJudgeDTO dto) {
        try {
            return fxCaseJudgeManager.specialCaseJudgeList(dto);
        } catch (Exception e) {
            log.error("获取专案研判列表列表数据失败", e);
            throw new TRSException("获取专案研判列表列表数据失败！" + e.getMessage());
        }
    }

    /**
     * 新增专案研判
     *
     * @param judgeDTO judgeDTO
     */
    @Override
    public void addCaseJudge(SpecialCaseJudgeDTO judgeDTO) {
        try {
            fxCaseJudgeManager.addCaseJudge(judgeDTO);
        } catch (Exception e) {
            log.error("新增专案研判失败", e);
            throw new TRSException("新增专案研判失败！" + e.getMessage());
        }
    }

    /**
     * 删除专案研判
     *
     * @param ids ids
     */
    @Override
    public void deleteCaseJudge(String ids) {
        try {
            fxCaseJudgeManager.deleteCaseJudge(ids);
        } catch (Exception e) {
            log.error("删除专案研判失败", e);
            throw new TRSException("删除专案研判失败！" + e.getMessage());
        }
    }

    /**
     * 导出专案研判
     *
     * @param dto      dto
     * @param response response
     */
    @Override
    public void exportCaseJudge(SpecialCaseJudgeDTO dto, HttpServletResponse response) {
        try {
            fxCaseJudgeManager.exportCaseJudge(dto, response);
        } catch (Exception e) {
            log.error("导出专案研判失败", e);
            throw new TRSException("导出专案研判失败！" + e.getMessage());
        }
    }

    /**
     * 获取人员关系图谱
     *
     * @param dto dto
     * @return RestfulResultsV2
     */
    @Override
    public RestfulResultsV2 getPersonRelationAtlas(SpecialCaseJudgeDTO dto) {
        try {
            return fxCaseJudgeManager.getPersonRelationAtlas(dto);
        } catch (Exception e) {
            log.error("查询人员关系图谱失败", e);
            throw new TRSException("查询人员关系图谱失败！" + e.getMessage());
        }
    }

    /**
     * 添加或编辑人员关系图谱
     *
     * @param dto dto
     */
    @Override
    public void addOrEditPersonRelationAtlas(PersonRelationAtlasDTO dto) {
        try {
            fxCaseJudgeManager.addOrEditPersonRelationAtlas(dto);
        } catch (Exception e) {
            log.error("增加或编辑人员关系图谱失败", e);
            throw new TRSException("增加或编辑人员关系图谱失败！" + e.getMessage());
        }
    }

    /**
     * 删除人员关系图谱
     *
     * @param id  id
     * @param isDel isDel
     */
    @Override
    public void deletePersonRelationAtlas(String id,Integer isDel) {
        try {
            fxCaseJudgeManager.deletePersonRelationAtlas(id,isDel);
        } catch (Exception e) {
            log.error("删除人员关系图谱失败", e);
            throw new TRSException("删除人员关系图谱失败！" + e.getMessage());
        }
    }
}
