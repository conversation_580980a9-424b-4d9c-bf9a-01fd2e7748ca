package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.common.core.fx.entity.Person;
import com.trs.police.common.core.vo.AreaStatisticsVO;
import com.trs.police.subject.domain.dto.ClueExcavateDTO;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.WarningSearchDTO;
import com.trs.police.subject.domain.entity.sw.SwPerson;
import com.trs.police.subject.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 人员管控Mapper
 *
 * <AUTHOR>
 * @date 2024/4/23
 */
@Mapper
public interface PersonMapper extends BaseMapper<Person> {


    /**
     * 人员区域分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> arealDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * 异常人员区域分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> errorArealDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * 静默人员区域分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> jmryArealDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * 本地聚集人员区域分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> bdjjArealDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * 人员类别分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> categoricalDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * 活跃人员类别分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> activeCategoricalDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * 人员状态分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> stateDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * 活跃人员状态分布
     *
     * @param dto dto
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> activeStateDistribution(@Param("dto") StatisticsDTO dto);

    /**
     * 布控人员列表
     *
     * @param dto  dto
     * @param page page
     * @return {@link Page}<{@link PersonVO}>
     */
    Page<PersonVO> bkPersonList(@Param("dto") PersonDTO dto, Page<PersonVO> page);

    /**
     * 布控人员
     *
     * @param dto  dto
     * @param page page
     * @return {@link Page}<{@link PersonVO}>
     */
    Page<PersonVO> bkPersonRelations(@Param("dto") PersonDTO dto, Page<Person> page);

    /**
     * 活跃人员列表
     *
     * @param dto  dto
     * @param page page
     * @return {@link Page}<{@link PersonVO}>
     */
    Page<PersonVO> activePersonList(@Param("dto") PersonDTO dto, Page<Person> page);

    /**
     * 异常人员列表
     *
     * @param dto  dto
     * @param page page
     * @return {@link Page}<{@link PersonVO}>
     */
    Page<PersonVO> errorPersonList(@Param("dto") PersonDTO dto, Page<Person> page);

    /**
     * 静默人员列表
     *
     * @param dto  dto
     * @param page page
     * @return {@link Page}<{@link PersonVO}>
     */
    Page<PersonVO> jmPersonList(@Param("dto") PersonDTO dto, Page<Person> page);

    /**
     * 本地聚集人员列表
     *
     * @param dto  dto
     * @param page page
     * @return {@link Page}<{@link PersonVO}>
     */
    Page<PersonVO> bdjjPersonList(@Param("dto") PersonDTO dto, Page<Person> page);

    /**
     * 顶部统计
     *
     * @param dto dto
     * @return {@link PersonalStatisticsVO}
     */
    PersonalStatisticsVO topStatistics(@Param("dto") StatisticsDTO dto);

    /**
     * 获取监控人员预警列表
     *
     * @param page     分页
     * @param dto      查询参数
     * @param modelIds 所需模型
     * @param ids      预警id集合
     * @return 列表集合
     */
    Page<FxMonitorWarningListVO> monitorWarningList(@Param("page") Page<Object> page, @Param("dto") WarningSearchDTO dto, @Param("modelIds") List<Long> modelIds, @Param("ids") List<Long> ids);

    /**
     * 轨迹信息列表count
     *
     * @param dto         dto查询参数
     * @param currentUser 登录用户
     * @return 轨迹信息列表的总数
     */
    Long warningCount(@Param("dto") PersonDTO dto, @Param("user") CurrentUser currentUser);

    /**
     * sw专题-轨迹信息列表count
     *
     * @param dto         dto查询参数
     * @param swPersonLabels 人员标签id列表
     * @param modelIds 预警模型id列表
     * @return 轨迹信息列表的总数
     */
    Long swWarningCount(@Param("dto") PersonDTO dto,
                        @Param("personLabelList") List<Long> swPersonLabels,
                        @Param("modelIds") List<String> modelIds);

    /**
     * 获取区域人员
     *
     * @param dto      dto
     * @param user     user
     * @param startNum 分页起始位置
     * @return {@link List}<{@link PersonTrackVO}>
     */
    List<PersonTrackVO> trackList(@Param("dto") PersonDTO dto, @Param("user") CurrentUser user, @Param("startNum") Integer startNum);


    /**
     * sw专题-区域活跃人员/区域人员轨迹
     *
     * @param dto      dto
     * @param modelIds 模型id列表
     * @param swPersonLabels 人员标签id列表
     * @param page     page
     * @return {@link Page}<{@link PersonTrackVO}>
     */
    Page<PersonTrackVO> swTrackList(@Param("dto") PersonDTO dto,
                                    @Param("modelIds") List<Long> modelIds,
                                    @Param("personLabelList") List<Long> swPersonLabels,
                                    Page<PersonTrackVO> page);

    /**
     * 中部统计
     *
     * @param dto dto
     * @return {@link PersonalStatisticsVO}
     */
    List<PersonalStatisticsVO> midStatistics(@Param("dto") PersonDTO dto);

    /**
     * 中部区域统计
     *
     * @param dto dto
     * @return {@link PersonalStatisticsVO}
     */
    List<PersonalStatisticsVO> midAreaStatistics(@Param("dto") PersonDTO dto);

    /**
     * sw专题-弹窗区域统计
     *
     * @param dto dto
     * @return {@link PersonalStatisticsVO}
     */
    List<PersonalStatisticsVO> swAreaStatistics(@Param("dto") PersonDTO dto);

    /**
     * 获取t_warning_source_type表
     *
     * @return {@link List}<{@link WarningSourceTypeEntity}>
     */
    @Select("select * from t_warning_source_type")
    List<WarningSourceTypeEntity> findAllWarningSourceType();

    /**
     * 检索异常行为人员列表
     *
     * @param dto          dto参数
     * @param personStatus 人员状态
     * @param personType   人员类型/所属宗教
     * @param page         分页参数
     * @return 分页数据
     */
    Page<ErrorPersonExcavateVO> getErrorPersonPageList(@Param("dto") ClueExcavateDTO dto, @Param("personStatus") String personStatus, @Param("personType") String personType, @Param("page") Page page);

    /**
     * 检索异常行为人员轨迹列表
     *
     * @param dto  dto参数
     * @param page 分页参数
     * @return 分页数据
     */
    Page<ErrorPersonExcavateVO> getErrorPersonTrackPageList(@Param("dto") ClueExcavateDTO dto, @Param("page") Page page);

    /**
     * sw专题-顶部统计-统计人员数据
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @return {@link PersonalStatisticsVO}
     */
    PersonalStatisticsVO swTopStatisticsPerson(@Param("dto") StatisticsDTO dto,
                                               @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-顶部统计-统计其他信息
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @return {@link PersonalStatisticsVO}
     */
    PersonalStatisticsVO swTopStatisticsOtherInfo(
            @Param("dto") StatisticsDTO dto,
            @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-中部区域统计
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @return {@link PersonalStatisticsVO}
     */
    PersonalStatisticsVO swMidAreaStatistics(@Param("dto") StatisticsDTO dto,
                                             @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-区域人员数量
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> swAreaPersonCount(@Param("dto") StatisticsDTO dto,
                                                 @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-区域活跃数和预警数
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> swAreaActiveAndWarningCount(@Param("dto") StatisticsDTO dto,
                                                           @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-区域活跃数和预警数（直接查询指定区域）
     *
     * @param dto       dto
     * @param labelIds  标签id集合
     * @return {@link List}<{@link PersonalStatisticsVO}>
     */
    List<PersonalStatisticsVO> swAreaActiveAndWarningCountByAreaCodes(@Param("dto") StatisticsDTO dto,
                                                                      @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-重要人员预警
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @param page     page
     * @return {@link NewestSensitivePersonVO}
     */
    List<NewestSensitivePersonVO> importantPersonWarning(@Param("dto") PersonDTO dto,
                                                         @Param("labelIds") List<Long> labelIds,
                                                         Page<NewestSensitivePersonVO> page);

    /**
     * sw专题-人员管控-活跃人员趋势
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @return {@link List}<{@link AreaStatisticsVO}>
     */
    List<PersonActivityTrendsVO> activeStatistic(@Param("dto") PersonDTO dto,
                                                 @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-人员管控-布控人员列表
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @param page     page
     * @return {@link List}<{@link PersonVO}>
     */
    Page<PersonVO> swBkPersonList(@Param("dto") PersonDTO dto,
                                  @Param("labelIds") List<Long> labelIds,
                                  Page<PersonVO> page);


    /**
     * sw专题-人员管控-人员轨迹人员信息，不需要分页信息
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @return {@link List}<{@link PersonVO}>
     */
    List<PersonVO> swBkPersonListV2(@Param("dto") PersonDTO dto,
                                  @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-人员管控-人员轨迹人员信息，不需要分页信息
     *
     * @param dto      dto
     * @param labelIds 标签id集合
     * @return {@link List}<{@link PersonVO}>
     */
    List<PersonVO> swBkPersonListV3(@Param("dto") PersonDTO dto,
                                    @Param("labelIds") List<Long> labelIds);

    /**
     * sw专题-顶部统计-异常人数(进京、赴省模型)
     *
     * @param dto      dto
     * @param modelIds 模型id集合
     * @return {@link Integer}
     */
    Integer getClueExcavateCount(@Param("dto") StatisticsDTO dto,
                                 @Param("modelIds") List<Long> modelIds);


    /**
     * sw专题-根据人员id获取人员轨迹信息
     *
     * @param personIdList 人员id集合
     * @param gjlx         轨迹类型
     * @param page         分页信息
     * @return {@link List}<{@link PersonTrackVO}>
     */
    Page<PersonTrackVO> swPersonTrackListByPersonIds(@Param("personIdList") List<Long> personIdList,
                                                     @Param("gjlx") String gjlx,
                                                     Page<PersonTrackVO> page);

    /**
     * sw专题-根据人员id获取人员标签信息
     *
     * @param idCardList idCard集合
     * @return {@link List}<{@link SwPerson}>
     */
    List<SwPerson> getPersonLabelByIdCard(@Param("idCardList") List<String> idCardList);

    /**
     * sw专题-批量插入sw人员信息
     *
     * @param swPersonList sw人员集合
     * @return int
     */
    int insertSwPerson(@Param("swPersonList") List<SwPerson> swPersonList);

    /**
     * sw专题-根据预警id获取预警模型id
     *
     * @param warningIdList 预警id集合
     * @return {@link List}<{@link PersonVO}>
     */
    List<PersonTrackVO> getWarningModelId(@Param("warningIdList") List<Long> warningIdList);

    /**
     * sw专题-根据idCard获取人员真实姓名
     *
     * @param idCardList idCard集合
     * @return {@link List}<{@link PersonVO}>
     */
    List<PersonVO> getPersonRealName(@Param("idCardList") List<String> idCardList);

    /**
     * fk专题-人员动向-本地管控
     *
     * @param dto      dto
     * @param page      page
     * @return {@link List}<{@link PersonVO}>
     */
    Page<PersonVO> fkPersonLocalTrend(@Param("dto") PersonDTO dto, Page<Object> page);
}
