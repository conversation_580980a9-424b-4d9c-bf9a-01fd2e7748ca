package com.trs.police.subject.fk.controller;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.control.AreaListVO;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.SubjectJqtsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.NewestSensitivePersonVO;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.domain.vo.WarningModelStatisticsVO;
import com.trs.police.subject.fk.service.FkPersonService;
import com.trs.police.subject.common.service.scene.ISubjectSceneSearch;
import com.trs.police.subject.common.service.scene.SubjectSceneSearchFactory;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 高新反恐人员
 *
 * <AUTHOR>
 * @date 2424/06/26
 */
@RequestMapping(value = {"/fk/person", "/public/fk/person"})
@RestController
public class FkPersonController {

    @Resource
    private FkPersonService fkPersonService;

    /**
     * fk-人员区域分布
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @GetMapping("/fkArealDistribution")
    public RestfulResultsV2<PersonalStatisticsVO> fkArealDistribution(StatisticsDTO dto) {
        return fkPersonService.fkArealDistribution(dto);
    }

    /**
     * fk-敏感人员
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @GetMapping("/fkSensitivenessPerson")
    public RestfulResultsV2<PersonalStatisticsVO> fkSensitivenessPerson(StatisticsDTO dto) {
        return fkPersonService.fkSensitivenessPerson(dto);
    }

    /**
     * fk-管控区域
     *
     * @param dto dto
     * @param pageParams pageParams
     * @return {@link RestfulResultsV2}<{@link AreaListVO}>
     */
    @GetMapping("/fkControlArea")
    public RestfulResultsV2<AreaListVO> fkControlArea(StatisticsDTO dto, PageParams pageParams) {
        return fkPersonService.fkControlArea(dto, pageParams);
    }

    /**
     * 根据标签获取idCards
     *
     * @param tag tag
     * @return {@link String}
     */
    @GetMapping("/getIdCardsByTag")
    public String getIdCardsByTag(String tag) {
        return fkPersonService.getIdCardsByTag(tag);
    }

    /**
     * FK专题大屏-人员预警模型
     *
     * @param dto 请求参数
     * @return {@link RestfulResultsV2}<{@link WarningModelStatisticsVO}>
     */
    @GetMapping("/warning-model")
    public RestfulResultsV2<WarningModelStatisticsVO> fkPersonWarningModel(StatisticsDTO dto) {
        return fkPersonService.fkPersonWarningModel(dto);
    }

    /**
     * FK专题大屏-人员预警统计
     *
     * @param dto 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @GetMapping("/warning-statistics")
    public RestfulResultsV2<PersonalStatisticsVO> fkPersonWarningStatistics(StatisticsDTO dto) {
        return fkPersonService.fkPersonWarningStatistics(dto);
    }

    /**
     * FK专题大屏-杆体预警统计（感知源）
     *
     * @param dto 请求参数
     * @param pageParams 分页参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @GetMapping("/warning-source-statistics")
    public RestfulResultsV2<PersonalStatisticsVO> fkPersonWarningSourceStatistics(StatisticsDTO dto, PageParams pageParams) {
        return fkPersonService.fkPersonWarningSourceStatistics(dto, pageParams);
    }

    /**
     * FK专题大屏-预警地图统计
     *
     * @param dto 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @GetMapping("/warning-map-statistics")
    public RestfulResultsV2<PersonalStatisticsVO> fkPersonWarningMapStatistics(StatisticsDTO dto) {
        return fkPersonService.fkPersonWarningMapStatistics(dto);
    }

    /**
     * FK专题大屏-最新敏感人员
     *
     * @param dto 请求参数
     * @param pageParams 分页参数
     * @return {@link RestfulResultsV2}<{@link NewestSensitivePersonVO}>
     */
    @GetMapping("/newest-sensitive-person-list")
    public RestfulResultsV2<NewestSensitivePersonVO> fkPersonNewestSensitivePersonList(StatisticsDTO dto, PageParams pageParams) {
        return fkPersonService.fkPersonNewestSensitivePersonList(dto, pageParams);
    }

    /**
     * FK专题大屏-人员预警概况
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/yjgk")
    public RestfulResultsV2<PersonalStatisticsVO> yjgk(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        ISubjectSceneSearch subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-personYjgkStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * FK专题大屏-人员区域分布
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/areaStatistics")
    public RestfulResultsV2<PersonalStatisticsVO> areaStatistics(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        ISubjectSceneSearch subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-personAreaStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * FK专题大屏-人员预警概况
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/personLabelStatistics")
    public RestfulResultsV2<PersonalStatisticsVO> personLabelStatistics(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        ISubjectSceneSearch subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-personLabelStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 高新FK专题大屏-人员动向
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/personTrend")
    public RestfulResultsV2<PersonalStatisticsVO> personTrend(@RequestBody SubjectSceneContext<PersonDTO> context) {
        AbstractPersonControlAnalysisImpl subjectSceneSearchImpl = (AbstractPersonControlAnalysisImpl) SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-personTrend");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context))
                .addTotalCount(subjectSceneSearchImpl.getTotal())
                .addPageNum(context.getPageNum())
                .addPageSize(context.getPageSize());
    }

    /**
     * 高新FK专题大屏-所有人轨迹信息
     *
     * @param context 请求参数
     * @return {@link RestfulResultsV2}<{@link PersonalStatisticsVO}>
     */
    @PostMapping("/trackList")
    public RestfulResultsV2<PersonTrackVO> trackList(@RequestBody SubjectSceneContext<PersonDTO> context) {
        AbstractPersonControlAnalysisImpl subjectSceneSearchImpl = (AbstractPersonControlAnalysisImpl) SubjectSceneSearchFactory.getSubjectSceneSearchImpl("fk-trackList");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context))
                .addTotalCount(subjectSceneSearchImpl.getTotal())
                .addPageNum(context.getPageNum())
                .addPageSize(context.getPageSize());
    }

}
