package com.trs.police.subject.sw.service.scene;

import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sw专题-态势感知-中部地图统计实现类
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class SwSituationAnalysisMidAreaStatisticsImpl extends AbstractSwSituationAnalysisImpl<PersonalStatisticsVO> {

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private DictService dictService;

    @Override
    protected List<PersonalStatisticsVO> doSearch(SubjectSceneContext<StatisticsDTO> context) {
        Map<String, Long> areaPersonCountMap = getSwPersonDeptCodes()
                .stream()
                .filter(vo -> Objects.nonNull(vo.getValue()))
                .collect(Collectors.groupingBy(vo -> {
                            if (vo.getValue().length() >= 6) {
                                return vo.getValue().substring(0, 6);
                            } else {
                                return vo.getValue();
                            }
                        },
                        Collectors.counting()));
        Map<String, PersonalStatisticsVO> voMap = personMapper.swAreaActiveAndWarningCountByAreaCodes(context.getDto(), getSwPersonLabels())
                .stream()
                .collect(Collectors.toMap(PersonalStatisticsVO::getAreaCode, Function.identity()));
        List<DistrictDto> dtos = dictService.getDistrictTree(getParentAreaCode()).stream()
                .flatMap(d -> {
                    List<DistrictDto> districtDtos = d.getChildren();
                    districtDtos.add(d);
                    return districtDtos.stream();
                }).sorted(Comparator.comparing(DistrictDto::getCode)).collect(Collectors.toList());
        List<PersonalStatisticsVO> statisticsVoList = dtos.stream().map(districtDto -> {
            PersonalStatisticsVO vo = voMap.getOrDefault(districtDto.getCode(), new PersonalStatisticsVO());
            vo.setAreaName(districtDto.getShortName());
            vo.setAreaCode(districtDto.getCode());
            vo.setAreaShowName(districtDto.getShortName());
            //人数
            vo.setTotalCount(areaPersonCountMap.getOrDefault(districtDto.getCode(), 0L));
            return vo;
        }).filter(vo ->!vo.getAreaCode().equals(getParentAreaCode())).collect(Collectors.toList());
        return statisticsVoList;
    }

    /**
     * 获取部门编码
     *
     * @return {@link List}<{@link KeyValueVO}>
     */
    public List<KeyValueVO> getSwPersonDeptCodes() {
        return fkPersonMapper.getFkPersonControlStationsByLabelIds(getSwPersonLabels());
    }

    /**
     * 获取人员标签
     *
     * @return {@link List}<{@link Long}>
     */
    public List<Long> getSwPersonLabels() {
        String personLabelArray = BeanFactoryHolder.getEnv().getProperty("sw.subject.person.label", "550758,550759,550763");
        return Arrays.stream(personLabelArray.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 获取区域编码
     *
     * @return {@link List}<{@link String}>
     */
    public List<String> getAreaCode() {
        String areaCode = BeanFactoryHolder.getEnv().getProperty("sw.subject.areaCode",
                "511602,511603,511621,511622,511623,511681");
        return Arrays.asList(areaCode.split(","));
    }

    /**
     * 获取父级区域编码
     *
     * @return {@link String}
     */
    public String getParentAreaCode() {
        return BeanFactoryHolder.getEnv().getProperty("sw.subject.parent.areaCode", "511600");
    }

    /**
     * 获取模型id
     *
     * @return {@link List}<{@link Long}>
     */
    public List<Long> getModelIds() {
        String swModelIds = BeanFactoryHolder.getEnv().getProperty("sw.subject.warning.model", "8,9");
        return Arrays.stream(swModelIds.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public String key() {
        return "sw-midAreaStatistics";
    }

    @Override
    public String desc() {
        return "中部地图统计";
    }
}
