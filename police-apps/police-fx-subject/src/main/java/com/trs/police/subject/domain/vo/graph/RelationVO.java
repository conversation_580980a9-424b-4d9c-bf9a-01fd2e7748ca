package com.trs.police.subject.domain.vo.graph;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: dingkeyu
 * @date: 2024/07/04
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RelationVO implements Serializable {

    /**
     * 开始节点
     */
    private String source;

    /**
     * 结束节点
     */
    private String target;

    /**
     * 关联类型
     */
    private String type;

    /**
     * 连线颜色， 默认-0，红色-1
     */
    private Integer color;

    /**
     * 关联关系
     */
    private String relationship;

}
