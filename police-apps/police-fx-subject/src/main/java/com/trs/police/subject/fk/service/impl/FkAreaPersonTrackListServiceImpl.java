package com.trs.police.subject.fk.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.ImportantAreaMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 风险防控-区域人员轨迹
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public abstract class FkAreaPersonTrackListServiceImpl extends AbstractPersonControlAnalysisImpl<PersonTrackVO> {

    @Autowired
    private ImportantAreaMapper importantAreaMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private FkTrackListServiceImpl fkTrackListServiceImpl;


    @Override
    protected List<PersonTrackVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            if (StringUtils.isNullOrEmpty(dto.getAreaId())){
                return new ArrayList<>();
            }
            //默认固定返回100条 因为这个场景只需要展示最新的轨迹并撒点，不需要进行分页，分页的话执行count操作耗时太多
            Integer limit = BeanFactoryHolder.getEnv().getProperty("subject.fk.track.limit", Integer.class, 100);
            Page<PersonTrackVO> page = new Page<>(1, limit);
            page.setSearchCount(false);
            Page<PersonTrackVO> voList = fkPersonMapper.fkPersonTrackList(dto,page);
            // 设置总数
            if (CollectionUtils.isEmpty(voList.getRecords())) {
                return new ArrayList<>();
            }
            //  获取人员标签名称,已建档的使用档案中的人员标签,未建档的使用最新的一次研判标签
            voList.getRecords().stream().forEach(personVO -> {
                if (personVO.getOnRecord().equals(0)){
                    fkTrackListServiceImpl.findLastJudgedLabel(personVO);
                }
                fkTrackListServiceImpl.getPersonLabels(personVO);
            });
            List<String> areaPointList = getAreaPointList(Long.valueOf(dto.getAreaId()));
            List<String> idCardList = voList.getRecords().stream().map(PersonVO::getIdCard).collect(Collectors.toList());
            Expression expression = buildEsSearchExpression(idCardList, areaPointList);
            //检索es表获取轨迹
            return voList.getRecords();
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }

    private List<String> getAreaPointList(Long areaId) {
        ImportantAreaEntity areaEntities = importantAreaMapper.selectById(areaId);
        return handleGeo(areaEntities.getGeometries());
    }

    private List<String> handleGeo(GeometryVO[] geometries) {
        return Arrays.stream(geometries).map(geometryVO -> {
                    if (geometryVO.getType().equals("circle")) {
                        String replace = geometryVO.getGeometry().replace("POINT(", "").replace(")", "");
                        return (String.format("circle (%s %s)", replace, geometryVO.getProperties().getRadius()));
                    }
                    if (geometryVO.getType().equals("point")) {
                        return null;
                    }
                    return geometryVO.getGeometry();
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Expression buildEsSearchExpression(List<String> idCardList,List<String> geo) {
        return ExpressionBuilder.And(
                ExpressionBuilder.Condition("tzlx", Operator.Equal, "身份证"),
                ExpressionBuilder.Condition("tzzhm", Operator.In, idCardList),
                ExpressionBuilder.Condition("geopoint", Operator.WKT, geo));
    }
    @Override
    public String key() {
        return "fk-areaPersonTrackList";
    }

    @Override
    public String desc() {
        return "区域人员轨迹";
    }
}
