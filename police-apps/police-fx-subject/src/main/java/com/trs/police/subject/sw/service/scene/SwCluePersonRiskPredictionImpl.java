package com.trs.police.subject.sw.service.scene;

import com.trs.police.subject.domain.dto.ClueExcavateDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.ClueExcavateListVO;
import com.trs.police.subject.helper.DistrictHelper;
import com.trs.police.subject.fx.manager.FxActionExcavateManager;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * sw专题-线索挖掘-人员风险预测实现类
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
@Slf4j
public class SwCluePersonRiskPredictionImpl extends AbstractSwClueExcavateAnalysisImpl<ClueExcavateListVO> {

    @Autowired
    private FxActionExcavateManager fxActionExcavateManager;

    @Autowired
    private DistrictHelper districtHelper;

    @Override
    protected List<ClueExcavateListVO> doSearch(SubjectSceneContext<ClueExcavateDTO> context) {
        try {
            ClueExcavateDTO dto = context.getDto();
            dto.setSubjectType(districtHelper.configSubjectType(dto.getSubjectType()));
            dto.setPageNum(context.getPageNum());
            dto.setPageSize(context.getPageSize());

            // 调用线索挖掘管理器查询人员风险预测
            RestfulResultsV2<ClueExcavateListVO> resultV2 = fxActionExcavateManager.clueExcavateList(dto);
            // 设置总数
            if (resultV2.getSummary() != null) {
                this.total = resultV2.getSummary().getTotal();
            }
            resultV2.getDatas().forEach(item -> item.setReligion("涉稳"));
            return resultV2.getDatas();
        } catch (Exception e) {
            log.error("查询人员风险预测异常", e);
            throw new RuntimeException("查询人员风险预测异常", e);
        }
    }

    @Override
    public String key() {
        return "sw-cluePersonRiskPrediction";
    }

    @Override
    public String desc() {
        return "人员风险预测";
    }
}
