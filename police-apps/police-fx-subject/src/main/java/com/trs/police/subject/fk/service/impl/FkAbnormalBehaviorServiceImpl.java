package com.trs.police.subject.fk.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.FkWarningMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 风险防控-人员异常行为
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public abstract class FkAbnormalBehaviorServiceImpl extends AbstractPersonControlAnalysisImpl<PersonTrackVO> {

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private FkWarningMapper fkWarningMapper;

    @Autowired
    private FkTrackListServiceImpl fkTrackListServiceImpl;

    @Override
    protected List<PersonTrackVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            //默认固定返回100条 因为这个场景只需要展示最新的轨迹并撒点，不需要进行分页，分页的话执行count操作耗时太多
            Integer limit = BeanFactoryHolder.getEnv().getProperty("subject.fk.track.limit", Integer.class, 100);
            Page<PersonTrackVO> page = new Page<>(1, limit);
            page.setSearchCount(false);
            Page<PersonTrackVO> voList = fkPersonMapper.fkPersonTrackListV2(dto,page);
            // 设置总数
            if (CollectionUtils.isEmpty(voList.getRecords())) {
                return new ArrayList<>();
            }
            //  获取人员标签名称,已建档的使用档案中的人员标签,未建档的使用最新的一次研判标签
            voList.getRecords().stream().forEach(personVO -> {
                if (personVO.getOnRecord().equals(0)){
                    fkTrackListServiceImpl.findLastJudgedLabel(personVO);
                }
                fkTrackListServiceImpl.getPersonLabels(personVO);
                //判断是否研判
                buildPersonJudge(personVO);
            });
            List<String> idCardList = voList.getRecords().stream().map(PersonVO::getIdCard).collect(Collectors.toList());
            return voList.getRecords();
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }

    private void buildPersonJudge(PersonTrackVO personVO) {
        Integer judgeCount = fkWarningMapper.getPersonJudge(personVO.getId());
        if (judgeCount > 0){
            personVO.setPersonStatus("已研判");
        }else {
            personVO.setPersonStatus("未研判");
        }
    }

    @Override
    public String key() {
        return "fk-abnormalBehavior";
    }

    @Override
    public String desc() {
        return "fk-人员异常行为";
    }
}
