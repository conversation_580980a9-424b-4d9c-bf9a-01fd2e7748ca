package com.trs.police.subject.fk.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.ImportantAreaMapper;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.ImportantAreaVO;
import com.trs.police.subject.sw.service.scene.AbstractSwSituationAnalysisImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 风险防控-重点目标单位
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public abstract class FkImportantTargetUnitsServiceImpl extends AbstractSwSituationAnalysisImpl<ImportantAreaVO> {

    @Autowired
    private ImportantAreaMapper importantAreaMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;


    @Override
    protected List<ImportantAreaVO> doSearch(SubjectSceneContext<StatisticsDTO> context) {
        try {
            StatisticsDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            Page<Object> page = new Page<>(context.getPageNum(), context.getPageSize());
            Page<ImportantAreaVO> pageList = importantAreaMapper.fkImportantTargetUnits(dto, page);
            // 设置总数
            this.total = pageList.getTotal();
            if (CollectionUtils.isEmpty(pageList.getRecords())) {
                return new ArrayList<>();
            }
            return pageList.getRecords();
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }

    @Override
    public String key() {
        return "fk-importantTargetUnits";
    }

    @Override
    public String desc() {
        return "重点目标单位";
    }
}
