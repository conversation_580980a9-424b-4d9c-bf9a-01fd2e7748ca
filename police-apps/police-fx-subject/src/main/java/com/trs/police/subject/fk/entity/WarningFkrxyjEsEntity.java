package com.trs.police.subject.fk.entity;

import java.util.Date;

/**
 * 反恐人像预警(高新)
 *
 * <AUTHOR>
 * @Date 2023/12/6 11:02
 */
public class WarningFkrxyjEsEntity {

    private Long id;
    private Date createTime;
    private Integer age;
    private String alertType;
    private String captureAddress;
    private Date captureTime;
    private String date;
    private String deviceCode;
    private String facePhoto;
    private String facePosition;
    private String group;
    private String idCard;
    private String latitude;
    private String libName;
    private String longitude;
    private String photo;
    private String similarity;
    private String suspectName;
    private String suspectPhoto;
    private String tarLibName;
    private Integer warningLevel;
    /**
     * 人员姓名
     */
    private String name;

    /**
     * 模型名称：有轨迹即预警，FK首次入区，同杆徘徊，静默
     */
    private String warningModel;

    /**
     * 民族-值来源于：SELECT * FROM `t_dict` where type='nation';
     */
    private Integer nation;
}
