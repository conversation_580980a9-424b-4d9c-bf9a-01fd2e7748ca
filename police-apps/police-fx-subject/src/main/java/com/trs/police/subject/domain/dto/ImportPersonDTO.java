package com.trs.police.subject.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.police.subject.common.service.IPersonDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Date;

/**
 * 管控人员导入dto
 *
 * <AUTHOR>
 * @date 2024/4/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
public class ImportPersonDTO extends BaseDTO implements IPersonDTO {

    private Long index;

    /**
     * 真实姓名
     */
    @ExcelProperty("姓名")
    private String realName;

    /**
     * 身份证号
     */
    @ExcelProperty("身份证号")
    private String idCard;

    /**
     * 人员编号
     */
    @ExcelProperty("人员编号")
    private String personNumber;

    /**
     * 性别
     */
    @ExcelProperty("性别")
    private String gender;

    /**
     * 民族
     */
    @ExcelProperty("民族")
    private String nation;

    /**
     * 档案编号
     */
    @ExcelProperty("档案编号")
    private String archiveNumber;

    /**
     * 别名
     */
    @ExcelProperty("别名")
    private String alias;

    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private LocalDate birthday;

    /**
     * 年龄
     */
    @ExcelProperty("年龄")
    private Integer age;

    /**
     * 出生地
     */
    @ExcelProperty("出生地")
    private String areaName;

    /**
     * 文化程度
     */
    @ExcelProperty("文化程度")
    private String whcd;

    /**
     * 婚姻状况
     */
    @ExcelProperty("婚姻状况")
    private String hyzk;

    /**
     * 政治面貌
     */
    @ExcelProperty("政治面貌")
    private String zzmm;

    /**
     * 身份
     */
    @ExcelProperty("身份")
    private String sf;

    /**
     * 联系方式
     */
    @ExcelProperty("联系方式")
    private String phone;

    /**
     * 专长
     */
    @ExcelProperty("专长")
    private String zc;

    /**
     * 组织类别
     */
    @ExcelProperty("组织类别")
    private String orgType;

    /**
     * 人员分类
     */
    @ExcelProperty("人员分类")
    private String personType;

    /**
     * 组织职务
     */
    @ExcelProperty("组织职务")
    private String orgPost;

    /**
     * 加入邪教原因
     */
    @ExcelProperty("加入邪教原因")
    private String orgJoinReason;

    /**
     * 加入组织时间
     */
    @ExcelProperty("加入组织时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date orgJoinTime;

    /**
     * 人员状态
     */
    @ExcelProperty("人员状态")
    private String personStatus;

    /**
     * 思想状况
     */
    @ExcelProperty("思想状况")
    private String thought;

    /**
     * 列控时间
     */
    @ExcelProperty("列控时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date lkTime;

    /**
     * 列控级别
     */
    @ExcelProperty("列控级别")
    private String lkStatus;

    /**
     * DNA编号
     */
    @ExcelProperty("DNA编号")
    private String dnabj;

    /**
     * 指纹编号
     */
    @ExcelProperty("指纹编号")
    private String zwbh;

    /**
     * 虹膜编号
     */
    @ExcelProperty("虹膜编号")
    private String hmbh;

    /**
     * 笔迹编号
     */
    @ExcelProperty("笔迹编号")
    private String bjbh;

    /**
     * 声纹编号
     */
    @ExcelProperty("声纹编号")
    private String swbh;

    /**
     * 身高
     */
    @ExcelProperty("身高")
    private Integer high;

    /**
     * 性格特点
     */
    @ExcelProperty("性格特点")
    private String xgtd;

    /**
     * 体重
     */
    @ExcelProperty("体重")
    private String weight;

    /**
     * 其他明显特征
     */
    @ExcelProperty("其他明显特征")
    private String qtmxtz;

    /**
     * 户口所在地
     */
    @ExcelProperty("户口所在地")
    private String hkszd;

    /**
     * 户口性质
     */
    @ExcelProperty("户口性质")
    private String hkxz;

    /**
     * 所属派出所
     */
    @ExcelProperty("所属派出所")
    private String sspcs;

    /**
     * 派出所电话
     */
    @ExcelProperty("派出所电话")
    private String pcsdh;

    /**
     * 户口详细地址
     */
    @ExcelProperty("户口详细地址")
    private String hkxxdz;

    /**
     * 服务处所
     */
    @ExcelProperty("服务处所")
    private String fwcs;

    /**
     * 服务处所电话
     */
    @ExcelProperty("服务处所电话")
    private String fwcsdh;

    /**
     * 服务行业
     */
    @ExcelProperty("服务行业")
    private String fwhy;

    /**
     * 服务处所名称
     */
    @ExcelProperty("服务处所名称")
    private String fwcsmc;


    /**
     * 工作单位详细地址
     */
    @ExcelProperty("工作单位详细地址")
    private String gzdwxxdz;

    /**
     * 管控单位
     */
    @ExcelProperty("管控单位")
    private String controlUnit;

    /**
     * 管控派出所
     */
    @ExcelProperty("管控派出所")
    private String controlPoliceStation;

    /**
     * 管控责任人
     */
    @ExcelProperty("管控责任人")
    private String controlZrr;

    /**
     * 责任人电话
     */
    @ExcelProperty("责任人电话")
    private String zrrPhone;

    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(realName, "姓名不能为空");
        PreConditionCheck.checkNotEmpty(idCard, "身份证号不能为空");
        PreConditionCheck.checkNotEmpty(personNumber, "人员编号不能为空");
        PreConditionCheck.checkNotEmpty(gender, "性别不能为空");
        return true;
    }

    @Override
    public String getPersonName() {
        return this.realName;
    }
}
