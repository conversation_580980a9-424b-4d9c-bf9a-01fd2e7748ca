package com.trs.police.subject.sw.listener;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.fx.entity.Person;
import com.trs.police.common.openfeign.starter.service.ControlService;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.subject.common.listener.AbsImportPersonListener;
import com.trs.police.subject.fx.converter.FxPersonConverter;
import com.trs.police.subject.domain.dto.ImportSwPersonDTO;
import com.trs.police.subject.domain.entity.SwPersonDetailEntity;
import com.trs.police.subject.domain.vo.ImportPersonVO;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.police.subject.common.mapper.SwPersonDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
@Slf4j
public class ImportSwPersonListener extends AbsImportPersonListener<ImportSwPersonDTO> {

    private PersonMapper personMapper;

    private PermissionService permissionService;

    private SwPersonDetailMapper swPersonDetailMapper;

    public ImportSwPersonListener(PersonMapper personMapper,
                                  PermissionService permissionService,
                                  OssService ossService,
                                  ControlService controlService,
                                  ImportPersonVO importPersonVO,
                                  Environment environment,
                                  SwPersonDetailMapper swPersonDetailMapper) {
        super(personMapper, permissionService, ossService, controlService, importPersonVO, environment);
        this.personMapper = personMapper;
        this.permissionService = permissionService;
        this.swPersonDetailMapper = swPersonDetailMapper;
    }

    @Override
    protected Person toPerson(ImportSwPersonDTO data) throws ServiceException {
        data.isValid();
        Person person = FxPersonConverter.CONVERTER.dtoToDo(data);
        // 构建证件照
        buildPhoto(person);
        // 构建管控区域
        buildControlArea(person);

        return person;
    }

    @Override
    protected void savePerson(Person person) {
        person.setSubjectType(getSubjectType());
        CurrentUser currentUser = permissionService.getCurrentUserWithRole();
        if (person.getId() != null) {
            person.setUpdateUserId(currentUser.getId());
            person.setUpdateDeptId(currentUser.getDeptId());
            person.setUpdateTime(LocalDateTime.now());
            personMapper.updateById(person);
        } else {
            person.fillAuditFields(currentUser);
            personMapper.insert(person);
        }
    }

    @Override
    protected void savePersonDetail(ImportSwPersonDTO data) {
        SwPersonDetailEntity swPersonDetailEntity = FxPersonConverter.CONVERTER.dtoToDetailDo(data);
        SwPersonDetailEntity entity = swPersonDetailMapper.selectOne(new QueryWrapper<SwPersonDetailEntity>().eq("id_card", swPersonDetailEntity.getIdCard()));
        if (Objects.nonNull(entity)) {
            swPersonDetailEntity.setId(entity.getId());
            swPersonDetailMapper.updateById(swPersonDetailEntity);
        } else {
            swPersonDetailMapper.insert(swPersonDetailEntity);
        }

    }

    @Override
    protected String getSubjectType() {
        return "sw";
    }
}
