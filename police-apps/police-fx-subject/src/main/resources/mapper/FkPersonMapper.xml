<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.FkPersonMapper">

    <resultMap id="fkPersonMap" type="com.trs.police.subject.domain.vo.PersonTrackVO">
        <result property="personLabelIds" column="personLabelIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
    </resultMap>

    <select id="fkArealDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            c.control_station AS areaCode,
            count(1) AS followCount
        FROM
            t_profile_person p
        LEFT JOIN t_profile_person_police_control c ON p.id = c.person_id
        <where>
            p.deleted =0
            <!-- 查询所有fk标签及子标签的人员 -->
            AND JSON_OVERLAPS( p.person_label,
            (
            SELECT JSON_ARRAYAGG( l.id )
            FROM t_profile_label l
            WHERE CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', #{dto.personLabel}, '-%' )
            ))> 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.create_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY
            c.control_station
    </select>

    <select id="fkSensitivenessPerson" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            person_label_element AS personStatus,
            COUNT(1) AS totalCount
        FROM
            t_profile_person p,
        JSON_TABLE(p.person_label, '$[*]' COLUMNS (person_label_element INTEGER PATH '$')) AS jt
        <where>
            p.deleted = 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.create_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY
            person_label_element
    </select>

    <select id="fkSensitivenessGroup" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            group_label_element AS personStatus,
            COUNT(1) AS totalCount
        FROM
        t_profile_group p,
        JSON_TABLE(p.group_label, '$[*]' COLUMNS (group_label_element INTEGER PATH '$')) AS jt
        <where>
            p.deleted = 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.create_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY
            group_label_element
    </select>

    <select id="fkPersonWarningModel" resultType="com.trs.police.subject.domain.vo.WarningModelStatisticsVO">
        SELECT
            model_ids.model_id AS `key`,
            COUNT(DISTINCT w.id) AS `count`,
            COUNT(DISTINCT wt.person_id) AS personCount
        FROM
            t_warning w
                JOIN
            JSON_TABLE(
                    w.model_id,
                    '$[*]' COLUMNS (model_id BIGINT PATH '$')
                ) AS model_ids
                INNER JOIN
            t_warning_track wt ON w.id = wt.warning_id
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND w.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND w.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="labelIdJsonArray != null">
                AND JSON_OVERLAPS(w.person_label, #{labelIdJsonArray})
            </if>
        </where>
        GROUP BY
            model_ids.model_id
    </select>

    <select id="getWarningModelNameByIds" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select
            id as `key`,
            title as value
        from t_control_monitor_warning_model
        <where>
            <if test="modelIds != null  and modelIds.size() > 0">
                AND id in
                <foreach collection="modelIds" item="modelId" open="(" close=")" separator=",">
                    #{modelId}
                </foreach>
            </if>
        </where>

    </select>

    <select id="fkPersonWarningStatistics" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        select
            SUBSTRING(ppc.control_station, 1, 6) as areaCode,
            count(1) as warningCount,
            sum(case when p.status in (2, 3, 4) then 1 else 0 end) as signCount,
            sum(case when p.status in (3, 4) then 1 else 0 end) as feedbackCount
        from (select wp.*,
        ROW_NUMBER() OVER (PARTITION BY wp.warning_id ORDER BY wp.create_time DESC) AS rn
        from t_warning_process wp
        left join t_warning w on wp.warning_id = w.id
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND wp.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND wp.create_time &lt;= #{dto.endTime}
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(w.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        ) p
        left join t_warning_track wt on wt.warning_id = p.warning_id
        left join t_profile_person_police_control ppc ON wt.person_id = ppc.person_id
        where p.rn = 1
        group by SUBSTRING(ppc.control_station, 1, 6)
        having areaCode is not null
    </select>

    <select id="fkPersonWarningSourceStatistics"
            resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            s.code as areaId,
            s.district_code as areaCode,
            s.district_name as areaName,
            s.name as modelName,
            t.warningCount
        FROM (
        SELECT
        source_id,
        count(1) as warningCount
        FROM t_warning_track
            <where>
                <if test="dto.startTime != null and dto.startTime != ''">
                    AND activity_time >= #{dto.startTime}
                </if>
                <if test="dto.endTime != null and dto.endTime != ''">
                    AND activity_time &lt;= #{dto.endTime}
                </if>
            </where>
            GROUP BY source_id
            ORDER BY warningCount DESC
        ) t
        LEFT JOIN t_control_warning_source s ON t.source_id = s.unique_key
        WHERE s.id is not null
    </select>

    <resultMap id="newestSensitivePersonList" type="com.trs.police.subject.domain.vo.NewestSensitivePersonVO">
        <result property="warningId" column="warning_id"/>
        <result property="activityTime" column="activity_time"/>
        <result property="activityAddress" column="activityAddress"/>
        <result property="personName" column="personName"/>
        <result property="idNumber" column="id_number"/>
        <result property="personLabelIds" column="person_label"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result property="personProfileId" column="personProfileId"/>
        <result property="personPhoto" column="personPhoto"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="fkPersonNewestSensitivePersonList" resultMap="newestSensitivePersonList">
        SELECT
            wt.warning_id,
            wt.activity_time,
            wt.address as activityAddress,
            p.name as personName,
            p.id_number,
            p.person_label,
            p.id as personProfileId,
            JSON_ARRAY_APPEND(
            IFNULL(p.photo, '[]'),
            '$',
            JSON_OBJECT('id', -1, 'url', CONCAT('/oss/photo/', p.id_number))
            ) as personPhoto
        FROM
            t_warning_track wt
        LEFT JOIN t_profile_person p
        ON wt.person_id = p.id
        <where>
            <if test="personIds != null and personIds.size() > 0">
                AND wt.person_id in
                <foreach collection="personIds" item="personId" open="(" close=")" separator=",">
                    #{personId}
                </foreach>
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND wt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND wt.activity_time &lt;= #{dto.endTime}
            </if>
        </where>
        group by wt.person_id
        order by wt.activity_time DESC
    </select>

    <select id="getPersonLabelName" resultType="com.trs.police.common.core.vo.KeyValueVO">
        SELECT
            id as `key`,
            name as value
        FROM
            t_profile_label
        <where>
            <if test="labelIds != null and labelIds.size() > 0">
                AND id in
                <foreach collection="labelIds" item="labelId" open="(" close=")" separator=",">
                    #{labelId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getActiveCountAndWarningCountByFkPersonLabels" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            SUBSTRING(ppc.control_station, 1, 6) AS areaCode,
            COUNT(DISTINCT wt.person_id) AS activeCount,
            COUNT(wt.warning_id) AS warningCount
        FROM
            t_warning_track wt
                JOIN
            t_warning w ON wt.warning_id = w.id
                JOIN
            t_profile_person_police_control ppc ON wt.person_id = ppc.person_id
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND w.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND w.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(w.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        GROUP BY
            SUBSTRING(ppc.control_station, 1, 6)
    </select>

    <select id="fkPersonYjgkStatisticsJd" resultType="java.lang.Long">
        SELECT
            count(DISTINCT p.id) jdCount
        from t_profile_person p
        where
        <include refid="fkWhere"></include>
    </select>

    <select id="fkPersonYjgkStatisticsJq" resultType="java.lang.Long">
        SELECT
            count(DISTINCT tps.id) jqCount
        from t_profile_person p, t_profile_sthy tps
        where
        <include refid="fkWhere"></include>
            and tps.BJRZJHM  = p.zjhm
        <if test="context.startTime != null and context.startTime != ''">
            AND tps.bjsj >= #{context.startTime}
        </if>
        <if test="context.endTime != null and context.endTime != ''">
            AND tps.bjsj &lt;= #{context.endTime}
        </if>
    </select>

    <select id="fkPersonYjgkStatisticsYj" resultType="java.lang.Long">
        SELECT
            count(DISTINCT twt.id) yjCount
        from t_profile_person p, t_warning_track twt
        where
        <include refid="fkWhere"></include>
            and twt.person_id = p.id
        <if test="context.startTime != null and context.startTime != ''">
            AND twt.activity_time >= #{context.startTime}
        </if>
        <if test="context.endTime != null and context.endTime != ''">
            AND twt.activity_time &lt;= #{context.endTime}
        </if>
    </select>

    <select id="fkPersonAreaStatistics" resultType="com.trs.police.subject.domain.vo.PoliceSubjectStatisticsVO">
        SELECT substring(pc.control_station, 1, 6) as `key`,
               d.name as name,
               count(*) count
        from t_profile_person p
                 left join t_profile_person_police_control pc on pc.person_id = p.id
                 left join t_district d on (substring(pc.control_station, 1, 6) = d.code)
        where
        <include refid="fkWhere"></include>
            and pc.control_station is not null
        group by substring(pc.control_station, 1, 6)
    </select>

    <select id="fkPersonLabelStatistics"
            resultType="com.trs.police.subject.domain.vo.PoliceSubjectStatisticsVO">
        SELECT t1.personLabel as `key`,
               l.name as name,
               count(*) as count
        from t_profile_person p
                 JOIN JSON_TABLE(person_label, '$[*]' COLUMNS(personLabel INT PATH '$')) AS t1
                 left join t_profile_label l on l.id = t1.personLabel
        where
        <include refid="fkWhere"></include>
        GROUP BY t1.personLabel
        order by count desc
    </select>
    <select id="getModelName" resultType="com.trs.police.common.core.vo.KeyValueVO">
        select
        w.id as `key`,
        w.title as value
        from t_control_monitor_warning_model w
        <where>
            <if test="modelIds != null and modelIds.size() > 0">
                w.id in
                <foreach collection="modelIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getFkPersonControlStationsByLabelIds" resultType="com.trs.police.common.core.vo.KeyValueVO">
        SELECT
            pc.person_id as `key`,
            pc.control_station as `value`
        FROM
            t_profile_person_police_control pc
        JOIN t_profile_person p ON pc.person_id = p.id
        <where>
            <if test="personLabelList != null and personLabelList.size() > 0">
                and JSON_OVERLAPS(p.person_label, JSON_ARRAY
                <foreach collection="personLabelList" item="personLabel" open="(" separator=","  close=")">
                    #{personLabel}
                </foreach>)
            </if>
        </where>

    </select>
    <select id="fkAreaPersonTrackList" resultType="com.trs.police.subject.domain.vo.PersonTrackVO">
        select
         p.*
        from t_warning_fk_person p
        join t_warning_fkrxyj fkr on
    </select>
    <select id="fkPersonTrackList" resultMap="fkPersonMap">
        select
        p.id as id,
        p.name as name,
        p.id_card as idCard,
        p.on_record as onRecord,
        p.label_id as personLabelIds,
        p.photo as avatar
        from t_warning_fk_person p
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.last_warning_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.last_warning_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'idCard'">
                        AND p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField == 'name'">
                        AND p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField = 'fullText'">
                        AND (
                        p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                        OR p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                        )
                    </when>
                </choose>
            </if>
        </where>
        order by p.last_warning_time desc
    </select>
    <select id="getLastJudgedLabel" resultType="java.lang.String">
        select label_id
        from t_warning_fk_person_ryrp
        <where>
            fk_person = #{id} and deleted = 0
        </where>
        order by create_time desc
        limit 1
    </select>
    <select id="fkPersonTrackListV2" resultType="com.trs.police.subject.domain.vo.PersonTrackVO">
        select
        p.id as id,
        p.name as name,
        p.id_card as idCard,
        p.on_record as onRecord,
        p.label_id as personLabelIds,
        p.photo as avatar
        from t_warning_fk_person p
        <where>
            p.control_level != 4
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.last_warning_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.last_warning_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'idCard'">
                        AND p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField == 'name'">
                        AND p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField = 'fullText'">
                        AND (
                        p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                        OR p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                        )
                    </when>
                </choose>
            </if>
        </where>
        order by p.last_warning_time desc
    </select>

    <sql id="fkWhere">
        p.deleted = 0
        and JSON_OVERLAPS(p.person_label, JSON_ARRAY
        <foreach collection="context.dto.personLabels" item="personLabel" open="(" separator="," close=")">
            #{personLabel}
        </foreach>)
    </sql>
</mapper>