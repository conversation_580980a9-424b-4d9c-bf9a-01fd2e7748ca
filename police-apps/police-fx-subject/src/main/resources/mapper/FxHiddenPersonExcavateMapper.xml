<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.FxHiddenPersonMapper">

    <select id="hiddenPersonList" resultType="com.trs.police.subject.domain.vo.HiddenPerosnExcavateVO">
        <!-- 隐性人员的身份证号码可能为空，为空则使用手机号码进行关联 -->
        SELECT
            *,
            tfy.zjhm as IdCard,
            tfy.xm as realName,
            tfy.sjhm as tel,
            IFNULL((SELECT status FROM tb_fx_task_process WHERE (task_id = tfy.zjhm OR task_id = tfy.sjhm) AND subject_type = #{dto.subjectType} AND clue_excavate_type = 3), 1) as warningStatus,
            (SELECT COUNT(1) FROM tb_fx_feedback WHERE (task_id = tfy.zjhm OR task_id = tfy.sjhm) AND subject_type = #{dto.subjectType} AND clue_excavate_type = 3) as feedbackCount,
            (SELECT feedback FROM tb_fx_feedback WHERE (task_id = tfy.zjhm OR task_id = tfy.sjhm) AND subject_type = #{dto.subjectType} AND clue_excavate_type = 3) as feedbackContent
        FROM tb_fx_yxrywj tfy
        <where>
            <if test="dto.worth != null">
                AND (
                tfy.zjhm IN (select distinct task_id from tb_fx_feedback where JSON_EXTRACT(feedback, '$.worth') = #{dto.worth}
                and subject_type = #{dto.subjectType} and clue_excavate_type = 3) OR
                tfy.sjhm IN (select distinct task_id from tb_fx_feedback where JSON_EXTRACT(feedback, '$.worth') = #{dto.worth}
                and subject_type = #{dto.subjectType} and clue_excavate_type = 3)
                )
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                AND (
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(5)">
                    <!-- 未反馈: 状态为已签收或者已完结且没有反馈记录 -->
                    (
                        (
                            tfy.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 3 and
                            (t1.status = 2 or (t1.status = 4 and not exists(select 1 from tb_fx_feedback t2 where t2.task_id = tfy.zjhm and t2.subject_type = #{dto.subjectType} and t2.clue_excavate_type = 3))))
                        ) OR
                        (
                            tfy.sjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 3 and
                            (t1.status = 2 or (t1.status = 4 and not exists(select 1 from tb_fx_feedback t2 where t2.task_id = tfy.sjhm and t2.subject_type = #{dto.subjectType} and t2.clue_excavate_type = 3))))
                        )
                    ) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(1)">
                    <!-- 未签收 -->
                    (   tfy.zjhm NOT IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 3)
                    AND
                        tfy.sjhm NOT IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 3)) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(2)">
                    <!-- 未签收 -->
                    (   tfy.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 3 AND t1.status IN (2,3))
                    AND
                    tfy.sjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 3 AND t1.status IN (2,3))) OR
                </if>
                    (
                        tfy.zjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 3 and t1.status in
                        <foreach collection="dto.warningStatus.split(',')" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>)
                    OR
                        tfy.sjhm IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 3 and t1.status in
                        <foreach collection="dto.warningStatus.split(',')" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>)
                    )
                )
            </if>
            <if test="dto.haveIdCard">
                AND tfy.zjhm IS NOT NULL
                AND tfy.zjhm != ''
            </if>
            <if test="dto.haveTel">
                AND tfy.sjhm IS NOT NULL
                AND tfy.sjhm != ''
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'fullText'">
                        AND (
                            xm LIKE CONCAT('%',#{dto.searchValue},'%') OR
                            zjhm LIKE CONCAT('%',#{dto.searchValue},'%') OR
                            sjhm LIKE CONCAT('%',#{dto.searchValue},'%') OR
                            imsi LIKE CONCAT('%',#{dto.searchValue},'%')
                        )
                    </when>
                    <otherwise>
                        AND ${dto.searchField} LIKE CONCAT('%',#{dto.searchValue},'%')
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
</mapper>