<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.PersonMapper">

    <resultMap id="monitorWarningListResult" type="com.trs.police.subject.domain.vo.FxMonitorWarningListVO">
        <result column="content" property="content"/>
        <result column="warningType" property="warningType"/>
        <result column="warningTime" property="warningTime"/>
        <result column="warningId" property="warningId"/>
        <result column="monitorId" property="monitorId"/>
        <result column="fxWarningStatusCode" property="warningStatusCode"/>
        <result column="warningLevelCode" property="warningLevelCode"/>
        <result column="modelId" property="modelId"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="activityAddress" property="activityAddress"/>
    </resultMap>

    <resultMap id="personalStatisticsVO" type="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        <result column="geometries" property="geometries"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <resultMap id="errorPersonResultMap" type="com.trs.police.subject.domain.vo.ErrorPersonExcavateVO">
        <result column="photos" property="photos"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
    </resultMap>

    <resultMap id="newestSensitivePersonList" type="com.trs.police.subject.domain.vo.NewestSensitivePersonVO">
        <result property="warningId" column="warningId"/>
        <result property="activityTime" column="activityTime"/>
        <result property="activityAddress" column="activityAddress"/>
        <result property="personName" column="personName"/>
        <result column="warningLevel" property="warningLevel"/>
        <result property="idNumber" column="idNumber"/>
        <result property="personLabelIds" column="personLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result property="personProfileId" column="personProfileId"/>
        <result property="personPhoto" column="personPhoto"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="swBkPersonListMap" type="com.trs.police.subject.domain.vo.PersonVO">
        <result property="id" column="id"/>
        <result property="warningIds" column="warningIds"/>
        <result property="realName" column="realName"/>
        <result property="warningLevel" column="warningLevel"/>
        <result property="idCard" column="idCard"/>
        <result property="personLabelIds" column="personLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result property="photo" column="personPhoto"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToFileInfoHandler"/>
    </resultMap>

    <resultMap id="personTrackMap" type="com.trs.police.subject.domain.vo.PersonTrackVO">
        <result property="id" column="id"/>
        <result property="realName" column="realName"/>
        <result property="idCard" column="idCard"/>
        <result property="personLabelIds" column="personLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="modelId" property="modelId"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result property="photo" column="personPhoto"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToFileInfoHandler"/>
    </resultMap>

    <resultMap id="swPersonMap" type="com.trs.police.subject.domain.entity.sw.SwPerson">
        <result property="zjhm" column="zjhm"/>
        <result property="personLabel" column="personLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
    </resultMap>
    <resultMap id="swWarningModel" type="com.trs.police.subject.domain.vo.PersonTrackVO">
        <result property="warningId" column="warningId"/>
        <result property="modelId" column="modelId"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
    </resultMap>
    <resultMap id="fkPersonMap" type="com.trs.police.subject.domain.vo.PersonVO">
        <result property="personLabelIds" column="personLabelIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
    </resultMap>

    <insert id="insertSwPerson">
        INSERT INTO tb_sw_person (
        zjhm, xm, xzdz, zjxy, jg,
        police_station_unit_name, police_station_area_code,
        police_station_unit_code, tags, person_label,person_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.zjhm}, #{item.xm}, #{item.xzdz}, #{item.zjxy}, #{item.jg},
            #{item.policeStationUnitName}, #{item.policeStationAreaCode},
            #{item.policeStationUnitCode}, #{item.tags}, #{item.personLabel},
            #{item.personId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        xm = VALUES(xm),
        xzdz = VALUES(xzdz),
        zjxy = VALUES(zjxy),
        jg = VALUES(jg),
        police_station_unit_name = VALUES(police_station_unit_name),
        police_station_area_code = VALUES(police_station_area_code),
        police_station_unit_code = VALUES(police_station_unit_code),
        tags = VALUES(tags),
        person_label = VALUES(person_label),
        person_id = VALUES(person_id)
    </insert>

    <select id="arealDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        p.control_area_code as areaCode,
        p.control_area_name as areaName,
        count(1) as totalCount,
        <include refid="commonStatistic"></include>
        FROM tb_fx_person p
        <where>
            <bind name="districtCode"
                  value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().getDept().getDistrictCode()"/>
            <!-- fx专题增加数据权限,泸州市的能看全部 -->
            <if test="'fx'.equals(dto.subjectType) and !'510500'.equals(districtCode)">
                AND p.control_area_code = #{districtCode}
            </if>
            <include refid="commonCondition"></include>
        </where>
        GROUP BY p.control_area_code
    </select>

    <select id="errorArealDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        p.control_area_code as areaCode,
        p.control_area_name as areaName,
        count(DISTINCT p.id_card) as totalCount,
        <include refid="commonStatistic"></include>
        FROM tb_fx_person p
        LEFT JOIN t_warning_fxryyj wf ON p.id_card = wf.id_card
        <where>
            <if test="dto.tags != null and dto.tags != ''">
                <foreach collection="dto.tags.split(',')" item="item" open="(" close=")" separator="OR">
                    JSON_CONTAINS(warning_tags, CONCAT('\"', #{item}, '\"'))
                </foreach>
            </if>
            <include refid="commonCondition"></include>
        </where>
        GROUP BY p.control_area_code
    </select>

    <select id="jmryArealDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        p.control_area_code as areaCode,
        p.control_area_name as areaName,
        count(DISTINCT p.id_card) as totalCount,
        <include refid="commonStatistic"></include>
        FROM
        tb_fx_person p
        <where>
            p.care_status = 1
            AND p.subject_type = #{dto.subjectType}
            AND p.id_card NOT IN (
            SELECT
            DISTINCT
            p.id_card AS idCard
            FROM
            tb_fx_monitor_person_relation pr
            LEFT JOIN tb_fx_person p ON pr.id_card = p.id_card
            LEFT JOIN t_warning w ON pr.monitor_id = w.monitor_id
            WHERE p.subject_type = #{dto.subjectType} )
            <include refid="commonCondition"></include>
        </where>
        GROUP BY p.control_area_code
    </select>

    <select id="bdjjArealDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        p.control_area_code as areaCode,
        p.control_area_name as areaName,
        count(DISTINCT p.id_card) as totalCount,
        <include refid="commonStatistic"></include>
        FROM
        tb_fx_person p
        <where>
            p.subject_type = #{dto.subjectType}
            AND p.id_card IN (
            SELECT
            DISTINCT id_card_element
            FROM
            tb_fx_qtxswj tfq,
            JSON_TABLE (tfq.group_id_cards,'$[*]' COLUMNS ( id_card_element VARCHAR ( 255 ) PATH '$' )) AS jt
            WHERE tfq.subject_type = #{dto.subjectType} )
            <include refid="commonCondition"></include>
        </where>
        GROUP BY p.control_area_code
    </select>

    <select id="categoricalDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        <include refid="selectPersonType"></include>
        as personTypeName,
        count(1) as totalCount
        FROM tb_fx_person p
        <if test="dto.subjectType == 'sw'">
            LEFT JOIN tb_sw_person_detail tspd ON tspd.id_card = p.id_card,
            JSON_TABLE ( tspd.person_type, '$[*]' COLUMNS ( person_type_element VARCHAR ( 255 ) PATH '$' )) AS jt
        </if>
        <where>
            <include refid="commonCondition"></include>
        </where>
        GROUP BY
        <include refid="groupByPersonType"></include>
        ORDER BY totalCount DESC
    </select>

    <select id="activeCategoricalDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        <include refid="selectPersonType"></include>
        AS personTypeName,
        count(distinct(pr.id_card)) AS totalCount
        FROM tb_fx_monitor_person_relation pr
        LEFT JOIN tb_fx_person p ON pr.id_card = p.id_card
        LEFT JOIN t_warning w ON pr.monitor_id = w.monitor_id
        <if test="dto.subjectType == 'sw'">
            LEFT JOIN tb_sw_person_detail tspd ON tspd.id_card = p.id_card,
            JSON_TABLE (tspd.person_type,'$[*]' COLUMNS ( person_type_element VARCHAR ( 255 ) PATH '$' )) AS jt
        </if>
        <where>
            AND p.person_status != '死亡'
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND p.subject_type = #{dto.subjectType}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND w.warning_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND w.warning_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                AND p.person_type IN
                <foreach collection="dto.personType.split(',')" item="type" separator="," open="(" close=")">
                    #{type}
                </foreach>
            </if>
            <if test="dto.personStatus != null and dto.personStatus != ''">
                AND p.person_status IN
                <foreach collection="dto.personStatus.split(',')" item="status" separator="," open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND p.control_area_code IN
                <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
        GROUP BY
        <include refid="groupByPersonType"></include>
        ORDER BY totalCount DESC
    </select>

    <select id="stateDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        p.person_status as personStatusName,
        count(1) as totalCount
        FROM tb_fx_person p
        <where>
            <include refid="commonCondition"></include>
        </where>
        GROUP BY p.person_status
        ORDER BY totalCount DESC
    </select>

    <select id="activeStateDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        p.person_status AS personStatusName,
        count(distinct(pr.id_card)) AS totalCount
        FROM tb_fx_monitor_person_relation pr
        LEFT JOIN tb_fx_person p ON pr.id_card = p.id_card
        LEFT JOIN t_warning w ON pr.monitor_id = w.monitor_id
        <where>
            AND p.person_status != '死亡'
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND p.subject_type = #{dto.subjectType}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND w.warning_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND w.warning_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                AND p.person_type IN
                <foreach collection="dto.personType.split(',')" item="type" separator="," open="(" close=")">
                    #{type}
                </foreach>
            </if>
            <if test="dto.personStatus != null and dto.personStatus != ''">
                AND p.person_status IN
                <foreach collection="dto.personStatus.split(',')" item="status" separator="," open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND p.control_area_code IN
                <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
        GROUP BY p.person_status
        ORDER BY totalCount DESC
    </select>

    <select id="bkPersonList" resultType="com.trs.police.subject.domain.vo.PersonVO">
        SELECT
        p.id_card AS idCard,
        p.real_name AS realName,
        p.avatar,
        p.person_number AS personNumber,
        p.birthday,
        p.person_type AS personType,
        p.person_category AS personCategory,
        p.lk_status AS lkStatus,
        p.care_status AS careStatus
        FROM tb_fx_person p
        <where>
            <bind name="districtCode"
                  value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().getDept().getDistrictCode()"/>
            <!-- fx专题增加数据权限,泸州市的能看全部 -->
            <if test="'fx'.equals(dto.subjectType) and !'510500'.equals(districtCode)">
                AND p.control_area_code = #{districtCode}
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                AND p.person_type IN
                <foreach collection="dto.personType.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.personCategory != null and dto.personCategory != ''">
                AND p.person_category IN
                <foreach collection="dto.personCategory.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND p.subject_type = #{dto.subjectType}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                <if test="dto.subjectType == 'fx'">
                    AND p.lk_time >= #{dto.startTime}
                </if>
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                <if test="dto.subjectType == 'fx'">
                    AND p.lk_time &lt;= #{dto.endTime}
                </if>
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND p.control_area_code IN
                <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="dto.lkStatus != null and dto.lkStatus != ''">
                AND p.lk_status = #{dto.lkStatus}
            </if>
            <if test="dto.careStatus != null">
                AND p.care_status = #{dto.careStatus}
            </if>
            <if test="dto.flowStatus != null and dto.flowStatus != ''">
                AND p.flow_status = #{dto.flowStatus}
            </if>
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND p.subject_type = #{dto.subjectType}
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                AND (
                p.real_name like CONCAT('%',#{dto.searchValue},'%') OR
                p.id_card like CONCAT('%',#{dto.searchValue},'%')
                )
            </if>
        </where>
    </select>

    <select id="bkPersonRelations" resultType="com.trs.police.subject.domain.vo.PersonVO">
        SELECT
        DISTINCT
        p.id_card AS idCard,
        p.real_name AS realName,
        p.avatar,
        p.person_number AS personNumber,
        p.birthday,
        p.person_type AS personType,
        p.lk_status AS lkStatus
        FROM tb_fx_monitor_person_relation pr
        LEFT JOIN tb_fx_person p ON pr.id_card = p.id_card
        <where>
            <if test="dto.idCard != null and dto.idCard != ''">
                AND p.id_card = #{dto.idCard}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.lk_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.lk_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND p.control_area_code IN
                <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="dto.lkStatus != null and dto.lkStatus != ''">
                AND p.lk_status = #{dto.lkStatus}
            </if>
            <if test="dto.flowStatus != null and dto.flowStatus != ''">
                AND p.flow_status = #{dto.flowStatus}
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                AND (
                p.real_name like CONCAT('%',#{dto.searchValue},'%') OR
                p.id_card like CONCAT('%',#{dto.searchValue},'%')
                )
            </if>
        </where>
    </select>

    <select id="errorPersonList" resultType="com.trs.police.subject.domain.vo.PersonVO">
        SELECT
        DISTINCT
        p.id_card AS idCard,
        p.real_name AS realName,
        p.avatar,
        p.person_number AS personNumber,
        p.birthday,
        p.person_type AS personType,
        p.person_category AS personCategory,
        p.lk_status AS lkStatus,
        p.care_status AS careStatus
        FROM tb_fx_person p
        LEFT JOIN t_warning_fxryyj wf ON p.id_card = wf.id_card
        <where>
            <if test="dto.tags != null and dto.tags != ''">
                <foreach collection="dto.tags.split(',')" item="item" open="(" close=")" separator="OR">
                    JSON_CONTAINS(warning_tags, CONCAT('\"', #{item}, '\"'))
                </foreach>
            </if>
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND p.subject_type = #{dto.subjectType}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.lk_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.lk_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND p.control_area_code IN
                <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="dto.lkStatus != null and dto.lkStatus != ''">
                AND p.lk_status = #{dto.lkStatus}
            </if>
            <if test="dto.flowStatus != null and dto.flowStatus != ''">
                AND p.flow_status = #{dto.flowStatus}
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                AND (
                p.real_name like CONCAT('%',#{dto.searchValue},'%') OR
                p.id_card like CONCAT('%',#{dto.searchValue},'%')
                )
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                AND p.person_type IN
                <foreach collection="dto.personType.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.personCategory != null and dto.personCategory != ''">
                AND p.person_category IN
                <foreach collection="dto.personCategory.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="jmPersonList" resultType="com.trs.police.subject.domain.vo.PersonVO">
        SELECT
        p.id_card AS idCard,
        p.real_name AS realName,
        p.avatar,
        p.person_number AS personNumber,
        p.birthday,
        p.person_type AS personType,
        p.person_category AS personCategory,
        p.lk_status AS lkStatus,
        p.care_status AS careStatus
        FROM
        tb_fx_person p
        <where>
            p.care_status = 1
            AND p.subject_type = #{dto.subjectType}
            AND p.id_card NOT IN (
            SELECT
            DISTINCT
            p.id_card AS idCard
            FROM
            tb_fx_monitor_person_relation pr
            LEFT JOIN tb_fx_person p ON pr.id_card = p.id_card
            LEFT JOIN t_warning w ON pr.monitor_id = w.monitor_id
            WHERE p.subject_type = #{dto.subjectType} )
            <include refid="commonCondition"></include>
        </where>
    </select>

    <select id="bdjjPersonList" resultType="com.trs.police.subject.domain.vo.PersonVO">
        SELECT
        p.id_card AS idCard,
        p.real_name AS realName,
        p.avatar,
        p.person_number AS personNumber,
        p.birthday,
        p.person_type AS personType,
        p.person_category AS personCategory,
        p.lk_status AS lkStatus,
        p.care_status AS careStatus
        FROM
        tb_fx_person p
        <where>
            p.subject_type = #{dto.subjectType}
            AND p.id_card IN (
            SELECT
            DISTINCT id_card_element
            FROM
            tb_fx_qtxswj tfq,
            JSON_TABLE (tfq.group_id_cards,'$[*]' COLUMNS ( id_card_element VARCHAR ( 255 ) PATH '$' )) AS jt
            WHERE
            tfq.subject_type = #{dto.subjectType}
            )
            <include refid="commonCondition"></include>
        </where>
    </select>

    <select id="activePersonList" resultType="com.trs.police.subject.domain.vo.PersonVO">
        SELECT
        DISTINCT
        p.id_card AS idCard,
        p.real_name AS realName,
        p.avatar,
        p.person_number AS personNumber,
        p.birthday,
        p.person_type AS personType,
        p.person_category AS personCategory,
        p.lk_status AS lkStatus,
        p.care_status AS careStatus
        FROM tb_fx_monitor_person_relation pr
        LEFT JOIN tb_fx_person p ON pr.id_card = p.id_card
        LEFT JOIN t_warning w ON pr.monitor_id = w.monitor_id
        <where>
            <bind name="districtCode"
                  value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().getDept().getDistrictCode()"/>
            <choose>
                <when test="dto.idCard != null and dto.idCard != ''">
                    AND p.id_card = #{dto.idCard}
                </when>
                <otherwise>
                    <if test="dto.startTime != null and dto.startTime != ''">
                        AND w.warning_time >= #{dto.startTime}
                    </if>
                    <if test="dto.endTime != null and dto.endTime != ''">
                        AND w.warning_time &lt;= #{dto.endTime}
                    </if>
                </otherwise>
            </choose>
            <!-- fx专题增加数据权限,泸州市的能看全部 -->
            <if test="'fx'.equals(dto.subjectType) and !'510500'.equals(districtCode)">
                AND p.control_area_code = #{districtCode}
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND p.control_area_code IN
                <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="dto.lkStatus != null and dto.lkStatus != ''">
                AND p.lk_status = #{dto.lkStatus}
            </if>
            <if test="dto.flowStatus != null and dto.flowStatus != ''">
                AND p.flow_status = #{dto.flowStatus}
            </if>
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND p.subject_type = #{dto.subjectType}
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                AND (
                p.real_name like CONCAT('%',#{dto.searchValue},'%') OR
                p.id_card like CONCAT('%',#{dto.searchValue},'%')
                )
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                AND p.person_type IN
                <foreach collection="dto.personType.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.personCategory != null and dto.personCategory != ''">
                AND p.person_category IN
                <foreach collection="dto.personCategory.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="topStatistics" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        count(1) AS totalCount,
        <include refid="commonStatistic"></include>
        FROM tb_fx_person p
        <where>
            <include refid="commonCondition"></include>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                AND (
                p.real_name like CONCAT('%',#{dto.searchValue},'%') OR
                p.id_card like CONCAT('%',#{dto.searchValue},'%')
                )
            </if>
        </where>
    </select>

    <select id="monitorWarningList" resultMap="monitorWarningListResult">
        SELECT
        w.id as warningId,
        w.content as content,
        w.warning_type as warningType,
        w.warning_time as warningTime,
        w.monitor_id as monitorId,
        w.warning_level as warningLevelCode,
        w.fx_warning_status as fxWarningStatusCode,
        w.model_id as modelId,
        w.activity_address as activityAddress,
        p.real_name as realName,
        p.id_card as idCard,
        p.person_type as personType,
        p.person_status as personStatus,
        p.area_code as areaCode,
        p.area_name as areaName
        FROM tb_fx_person p
        join tb_fx_monitor_person_relation m on p.id_card = m.id_card
        join t_warning w on m.monitor_id = w.monitor_id
        <where>
            p.subject_type = 'fx'
            <if test="dto.warningBeginTime != null">
                AND w.warning_time &gt;= #{dto.warningBeginTime}
            </if>
            <if test="dto.warningEndTime != null">
                AND w.warning_time &lt;= #{dto.warningEndTime}
            </if>
            <if test="dto.warningStatus != null">
                AND w.fx_warning_status = #{dto.warningStatus}
            </if>
            <if test="dto.warningLevel != null">
                AND w.warning_level = #{dto.warningLevel}
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                AND p.person_type = #{dto.personType}
            </if>
            <if test="dto.personStatus != null and dto.personStatus != ''">
                AND p.person_status = #{dto.personStatus}
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND p.control_area_code = #{dto.areaCode}
            </if>
            <if test="dto.warningType != null and dto.warningType != ''">
                AND w.warning_type = #{dto.warningType}
            </if>
            <if test="modelIds != null">
                AND JSON_OVERLAPS((ifnull(w.model_id,'[]')),
                (select JSON_ARRAYAGG(id) from t_control_monitor_warning_model where id in
                <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                    ${modelId}
                </foreach>
                ))>0
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == '全部' or dto.searchField == 'fullText'">
                        AND (p.real_name LIKE CONCAT('%',#{dto.searchValue},'%')
                        OR p.id_card LIKE CONCAT('%',#{dto.searchValue},'%')
                        OR w.activity_address LIKE CONCAT('%',#{dto.searchValue},'%'))
                    </when>
                    <otherwise>
                        AND p.${dto.searchField} LIKE CONCAT('%',#{dto.searchValue},'%')
                    </otherwise>
                </choose>
            </if>
            <if test="ids != null">
                AND w.id IN
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        <if test="dto.orderField != null">
            ORDER BY ${dto.orderField} ${dto.orderValue}
        </if>
    </select>
    <select id="warningCount" resultType="java.lang.Long">
        select count(*) from t_warning_track twt where twt.warning_id in (
        select tw.id
        FROM tb_fx_monitor_person_relation p
        JOIN t_warning tw ON p.monitor_id = tw.monitor_id
        <where>
            AND tw.hit_subject = #{dto.hitSubject}
            AND tw.hit_subject_scene = #{dto.hitSubjectScene}
            <if test="dto.worth != null and dto.worth != ''">
                AND tw.id IN (select distinct warning_id from t_warning_feedback where JSON_EXTRACT(feedback, '$.worth')
                = #{dto.worth} and type = 2)
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                <choose>
                    <when test="dto.warningStatus == 5">
                        <!-- 未反馈: 状态为已签收或者已完结，没有反馈记录 -->
                        and tw.id in (select warning_id from t_warning_process where not exists (select 1 from
                        t_warning_feedback where warning_id=tw.id and type = 2) and (status=2 or status=4))
                    </when>
                    <otherwise>
                        <!--                        and tw.id in  (select warning_id from t_warning_process where id= (select process_id from t_warning_notify where warning_id=tw.id  and user_id=#{user.id} and dept_id=#{user.deptId}) and status=#{dto.warningStatus})-->
                        and tw.id in (select warning_id from t_warning_process where status=#{dto.warningStatus})
                    </otherwise>
                </choose>
            </if>
            <if test="dto.signOverdue != null">
                <!--                and tw.id in (select warning_id from t_warning_process where id= (select process_id from t_warning_notify where warning_id=tw.id and user_id=#{user.id} and dept_id=#{user.deptId}) and sign_overdue=#{dto.signOverdue})-->
                and tw.id in (select warning_id from t_warning_process where sign_overdue=#{dto.signOverdue})
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND tw.activity_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND tw.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.areaId != null and dto.areaId != ''">
                AND JSON_OVERLAPS(tw.area_id, concat('[', #{dto.areaId}, ']'))
            </if>
        </where>
        )
    </select>
    <select id="trackList" resultType="com.trs.police.subject.domain.vo.PersonTrackVO">
        SELECT
        distinct
        p.id_card AS idCard,
        tw.area_id as areaId,
        twt.datasource_type as gjlx,
        twt.longitude as jdwgs84,
        twt.latitude as wdwgs84,
        twt.source_type as gzylx,
        twt.source_id as gzybh,
        IFNULL(twt.address, '- -') as activityAddress,
        twt.activity_time as activityTime,
        tw.id as warningId
        FROM
        t_warning_track twt
        INNER JOIN tb_fx_monitor_person_relation p ON
        twt.monitor_id = p.monitor_id
        LEFT JOIN t_warning tw ON
        twt.warning_id = tw.id
        <where>
            AND tw.hit_subject = #{dto.hitSubject}
            AND tw.hit_subject_scene = #{dto.hitSubjectScene}
            <if test="dto.worth != null and dto.worth != ''">
                AND tw.id IN (select distinct warning_id from t_warning_feedback where JSON_EXTRACT(feedback, '$.worth')
                = #{dto.worth} and type = 2)
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                <choose>
                    <when test="dto.warningStatus == 5">
                        <!-- 未反馈: 状态为已签收或者已完结，没有反馈记录 -->
                        and tw.id in (select warning_id from t_warning_process where not exists (select 1 from
                        t_warning_feedback where warning_id=tw.id and type = 2) and (status=2 or status=4))
                    </when>
                    <otherwise>
                        <!--                        and tw.id in  (select warning_id from t_warning_process where id= (select process_id from t_warning_notify where warning_id=tw.id  and user_id=#{user.id} and dept_id=#{user.deptId}) and status=#{dto.warningStatus})-->
                        and tw.id in (select warning_id from t_warning_process where status=#{dto.warningStatus})
                    </otherwise>
                </choose>
            </if>
            <if test="dto.signOverdue != null">
                <!--                and tw.id in  (select warning_id from t_warning_process where id= (select process_id from t_warning_notify where warning_id=tw.id  and user_id=#{user.id} and dept_id=#{user.deptId}) and sign_overdue=#{dto.signOverdue})-->
                and tw.id in (select warning_id from t_warning_process where sign_overdue=#{dto.signOverdue})
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND tw.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND tw.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.areaId != null and dto.areaId != ''">
                AND JSON_OVERLAPS(tw.area_id, concat('[', #{dto.areaId}, ']'))
            </if>
        </where>
        order by tw.activity_time desc
        limit #{startNum}, #{dto.pageSize}
    </select>
    <!--    <select id="trackList" resultType="com.trs.police.subject.domain.vo.PersonTrackVO">-->
    <!--        SELECT-->
    <!--        DISTINCT-->
    <!--        p.id ,-->
    <!--        p.id_card AS idCard,-->
    <!--        p.real_name AS realName,-->
    <!--        p.person_number AS personNumber,-->
    <!--        p.gender,-->
    <!--        p.nation,-->
    <!--        p.birthday,-->
    <!--        p.avatar,-->
    <!--        p.org_post as orgPost,-->
    <!--        p.org_join_time as orgJoinTime,-->
    <!--        p.person_type as personType,-->
    <!--        ta.id as areaId,-->
    <!--        ta.location_type as locationType,-->
    <!--        twt.datasource_type as gjlx,-->
    <!--        twt.longitude as jdwgs84,-->
    <!--        twt.latitude as wdwgs84,-->
    <!--        twt.source_type as gzylx,-->
    <!--        twt.source_id as gzybh,-->
    <!--        ifnull( twt.address, '- -') as activityAddress,-->
    <!--        twt.activity_time as activityTime-->
    <!--        From-->
    <!--        t_warning_track twt-->
    <!--        LEFT JOIN tb_fx_person p ON-->
    <!--        twt.person_id = p.id-->
    <!--        LEFT JOIN t_warning tw ON-->
    <!--        twt.warning_id = tw.id-->
    <!--        LEFT JOIN t_control_important_area ta ON-->
    <!--        JSON_CONTAINS(tw.area_id, CAST(ta.id AS JSON), '$')-->
    <!--        <where>-->
    <!--            1=1-->
    <!--            <if test="dto.startTime != null and dto.startTime != ''">-->
    <!--                AND twt.activity_time >= #{dto.startTime}-->
    <!--            </if>-->
    <!--            <if test="dto.endTime != null and dto.endTime != ''">-->
    <!--                AND twt.activity_time &lt;= #{dto.endTime}-->
    <!--            </if>-->
    <!--            <if test="dto.locationType != null and dto.locationType != ''">-->
    <!--                AND ta.location_type = #{dto.locationType}-->
    <!--            </if>-->
    <!--            <if test="dto.areaId != null and dto.areaId != ''">-->
    <!--                AND ta.id IN-->
    <!--                <foreach item="code" collection="dto.areaId.split(',')" open="(" separator="," close=")">-->
    <!--                    #{code}-->
    <!--                </foreach>-->
    <!--            </if>-->
    <!--        </where>-->
    <!--    </select>-->
    <select id="midStatistics" resultMap="personalStatisticsVO">
        SELECT
        jt.area_id_element AS areaId,
        DATE_FORMAT(twt.activity_time, '%m-%d') AS statisticsTime,
        COUNT(*) AS statisticsCount
        FROM
        t_warning tw
        JOIN JSON_TABLE(tw.area_id, '$[*]' COLUMNS (area_id_element INT PATH '$')) AS jt ON TRUE
        LEFT JOIN t_warning_track twt ON
        tw.id = twt.warning_id
        INNER JOIN tb_fx_monitor_person_relation p ON
        twt.monitor_id = p.monitor_id
        <where>
            tw.area_id is not null and JSON_LENGTH(tw.area_id) > 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND twt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND twt.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.idCard != null and dto.idCard != ''">
                AND p.id_card = #{dto.idCard}
            </if>
            <if test="dto.locationType != null and dto.locationType != ''">
                AND ta.location_type = #{dto.locationType}
            </if>
            <if test="dto.areaId != null and dto.areaId != ''">
                AND jt.area_id_element IN
                <foreach item="areaId" collection="dto.areaId.split(',')" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        </where>
        GROUP BY
        jt.area_id_element,
        DATE_FORMAT(activity_time , '%m-%d');
    </select>
    <select id="midAreaStatistics" resultMap="personalStatisticsVO">
        SELECT
        jt.area_id_element AS areaId,
        twt.longitude AS jdwgs84,
        twt.latitude AS wdwgs84,
        p.id_card as idCard,
        COUNT(*) AS totalCount
        FROM
        t_warning tw
        JOIN JSON_TABLE(tw.area_id, '$[*]' COLUMNS (area_id_element INT PATH '$')) AS jt ON TRUE
        LEFT JOIN t_warning_track twt ON
        tw.id = twt.warning_id
        INNER JOIN tb_fx_monitor_person_relation p ON
        twt.monitor_id = p.monitor_id
        <where>
            tw.area_id is not null and JSON_LENGTH(tw.area_id) > 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND twt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND twt.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.idCard != null and dto.idCard != ''">
                AND p.id_card = #{dto.idCard}
            </if>
            <if test="dto.locationType != null and dto.locationType != ''">
                AND ta.location_type = #{dto.locationType}
            </if>
            <if test="dto.areaId != null and dto.areaId != ''">
                AND jt.area_id_element IN
                <foreach item="areaId" collection="dto.areaId.split(',')" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        </where>
        GROUP BY
        jt.area_id_element;
    </select>
    <select id="swAreaStatistics" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        jt.area_id_element AS areaId,
        twt.longitude AS jdwgs84,
        twt.latitude AS wdwgs84,
        p.id_number as idCard,
        COUNT(*) AS totalCount
        FROM
        t_warning tw
        JOIN JSON_TABLE(tw.area_id, '$[*]' COLUMNS (area_id_element INT PATH '$')) AS jt ON TRUE
        LEFT JOIN t_warning_track twt ON tw.id = twt.warning_id
        INNER JOIN t_profile_person p ON
        twt.person_id = p.id
        <where>
            tw.area_id is not null and JSON_LENGTH(tw.area_id) > 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND twt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND twt.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.idCard != null and dto.idCard != ''">
                AND p.id_number = #{dto.idCard}
            </if>
            <if test="dto.areaId != null and dto.areaId != ''">
                AND jt.area_id_element IN
                <foreach item="areaId" collection="dto.areaId.split(',')" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        </where>
        GROUP BY
        jt.area_id_element;
    </select>

    <select id="getErrorPersonPageList" resultMap="errorPersonResultMap">
        select
        tfp.real_name as realName,
        tfp.id_card as idCard,
        tfp.person_type as religion,
        tfp.person_status as personStatus,
        tfp.person_category as personCategory,
        tfp.lk_status as lkStatus,
        tfp.birthday as birthday,
        COUNT(*) as personCount,
        MAX(twt.activity_time) as finalActivityTime,
        IFNULL((SELECT status FROM tb_fx_task_process WHERE task_id = tfp.id_card AND subject_type = #{dto.subjectType}
        AND clue_excavate_type = #{dto.clueExcavateType}), 1) as warningStatus,
        (SELECT COUNT(1) FROM tb_fx_feedback WHERE task_id = tfp.id_card AND subject_type = #{dto.subjectType} AND
        clue_excavate_type = #{dto.clueExcavateType}) as feedbackCount
        from tb_fx_person tfp
        join tb_fx_monitor_person_relation tpmr on tpmr.id_card = tfp.id_card
        join t_warning_track twt on tpmr.monitor_id = twt.monitor_id
        <where>
            <bind name="districtCode"
                  value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().getDept().getDistrictCode()"/>
            <!-- fx专题增加数据权限,泸州市的能看全部 -->
            <if test="'fx'.equals(dto.subjectType) and !'510500'.equals(districtCode)">
                AND tfp.control_area_code = #{districtCode}
            </if>
            <if test="dto.worth != null">
                AND tfp.id_card IN (select distinct task_id from tb_fx_feedback where JSON_EXTRACT(feedback, '$.worth')
                = #{dto.worth} and subject_type = #{dto.subjectType} and clue_excavate_type = #{dto.clueExcavateType})
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                AND (
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(5)">
                    <!-- 未反馈: 状态为已签收或者已完结且没有反馈记录 -->
                    (tfp.id_card IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = #{dto.clueExcavateType} and
                    (t1.status = 2 or (t1.status = 4 and not exists(select 1 from tb_fx_feedback t2 where t2.task_id =
                    tfp.id_card and t2.subject_type = #{dto.subjectType} and t2.clue_excavate_type =
                    #{dto.clueExcavateType}))))
                    ) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(1)">
                    <!-- 未签收 -->
                    (tfp.id_card NOT IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = #{dto.clueExcavateType})) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(2)">
                    <!-- 已签收包括（已签收、未反馈、已反馈） -->
                    (tfp.id_card IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                    #{dto.subjectType} and t1.clue_excavate_type = #{dto.clueExcavateType} AND t1.status IN (2,3))) OR
                </if>
                tfp.id_card IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type =
                #{dto.subjectType} and t1.clue_excavate_type = #{dto.clueExcavateType} and t1.status in
                <foreach collection="dto.warningStatus.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
                )
            </if>
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND tfp.subject_type = #{dto.subjectType}
            </if>
            <if test="personStatus != null and personStatus != ''">
                AND tfp.person_status IN
                <foreach collection="personStatus.split(',')" item="personStatus" open="(" separator="," close=")">
                    #{personStatus}
                </foreach>
            </if>
            <if test="personType != null and personType != ''">
                AND tfp.person_type = #{personType}
            </if>
            <if test="dto.activityStartTime != null and dto.activityStartTime != ''">
                AND twt.activity_time &gt;= #{dto.activityStartTime}
            </if>
            <if test="dto.activityEndTime != null and dto.activityEndTime != ''">
                AND twt.activity_time &lt;= #{dto.activityEndTime}
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'idCard'">
                        AND tfp.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField == 'realName'">
                        AND tfp.real_name LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField = 'fullText'">
                        AND (
                        tfp.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                        OR tfp.real_name LIKE CONCAT('%', #{dto.searchValue}, '%')
                        )
                    </when>
                </choose>
            </if>
        </where>
        GROUP BY tfp.id_card
        ORDER BY twt.${dto.orderField} ${dto.orderType}
    </select>
    <select id="getErrorPersonTrackPageList" resultMap="errorPersonResultMap">
        select
        tfp.real_name as realName,
        twt.address as address,
        tfp.person_type as religion,
        twt.activity_time as activityTime,
        twt.datasource_type as gjlx,
        twt.photos as photos,
        twt.similarity as similarity,
        tfp.avatar as avatar
        from tb_fx_person tfp
        join tb_fx_monitor_person_relation tpmr on tpmr.id_card = tfp.id_card
        join t_warning_track twt on tpmr.monitor_id = twt.monitor_id
        where tpmr.monitor_id is not null and tfp.id_card = #{dto.idCard}
        order by twt.activity_time desc
    </select>
    <select id="swTopStatisticsPerson" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        COUNT(DISTINCT p.id) as totalCount
        FROM t_profile_person p
        <where>
            p.deleted = 0
            <if test="labelIds != null and labelIds.size > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="labelIds" item="labelId" separator="," open="JSON_ARRAY(" close=")">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="swTopStatisticsOtherInfo" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        COUNT(DISTINCT wt.person_id) as activeCount,
        COUNT(DISTINCT wt.warning_id) as warningCount
        from
        t_warning_track wt
        JOIN t_profile_person_police_control ppc ON wt.person_id = ppc.person_id
        JOIN t_profile_person p ON ppc.person_id = p.id
        <where>
            p.deleted = 0
            <if test="labelIds != null and labelIds.size > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="labelIds" item="labelId" separator="," open="JSON_ARRAY(" close=")">
                    #{labelId}
                </foreach>
                )
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND wt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND wt.activity_time &lt;= #{dto.endTime}
            </if>
        </where>
    </select>

    <select id="swMidAreaStatistics" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">

    </select>

    <select id="swAreaPersonCount" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        SUBSTRING(ppc.control_station, 1, 6) AS areaCode,
        COUNT(DISTINCT p.id) AS totalCount
        FROM
        t_profile_person p
        JOIN t_profile_person_police_control ppc ON p.id = ppc.person_id
        <where>
            p.deleted = 0
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        GROUP BY SUBSTRING(ppc.control_station, 1, 6)
        HAVING areaCode IS NOT NULL
    </select>

    <select id="swAreaActiveAndWarningCount" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        SUBSTRING(ppc.control_station, 1, 6) AS areaCode,
        COUNT(DISTINCT wt.person_id) AS activeCount,
        COUNT(wt.warning_id) AS warningCount
        FROM
        t_warning_track wt
        JOIN t_warning w ON wt.warning_id = w.id
        JOIN t_profile_person_police_control ppc ON wt.person_id = ppc.person_id
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND w.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND w.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(w.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        GROUP BY SUBSTRING(ppc.control_station, 1, 6)
        HAVING areaCode IS NOT NULL
    </select>

    <select id="swAreaActiveAndWarningCountByAreaCodes"
            resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
        SUBSTRING(ppc.control_station, 1, 6) AS areaCode,
        COUNT(DISTINCT wt.person_id) AS activeCount,
        COUNT(wt.warning_id) AS warningCount
        FROM
        t_warning_track wt
        JOIN t_profile_person_police_control ppc ON wt.person_id = ppc.person_id
        JOIN t_profile_person p ON ppc.person_id = p.id
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND wt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND wt.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        group by areaCode
    </select>

    <select id="importantPersonWarning" resultMap="newestSensitivePersonList">
        SELECT
        latest_track.warning_id as warningId,
        latest_track.activity_time as activityTime,
        latest_track.address as activityAddress,
        p.real_name as personName,
        p.id_card as idNumber,
        p.person_label as personLabel,
        p.warning_level as warningLevel,
        p.person_id as personProfileId,
        p.person_photo as personPhoto
        FROM
        tb_sw_bk_person p
        JOIN (
        SELECT person_id
        FROM t_warning_track
        <where>
            1=1
            <if test="dto.startTime != null and dto.startTime != ''">
                AND activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND activity_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY person_id
        ) filtered_persons ON p.person_id = filtered_persons.person_id
        JOIN (
        SELECT t1.*
        FROM t_warning_track t1
        JOIN (
        SELECT person_id, MAX(activity_time) as max_time
        FROM t_warning_track
        <where>
            1=1
            <if test="dto.startTime != null and dto.startTime != ''">
                AND activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND activity_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY person_id
        ) t2 ON t1.person_id = t2.person_id AND t1.activity_time = t2.max_time
        ) latest_track ON p.person_id = latest_track.person_id
        <where>
            <if test="dto.warningStatus != null">
                AND p.warning_level = #{dto.warningStatus}
            </if>
        </where>
        ORDER BY latest_track.activity_time DESC
    </select>
    <select id="activeStatistic" resultType="com.trs.police.subject.domain.vo.PersonActivityTrendsVO">
        SELECT
        DATE(wt.activity_time) AS time,
        COUNT(DISTINCT wt.person_id) AS activeCount
        FROM
        t_warning_track wt
        JOIN t_warning w ON wt.warning_id = w.id
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND wt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND wt.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(w.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        group by time
    </select>
    <select id="swBkPersonList" resultMap="swBkPersonListMap">
        SELECT
        p.person_id AS id,
        p.real_name AS realName,
        p.id_card AS idCard,
        GROUP_CONCAT(DISTINCT p.warning_id) as warningIds,
        p.person_label as personLabel,
        p.person_photo as personPhoto
        FROM tb_sw_bk_person p
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.statistic_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.statistic_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'idCard'">
                        AND p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField == 'realName'">
                        AND p.real_name LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField = 'fullText'">
                        AND (
                        p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                        OR p.real_name LIKE CONCAT('%', #{dto.searchValue}, '%')
                        )
                    </when>
                </choose>
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        group by p.person_id
        order by p.statistic_time DESC
    </select>

    <select id="swBkPersonListV3" resultMap="swBkPersonListMap">
        SELECT
        p.id AS id,
        GROUP_CONCAT(DISTINCT wt.warning_id) as warningIds,
        p.name AS realName,
        p.id_number AS idCard,
        p.person_label personLabel,
        tw.warning_level as warningLevel,
        JSON_ARRAY_APPEND(
        IFNULL(p.photo, '[]'),
        '$',
        JSON_OBJECT('id', -1, 'url', CONCAT('/oss/photo/', p.id_number))
        ) as personPhoto
        FROM t_profile_person p
        JOIN t_warning_track wt ON wt.person_id = p.id AND wt.activity_time >= #{dto.startTime} AND wt.activity_time &lt;= #{dto.endTime}
        JOIN t_warning tw ON wt.warning_id = tw.id
        <where>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'idCard'">
                        AND p.id_number LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField == 'realName'">
                        AND p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField = 'fullText'">
                        AND (
                        p.id_number LIKE CONCAT('%', #{dto.searchValue}, '%')
                        OR p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                        )
                    </when>
                </choose>
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        group by p.id
        order by wt.activity_time DESC
    </select>

    <select id="swBkPersonListV2" resultMap="swBkPersonListMap">
        SELECT
        p.person_id AS id,
        p.real_name AS realName,
        p.id_card AS idCard,
        GROUP_CONCAT(DISTINCT p.warning_id) as warningIds,
        p.person_label as personLabel,
        p.person_photo as personPhoto
        FROM tb_sw_bk_person p
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.statistic_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.statistic_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.idCard != null and dto.idCard != ''">
                AND p.id_card LIKE CONCAT('%', #{dto.idCard}, '%')
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'idCard'">
                        AND p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField == 'realName'">
                        AND p.real_name LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField = 'fullText'">
                        AND (
                        p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                        OR p.real_name LIKE CONCAT('%', #{dto.searchValue}, '%')
                        )
                    </when>
                </choose>
            </if>
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
        group by p.person_id
    </select>


    <select id="getClueExcavateCount" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT p.id)
        FROM
        t_warning_track wt
        JOIN t_warning w ON wt.warning_id = w.id
        JOIN t_profile_person p ON wt.person_id = p.id
        <where>
            p.deleted = 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND wt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND wt.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="modelIds != null and modelIds.size() > 0">
                AND JSON_OVERLAPS(w.model_id,
                <foreach collection="modelIds" item="modelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{modelId}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="swPersonTrackListByPersonIds" resultType="com.trs.police.subject.domain.vo.PersonTrackVO">
        SELECT
        wt.person_id AS id,
        CAST(wt.longitude AS CHAR) AS jdwgs84,
        CAST(wt.latitude AS CHAR) AS wdwgs84,
        DATE_FORMAT(wt.activity_time, '%Y-%m-%d %H:%i:%s') AS activityTime,
        wt.address AS activityAddress,
        d.cn_name AS gjlx
        FROM
        t_warning_track wt
        LEFT JOIN
        t_warning_source_type d ON d.en_name = wt.datasource_type
        <where>
            1=1
            <if test="gjlx != null and gjlx != ''">
                AND d.cn_name like CONCAT(#{gjlx}, '%')
            </if>
            AND wt.person_id IN
            <foreach collection="personIdList" item="personId" separator="," open="(" close=")">
                #{personId}
            </foreach>
        </where>
        order by wt.activity_time desc
    </select>
    <select id="swTrackList" resultMap="personTrackMap">
        SELECT
        distinct
        p.id as id,
        p.name AS realName,
        p.id_number AS idCard,
        p.person_label as personLabel,
        tw.model_id as modelId,
        JSON_ARRAY_APPEND(
        IFNULL(p.photo, '[]'),
        '$',
        JSON_OBJECT('id', -1, 'url', CONCAT('/oss/photo/', p.id_number))
        ) as personPhoto,
        tw.area_id as areaId,
        twt.datasource_type as gjlx,
        twt.longitude as jdwgs84,
        twt.latitude as wdwgs84,
        twt.source_type as gzylx,
        twt.source_id as gzybh,
        IFNULL(twt.address, '- -') as activityAddress,
        twt.activity_time as activityTime,
        tw.id as warningId
        FROM
        t_warning_track twt
        JOIN t_profile_person p ON
        twt.person_id = p.id
        JOIN t_warning tw ON
        twt.warning_id = tw.id
        <where>
            1=1
            <if test="personLabelList != null and personLabelList.size() > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="personLabelList" item="personLabel" separator="," open="JSON_ARRAY(" close=")">
                    #{personLabel}
                </foreach>
                )
            </if>
            <if test="modelIds != null and modelIds.size() > 0">
                AND JSON_OVERLAPS(tw.model_id,
                <foreach collection="modelIds" item="modelId" separator="," open="JSON_ARRAY(" close=")">
                    #{modelId}
                </foreach>
                )
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND twt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND twt.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.areaId != null and dto.areaId != '' and dto.locationType == 2">
                AND tw.id in
                <foreach collection="dto.areaId.split(',')" item="id" open="(" separator="," close=")">
                    CAST(#{id} AS SIGNED)
                </foreach>
            </if>
            <if test="dto.areaId != null and dto.areaId != '' and dto.locationType == 1">
                AND JSON_OVERLAPS(tw.area_id,
                <foreach collection="dto.areaId.split(',')" item="id" open="JSON_ARRAY(" separator="," close=")">
                    CAST(#{id} AS SIGNED)
                </foreach>
                )
            </if>
        </where>
        order by twt.activity_time desc
    </select>
    <select id="swWarningCount" resultType="java.lang.Long">
        SELECT
        count(distinct twt.id)
        FROM
        t_warning_track twt
        INNER JOIN t_profile_person p ON
        twt.person_id = p.id
        LEFT JOIN t_warning tw ON
        twt.warning_id = tw.id
        <where>
            1=1
            <if test="personLabelList != null and personLabelList.size() > 0">
                AND JSON_OVERLAPS(p.person_label,
                <foreach collection="personLabelList" item="personLabel" separator="," open="JSON_ARRAY(" close=")">
                    #{personLabel}
                </foreach>
                )
            </if>
            <if test="modelIds != null and modelIds.size() > 0">
                AND JSON_OVERLAPS(tw.model_id,
                <foreach collection="modelIds" item="modelId" separator="," open="JSON_ARRAY(" close=")">
                    #{modelId}
                </foreach>
                )
            </if>
            <if test="dto.worth != null and dto.worth != ''">
                AND tw.id IN (select distinct warning_id from t_warning_feedback where JSON_EXTRACT(feedback, '$.worth')
                = #{dto.worth} and type = 2)
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                <choose>
                    <when test="dto.warningStatus == 5">
                        <!-- 未反馈: 状态为已签收或者已完结，没有反馈记录 -->
                        and tw.id in (select warning_id from t_warning_process where not exists (select 1 from
                        t_warning_feedback where warning_id=tw.id and type = 2) and (status=2 or status=4))
                    </when>
                    <otherwise>
                        <!--                        and tw.id in  (select warning_id from t_warning_process where id= (select process_id from t_warning_notify where warning_id=tw.id  and user_id=#{user.id} and dept_id=#{user.deptId}) and status=#{dto.warningStatus})-->
                        and tw.id in (select warning_id from t_warning_process where status=#{dto.warningStatus})
                    </otherwise>
                </choose>
            </if>
            <if test="dto.signOverdue != null">
                <!--                and tw.id in  (select warning_id from t_warning_process where id= (select process_id from t_warning_notify where warning_id=tw.id  and user_id=#{user.id} and dept_id=#{user.deptId}) and sign_overdue=#{dto.signOverdue})-->
                and tw.id in (select warning_id from t_warning_process where sign_overdue=#{dto.signOverdue})
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND twt.activity_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND twt.activity_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.areaId != null and dto.locationType != null and dto.locationType == 2">
                AND tw.id = #{dto.areaId}
            </if>
            <if test="dto.areaId != null and dto.locationType != null and dto.locationType == 1">
                AND JSON_OVERLAPS(tw.area_id,
                <foreach collection="dto.areaId.split(',')" item="id" open="JSON_ARRAY(" separator="," close=")">
                    CAST(#{id} AS SIGNED)
                </foreach>
                )
            </if>
        </where>
    </select>
    <select id="getPersonLabelByIdCard" resultMap="swPersonMap">
        SELECT
        p.id_number AS zjhm,
        p.name AS xm,
        p.id as personId,
        p.person_label AS personLabel
        FROM t_profile_person p
        WHERE p.id_number IN
        <foreach collection="idCardList" item="idCard" separator="," open="(" close=")">
            #{idCard}
        </foreach>
    </select>
    <select id="getWarningModelId" resultMap="swWarningModel">
        select
        w.id as warningId,
        w.model_id as modelId
        from t_warning w
        <where>
            <if test="warningIdList != null">
                w.id IN
                <foreach collection="warningIdList" item="warningId" separator="," open="(" close=")">
                    #{warningId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getPersonRealName" resultType="com.trs.police.subject.domain.vo.PersonVO">
        select
        p.name as realName,
        p.id_number as idCard
        from t_profile_person p
        where p.id_number IN
        <foreach collection="idCardList" item="idCard" separator="," open="(" close=")">
            #{idCard}
        </foreach>
    </select>
    <select id="fkPersonLocalTrend" resultMap="fkPersonMap">
        select
        p.id as id,
        p.id_card as idCard,
        p.name as realName,
        p.photo as avatar,
        w.warning_model as personCategory,
        <choose>
            <when test="dto.locationType == 1">
                p.label_id AS personLabels,
            </when>
            <otherwise>
                (select ryrp.label_id from t_warning_fk_person_ryrp ryrp
                where ryrp.fk_person_id = p.id order by ryrp.create_time desc limit 1) as personLabels,
            </otherwise>
        </choose>
        w.capture_address as activityAddress,
        w.capture_time as captureTime,
        w.face_photo as capturePhoto,
        from t_warning_fk_person p
        left join t_warning_fkrxyj w on p.id_card = w.id_card
        <where>
            <if test="dto.locationType == 1">
                AND p.on_record = 1
            </if>
            <if test="dto.locationType == 2">
                AND p.on_record = 0
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND p.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND p.create_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == 'idCard'">
                        AND p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField == 'name'">
                        AND p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                    </when>
                    <when test="dto.searchField = 'fullText'">
                        AND (
                        p.id_card LIKE CONCAT('%', #{dto.searchValue}, '%')
                        OR p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                        )
                    </when>
                </choose>
            </if>
        </where>
        order by p.create_time desc
    </select>


    <sql id="commonCondition">
        AND p.person_status != '死亡'
        <if test="dto.subjectType != null and dto.subjectType != ''">
            AND p.subject_type = #{dto.subjectType}
        </if>
        <if test="dto.lkStatus != null and dto.lkStatus != ''">
            AND p.lk_status = #{dto.lkStatus}
        </if>
        <if test="dto.careStatus != null">
            AND p.care_status = #{dto.careStatus}
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND p.lk_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND p.lk_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.personType != null and dto.personType != ''">
            AND p.person_type IN
            <foreach collection="dto.personType.split(',')" item="type" separator="," open="(" close=")">
                #{type}
            </foreach>
        </if>
        <if test="dto.personStatus != null and dto.personStatus != ''">
            AND p.person_status IN
            <foreach collection="dto.personStatus.split(',')" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
        </if>
        <if test="dto.personCategory != null and dto.personCategory != ''">
            AND p.person_category IN
            <foreach collection="dto.personCategory.split(',')" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
        </if>
        <if test="dto.areaCode != null and dto.areaCode != ''">
            AND p.control_area_code IN
            <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                #{code}
            </foreach>
        </if>
    </sql>
    <sql id="commonStatistic">
        COUNT(DISTINCT CASE WHEN p.care_status = 1 THEN CONCAT(p.id_card, p.care_status) END) AS followCount,
        COUNT(DISTINCT CASE WHEN p.lk_status = '本地流出' THEN CONCAT(p.id_card, p.flow_status) END) AS bdlcCount,
        COUNT(DISTINCT CASE WHEN p.lk_status = '外地流入' THEN CONCAT(p.id_card, p.flow_status) END) AS wdlrCount
    </sql>

    <sql id="groupByPersonType">
        <choose>
            <when test="dto.subjectType == 'sw'">
                person_type_element
            </when>
            <otherwise>
                p.person_type
            </otherwise>
        </choose>
    </sql>

    <sql id="selectPersonType">
        <choose>
            <when test="dto.subjectType == 'sw'">
                person_type_element
            </when>
            <otherwise>
                p.person_type
            </otherwise>
        </choose>
    </sql>
</mapper>
