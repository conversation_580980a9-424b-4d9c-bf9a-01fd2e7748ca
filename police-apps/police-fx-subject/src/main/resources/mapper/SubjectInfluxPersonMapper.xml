<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.SubjectInfluxPersonMapper">

    <select id="foreignPersonList" resultType="com.trs.police.subject.domain.vo.ForeignPersonExcavateVO">
        SELECT
            *,
            person_type as religion,
            area_name as registeredResidence,
            IFNULL((SELECT status FROM tb_fx_task_process WHERE task_id = tfip.id_card AND subject_type = #{dto.subjectType} AND clue_excavate_type = 4), 1) as warningStatus,
            (SELECT COUNT(1) FROM tb_fx_feedback WHERE task_id = tfip.id_card AND subject_type = #{dto.subjectType} AND clue_excavate_type = 4) as feedbackCount
        FROM tb_fx_influx_person tfip
        <where>
            <if test="dto.worth != null">
                AND tfip.id_card IN (select distinct task_id from tb_fx_feedback where JSON_EXTRACT(feedback, '$.worth') = #{dto.worth} and subject_type = #{dto.subjectType} and clue_excavate_type = 4)
            </if>
            <if test="dto.warningStatus != null and dto.warningStatus != ''">
                AND (
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(5)">
                    <!-- 未反馈: 状态为已签收或者已完结且没有反馈记录 -->
                    (tfip.id_card IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 4 and
                    (t1.status = 2 or (t1.status = 4 and not exists(select 1 from tb_fx_feedback t2 where t2.task_id = tfip.id_card and t2.subject_type = #{dto.subjectType} and t2.clue_excavate_type = 4))))
                    ) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(1)">
                    <!-- 未签收 -->
                    (tfip.id_card NOT IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 4)) OR
                </if>
                <if test="@com.trs.police.common.core.utils.StringUtil@convertToIntegerList(dto.warningStatus).contains(2)">
                    <!-- 已签收包括（已签收、未反馈、已反馈） -->
                    (tfip.id_card IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 4 AND t1.status IN (2,3))) OR
                </if>
                tfip.id_card IN (select distinct task_id from tb_fx_task_process t1 where t1.subject_type = #{dto.subjectType} and t1.clue_excavate_type = 4 and t1.status in
                <foreach collection="dto.warningStatus.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
                )
            </if>
            <if test="dto.regisAreaCode != null and dto.regisAreaCode != ''">
                AND tfip.area_code LIKE CONCAT(#{dto.regisAreaCode}, '%')
            </if>
            <if test="dto.influxStartTime != null and dto.influxStartTime != ''">
                AND tfip.first_appearance_time &gt;= #{dto.influxStartTime}
            </if>
            <if test="dto.influxEndTime != null and dto.influxEndTime != ''">
                AND tfip.first_appearance_time &lt;= #{dto.influxEndTime}
            </if>
            <if test="searchParams.searchValue != null and searchParams.searchValue != ''">
                AND (
                tfip.id_card LIKE CONCAT('%', #{searchParams.searchValue}, '%')
                OR tfip.real_name LIKE CONCAT('%', #{searchParams.searchValue}, '%')
                )
            </if>
        </where>
        <choose>
            <when test="sortParams.sortField != null and sortParams.sortField != ''">
                ORDER BY #{sortParams.sortField} #{sortParams.sortDirection}
            </when>
            <otherwise>
                ORDER BY first_appearance_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>