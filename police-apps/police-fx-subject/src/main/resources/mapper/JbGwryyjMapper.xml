<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.JbGwryyjMapper">

    <select id="selectExistZjhm" resultType="com.trs.police.subject.domain.entity.JbGwryyjEntity">
        select xyrcyzjzjhm, id from t_jb_gwryyj where xyrcyzjzjhm in
        <foreach collection="zjhmList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>