<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.ImportantAreaMapper">

    <select id="fkImportantTargetUnits" resultType="com.trs.police.subject.domain.vo.ImportantAreaVO">
        select
        a.`id` as id,
        a.name as name,
        (select d.name from t_dict d where d.type = 'control_warning_source_category' and d.code = a.category) as type,
        a.district_code as districtCode,
        a.district_name as districtName,
        (select count(distinct id_card) from t_warning_fkrxyj where JSON_CONTAINS(area_id, CAST(a.id AS JSON))) as activityPersonCount
        from t_control_important_area a
        <where>
            a.deleted = 0 and a.police_category = 12
            <if test="dto.areaCode != null">
                and a.district_code = #{dto.areaCode}
            </if>
        </where>
    </select>
</mapper>