<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.FkWarningMapper">

    <select id="influxFkWarningModalStat" resultType="com.trs.police.subject.domain.vo.FkWarningModalVO">
        select
        warning_model as modalName,
        count(1) as warningCount,
        count(DISTINCT id_card) as personNum
        from t_warning_fkrxyj
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND create_time &lt;= #{dto.endTime}
            </if>
        </where>
        group by warning_model ORDER BY warningCount desc
    </select>
    <select id="onRecordFkWarningModalStat" resultType="com.trs.police.subject.domain.vo.FkWarningModalVO">
        SELECT
        count(1) as warningCount,count(DISTINCT person_id) as personNum
        FROM
        t_warning as a LEFT JOIN t_warning_track as b on a.id = b.warning_id
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND a.create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND a.create_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.fkLabelIds != null">
                AND JSON_OVERLAPS ( person_label, #{dto.fkLabelIds}) > 0
            </if>

            <if test="dto.configIds != null">
                <foreach collection="dto.configIds" item="configId" separator="or" open="and (" close=")">
                    JSON_OVERLAPS (
                    ( ifnull( model_id, '[]' ) ),
                    ( SELECT warning_model FROM t_control_regular_monitor_config WHERE id = #{configId})
                    ) > 0
                </foreach>
            </if>
        </where>
    </select>
    <select id="fkPersonTrackListByIdCards" resultType="com.trs.police.subject.domain.vo.PersonTrackVO">
        select
        *
        from
        t_warning_
    </select>
    <select id="getPersonJudge" resultType="java.lang.Integer">
        select
        count(1)
        from
        t_warning_fk_person_ryrp
        <where>
            fk_person_id = #{id}
        </where>
    </select>

</mapper>