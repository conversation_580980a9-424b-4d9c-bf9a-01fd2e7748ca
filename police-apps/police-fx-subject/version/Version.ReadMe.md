# fx-subject\
## v17.4
- XMKFB-8428 后-【高新】- 提供FK专题大屏人员管控、风险防控接口

## v17.3
- XMKFB-8386 后-【广安】- 涉稳专题-重点人员预警慢问题排查

## v17.1
- XMKFB-8199【广安】涉稳专题，重点人员预警数据显示了最新一天里最早的一条预警数据
- XMKFB-8231 后-【广安】- 提供重大敏感案事件详情接口
- XMKFB-8222【广安】涉稳专题，时间段范围较大以后布控人员列表bkPersonList接口的响应速度需做优化

## v16.5
- XMKFB-7815 合 -【广安】- 态势大屏开发
- 

## v16.4
-XMKFB-7871[XMKFB-7814] 后-【广安】- 完成专题首页接口提供
-XMKFB-7872[XMKFB-7814] 后-【广安】- 完成人员管控相关接口提供
-XMKFB-7814 后-【广安】- 提供风险日历接口
-XMKFB-7875[XMKFB-7814] 后-【广安】- 完成线索挖掘相关接口提供
-XMKFB-7873[XMKFB-7814] 后-【广安】- 完成区域管控相关接口提供
XMKFB-8003 【广安】FK大屏，时间段范围较大以后预警概况数据接口报错


## v16.2
- XMKFB-7656[XMKFB-7653] 后-【广安】- FK 人员统计相关接口提供
- XMKFB-7657[XMKFB-7653]后-【广安】- 完成预警相关接口提供

```properties
# 只更新广安环境
# 广安fk专题人员区域代码，fk标签
subject.fk.parent.areaCode=511600
subject.fk.personLabel=45
```

## v12.4
- XMKFB-5977 泸州-FX线索管理问题

## RC20241218
- XMKFB-5666 泸州-FX需求扩展

## v12.1
- XMKFB-5460 【高新】涉黄大屏，预警统计和线索推送数据随时间段切换后未变化
- XMKFB-5490[XMKFB-5336] 后-【高新】- 涉黄大屏增加人员管控

## RC20241203
- XMKFB-5438 泸州-FX人员数据权限需求

## v11.4
- XMKFB-5339 后-【德阳】-北新机械厂-群体档案-上传附件bug

## RC20241115
- XMKFB-5185 【泸州】泸州FX大屏，区域人员轨迹按照状态筛选无效

## v11.2 发版日志
- XMKFB-5138 高新-【线索池】线索编号生产

## v11.1 发版日志
- XMKFB-4927 合-【泸州】-FX专题优化  
- XMKFB-4671 高新-涉黄人员大屏开发

## RC 20241108
- XMKFB-5037[XMKFB-5035] 前-人员档案修改

## RC 20241028
- XMKFB-4892[XMKFB-4745] 后-【高新】-线索池-预警数据接入优化  

## v10.4 发版日志
- XMKFB-4633[XMKFB-4490] 后-【泸州】-FX专题-线索挖掘/风险管控签收机制相关接口提供

## RC20240827
- XMKFB-3442 后-【高新】-民转刑警情统计逻辑优化
- XMKFB-3409 民转刑警情报告内容调整  
- XMKFB-3262 【自贡】FX系统部署  
- XMKFB-3351 后-【自贡】-自贡部署警种专题
- XMKFB-3412 【泸州】FX的区域预警问题
## v8.4 发版日志
- XMKFB-3327 后-【泸州】-FX专题优化20240820提  

## 20240821
- XMKFB-3381 泸州经侦大屏假币展示字段调整
- 泸州经侦大屏企业风险展示字段调整

## v8.3 发版日志
- XMKFB-3082 合-【泸州】-经侦专题添加人员管理后台  

## 8.2
- XMKFB-3007[XMKFB-2999] 后-【泸州】-关注对象增加区域关注  
- XMKFB-3006[XMKFB-2999] 后-【泸州】-经侦专题后端优化  

# 新增nacos配置（泸州环境）
# 经侦包括的主办单位
subject.zjzt.includeZbdw=

## RC20240806
- XMKFB-2971 高新-线索池+风险挖掘系统调整  

## v8.1 发版日志
- XMKFB-2948 合-【泸州】-FX专题地图撒点优化  

## RC20240729
- XMKFB-2882 后-【泸州】-经侦大屏-关注对象相关优化  
- XMKFB-2903[XMKFB-2901] 后-【泸州】-FX专题-关注对象检索逻辑变更  

## v7.4 发版日志
- XMKFB-2861 后-【泸州】-经侦专题后端优化
- XMKFB-2486 【泸州】FX预警签收流程开发  
- XMKFB-2864 后-【泸州】-经侦专题警情态势检索慢问题分析  
- XMKFB-2862 后-【泸州】-警情态势数据问题排查

新增nacos配置（泸州环境）
# 经侦专题地域映射
subject.zjzt.areaCodeMap=[{"areaName":"市局","showName":"泸州市","areaCode":"510500"},{"areaName":"江阳","showName":"江阳区","areaCode":"510502"},{"areaName":"龙马潭","showName":"龙马潭区","areaCode":"510504"},{"areaName":"纳溪","showName":"纳溪区","areaCode":"510503"},{"areaName":"泸县","showName":"泸县","areaCode":"510521"},{"areaName":"合江","showName":"合江县","areaCode":"510522"},{"areaName":"叙永","showName":"叙永县","areaCode":"510524"},{"areaName":"古蔺","showName":"古蔺县","areaCode":"510525"}]

## RC20240724
- 外来流入人员新返回感知源类型字段  
- XMKFB-2804 后-【泸州】-FX专题相关调整20240723  

## RC20240723
- XMKFB-2730 合-【泸州】-fx专题-窝点挖掘优化  
- XMKFB-2740 合-【泸州】-FX专题-隐形人员挖掘优化  

## v7.3 发版日志
- XMKFB-2565 【泸州】-FX问题处理20240710  
- XMKFB-2554[XMKFB-2487] 后-【泸州】-经侦专题-关注对象接口提供  
- XMKFB-2486 【泸州】FX预警签收流程开发  
- XMKFB-2552[XMKFB-2487] 后-【泸州】-经侦专题-警情态势部分接口提供

## RC 20240703
> XMKFB-2409[XMKFB-2407] 后-【泸州】-fx专题大屏后端优化  
> XMKFB-2412 [XMKFB-2425] 后-【泸州】-线索挖掘-异常活动挖掘列表和细览数据不一致  

## RC 20240701
> XMKFB-2404 后-【高新】-FK专题大屏优化    

## v7.1 发版日志
- XMKFB-2410 后-【泸州】-线索挖掘-外来人员流入增加流入时间展示  
- XMKFB-2372 [XMKFB-2369]后-【泸州】-fx后台-布控预警相关优化  
- XMKFB-2438[XMKFB-2425] 后-【泸州】-人员管控后团问题处理  
- XMKFB-2437[XMKFB-2425] 后-【泸州】-窝点挖掘和隐形人员挖掘问题处理 
- XMKFB-2436[XMKFB-2425] 后-【泸州】-专案研判支持细览页

## v6.4 发版日志
> XMKFB-2331 [XMKFB-2324] 后-【高新】-预警统计、大屏中部 接口提供  
> XMKFB-2175 [XMKFB-2168] 后-fx大屏优化（6-4）  
> XMKFB-2330 [XMKFB-2324] 后-【高新】-人员区域分布、敏感人员、管控区域接口提供  

## RC20240625
> XMKFB-2246 [XMKFB-2168] 后-异常活动人员挖掘接口提供
> XMKFB-2247 后-外来人员流入接口提供

## RC20240620
> XMKFB-2226 【泸州】FX的线索管理后台问题  

## v6.1 发版日志
> XMKFB-1977 【泸州】FX需求修改  

## RC 20240604
> XMKFB-1873[XMKFB-1816] 后-FX的人员预警包括：常控和临控的预警结果  

## v6.1 发版日志
> XMKFB-1823[XMKFB-1816] 后-敏感事件节点需要支持删除、修改  

## v5.4 发版日志
> XMKFB-1734 合-人员管控-活跃度分析支持点击  

## v5.3 发版日志
> XMKFB-1630[XMKFB-1629] 后-完成右侧线索列表开发  
> XMKFB-1629 后-【泸州GA】-FX专题-线索挖掘大屏开发  

## RC 20240520
> XMKFB-1665 【泸州】FX的布控预警列表问题  

## v5.2 发版日志
> XMKFB-1509[XMKFB-1498] 后-FX大屏问题处理及优化  
> XMKFB-1501[XMKFB-1498] 后-区域管控-左侧大屏接口提供
> XMKFB-1502[XMKFB-1498] 后-区域管控-中部、右侧接口提供
> XMKFB-1525[XMKFB-1498] 后-添加专案研判报错

## v5.1 发版日志
> XMKFB-1196[XMKFB-1187] 后-police-control 模块熟悉

## v1.5 发版日志
- XMKFB-1189[XMKFB-1187] 后-完成专案研判模块开发  
- XMKFB-1195[XMKFB-1187] 后-FX专题大屏人员相关接口提供  
- XMKFB-1190[XMKFB-1187] 合-【泸州公安】FX专题开发  
- 
```
```