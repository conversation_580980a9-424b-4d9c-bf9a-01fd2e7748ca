package com.trs.police.comparison.data.extract.domain.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/22 11:53
 */

@Data
@TableName("t_dict")
public class DictDO {

    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 英文名称 或 数字
     */
    private String value;

    /**
     * 数据类型 感知源类型 或 特征值类型
     */
    private String valueType;

}
