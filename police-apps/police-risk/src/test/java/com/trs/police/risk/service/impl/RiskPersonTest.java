package com.trs.police.risk.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.risk.domain.entity.RiskPerson;
import com.trs.police.risk.flow.RiskPersonDataFlow;
import com.trs.police.risk.mapper.RiskPersonMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/25
 */
@SpringBootTest
public class RiskPersonTest {

    @Autowired
    private RiskPersonDataFlow riskPersonDataFlow;
    @Autowired
    private RiskPersonMapper riskPersonMapper;

    @Test
    public void addPerson() throws Throwable {
        Map<String, Object> map = new HashMap<>();
        map.put("xm", "黄文隆");
        map.put("zjhm", "529298201106171973");
        map.put("gsdy", "510109000000");
        map.put("gsdymc", "四川省成都市高新区");
        String str = JSONObject.toJSONString(map);
        riskPersonDataFlow.persist(Arrays.asList(str));
    }

    @Test
    public void riskPersonListTest(){
        List<RiskPerson> riskPeople = riskPersonMapper.selectList(null);
        System.out.println(JSONObject.toJSONString(riskPeople));
    }
}
