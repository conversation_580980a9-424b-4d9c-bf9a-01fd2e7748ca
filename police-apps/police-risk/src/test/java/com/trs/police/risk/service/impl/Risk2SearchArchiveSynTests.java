package com.trs.police.risk.service.impl;

import com.trs.police.risk.RiskApp;
import com.trs.police.risk.service.Risk2SearchArchiveSyn;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * *@author:wen.wen
 * *@create 2025-01-08 19:17
 **/
@Slf4j
@SpringBootTest(classes = RiskApp.class)
public class Risk2SearchArchiveSynTests {

    @Resource
    private Risk2SearchArchiveSyn risk2SearchArchiveSyn;

    @Test
    public void test() {
        risk2SearchArchiveSyn.syncHistoryData("2024-04-03","2024-04-03");
    }
}
