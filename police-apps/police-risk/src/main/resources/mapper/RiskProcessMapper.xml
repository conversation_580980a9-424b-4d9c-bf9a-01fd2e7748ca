<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.risk.mapper.RiskProcessMapper">
    <resultMap id="urgeMap" type="com.trs.police.risk.domain.vo.UrgeVO">
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <association property="handleStatus" column="{code=handleStatus,type=risk_status_type}"
            select="com.trs.police.common.core.mapper.CommonMapper.getDict"
            javaType="com.trs.police.common.core.vo.CodeNameVO"/>
    </resultMap>

    <update id="updateStatusByRiskId">
        update t_risk_process
        set status = #{status,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler}
        where risk_id = #{riskId}
    </update>
    <delete id="deleteRelationsByIdBatch">
        <!--流程-->
        delete from t_risk_process where id in
        <foreach collection="ids" open="(" close=")" item="id">
            #{id}
        </foreach>;
        <!--未读-->
        delete from t_risk_user_relation u where u.risk_id = #{riskId} and u.dept_id in
        <foreach collection="handleDept" open="(" close=")" item="id">
            #{id}
        </foreach>;
    </delete>
    <select id="getUrgeDept" resultMap="urgeMap">
        select p.dept_id,
               (select d.short_name from t_dept d where d.id = p.dept_id) as dept_name,
               p.status                                                   as handleStatus,
               'risk_status' as risk_status_type
        from t_risk_process p
        where p.risk_id = #{riskId}
    </select>

</mapper>