package com.trs.police.risk.domain.vo;

import com.trs.police.risk.domain.vo.traceableData.KeyWordVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimilarRiskWarnVO {

    /**
     * 电话号码
     */
    private String tel;

    /**
     * 风险警情内容
     */
    private String riskContent;

    /**
     * 风险类型
     */
    private String riskType;

    /**
     * 风险等级
     */
    private Long riskLevel;

    /**
     * 分数
     */
    private Double score;

    /**
     * 关键词
     */
    private List<KeyWordVO> scoreKeyWords;

    /**
     * 高危词
     */
    private String keywords;

}
