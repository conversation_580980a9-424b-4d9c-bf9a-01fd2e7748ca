package com.trs.police.risk.stragegy.createLogImpl;

import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.risk.domain.entity.OperationLogEntity;
import com.trs.police.risk.domain.entity.RiskProcess;
import com.trs.police.risk.domain.vo.DoneVO;
import com.trs.police.risk.mapper.RiskProcessMapper;
import com.trs.police.risk.stragegy.OperationLogStrategyFactory;
import com.trs.police.risk.stragegy.OperateLogStrategy;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/25
 */
@Service
@Slf4j
public class EditDoneOperateLogStrategy implements OperateLogStrategy, InitializingBean {

    @Resource
    RiskProcessMapper riskProcessMapper;

    @Override
    public <T>void createLog(T newObj, OperationLogEntity operationLogEntity, String desc) {
        DoneVO newDone = JsonUtil.parseSpecificObject(newObj, DoneVO.class);
        Long riskId = operationLogEntity.getRelatedId();
        RiskProcess process = riskProcessMapper.getsDoneProcess(riskId);
        DoneVO oldDone = process.getDone();
        StringBuilder stringBuilder = new StringBuilder();

        if (Boolean.compare(newDone.getResolveStatus(), oldDone.getResolveStatus()) != 0) {
            stringBuilder.append("将化解状态：从 ").append(oldDone.getResolveStatus() ? "已化解" : "未化解")
                    .append(" 修改为 ").append(newDone.getResolveStatus() ? "已化解" : "未化解").append("\n");
        }

        if (Boolean.compare(newDone.getHandOverStatus(), oldDone.getHandOverStatus()) != 0) {
            stringBuilder.append("将是否移交党政：从 ").append(oldDone.getHandOverStatus() ? "已移交" : "未移交")
                    .append(" 修改为 ").append(newDone.getHandOverStatus() ? "已移交" : "未移交").append("\n");
        }

        if (StringUtils.compare(newDone.getRemark(), oldDone.getRemark()) != 0) {
            stringBuilder.append("将备注：从 ").append(oldDone.getRemark()).append(" 修改为 ")
                    .append(newDone.getRemark()).append("\n");
        }
        operationLogEntity.setDetail(stringBuilder.toString());
    }

    @Override
    public void afterPropertiesSet() {
        OperationLogStrategyFactory.register(Operation.EDIT_DONE, this);
    }
}
