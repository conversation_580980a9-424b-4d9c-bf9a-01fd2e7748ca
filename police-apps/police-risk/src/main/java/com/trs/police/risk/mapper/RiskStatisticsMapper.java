package com.trs.police.risk.mapper;

import com.trs.police.risk.domain.dto.RiskStatisticDTO;
import com.trs.police.risk.domain.vo.RiskDistributionVO;
import com.trs.police.risk.domain.vo.RiskStatisticVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/12/25
 * @description: 风险统计mapper
 */
@Mapper
public interface RiskStatisticsMapper {

    /**
     * 风险总数统计
     *
     * @param dto dto
     * @return {@link Long}
     */
    Long riskTotalCount(@Param("dto") RiskStatisticDTO dto);

    /**
     * 风险状态统计
     *
     * @param dto dto
     * @return {@link List}<{@link RiskDistributionVO}>
     */
    List<RiskStatisticVO> riskStatusStatistics(@Param("dto") RiskStatisticDTO dto);

    /**
     * 风险区域统计
     *
     * @param dto dto
     * @return {@link List}<{@link RiskStatisticVO}>
     */
    List<RiskStatisticVO> riskAreaStatistics(@Param("dto") RiskStatisticDTO dto);

    /**
     * 风险派出所统计
     *
     * @param dto dto
     * @return {@link List}<{@link RiskStatisticVO}>
     */
    List<RiskStatisticVO> riskPcsStatistics(@Param("dto") RiskStatisticDTO dto);
}
