package com.trs.police.risk.domain.vo;

import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.risk.domain.entity.RiskPersonRelation;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/12 14:49
 */
@Data
public class RelatedPersonVO {

    private Long id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idNumber;
    /**
     * 角色
     */
    private List<CodeNameVO> role;

    /**
     * 电话
     */
    private List<String> tel;
    /**
     * 备注
     */
    private String remark;
    /**
     * 人员档案id
     */
    private Long personId;

    /**
     * 户籍所在地
     */
    private String registeredResidenceDetail;

    private Long riskId;


    /**
     * 转entity
     *
     * @return RiskPersonRelation
     */
    public RiskPersonRelation toEntity(){
        RiskPersonRelation relation = new RiskPersonRelation();
        relation.setId(this.id);
        relation.setName(this.name);
        relation.setIdNumber(this.idNumber);
        relation.setRole(CodeNameVO.codeNameListToLongList(this.getRole()));
        relation.setTel(this.getTel());
        relation.setRemark(remark);
        relation.setRiskId(riskId);
        return relation;
    }


}
