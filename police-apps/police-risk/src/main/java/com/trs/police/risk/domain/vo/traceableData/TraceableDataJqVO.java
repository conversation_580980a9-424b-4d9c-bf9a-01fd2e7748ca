package com.trs.police.risk.domain.vo.traceableData;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/3 17:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TraceableDataJqVO extends TraceableDataBaseVO{

    private static final long serialVersionUID = 1567833566484747060L;

    /**
     * 发生地点
     */
    private String address;

    /**
     * 处警反馈
     */
    private List<FeedbackVO> feedbacks;
}
