package com.trs.police.risk.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Objects;
import lombok.Getter;

/**
 * 风险-溯源数据关联类型
 *
 * <AUTHOR>
 * @date 2023/5/16 16:12
 */

public enum RiskRelatedTypeEnum {

    MANUAL(1, "手动关联"),
    AUTO(2, "自动关联");


    RiskRelatedTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    @EnumValue
    @JsonValue
    private final int code;

    @Getter
    private final String name;


    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static RiskRelatedTypeEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (RiskRelatedTypeEnum typeEnum : RiskRelatedTypeEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }


}
