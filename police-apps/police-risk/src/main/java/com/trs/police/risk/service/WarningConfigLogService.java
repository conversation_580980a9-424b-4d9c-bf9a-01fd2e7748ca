package com.trs.police.risk.service;

import com.trs.police.risk.aspect.OperationLog;
import com.trs.police.risk.domain.entity.OperationLogEntity;

/**
 * <AUTHOR>
 * @date 2023/9/7 11:08
 */
public interface WarningConfigLogService {

    /**
     * 详情设置
     *
     * @param targetObjectId     id
     * @param operationLog       操作记录
     * @param newObj             新值
     * @param operationLogEntity 操作记录
     */
    void createOperateLog(Long targetObjectId, OperationLog operationLog, Object newObj,
        OperationLogEntity operationLogEntity);

}
