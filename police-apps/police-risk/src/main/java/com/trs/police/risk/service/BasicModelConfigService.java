package com.trs.police.risk.service;

import com.trs.police.risk.domain.dto.ModelStatusDTO;
import com.trs.police.risk.domain.vo.RuleConfigBasicInfoVO;
import com.trs.web.builder.base.RestfulResultsV2;

/**
 * 模型基础配置服务接口
 * <p>
 * 提供模型基础配置相关的业务操作接口定义
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public interface BasicModelConfigService {
    /**
     * 根据ID获取模型基础配置信息
     *
     * @param code 模型编码
     * @return 模型基础配置实体
     */
    RestfulResultsV2<RuleConfigBasicInfoVO> getBasicInfo(String code);

    /**
     *开关模型状态
     *
     * @param modelStatusDTO 模型状态空DTO
     * @return 打开或关闭是否成功
     */
    RestfulResultsV2<String> switchModel(ModelStatusDTO modelStatusDTO);

    /**
     * 新增或修改模型基础配置信息
     *
     * @param vo 传入的实体
     * @return 模型基础配置实体
     */
    RestfulResultsV2<String> saveOrUpdateBasicInfo(RuleConfigBasicInfoVO vo);
}
