package com.trs.police.risk.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 模型基础配置
 *
 * <AUTHOR>
 * @since 2025/2/18 13:46
 */

@Getter
@Setter
@TableName("t_basic_model_config")
public class BasicModelConfig extends AbstractBaseEntity {

    /**
     * 模型唯一编码
     */
    private String code;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型介绍
     */
    private String modelDesc;

    /**
     * 预警模型
     */
    private String warnModel;

    /**
     * 模型分类
     */
    private String modelCategory;

    /**
     * 数据范围
     */
    private String dataScope;

    /**
     * 风险分值公式
     */
    private String riskScoreFormula;

    /**
     * 模型是否生效 true: 生效  false: 失效
     */
    private Boolean open;

}

