package com.trs.police.risk.service;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.risk.domain.entity.RiskPersonRelation;
import com.trs.police.risk.domain.vo.RelatedPersonVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/11 10:27
 */
public interface RiskPersonRelationService {

    /**
     * 新增相关人员
     *
     * @param personVO 人员信息
     */
    void createRelatedPerson(RelatedPersonVO personVO);

    /**
     * 编辑相关人员
     *
     * @param personVO 人员信息
     */
    void editRelatedPerson(RelatedPersonVO personVO);

    /**
     * 相关人员编辑回显
     *
     * @param relationId 关系id
     * @return 编辑回显信息
     */
    RelatedPersonVO getRelatedPersonEditInfo(Long relationId);

    /**
     * 相关人员
     *
     * @param riskId  风险id
     * @param request 请求参数
     * @return 相关人员信息
     */
    PageResult<RelatedPersonVO> getRelatedPerson(Long riskId, ListParamsRequest request);

    /**
     * 关联人员-取消关联
     *
     * @param relationId 关联id
     */
    void deletePersonRelation(Long relationId);

    /**
     * 相关人员不分页
     *
     * @param riskId 风险id
     * @return 相关人员
     */
    List<RelatedPersonVO> getRelatedPersonByRiskId(Long riskId);

    /**
     * 批量插入相关人员
     *
     * @param personRelations 相关人员
     */
    void insertBatch(List<RiskPersonRelation> personRelations);
}
