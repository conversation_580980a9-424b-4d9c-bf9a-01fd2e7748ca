package com.trs.police.risk.service.impl;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.ExceptionMessageConstant;
import com.trs.police.common.core.constant.enums.PermissionParamsEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.CommonMapper;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.*;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.risk.RiskHeadVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.risk.config.RiskConfig;
import com.trs.police.risk.constant.RiskConstant;
import com.trs.police.risk.constant.enums.*;
import com.trs.police.risk.domain.entity.*;
import com.trs.police.risk.domain.vo.*;
import com.trs.police.risk.domain.vo.RiskProcessVO.DoneProcessVO;
import com.trs.police.risk.domain.vo.push.ScoreDisplayVO;
import com.trs.police.risk.domain.vo.traceableData.ClueSourceDetailVO;
import com.trs.police.risk.domain.vo.traceableData.FeedbackVO;
import com.trs.police.risk.domain.vo.traceableData.TraceableDataBaseVO;
import com.trs.police.risk.domain.vo.traceableData.TraceableDataListVO;
import com.trs.police.risk.mapper.*;
import com.trs.police.risk.service.RiskDisplayService;
import com.trs.police.risk.service.RiskPersonRelationService;
import com.trs.police.risk.service.RiskUserRelationService;
import com.trs.police.risk.service.TraceableDataService;
import com.trs.police.risk.util.RiskUtil;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.trs.police.risk.constant.RiskConstant.COPY_ROLE_OPERATIONS;
import static com.trs.police.risk.constant.RiskConstant.LIST_TYPE_COPY;

/**
 * 风险详情查询
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RiskDisplayServiceImpl implements RiskDisplayService {

    @Resource
    private RiskMapper riskMapper;
    @Resource
    private RiskProcessMapper riskProcessMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RiskCopyMapper riskCopyMapper;
    @Resource
    private RiskFeedbackMapper riskFeedbackMapper;
    @Resource
    private DictService dictService;
    @Resource
    private RiskUserRelationMapper riskUserRelationMapper;
    @Resource
    private RiskUserRelationService riskUserRelationService;
    @Resource
    private RiskTraceableDataRelationMapper riskTraceableDataRelationMapper;
    @Resource
    private TraceableDataService traceableDataService;

    @Resource
    private TraceableDataClueMapper traceableDataClueMapper;

    @Resource
    private TraceableDataJqMapper traceableDataJqMapper;

    @Resource
    private RiskRelatedPersonImportMapper riskRelatedPersonImportMapper;

    @Resource
    private RiskPersonRelationService riskPersonRelationService;

    @Resource
    private CommonMapper commonMapper;

    @Resource
    private RiskConfig riskConfig;

    private final Map<TraceableDataTypeEnum, TraceableDataBaseMapper> mappers = new HashMap<>(2);

    public RiskDisplayServiceImpl(
            TraceableDataJqMapper traceableDataJqMapper,
            TraceableDataClueMapper traceableDataClueMapper) {
        mappers.put(TraceableDataTypeEnum.JQ, traceableDataJqMapper);
        mappers.put(TraceableDataTypeEnum.CLUE, traceableDataClueMapper);
    }

    @Override
    public RiskProcessVO getRiskProcess(Long id) {
        final Risk risk = riskMapper.selectById(id);
        RiskProcessVO vo = new RiskProcessVO();
        //分派
        vo.setDispatch(getDispatchNode(risk.getDispatch()));
        //签收
        vo.setSign(getSignNode(risk.getSign(), risk.getJudgeOverdue()));
        //研判
        vo.setJudge(getJudgeNode(risk.getJudge(), risk.getJudgeOverdue()));
        //处置
        List<RiskProcess> riskProcesses = riskProcessMapper.getByRiskId(id);
        vo.setDisposal(getDisposalNodes(riskProcesses));
        //办结
        vo.setDone(getDoneList(riskProcesses));
        //取消处置
        vo.setCancelDisposal(getCancelDisposalNode(risk.getCancel()));
        //若取消处置，删除处置和办结节点
        if (vo.getCancelDisposal() != null) {
            vo.setDisposal(null);
            vo.setDone(null);
        }
        return vo;
    }

    private ProcessNodeVO getDispatchNode(DispatchVO dispatchVO) {
        if (dispatchVO == null) {
            return null;
        }
        ProcessNodeVO dispatchNode = new ProcessNodeVO(dispatchVO);
        dispatchNode.setType(dispatchVO.getType());
        SimpleDeptVO dept = dispatchVO.getResponsibleDept();
        dispatchNode.setOperation(new ProcessNodeVO.Operation("分派至 " + dept.getDeptName()));
        return dispatchNode;
    }

    private ProcessNodeVO getSignNode(OperateVO signVO, Boolean isOverdue) {
        if (signVO == null) {
            return null;
        }
        ProcessNodeVO signNode = new ProcessNodeVO(signVO);
        signNode.setOperation(new ProcessNodeVO.Operation(isOverdue, "签收"));
        return signNode;
    }

    private ProcessNodeVO getJudgeNode(JudgeVO judgeVO, Boolean isOverdue) {
        if (judgeVO == null) {
            return null;
        }
        ProcessNodeVO judgeNode = new ProcessNodeVO(judgeVO);
        judgeNode.setOperation(new ProcessNodeVO.Operation(isOverdue, "研判"));
        //不需处置，添加不处置原因
        List<KeyValueVO> contents = new ArrayList<>();
        contents.add(new KeyValueVO("是否处置", Boolean.TRUE.equals(judgeVO.getIsHandle()) ? "需要处置" : "不予处置"));
        if (Boolean.FALSE.equals(judgeVO.getIsHandle())) {
            RiskOperateEnum reason = judgeVO.getReasonType();
            contents.add(new KeyValueVO("不处置原因",
                    RiskOperateEnum.OTHER.equals(reason) ? judgeVO.getReason() : reason.getName()));
        }
        contents.add(new KeyValueVO("研判意见", judgeVO.getContent()));
        judgeNode.setContents(contents);
        judgeNode.setAttachments(judgeVO.getAttachments());
        judgeNode.setIsHandle(judgeVO.getIsHandle());
        return judgeNode;
    }

    private List<ProcessNodeVO> getDisposalNodes(List<RiskProcess> riskProcesses) {
        return riskProcesses.stream().map(entity -> {
            ProcessNodeVO nodeVO = new ProcessNodeVO();
            nodeVO.setTime(TimeUtil.getSimpleTime(entity.getCreateTime()));
            SimpleUserVO user = new SimpleUserVO();
            DeptDto dto = permissionService.getDeptById(entity.getDeptId());
            user.setDeptShortName(dto.getShortName());
            nodeVO.setUserInfo(user);
            RiskStatusEnum status = entity.getStatus();
            nodeVO.setRiskStatus(new CodeNameVO(status.getCode(), status.getName()));
            nodeVO.setOperation(getOperation(entity));
            return nodeVO;
        }).collect(Collectors.toList());
    }

    private ProcessNodeVO getCancelDisposalNode(CancelVO cancelVO) {
        if (cancelVO == null) {
            return null;
        }
        ProcessNodeVO cancelNode = new ProcessNodeVO(cancelVO);
        cancelNode.setOperation(new ProcessNodeVO.Operation("取消处置"));
        RiskOperateEnum reasonEnum = cancelVO.getReasonType();
        cancelNode.setContents(List.of(new KeyValueVO("取消原因",
                RiskOperateEnum.OTHER.equals(reasonEnum) ? cancelVO.getReason() : reasonEnum.getName())));
        return cancelNode;
    }

    private DoneProcessVO getDoneList(List<RiskProcess> riskProcesses) {
        //查询办结：所有流程里办结时间最晚的
        RiskProcess process = riskProcesses.stream()
                .filter(p -> Objects.nonNull(p.getDone()))
                .max(Comparator.comparing(p -> p.getDone().getOperateTime()))
                .orElse(null);
        if (process == null) {
            return null;
        }
        DoneProcessVO doneProcessVO = new DoneProcessVO();
        List<ProcessNodeVO> doneList = new ArrayList<>();
        if (process.getDone() != null) {
            ProcessNodeVO doneNode = new ProcessNodeVO(process.getDone());
            doneNode.setOperation(new ProcessNodeVO.Operation(false, "提交办结"));
            doneProcessVO.setTime(TimeUtil.getSimpleTime(process.getDone().getOperateTime()));
            doneNode.setAttachments(process.getDone().getAttachments());
            doneList.add(doneNode);
        }
        if (process.getAgree() != null) {
            ProcessNodeVO doneNode = new ProcessNodeVO(process.getAgree());
            doneNode.setOperation(new ProcessNodeVO.Operation(false, "同意办结"));
            doneProcessVO.setTime(TimeUtil.getSimpleTime(process.getAgree().getOperateTime()));
            doneNode.setAttachments(process.getDone().getAttachments());
            doneList.add(doneNode);
        }
        if (process.getReject() != null) {
            ProcessNodeVO doneNode = new ProcessNodeVO(process.getReject());
            doneNode.setOperation(new ProcessNodeVO.Operation(false, "驳回办结"));
            doneProcessVO.setTime(TimeUtil.getSimpleTime(process.getReject().getOperateTime()));
            List<KeyValueVO> contents = List.of(new KeyValueVO("驳回原因", process.getReject().getReason()));
            doneNode.setAttachments(process.getDone().getAttachments());
            doneNode.setContents(contents);
            doneList.add(doneNode);
        }
        doneProcessVO.setDoneItem(doneList);
        return doneProcessVO;
    }

    private ProcessNodeVO.Operation getOperation(RiskProcess process) {
        List<String> overdueInfo = new ArrayList<>();
        if (Boolean.TRUE.equals(process.getSignOverdue())) {
            overdueInfo.add("签收");
        }
        if (Boolean.TRUE.equals(process.getReplyOverdue())) {
            overdueInfo.add("反馈");
        }
        String result = overdueInfo.isEmpty() ? "" : String.join("、", overdueInfo) + "逾期";
        return new ProcessNodeVO.Operation(process.getReplyOverdue() || process.getSignOverdue(), result);
    }

    @Override
    public RiskHeadVO getRiskHead(Long id, String type) {
        Risk risk = getRisk(id);
        RiskStatusEnum status = risk.getRiskStatus();
        if ("handle".equals(type)) {
            RiskProcess process = riskProcessMapper.getByDeptIdAndRiskId(AuthHelper.getNotNullUser().getDeptId(), id);
            if (Objects.nonNull(process)) {
                status = process.getStatus();
            }
        }
        TraceableDataTypeEnum source = risk.getSource();
        //处理标签
        List<String> tags = new ArrayList<>();
        mappers.values().parallelStream()
                .map(mapper -> mapper.getRiskTraceableDataTagCode(risk.getId()))
                .flatMap(List::stream)
                .forEach(tags::add);
        List<Long> tagResult = new ArrayList<>();
        tags.forEach(tagStr -> {
            String numbers = tagStr.substring(1, tagStr.length() - 1);
            Arrays.asList(numbers.split(",")).forEach(number -> tagResult.add(Long.parseLong(number.trim())));
        });

        //处理来源
        CodeNameVO codeNameVO = new CodeNameVO();
        if (source != null && source.getCode() == TraceableDataTypeEnum.CLUE.getCode()) {
            //线索查询具体线索
            ClueSourceDetailVO clueSourceDetailVO = traceableDataClueMapper.getCurrentSourceDetailByRiskId(
                    risk.getId());
            if (Objects.nonNull(clueSourceDetailVO) && Objects.nonNull(clueSourceDetailVO.getSource())) {
                codeNameVO = clueSourceDetailVO.getSource();
            } else {
                codeNameVO.setName(TraceableDataTypeEnum.MANUAL.getName());
                codeNameVO.setCode(TraceableDataTypeEnum.MANUAL.getCode());
            }
        } else if (source != null) {
            codeNameVO.setName(source.getName());
            codeNameVO.setCode(source.getCode());
        }
        DictDto dictDto = dictService.getDictByTypeAndCode("risk_type", risk.getRiskType());
        RiskTraceableDataRelation currentRelation = riskTraceableDataRelationMapper.getCurrentByRiskId(id);
        String tel;
        String idNumber;
        if (Objects.nonNull(currentRelation)) {
            TraceableDataBaseVO current = traceableDataService.getByRelation(currentRelation);
            tel = current.getTel();
            idNumber = current.getIdNumber();
        } else {
            idNumber = "";
            tel = "";
        }
        return RiskHeadVO.builder()
                .id(risk.getId())
                .riskStatus(new CodeNameVO(status.getCode(), status.getName()))
                .title(risk.getTitle())
                .riskLevel(new CodeNameVO(risk.getRiskLevel().getCode(), risk.getRiskLevel().getName()))
                .riskCode(risk.getRiskCode())
                .source(codeNameVO)
                .riskType(dictDto != null ? dictDto.toCodeNameVO() : new CodeNameVO())
                .riskParentType(dictDto != null ? dictDto.getPid() : null)
                .riskScore(risk.getScore())
                .warningTime(TimeUtil.getSimpleTime(risk.getWarningTime()))
                .timeLimit(TimeUtil.getSimpleTime(risk.getTimeLimit()))
                .copyDept(riskCopyMapper.selectDeptNameByRiskId(risk.getId()))
                .jqCodes(fecthJqCodeHead(id, tel, idNumber, TraceableDataTypeEnum.JQ.getCode()))
                .clueCodes(riskMapper.get12345CodesHead(risk.getId(), tel, idNumber))
                .oldJqCodes(fecthJqCodeHead(id, tel, idNumber, TraceableDataTypeEnum.OLD_JQ.getCode()))
                .tags(tagResult)
                .build();
    }

    private List<String> fecthJqCodeHead(Long id, String tel, String idNumber, Integer type) {
        final List<String> result = new ArrayList<>();
        List<String> telList = new ArrayList<>();
        if (!StringUtils.isNullOrEmpty(tel)) {
            telList = riskMapper.getJqCodesHead(id, tel, null, type);
        }
        List<String> idNumberList = new ArrayList<>();
        if (!StringUtils.isNullOrEmpty(idNumber) && !"无".equals(idNumber)) {
            idNumberList = riskMapper.getJqCodesHead(id, null, idNumber, type);
        }
        //去重
        Set<String> riskTraceableData = new HashSet<>(telList);
        riskTraceableData.addAll(idNumberList);
        // 将去重后的结果添加到 result 列表中
        result.addAll(riskTraceableData);
        return result;
    }

    private Risk getRisk(Long id) {
        Risk risk = riskMapper.selectById(id);
        if (risk == null) {
            throw new TRSException("风险不存在！");
        }
        return risk;
    }

    @Override
    public PageResult<TraceableDataListVO> getRiskTraceableData(Long id, ListParamsRequest pageParams) {
        return traceableDataService.getPage(id, pageParams);
    }

    @Override
    public void modifyRiskColumn(Long id, KeyValueVO value) {
        Risk risk = getRisk(id);
        if ("riskLevel".equals(value.getKey())) {
            risk.setRiskLevel(RiskLevelEnum.codeOf(Integer.parseInt(value.getValue())));
        } else if ("riskType".equals(value.getKey())) {
            risk.setRiskType(Long.parseLong(value.getValue()));
        }
        riskMapper.updateById(risk);
    }

    @Override
    public RiskSummaryVO getRiskSummary(Long id) {
        Risk risk = getRisk(id);
        return new RiskSummaryVO(risk.getContent());
    }

    @Override
    public DoneVO getDone(Long id) {
        RiskProcess done = riskProcessMapper.getDoneById(id);
        if (Objects.isNull(done)) {
            return null;
        }
        return done.getDone();
    }

    @Override
    public SimpleDeptVO[] getHandleDept(Long id) {
        return riskMapper.selectById(id).getHandleDept();
    }

    @Override
    public List<RiskDeptVO> getCopy(Long id) {
        //获取当前用户可以参看的部门
        List<Long> deptIdsByPermission = permissionService.getDeptIdsByPermission(
                PermissionParamsEnum.SELF_EQUAL_DEPT.getCode());

        return riskMapper.getCopy(id, deptIdsByPermission);
    }

    @Override
    public FeedbackListVO getFeedbackList(Long id, String type, List<KeyValueTypeVO> filterParams) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        List<FeedbackDetail> list = riskFeedbackMapper.getList(id, filterParams, currentUser);
        if (CollectionUtils.isEmpty(list)) {
            return new FeedbackListVO(0, 0, list);
        }
        //获取风险状态
        Risk risk = getRisk(id);
        RiskStatusEnum statusEnum = risk.getRiskStatus();

        RiskRoleEnum role = RiskRoleEnum.of(currentUser);
        for (FeedbackDetail feedback : list) {
            feedback.setOperations(List.of());
            feedback.setReplyOperations(List.of());
            if (RiskRoleEnum.DISPATCHER.equals(role)) {
                continue;
            }
            if (canReply(type, currentUser, feedback, statusEnum)) {
                feedback.setOperations(List.of(RiskConstant.REPLY));
            } else if (canEdit(currentUser, feedback, statusEnum)) {
                feedback.setOperations(List.of(RiskConstant.DELETE_FEEDBACK, RiskConstant.EDIT_FEEDBACK));
            }

            if (Objects.nonNull(feedback.getReply()) && currentUser.isSelf(feedback.getReply().getUser())) {
                feedback.setReplyOperations(List.of(RiskConstant.EDIT_REPLY, RiskConstant.DELETE_REPLY));
            }
        }
        saveFeedbackRelation(id, currentUser);
        return new FeedbackListVO(list.size(), 0, list);
    }

    private void saveFeedbackRelation(Long riskId, CurrentUser user) {
        List<RiskUserRelation> relations = riskUserRelationMapper.selectByRiskId(riskId, user.getId(),
                user.getDeptId());
        relations.forEach(f -> f.setIsRead(Boolean.TRUE));
        riskUserRelationService.updateBatchById(relations);
    }

    /**
     * 反馈是否可以回复，详情类型为台账、并且不是自己发起的反馈、反馈未被回复、风险是在办中状态
     *
     * @param type        详情类型
     * @param currentUser 当前用户信息
     * @param feedback    反馈
     * @param status      风险状态
     * @return 布尔
     */
    private boolean canReply(String type, CurrentUser currentUser, FeedbackDetail feedback, RiskStatusEnum status) {
        return RiskConstant.LIST_TYPE_STANDING_BOOK.equals(type)
                && !currentUser.isSelf(feedback.getFeedback().getUser())
                && Objects.isNull(feedback.getReply())
                && RiskStatusEnum.DOING == status;
    }

    /**
     * 反馈是否可以编辑
     *
     * @param currentUser 当前用户信息
     * @param feedback    反馈
     * @param status      风险状态
     * @return 布尔
     */
    private boolean canEdit(CurrentUser currentUser, FeedbackDetail feedback, RiskStatusEnum status) {
        return currentUser.isSelf(feedback.getFeedback().getUser())
                && Objects.isNull(feedback.getReply())
                && (status == RiskStatusEnum.DOING || status == RiskStatusEnum.WAITING_DONE);
    }

    @Override
    public List<UrgeVO> getUrgeDept(Long id) {
        return riskProcessMapper.getUrgeDept(id);
    }

    @Override
    public List<RiskDeptVO> getDept(Long id) {
        return riskMapper.getAllDept(id);
    }

    @Override
    public PageResult<RiskListVO> getList(ListParamsRequest request, String type) {
        final PageParams pageParams = request.getPageParams();
        final Boolean isFromScreen = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(),"isFromScreen", Boolean.class);
        // 添加默认值处理，如果未传递该参数则默认为false
        final Boolean isScreenRequest = isFromScreen != null ? isFromScreen : Boolean.FALSE;
        //获取用户风险相关角色
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        RiskRoleEnum role = RiskRoleEnum.of(currentUser);
        if (Objects.isNull(role) && !type.equals(RiskConstant.LIST_TYPE_COPY)) {
            //如果是抄送列表，不需要判断角色
            throw new TRSException(ExceptionMessageConstant.NO_SUCH_ROLE);
        }
        Page<RiskListVO> curPage = request.getPageParams().toPage();
        curPage.setSearchCount(false);
        ListParamsRequest listParamsRequest = getRiskListRequest(request, type, role);
        String areaCode = BeanFactoryHolder.getEnv().getProperty("risk.filter.area.code", "");
        curPage.setTotal(riskMapper.getPageCount(listParamsRequest, type, currentUser, areaCode));
        Page<RiskListVO> page = riskMapper.getPage(listParamsRequest, type, currentUser, areaCode, curPage);
        List<RiskListVO> risks = page.getRecords();
        buildRiskResult(risks);
        if(!isScreenRequest){
            // 单独统计未读数，反馈数，是否已读
            buildRiskStatisticResult(risks, currentUser.getDeptId());
        }
        //获取操作按钮
        for (RiskListVO risk : risks) {
            int statusInt = Integer.parseInt(risk.getRiskStatus().getCode().toString());
            RiskStatusEnum status = RiskStatusEnum.codeOf(statusInt);
            if (Objects.nonNull(role)) {
                //根据当前用户角色、风险状态、是否是抄送人
                risk.setOperations(role.getListOperations(status, type));
            } else {
                risk.setOperations(COPY_ROLE_OPERATIONS.getOrDefault(status, List.of()));
            }
            if (RiskConstant.LIST_TYPE_HANDLE.equals(type) && !CollectionUtils.isEmpty(risk.getOverdue())) {
                List<OverdueVO> overdueVOList = risk.getOverdue().stream()
                        .filter(e -> currentUser.getDeptId().equals(e.getDeptId()))
                        .collect(Collectors.toList());
                risk.setOverdue(overdueVOList);
            }
        }

        return PageResult.of(risks, pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    private void buildRiskResult(List<RiskListVO> risks) {
        if (CollectionUtils.isEmpty(risks)) {
            return;
        }
        List<Long> riskIds = risks.stream().map(RiskListVO::getId).distinct().collect(Collectors.toList());
        List<ListSourceVO> listSourcesList = riskMapper.getListSources(riskIds);
        List<CodeNameVO> riskStatusList = commonMapper.getDicts("risk_status");
        List<CodeNameVO> riskLevelList = commonMapper.getDicts("risk_level");
        List<OverdueVO> overdueList = riskMapper.selectOverdueInfos(riskIds);
        Map<Long, ListSourceVO> lisSourcesMap = listSourcesList.stream()
                .collect(Collectors.toMap(ListSourceVO::getId, vo -> vo));
        Map<Object, CodeNameVO> riskStatusMap = riskStatusList.stream()
                .collect(Collectors.toMap(CodeNameVO::getCode, vo -> vo));
        Map<Object, CodeNameVO> riskLevelMap = riskLevelList.stream()
                .collect(Collectors.toMap(CodeNameVO::getCode, vo -> vo));
        Map<Long, List<OverdueVO>> overdueMap = overdueList.stream()
                .collect(Collectors.groupingBy(OverdueVO::getId));
        risks.forEach(risk -> {
            risk.setSource(lisSourcesMap.getOrDefault(risk.getId(), null));
            risk.setRiskLevel(riskLevelMap.getOrDefault(risk.getRiskLevelType(), null));
            risk.setRiskStatus(riskStatusMap.getOrDefault(risk.getRiskStatusType(), null));
            risk.setOverdue(overdueMap.getOrDefault(risk.getId(), null));
        });
    }

    /**
     * 构造统计数据
     *
     * @param risks         风险列表
     * @param currentDeptId 当前用户组织的id
     */
    private void buildRiskStatisticResult(List<RiskListVO> risks, Long currentDeptId) {
        if (CollectionUtils.isEmpty(risks)) {
            return;
        }
        List<Long> riskIds = risks.stream().map(RiskListVO::getId).distinct().collect(Collectors.toList());
        List<RiskListVO> riskUnread = riskMapper.getCurrentRiskUnread(riskIds, currentDeptId);
        List<RiskListVO> feedbackCount = riskMapper.getFeedbackCount(riskIds);
        Map<Long, String> feedbackCountMap = feedbackCount.stream().collect(Collectors.toMap(RiskListVO::getId, RiskListVO::getFeedbackCount, (v1, v2) -> v1));
        Map<Long, Integer> riskUnreadMap = riskUnread.stream().collect(Collectors.toMap(RiskListVO::getId, RiskListVO::getUnreadCount, (v1, v2) -> v1));
        List<Long> readRiskIdList = riskMapper.getReadRiskIdList(riskIds, currentDeptId);
        risks.forEach(risk -> {
            risk.setUnreadCount(riskUnreadMap.getOrDefault(risk.getId(), 0));
            risk.setFeedbackCount(feedbackCountMap.getOrDefault(risk.getId(), "0"));
            risk.setIsRead(readRiskIdList.contains(risk.getId()));
        });
    }

    private ListParamsRequest getRiskListRequest(ListParamsRequest request, String type, RiskRoleEnum role) {
        //风险台账列表：根据当前用户数据权限匹配“主责单位”字段，如果主责单位为空，只有数据权限为“全部”时能查询
        if (RiskConstant.LIST_TYPE_STANDING_BOOK.equals(type)) {
            request.getFilterParams()
                    .addAll(permissionService.buildParamsByPermission(PermissionParamsEnum.SELF_EQUAL_DEPT.getCode()));
            //当用户角色为主责人时，不能看 待分派状态和已退回状态的风险
            if (RiskRoleEnum.RESPONSIBLE_PERSON.equals(role)) {
                request.getFilterParams().add(RiskConstant.RESPONSIBLE_PERSON_CANT_SEE_STATUS);
            }
        }
        return request;
    }

    @Override
    public PageResult<TodoTaskVO> getRiskTodo(PageParams pageParams) {
        try {
            final RiskRoleEnum role = RiskRoleEnum.of(AuthHelper.getNotNullUser());
            if (Objects.isNull(role)) {
                throw new TRSException(ExceptionMessageConstant.NO_SUCH_ROLE);
            }
            final String type = role.getDefaultTodoListType();
            ListParamsRequest request = new ListParamsRequest(pageParams);
            request.setFilterParams(List.of(new KeyValueTypeVO("todo", role.getEnName(), "string")));
            PageResult<RiskListVO> page = getList(request, type);
            List<TodoTaskVO> list = page.getItems().stream()
                    .map(item -> item.of(type)).collect(Collectors.toList());
            return PageResult.of(list, pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
        } catch (RuntimeException e) {
            log.error("查询列表出错！", e);
        }
        return PageResult.empty(pageParams);
    }

    @Override
    public RiskUnreadVO getUnreadCount() {
        RiskUnreadVO vo = new RiskUnreadVO();
        vo.setStandingBook(unreadCount(RiskConstant.LIST_TYPE_STANDING_BOOK));
        vo.setHandle(unreadCount(RiskConstant.LIST_TYPE_HANDLE));
        vo.setCopy(unreadCount(RiskConstant.LIST_TYPE_COPY));
        return vo;
    }

    private Long unreadCount(String type) {
        try {
            CurrentUser user = AuthHelper.getNotNullUser();
            RiskRoleEnum role = RiskRoleEnum.of(user);
            ListParamsRequest request = getRiskListRequest(new ListParamsRequest(PageParams.getDefaultPageParams()),
                    type, role);
            return riskMapper.countRiskUnread(request, type, user);
        } catch (TRSException e) {
            log.info("风险列表查询出错！", e);
        }
        return 0L;
    }

    @Override
    public Object getListParams(String type) {
        String module = RiskConstant.LIST_PARAMS_PREFIX + type;
        if (RiskConstant.LIST_TYPE_STANDING_BOOK.equals(type)) {
            RiskRoleEnum role = RiskRoleEnum.of(AuthHelper.getNotNullUser());
            if (Objects.nonNull(role)) {
                module = module + "-" + role.getEnName();
            }
        }
        return dictService.getListRequestParams(module);
    }

    @Override
    public RiskTagCount getRiskTabConfig(Long riskId) {
        return riskMapper.getRiskTabConfig(riskId);
    }

    @Override
    public ScoreDisplayVO getScore(Long riskId) {
        RiskTraceableDataRelation relation = riskTraceableDataRelationMapper.getCurrentByRiskId(riskId);
        if (Objects.isNull(relation)) {
            return null;
        }
        return traceableDataService.getScoreDetailByRelation(relation);
    }

    @Override
    public PageResult<RiskCardVO> getRelatedRisk(Long riskId, ListParamsRequest request) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        PageParams pageParams = request.getPageParams();
        RiskRoleEnum role = RiskRoleEnum.of(currentUser);
        String defaultTodoListType = Objects.nonNull(role) ? role.getDefaultTodoListType() : LIST_TYPE_COPY;
        Page<RiskCardVO> page = riskMapper.getRelatedRisk(riskId, request, currentUser.getDeptId(),
                defaultTodoListType, pageParams.toPage());
        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(),
                pageParams.getPageSize());
    }

    @Override
    public String getRiskTitle(Long riskId) {
        Risk risk = riskMapper.selectById(riskId);
        if (Objects.isNull(risk)) {
            return "- -";
        }
        return risk.getTitle();
    }

    @Override
    public TraceableDataBaseVO getCurrent(Long riskId) {
        Risk risk = riskMapper.selectById(riskId);
        if (risk.getSource() == TraceableDataTypeEnum.MANUAL) {
            return risk.toTraceableData();
        }
        risk.setIsRead(true);
        riskMapper.updateById(risk);
        checkUserRelation(risk);
        RiskTraceableDataRelation relation = riskTraceableDataRelationMapper.getCurrentByRiskId(riskId);
        if (relation == null) {
            return null;
        }

        //是否从警综平台同步下相关人员-异步同步
        RiskUtil.asyncRiskPersonRelation(risk, relation.getCode());

        return traceableDataService.getByRelation(relation);
    }

    /**
     * 新增或更新一条已读
     *
     * @param risk 风险
     */
    @Override
    public void checkUserRelation(Risk risk) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        RiskUserRelation riskUserRelation = riskUserRelationMapper.selectByRiskIdAndDeptId(risk.getId(),
                currentUser.getDeptId(), currentUser.getId());
        if (Objects.nonNull(riskUserRelation)) {
            riskUserRelation.setIsRead(true);
            riskUserRelationMapper.updateById(riskUserRelation);
        } else {
            riskUserRelation = new RiskUserRelation();
            riskUserRelation.setUserId(currentUser.getId());
            riskUserRelation.setDeptId(currentUser.getDeptId());
            riskUserRelation.setIsRead(true);
            riskUserRelation.setRiskId(risk.getId());
            riskUserRelationMapper.insert(riskUserRelation);
        }

    }

    @Override
    public DeptVO getRiskFeedbackDeptVo(Long riskId) {
        RiskTraceableDataRelation relation = riskTraceableDataRelationMapper.getCurrentByRiskId(riskId);
        if (Objects.isNull(relation)) {
            return null;
        }
        List<FeedbackVO> feedbacks = traceableDataJqMapper.getFeedbacks(relation.getCode(), false);
        if (Objects.isNull(feedbacks) || feedbacks.isEmpty()) {
            return null;
        }
        DeptVO deptVO = riskTraceableDataRelationMapper.getFeedBackDetpVo(feedbacks.get(0).getDeptCode());
        return deptVO;
    }

    @Override
    public void importRelatedPerson() {
        List<RiskTraceableDataRelation> relations = riskTraceableDataRelationMapper.selectAllByJjdbh();
        if (Objects.isNull(relations) || relations.isEmpty()) {
            log.info("导入相关人员查询溯源关系表为空。");
            return;
        }
        List<String> jjdbhs = relations.stream().map(e -> e.getCode()).collect(Collectors.toList());
        log.info("接警单编号数量为{}", jjdbhs.size());
        //in的数量不能超过1000，因此做分开查询in
        List<List<String>> otherJjdbhs = new ArrayList<List<String>>();
        int size = jjdbhs.size();
        int splitNumber = 900;
        int count = (size + splitNumber - 1) / splitNumber;
        for (int i = 0; i < count; i++) {
            List<String> subList = jjdbhs.subList(i * splitNumber, ((i + 1) * splitNumber > size ? size : splitNumber * (i + 1)));
            otherJjdbhs.add(subList);
        }
        List<RiskPersonImport> personImports = riskRelatedPersonImportMapper.selectPersonFromTesUser(otherJjdbhs);
        if (Objects.isNull(personImports) || personImports.isEmpty()) {
            log.info("导入相关人员查询原始库为空。");
            return;
        }
        List<RiskPersonRelation> personRelations = new ArrayList<>();
        personImports.forEach(personImport -> {
            handleRelation(relations, personImport, personRelations);
        });
        riskPersonRelationService.insertBatch(personRelations);
    }

    @Override
    public void downloadTraceableDataTest() {
        traceableDataService.downloadTraceableDataTest();
    }

    @Override
    public void riskReportExport(Long id) {
        traceableDataService.riskReportExport(id);
    }

    @Override
    public void highRiskExport(HttpServletResponse response, ExportParams params) {
        final String fileName = URLEncoder.encode("高风险警情", StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        List<RiskExportVO> exportList = new ArrayList<>();
        if (Boolean.FALSE.equals(params.getIsAll())) {
            if (params.getIds().isEmpty()) {
                return;
            }
            exportList = riskMapper.selectByIds(params.getIds(), params.getListParamsRequest().getSortParams());
            //查询当事人信息
        } else {
            //查全部
            List<Long> riskIds = getList(params.getListParamsRequest(), "standing-book")
                    .getItems().stream().map(RiskListVO::getId).collect(Collectors.toList());
            exportList = riskMapper.selectByIds(riskIds, params.getListParamsRequest().getSortParams());
        }
        AtomicInteger index = new AtomicInteger(1);
        exportList.forEach(e -> e.setXh(index.getAndIncrement()));
        try {
            String exportFilePath = "/template/highRiskExport.xlsx";
            InputStream inputStream = new ClassPathResource(exportFilePath).getInputStream();
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();
            excelWriter.fill(exportList, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void handleRelation(List<RiskTraceableDataRelation> relations, RiskPersonImport personImport, List<RiskPersonRelation> personRelations) {
        List<Long> sfCode = new ArrayList<>();
        RiskPersonRelation riskPersonRelation = new RiskPersonRelation();
        if (Objects.nonNull(personImport.getSf())) {
            String[] sf = personImport.getSf().split(",");
            Arrays.asList(sf).forEach(e -> sfCode.add(RiskRelationPersonEnum.getCodeByName(e)));
        }
        List<RiskTraceableDataRelation> currentRelation = relations.stream().filter(e -> personImport.getJjdbh().equals(e.getCode())).collect(Collectors.toList());
        if (Objects.isNull(currentRelation) || currentRelation.isEmpty()) {
            log.info("jjdbh为{},不存在对应接警单编号。", personImport.getJjdbh());
            return;
        }
        riskPersonRelation.setRiskId(currentRelation.get(0).getRiskId());
        riskPersonRelation.setName(personImport.getJqdsrxm());
        if (Objects.nonNull(personImport.getLxdh())) {
            riskPersonRelation.setTel(List.of(personImport.getLxdh()));
        }
        riskPersonRelation.setRole(sfCode);
        riskPersonRelation.setIdNumber(personImport.getZjhm());
        personRelations.add(riskPersonRelation);
    }
}
