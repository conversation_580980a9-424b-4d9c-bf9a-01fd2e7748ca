package com.trs.police.risk.domain.vo;

import com.trs.police.common.core.vo.OperateVO;
import com.trs.police.risk.constant.enums.RiskOperateEnum;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 取消处置
 *
 * <AUTHOR>
 * @date 2023/3/21 10:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CancelVO extends OperateVO implements Serializable {

    private static final long serialVersionUID = 1716825513584286753L;

    private RiskOperateEnum reasonType;

    private String reason;
}
