package com.trs.police.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.risk.domain.entity.RiskCopy;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:08
 */
@Mapper
public interface RiskCopyMapper extends BaseMapper<RiskCopy> {

    /**
     * 删除风险相关回复
     *
     * @param riskId 风险id
     * @return 删除数量
     */
    @Delete("delete from t_risk_copy where risk_id = #{riskId}")
    Integer deleteByRiskId(@Param("riskId") Long riskId);

    /**
     * 获取抄送
     *
     * @param riskId 风险id
     * @param deptId 部门id
     * @return 抄送
     */
    @Select("select * from t_risk_copy where risk_id = #{riskId} and dept_id = #{deptId}")
    RiskCopy getByRiskAndDept(@Param("riskId") Long riskId, @Param("deptId") Long deptId);


    /**
     * 根据风险id查询抄送部门简称
     *
     * @param riskId 风险id
     * @return 结果
     */
    @Select("select (select d.short_name from t_dept d where d.id = c.dept_id) from t_risk_copy c where c.risk_id = #{riskId}")
    List<String> selectDeptNameByRiskId(@Param("riskId") Long riskId);

    /**
     * 删除抄送相关信息
     *
     * @param id 抄送id
     */
    void deleteRelations(@Param("id")Long id);

    /**
     * 获取抄送单位
     *
     * @param riskId 风险ID
     * @return 抄送单位
     */
    @Select("select * from t_risk_copy where risk_id = #{riskId}")
    List<RiskCopy> getByRiskId(@Param("riskId")Long riskId);
}
