# 风险
## 17.4
>XMKFB-8383 后-【广安】- 指挥大屏-高风险预警慢问题排查

## 16.3
> XMKFB-7775【广安】风险，搜索风险警情数据接口响应速度慢需做优化

## RC20250401
feat: 添加SchedulerConfig

## 14.4
> XMKFB-6028 高风险-警情配置图形化
> XMKFB-6977【高新】警情分析专题，民转刑风险人员1、2月没有或缺少数据
```在 nacos risk.yaml 下添加配置 comparisonServiceHostPort 和 authContent 在具体环境下再获取
com:
  trs:
    risk:
      config:
        comparisonServiceHostPort: http://************:10011
        addTagRulePath: /comparison-api/controlCenter/addTagRule
        addOrUpdateRelationDataRulePath: /comparison-api/controlCenter/addRelationDataRule
        getRelationDataRuleDetailPath: /comparison-api/riskConfig/getRelationDataRuleTagDetail
        authContent: "Basic eXNfdGVzdDp5c190ZXN0MTIz"
```
> XMKFB-6983 后-【云哨】- 高风险警情使用AI大模型打出来的标签
```在nacos risk.yaml 下添加配置 控制白天和晚上调用大模型的速率
risk:
  process:
    ai: 
      daytimeCallRate: 0.2
      eveningTimeCallRate: 0.95

```

## 14.3
- flyway1069脚本修改后, flyway无法正常启动，需要备份原有数据，删除flyway历史表的1069的记录, 再启动
> XMKFB-6751 云哨-高风险警情AI优化

```在 nacos commons-kafka.yaml 下添加配置 才能开启大模型处理接处警数据
kafka:
  risk:
    largeLanguageModelStartup: true

```

# v14.3
>XMKFB-6893 【泸州】融合检索，风险档案显示的报警人姓名和报警方式均显示为了数字
>XMKFB-6028 高风险-警情配置图形化
```
表结构有修改 需要部署global模块
```

# v14.2
>XMKFB-6028 高风险-警情配置图形化
>XMKFB-6751 云哨-高风险警情AI优化
```在 nacos risk 配置下添加如下配置 具体的租户id 账号等去具体环境项目经理处申请
risk:
  process:
    ai:
      tenantId: 37
      minScore: 3
      topic: high_risk_warn_ai
      accessToken: 430f3a53171a4696
      userName: ga-demo
      largeLanguageModelApiUrlHostPort: https://llm.trscd.com.cn
      chatApiPath: /llmops/chat/apps/441/conversations/qas/answer

```
>XMKFB-6820 后 - 调整风险档案风险类别数据

# v14.1
>XMKFB-6007 省厅GA-高风险警情支持导出
>XMKFB-6649[XMKFB-6481] 后 - 风险档案更新消息中增加business_time字段
>XMKFB-6562[XMKFB-6561] 后-【广安】- 民转刑风险人员无数据问题排查

## v13.4
>XMKFB-6555 后-【广安】- 线上紧急bug修复

## RC2025017
>XMKFB-6470 后-【广安】- 高风险问题处理

## v13.3 发版文件
> 广安、泸州 民转刑报错问题处理

## v13.2 发版文件
>XMKFB-6217[XMKFB-6017] 完善风险相关档案数据推送入库  

## v13.1 发版文件
>XMKFB-6029 风险警情，风险数据按照报警时间筛选无效
>XMKFB-6034 高新-高风险预警优化

## RC20241230
>XMKFB-6088 【自贡】矛盾纠纷预防与化解大屏，风险办结情况里统计的已移交党政统计数字有误
>XMKFB-6110 自贡-矛盾纠纷大屏相关调整
>XMKFB-6112 后-【自贡】- 已办结高风险警情编辑异常

## v12.4 发版文件
>XMKFB-5931[XMKFB-5927] 后-【自贡】- 高风险警情相关接口提供
>XMKFB-5934[XMKFB-5927] 后-【自贡】- 特殊行业人员接口提供
>XMKFB-5932[XMKFB-5927] 后-【自贡】- 中部大屏接口提供
>XMKFB-5919 风险警情，风险反馈内容换行后提交报错

## 配置修改
```properties
# 自贡环境增加
risk.riskTraceableDataRelationConsumer=zg
```

## RC20241223
>XMKFB-5871 后-【广安】- 高风险bug

## v12.3 发版文件
>XMKFB-5669 合-【自贡】- 风险警情优化

## v12.2 发版文件
> XMKFB-5584 合- 【广安】- 高风险详情增加风险报告导出

## v11.4 发版文件
> XMKFB-5349 后-【省厅情指】- 高风险列表和细览页慢优化

## v11.3 发版文件
> XMKFB-5259[XMKFB-5239] 后-【自贡】- 高风险警情-溯源数据需要返回人员id
> XMKFB-5200 后-【自贡】-高风险警情出现了两个标题为无风险的风险
> XMKFB-5205 后-【广安】- 高风险警情的已读未读状态改为以部门为维度
> XMKFB-5290 【高新】民转刑人员，高风险重点关注人员处理完成后重复显示在了已处理和未处理列表中
> XMKFB-5293 【高新】民转刑风险人员的备注信息只能最多加载十条数据

## RC20241119
> 高风险警情数据接入优化

## v11.2 发版文件
> 高风险警情-溯源数据逻辑优化，只根据风险ID匹配  
> XMKFB-4821 【智慧云脑】民转刑-风险人员优化
> XMKFB-5029 【自贡】线索里新添加的超级检索人员未自动推送进入风险人员中
> 风险超期提醒优化  

## v11.1 发版文件
> XMKFB-4963 后-【自贡】-民转刑警情统计无数据

## RC20241105
> XMKFB-4911 合-【自贡】-高风险警情优化

## 10.5 发版文件
> XMKFB-4519 【高新】风险详情页中溯源数据按照类别筛选无效


## 10.3 发版文件
> XMKFB-4464 后-【泸州】-高风险警情签收时限调整

## 10.2 发版文件
> XMKFB-4293[XMKFB-4027] 接口支持 - 接口需要增加搜索相关参数
> XMKFB-4337 【自贡】风险人员，人员推荐和关注人员Tab中未统计显示未读数字
> XMKFB-4360 风险人员列表中判定显示的最新预警时间有误
> XMKFB-4356 【自贡】风险人员的风险等级提升后未显示系统提示消息
> XMKFB-4367 【自贡】风险人员，线索里新添加的人员数据未推送进入风险人员


## v9.4 发版文件
> XMKFB-4090 后-【自贡】-风险人员预警提醒相关开发
> XMKFB-4092 后-【自贡】-完成列表/详情/关注/风险积分趋势开发
> XMKFB-4089[XMKFB-3880] 后-【自贡】-风险人员数据接入
> XMKFB-4091 后-【自贡】-推送/回退/研判功能开发

## 新增配置

```yaml
# 风险人员接收消息
spring:
  cloud:
    stream:
      bindings:
        riskPersonInput:
          destination: risk-person
          group: risk-person-message-cousumer
          content-type: application/json
          binder: kafka
          producer:
            partition-count: 1
```

## v9.3 发版文件
> 市局的情指中心默认能看到所有的数据  

## v9.2 发版文件
> XMKFB-3929 高风险当未找到管控单位时默认为市局的情指中心
> XMKFB-3931 【高新】民转刑警情分析报告的内容应作优化处理
> XMKFB-3938 【高新】民转刑风险分析，当月统计显示的上期警情数量有误

## v9.1 发版文件
> XMKFB-3715 后-【广安】-风险台账检索逻辑优化
> XMKFB-3757 风险详情中需要展示责任单位的全称
> XMKFB-3759 后-【广安】-风险台账中出现了重复的风险
> XMKFB-3551 民转刑警情数据统计有误
> XMKFB-3657 【高新】警情分析专题，环比日期统计有误

## v8.5 发版文件
> XMKFB-3549 民转刑警情数据只显示了环比上升的派出所
> XMKFB-3548 【高新】民转型警情分类的排列顺序应固定展示
> XMKFB-3596 对生成的民转刑警情分析报告内容进行优化与调整
## RC20240827
> XMKFB-3442 后-【高新】-民转刑警情统计逻辑优化
> XMKFB-3427 报警人员信息为匿名时后端返回的姓名应为“未知”
## v8.4 发版文件
> XMKFB-3263 省厅日志接入
## v8.3 发版日志
> XMKFB-3141[XMKFB-3074] 后-【高新】-民转刑人员相关统计接口提供

## v8.2 发版日志
> XMKFB-3010 后-【广安】-风险管控时间异常

## v7.1 发版日志
> XMKFB-2476 后-【泸州】-风险列表检索慢优化  

## v6.1 发版日志
# 20240606
>XMKFB-1986、风险数据范围相关人员同步时机处理，改到入库完成后同步  
# 20240605
>XMKFB-1874、风险数据范围

# nacos配置更新
```
##添加风险数据过滤范围，通过处警单位编码来过滤风险数据
risk:
  dataAccess:
    filter:
      cjdwbm:
```

## v5.4 发版日志
# 20240530
>XMKFB-1781、【广安】风险模块关联警情时间bug

# nacos配置更新
```
##需要在风险的nacos配置中新增时间是否增加8小时配置信息
time:
  offset:
    enabled: true
```

## v5.2 发版日志
# 20240517
>XMKFB-1595、风险列表慢问题处理  
## v5.1 发版日志
# 20240509
>XMKFB-1414、【泸州】高风险问题处理  

# nacos配置更新
```
##需要在风险的nacos配置中新增jzpt的数据库多数据源信息-修改一下配置的链接信息为真实信息
    jzpt:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://************:3306/ul_portal?allowPublicKeyRetrieval=true&useSSL=false&useUnicode=true&characterEncoding=UTF-8&useSSL=false&allowMultiQueries=true&autoReconnect=true&serverTimezone=Asia/Shanghai
          username: root
          password: 123456
          druid:
            max-wait: 10000
            validation-query: select 1
            validation-query-timeout: 200
            initial-size: 5
            max-active: 5
            min-idle: 5


```

## v1.4 发版日志
# 20240415
>XMKFB-1032、【广安】风险详情页的溯源数据列表中无法显示手动关联的警情数据

## v1.4 发版日志
>XMKFB-867、【广安】风险处置bug  

## v1.5 发版日志
>XMKFB-1030、风险详情页中溯源数据列表按照处警反馈搜索出的结果有误  