package com.trs.police.fight.test;

import com.trs.common.exception.ServiceException;
import com.trs.police.fight.FightApp;
import com.trs.police.fight.mapper.FightCompositeClueRelationMapper;
import com.trs.police.fight.task.analysis.impl.CombatUnitClueAnalysis;
import com.trs.police.fight.task.analysis.impl.MainInvestigationClueAnalysis;
import com.trs.police.fight.task.analysis.impl.OtherPoliceCategoryAnalysis;
import com.trs.police.fight.task.analysis.impl.PersonalClueAnalysis;
import com.trs.police.fight.task.context.ClueAnalysisContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = FightApp.class)
public class CluePoolAnalysisTest {

    @Autowired
    private CombatUnitClueAnalysis combatUnitClueAnalysis;

    @Autowired
    private OtherPoliceCategoryAnalysis otherPoliceCategoryAnalysis;

    @Autowired
    private MainInvestigationClueAnalysis mainInvestigationClueAnalysis;

    @Autowired
    private PersonalClueAnalysis personalClueAnalysis;

    @Autowired
    private FightCompositeClueRelationMapper fightCompositeClueRelationMapper;

    @Test
    public void combatUnitTest() throws ServiceException {
        combatUnitClueAnalysis.analyzeScene(new ClueAnalysisContext());
    }

    @Test
    public void otherPoliceKindTest() throws ServiceException {
        otherPoliceCategoryAnalysis.analyzeScene(new ClueAnalysisContext());
    }

    @Test
    public void mainInvestigationTest() throws ServiceException {
        mainInvestigationClueAnalysis.analyzeScene(new ClueAnalysisContext());
    }

    @Test
    public void personalClueTest() throws ServiceException {
        personalClueAnalysis.analyzeScene(new ClueAnalysisContext());
    }

    @Test
    public void test() {
        List<Long> longs = fightCompositeClueRelationMapper.selectAllByClueIdsAndDataSource("32,37", 2);
        System.out.println();
    }
}
