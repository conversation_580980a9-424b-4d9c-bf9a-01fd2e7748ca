package com.trs.police.fight.test.sync;

import com.trs.police.common.core.entity.FightCompositeMessage;
import com.trs.police.fight.FightApp;
import com.trs.police.fight.mapper.FightCompositeMessageMapper;
import com.trs.police.fight.sync.bean.Message;
import com.trs.police.fight.sync.service.impl.builder.CompositeChatMsgBuilder;
import com.trs.police.fight.sync.service.impl.consumer.fight.FightMessageConsumer;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = FightApp.class)
public class FightMsgSyncTest {

    @Autowired
    private FightMessageConsumer fightMessageConsumer;

    @Autowired
    private CompositeChatMsgBuilder compositeChatMsgBuilder;

    @Resource
    private FightCompositeMessageMapper fightCompositeMessageMapper;

    @Test
    public void test() {
        FightCompositeMessage fightCompositeMessage = fightCompositeMessageMapper.selectById(8037);
        Message message = compositeChatMsgBuilder.buildMsg(fightCompositeMessage);
        fightMessageConsumer.consume(message);
    }
}
