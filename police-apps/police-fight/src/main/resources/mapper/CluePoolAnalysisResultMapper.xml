<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.fight.mapper.CluePoolAnalysisResultMapper">

    <sql id="cluePoolAnalysisResult">
        id,
        unique_key as uniqueKey,
        service_time as serviceTime,
        user_id as userId,
        user_name as userName,
        dept_id as deptId,
        dept_name as deptName,
        dept_code as deptCode,
        dept_type as deptType,
        area_code as areaCode,
        area_code_desc as areaCodeDesc,
        police_kind as policeKind,
        police_kind_desc as policeKindDesc,
        scene_type as sceneType,
        SUM(fight_count) AS fightCount,
        SUM(fight_result_xs_case_count) AS fightResultXsCaseCount,
        SUM(fight_result_Xz_case_count) AS fightResultXzCaseCount,
        SUM(fight_result_xs_suspect_count) AS fightResultXsSuspectCount,
        SUM(fight_result_xz_suspect_count) AS fightResultXzSuspectCount,
        SUM(fight_result_ministerial_supervise_count) AS fightResultMinisterialSuperviseCount,
        SUM(fight_result_provincial_supervise_count) AS fightResultProvincialSuperviseCount,
        SUM(fight_result_ministerial_congratulation_count) AS fightResultMinisterialCongratulationCount,
        SUM(fight_result_provincial_congratulation_count) AS fightResultProvincialCongratulationCount,
        SUM(fight_result_ministerial_leader_instruction_count) AS fightResultMinisterialLeaderInstructionCount,
        SUM(fight_result_provincial_leader_instruction_count) AS fightResultProvincialLeaderInstructionCount,
        SUM(fight_result_city_leader_instruction_count) AS fightResultCityLeaderInstructionCount,
        SUM(fight_result_ministerial_campaign_count) AS fightResultMinisterialCampaignCount,
<!--        group_concat(clue_case_sub_categorization_info separator";") AS clueCaseSubCategorizationInfo,-->
        SUM(clue_count) AS clueCount,
        SUM(clue_sw_Count) AS clueSwCount,
        SUM(clue_case_count) AS clueCaseCount,
        SUM(clue_inspection_invalid_count) AS clueInspectionInvalidCount,
        SUM(clue_inspection_care_count) AS clueInspectionCareCount,
        SUM(clue_inspection_establish_count) AS clueInspectionEstablishCount,
        SUM(clue_inspection_complete_count) AS clueInspectionCompleteCount,
        SUM(clue_inspection_overdue_count) AS clueInspectionOverdueCount,
        create_time as createTime,
        update_time as updateTime
    </sql>

    <select id="fetchPersonalCluePoolAnalysis" resultType="com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO">
        select
        <include refid="cluePoolAnalysisResult"/>
        from tb_clue_analysis_result
        <where>
            scene_type = "cluePersonalAnalysis"
            <if test="dto.searchField != null and dto.searchValue != null and dto.searchField != '' and dto.searchValue != ''">
                AND ${dto.searchField} LIKE CONCAT('%', #{dto.searchValue }, '%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                AND service_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
        </where>
        GROUP BY ${groupField}
        ORDER BY clueCount DESC
    </select>

    <select id="getPersonalCategorizationInfo" resultType="com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO">
        select
            user_id AS userId,
            clue_case_sub_categorization_info AS clueCaseSubCategorizationInfo
        from tb_clue_analysis_result
        <where>
            scene_type = "cluePersonalAnalysis"
            <if test="dto.searchField != null and dto.searchValue != null and dto.searchField != '' and dto.searchValue != ''">
                AND ${dto.searchField} LIKE CONCAT('%', #{dto.searchValue }, '%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                AND service_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
            AND clue_case_sub_categorization_info IS NOT NULL
            AND JSON_LENGTH(clue_case_sub_categorization_info) > 0
        </where>
    </select>


    <select id="fetchCluePoolAnalysis" resultType="com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO">
        select
        <include refid="cluePoolAnalysisResult"/>
        from tb_clue_analysis_result
        <where>
            1 = 1
            <if test="dto.searchField != null and dto.searchValue != null and dto.searchField != '' and dto.searchValue != ''">
                AND ${dto.searchField} LIKE CONCAT('%', #{dto.searchValue}, '%')
            </if>
            <if test="groupField != null and groupField != '' ">
                AND scene_type = #{groupField}
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                AND service_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
        </where>
        GROUP BY dept_id
        ORDER BY clueCount DESC
    </select>

    <select id="getCategorizationInfo" resultType="com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO">
        select
            dept_id AS deptId,
            clue_case_sub_categorization_info AS clueCaseSubCategorizationInfo
        from tb_clue_analysis_result
        <where>
            1 = 1
            <if test="dto.searchField != null and dto.searchValue != null and dto.searchField != '' and dto.searchValue != ''">
                AND ${dto.searchField} LIKE CONCAT('%', #{dto.searchValue}, '%')
            </if>
            <if test="groupField != null and groupField != '' ">
                AND scene_type = #{groupField}
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                AND service_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
            AND clue_case_sub_categorization_info IS NOT NULL
            AND JSON_LENGTH(clue_case_sub_categorization_info) > 0
        </where>
    </select>

    <select id="fetchAreaCluePoolAnalysis"
            resultType="com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO">
        select
        <include refid="cluePoolAnalysisResult"/>
        from tb_clue_analysis_result
        <where>
            scene_type = "cluePersonalAnalysis"
            <if test="dto.searchField != null and dto.searchValue != null and dto.searchField != '' and dto.searchValue != ''">
                AND ${dto.searchField} LIKE CONCAT('%', #{dto.searchValue }, '%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                AND service_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
        </where>
        GROUP BY area_code
        ORDER BY clueCount DESC
    </select>
    <select id="getAreaCategorizationInfo" resultType="com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO">
        select
            area_code AS areaCode,
            clue_case_sub_categorization_info AS clueCaseSubCategorizationInfo
        from tb_clue_analysis_result
        <where>
            scene_type = "cluePersonalAnalysis"
            <if test="dto.searchField != null and dto.searchValue != null and dto.searchField != '' and dto.searchValue != ''">
                AND ${dto.searchField} LIKE CONCAT('%', #{dto.searchValue }, '%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                AND service_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
            AND clue_case_sub_categorization_info IS NOT NULL
            AND JSON_LENGTH(clue_case_sub_categorization_info) > 0
        </where>
    </select>

    <select id="fetchPoliceCluePoolAnalysis"
            resultType="com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO">
        select
        <include refid="cluePoolAnalysisResult"/>
        from tb_clue_analysis_result
        <where>
            scene_type = "cluePersonalAnalysis"
            and (dept_type = 4 or dept_type = 5)
            and dept_code != '' and dept_code is not null
            <if test="dto.searchField != null and dto.searchValue != null and dto.searchField != '' and dto.searchValue != ''">
                AND ${dto.searchField} LIKE CONCAT('%', #{dto.searchValue }, '%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                AND service_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
        </where>
        GROUP BY LEFT(dept_code, 8)
        ORDER BY clueCount DESC
    </select>

    <select id="getPoliceCategorizationInfo" resultType="com.trs.police.fight.domain.vo.CluePoolAnalysisResultVO">
        select
            dept_code AS deptCode,
            clue_case_sub_categorization_info AS clueCaseSubCategorizationInfo
        from tb_clue_analysis_result
        <where>
            scene_type = "cluePersonalAnalysis"
            and (dept_type = 4 or dept_type = 5)
            and dept_code != '' and dept_code is not null
            <if test="dto.searchField != null and dto.searchValue != null and dto.searchField != '' and dto.searchValue != ''">
                AND ${dto.searchField} LIKE CONCAT('%', #{dto.searchValue }, '%')
            </if>
            <if test="dto.startTime != null and dto.endTime != null">
                AND service_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
            AND clue_case_sub_categorization_info IS NOT NULL
            AND JSON_LENGTH(clue_case_sub_categorization_info) > 0
        </where>
    </select>
</mapper>