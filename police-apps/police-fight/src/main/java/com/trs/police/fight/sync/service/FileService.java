package com.trs.police.fight.sync.service;

import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.fight.sync.constant.DataSource;

import java.util.Optional;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 数据来源
     *
     * @return 数据来源
     */
    DataSource dataSource();

    /**
     * 本地系统的文件
     *
     * @param file 文件id
     * @return 文件信息
     */
    Optional<FileInfoVO> convert(FileInfoVO file);
}
