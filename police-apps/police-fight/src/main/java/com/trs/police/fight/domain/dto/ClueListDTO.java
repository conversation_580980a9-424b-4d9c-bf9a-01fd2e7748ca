package com.trs.police.fight.domain.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseListDTO;
import com.trs.common.utils.StringUtils;
import com.trs.police.fight.constant.CluePoolConstant;
import lombok.Data;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/20 9:44
 * @since 1.0
 */
@Data
public class ClueListDTO extends BaseListDTO {

    /**
     * 我的上报/线索池列表
     * own:我上报的
     * dept:本部门的
     */
    private String dataType;

    /**
     * 主侦/中心审批列表
     * specific:主侦
     * center：中心
     */
    private String approveType;

    /**
     * 审批单是否已经处理了
     * <ul>
     *     <li>0：未处理</li>
     *     <li>1：已处理</li>
     * </ul>
     */
    private String approveIsDeal;

    /**
     * 涉案 涉稳
     */
    private String clueType;

    /**
     * 类别，多个逗号分割
     */
    private String policeKind;

    /**
     * 主责警种名称，多个逗号分割
     */
    private String policeType;

    /**
     * 是否协同
     * 0：否
     * 1：是
     */
    private String shared;

    /**
     * 同意标识过滤
     * <ul>
     *     <li>0：待定</li>
     *     <li>1：同意</li>
     *     <li>-1：不同意</li>
     * </ul>
     */
    private String agreed;

    /**
     * 是否发起情指线索
     * 0：否
     * 1：是
     */
    private String xiansuoShared;

    /**
     * 初研状态，多个逗号分割
     * 10	关注
     * 11	无效
     * 20	立线
     * 21	成案
     */
    private String status;

    /**
     * 上报开始时间
     */
    private String startTime;

    /**
     * 上报结束时间
     */
    private String endTime;

    /**
     * 搜索类型
     * ALL：全部
     */
    private String searchType;

    /**
     * 搜索词
     */
    private String keyword;

    /**
     * 报送单位ID，多个逗号分割
     */
    private String deptIds;

    /**
     * 审批状态
     * <ul>
     *  <li>0：待审批</li>
     *  <li>1：审批通过</li>
     *  <li>-1：审批不通过</li>
     * </ul>
     */
    private String shenpiStatus;

    /**
     * 移交审批状态
     * <ul>
     *     <li>0：待审批</li>
     *     <li>1：审批中</li>
     *     <li>2：通过</li>
     *     <li>-1：驳回</li>
     * </ul>
     */
    private String yijiaoShenpiStatus;

    private Integer draftsFlag = 0;

    private List<String> policeKindList;

    private List<String> policeTypeList;

    private List<String> statusList;

    private List<String> deptIdList;

    private List<String> shenpiStatusList;

    private List<String> yijiaoShenpiStatusList;

    @Override
    protected boolean checkParams() throws ServiceException {
        if (StringUtils.isNotEmpty(getApproveType())) {
            if (!CluePoolConstant.CLUE_APPROVE_LIST_TYPE_SPECIFIC.equals(getApproveType())
                    && !CluePoolConstant.CLUE_APPROVE_LIST_TYPE_CENTER.equals(getApproveType())) {
                throw new ServiceException(String.format("暂不支持的审批类型[%s]", getApproveType()));
            }
        }
        if (StringUtils.isNotEmpty(getKeyword())) {
            PreConditionCheck.checkNotEmpty(getSearchType(), "搜索类型不能为空！");
        }
        //初始化多值参数
        if (StringUtils.isNotEmpty(getPoliceKind())) {
            policeKindList = StringUtils.getList(getPoliceKind(), true);
        }
        if (StringUtils.isNotEmpty(getPoliceKind())) {
            policeTypeList = StringUtils.getList(getPoliceType(), true);
        }
        if (StringUtils.isNotEmpty(getStatus())) {
            statusList = StringUtils.getList(getStatus(), true);
        }
        if (StringUtils.isNotEmpty(getDeptIds())) {
            deptIdList = StringUtils.getList(getDeptIds(), true);
        }
        if (StringUtils.isNotEmpty(getShenpiStatus())) {
            shenpiStatusList = StringUtils.getList(getShenpiStatus(), true);
        }
        if (StringUtils.isNotEmpty(getYijiaoShenpiStatus())){
            yijiaoShenpiStatusList = StringUtils.getList(getYijiaoShenpiStatus(), true);
        }
        return true;
    }
}
