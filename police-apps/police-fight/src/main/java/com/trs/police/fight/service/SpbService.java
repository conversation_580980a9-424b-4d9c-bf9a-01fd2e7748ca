package com.trs.police.fight.service;

import com.trs.police.fight.domain.vo.Collaboration.ReviewInfoVO;

import java.util.List;

/**
 * 审批表服务
 *
 * <AUTHOR>
 */
public interface SpbService {

    /**
     * 构造审批表
     *
     * @param vos vs
     * @param virtualNodeKey 虚拟节点的配置key
     * @return 审批表审批信息
     */
    List<ReviewInfoVO> generateSpb(List<ReviewInfoVO> vos, String virtualNodeKey);

    /**
     * 构造指令单信息
     *
     * @param vos zld
     *  @param virtualNodeKey 虚拟节点的配置key
     * @return zld
     */
    List<ReviewInfoVO> generateZld(List<ReviewInfoVO> vos, String virtualNodeKey);
}
