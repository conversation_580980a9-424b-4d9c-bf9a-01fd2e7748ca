package com.trs.police.fight.domain.dto;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.common.utils.StringUtils;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * SimilarityClueListDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-03-20 15:25:52
 */
@Data
public class SimilarityClueListDTO extends BaseDTO {

    /**
     * 线索ID，新建为0
     */
    private String id;

    /**
     * 涉及证件号码，多个逗号分隔，4个字段不能同时为空
     */
    private String relatedPerson;

    /**
     * 涉及手机号，多个逗号分隔，4个字段不能同时为空
     */
    private String relatedPhone;

    /**
     * 涉及案件，多个逗号分隔，4个字段不能同时为空
     */
    private String relatedCase;

    /**
     * 涉及车牌，多个逗号分隔，4个字段不能同时为空
     */
    private String relatedCar;

    /**
     * 相识度前N条数据
     */
    private String topN;

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getId(), "线索ID不能为空");
        checkNotEmpty(getTopN(), "相识度不能为空");
        if (StringUtils.isEmpty(getRelatedPerson())
                && StringUtils.isEmpty(getRelatedPhone())
                && StringUtils.isEmpty(getRelatedCar())
                && StringUtils.isEmpty(getRelatedCase())) {
            throw new ServiceException("related__不能同时为空");
        }
        return true;
    }
}
