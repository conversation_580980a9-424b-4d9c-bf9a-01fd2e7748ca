package com.trs.police.fight.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 类型
 *
 * <AUTHOR>
 * @since 2022/5/16 14:09
 **/
public enum CompositeFieldTypeEnum {
    /**
     * 枚举
     */
    BASE("0", "基本类型"),
    USER("1", "用户信息"),
    DEPT("2", "部门信息"),
    EVENT("3", "案事件信息"),
    SUSPECT("4", "嫌疑人信息"),
    FILE("5", "文件信息"),
    USERINFO("6", "用户-部门信息"),
    DATE("7", "时间信息"),
    INVITE("8", "邀请人信息"),
    ROLE("9", "合成角色信息"),
    COMPOSITE("10", "合成信息"),
    APPLY("11", "申请信息"),
    RESULT("12", "战果信息"),
    OBJECT("13", "对象");
    @Getter
    @EnumValue
    private final String code;

    @Getter
    private final String name;


    CompositeFieldTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }
}
