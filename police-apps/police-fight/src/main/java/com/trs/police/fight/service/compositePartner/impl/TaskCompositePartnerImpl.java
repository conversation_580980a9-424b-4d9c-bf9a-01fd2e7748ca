package com.trs.police.fight.service.compositePartner.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.ExceptionMessageConstant;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.enums.CompositePartnerWay;
import com.trs.police.fight.domain.dto.CompositePartnerDTO;
import com.trs.police.fight.domain.entity.BigScreenDutyUserEntity;
import com.trs.police.fight.domain.vo.CompositeUserInfo;
import com.trs.police.fight.mapper.BigScreenDutyUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/12/23
 * @description: 获取任务中的作战参与人员
 */
@Component
@ConditionalOnProperty(prefix = "ys.fight.composite.partner.task", name = "enabled", havingValue = "true")
public class TaskCompositePartnerImpl extends AbsCompositePartnerImpl {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private BigScreenDutyUserMapper bigScreenDutyUserMapper;

    /**
     * 获取任务中的作战参与人员
     *
     * @param dto dto
     * @return {@link List}<{@link CompositeUserInfo}>
     */
    @Override
    public List<CompositeUserInfo> getCompositePartner(CompositePartnerDTO dto) {
        if (StringUtils.isEmpty(dto.getLeadDeptIds())) {
            return new ArrayList<>();
        }
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        // 拉取牵头单位的指挥长主副班
        List<Long> deptIds = Arrays.stream(dto.getLeadDeptIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<String> districtCodeList = permissionService.getDeptByIds(deptIds).stream()
                .map(DeptDto::getDistrictCode)
                .distinct()
                .collect(Collectors.toList());
        QueryWrapper<BigScreenDutyUserEntity> queryWrapper = new QueryWrapper<BigScreenDutyUserEntity>()
                .eq("duty_time", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD) + " 00:00:00")
                .eq("nature", "指挥长")
                .in("district_code", districtCodeList);
        List<BigScreenDutyUserEntity> dutyUsers = bigScreenDutyUserMapper.selectList(queryWrapper);
        return buildCompositePartner(dutyUsers, str -> true, "task");
    }

    /**
     * way
     *
     * @return {@link CompositePartnerWay}
     */
    @Override
    public CompositePartnerWay way() {
        return CompositePartnerWay.TASK;
    }
}
