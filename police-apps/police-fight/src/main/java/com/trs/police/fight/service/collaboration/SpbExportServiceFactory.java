package com.trs.police.fight.service.collaboration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.EnvEnum;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.enums.CollaborationType;
import com.trs.police.fight.domain.vo.Collaboration.ReviewInfoVO;
import com.trs.police.fight.domain.vo.CollaborationVO;
import com.trs.police.fight.helper.CollaborationTypeHelper;
import com.trs.police.fight.service.CollaborationService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.utils.TimeUtils.YYYYMMDD;

/**
 * 审批表导出服务工厂
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpbExportServiceFactory {

    @Autowired
    private List<SpbExportService> services;

    @Autowired
    private DictService dictService;

    @Autowired
    private CollaborationTypeHelper collaborationTypeHelper;

    @Autowired
    private CollaborationService collaborationService;

    @Autowired
    private OssService ossService;

    @Autowired
    private PermissionService permissionService;


    /**
     * 导出协作得审批表
     *
     * @param collaborationId 协作id
     */
    public void exportSpb(Long collaborationId) {
        CollaborationVO vo = collaborationService.approveFormInfo(collaborationId);
        ConfigureBuilder builder = Configure.builder();
        bindConfig(vo, builder);
        Map<String, Object> map = buildMap(vo);
        try (
                InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("template/" + getTemplatePath(vo, null));
                XWPFTemplate exportTemplate = XWPFTemplate.compile(inputStream, builder.build()).render(map);
        ) {
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            response.reset();
            response.setContentType("application/x-msdownload");
            response.setCharacterEncoding("utf-8");
            String name = String.format("%s-审批表-%s.docx", vo.getTitle(), TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS2));
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(name, StandardCharsets.UTF_8));
            exportTemplate.writeAndClose(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出失败", e);
        }
    }

    /**
     * 下载协作的指令单
     *
     * @param collaborationId 协作ID
     */
    public void downloadZld(Long collaborationId) {
        CollaborationVO vo = collaborationService.approveFormInfo(collaborationId);
        ConfigureBuilder builder = Configure.builder();
        bindConfig(vo, builder);
        Map<String, Object> map = buildMap(vo);
        try (
                InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("template/" + getTemplatePath(vo, "zld"));
                XWPFTemplate exportTemplate = XWPFTemplate.compile(inputStream, builder.build()).render(map);
        ) {
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            response.reset();
            response.setContentType("application/x-msdownload");
            response.setCharacterEncoding("utf-8");
            String name = String.format("%s-指令单-%s.docx", vo.getTitle(), TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS2));
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(name, StandardCharsets.UTF_8));
            exportTemplate.writeAndClose(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出失败", e);
        }
    }

    /**
     * 导出协作的指令单
     *
     * @param collaborationId 协作id
     * @return 指令单文件的字节数组
     */
    public byte[] exportZld(Long collaborationId) {
        CollaborationVO vo = collaborationService.approveFormInfo(collaborationId);
        ConfigureBuilder builder = Configure.builder();
        bindConfig(vo, builder);
        Map<String, Object> map = buildMap(vo);
        try (
                InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("template/" + getTemplatePath(vo, "zld"));
                XWPFTemplate exportTemplate = XWPFTemplate.compile(inputStream, builder.build()).render(map);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ) {
            // 将文档内容写入字节数组输出流
            exportTemplate.writeAndClose(outputStream);
            // 上传到oss
            String name = String.format("%s-指令单.docx", vo.getTitle());
            try (InputStream input = new ByteArrayInputStream(outputStream.toByteArray())) {
                MultipartFile multipartFile = new MockMultipartFile(name, name, null, input);
                FileInfoVO fileInfo = ossService.upload(multipartFile, true);
                // 下载pdf
                if (StringUtils.isNotEmpty(fileInfo.getPdfUrl())) {
                    String pdfUrl = fileInfo.getPdfUrl();
                    byte[] body = ossService.getFile(pdfUrl.replaceAll("/oss/file/", "")).getBody();
                    return body;
                }
            }
        } catch (Exception e) {
            log.error("导出指令单失败", e);
            throw new TRSException(e.getMessage());
        }
        throw new TRSException("生成pdf失败");
    }

    /**
     * 获取导出word的模板路径
     *
     * @param vo vo
     * @param exportType 类型 指令单：zld 审批表：spb(默认)
     * @return 模板路径
     */
    public String getTemplatePath(CollaborationVO vo, String exportType) {
        // 获取环境信息
        String envCode = dictService.getEnvCode();
        Optional<EnvEnum> envEnum = EnvEnum.ofCode(envCode);
        // 获取协作类别
        Optional<CollaborationType> type = collaborationTypeHelper.getTypeEnumById(vo.getCollaborationType());
        // 找到对应得服务构造map
        for (SpbExportService service : services) {
            if (service.support(envEnum.orElse(null), type.orElse(null))) {
                return service.getTemplatePath(envEnum.orElse(null), type.orElse(null), exportType);
            }
        }
        return null;
    }

    /**
     * 绑定导出配置
     *
     * @param vo vo
     * @param builder builder
     */
    public void bindConfig(CollaborationVO vo, ConfigureBuilder builder) {
        getSupportService(vo).forEach(service -> service.addBinder(builder));
    }


    /**
     * 构造导出word的map
     *
     * @param vo vo
     * @return word参数
     */
    public Map<String, Object> buildMap(CollaborationVO vo) {
        // 公共参数
        Map<String, Object> map = builCommondMap(vo);
        // 审批信息
        addApprovalInfo(vo, map);
        // 找到对应得服务构造map
        Optional<CollaborationType> type = collaborationTypeHelper.getTypeEnumById(vo.getCollaborationType());
        for (SpbExportService service : getSupportService(vo)) {
            Map<String, Object> other = service.buildMap(vo, type.orElse(null), map);
            map.putAll(other);
        }
        return map;
    }

    private List<SpbExportService> getSupportService(CollaborationVO vo) {
        // 获取环境信息
        String envCode = dictService.getEnvCode();
        Optional<EnvEnum> envEnum = EnvEnum.ofCode(envCode);
        // 获取协作类别
        Optional<CollaborationType> type = collaborationTypeHelper.getTypeEnumById(vo.getCollaborationType());
        List<SpbExportService> supportService = services.stream()
                .filter(service -> service.support(envEnum.orElse(null), type.orElse(null)))
                .collect(Collectors.toList());
        return supportService;
    }

    private Map<String, Object> builCommondMap(CollaborationVO vo) {
        if (CollectionUtils.isEmpty(vo.getEvents())) {
            vo.setAsjbh("无");
        } else {
            vo.setAsjbh(vo.getEvents().get(0).getAsjbh());
            vo.setAsjmc(vo.getEvents().get(0).getCaseName());
            vo.setAsjlb(vo.getEvents().get(0).getCaseFineType());
        }
        // 如果没有案事件名称，使用协作标题
        if (StringUtils.isEmpty(vo.getAsjmc())) {
            vo.setAsjmc(vo.getTitle());
        }
        vo.setCreateTime(TimeUtils.stringToString(vo.getCreateTime(), TimeUtils.YYYYMMDD2));
        vo.setStartApprovalTime(TimeUtils.stringToString(vo.getCreateTime(), TimeUtils.YYYYMMDD2));
        vo.setStartParentApprovalTime(TimeUtils.stringToString(vo.getCreateTime(), TimeUtils.YYYYMMDD2));
        List<String> cxmbList = new ArrayList<>();
        if (StringUtils.isNotEmpty(vo.getSearchContent())) {
            JSONArray jsonArray = JSON.parseArray(vo.getSearchContent());
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String goal = jsonObject.getString("goal");
                cxmbList.add(String.format("%s.%s", i+1, goal));
            }
        }
        Integer size = BeanFactoryHolder.getEnv().getProperty("jzxz.approval.info.cxmb.size", Integer.class, 3);
        List<String> cxmbOmitList = cxmbList.stream().limit(size).collect(Collectors.toList());
        if (cxmbList.size() > size) {
            cxmbOmitList.add("......");
        }
        vo.setCxmbOmit(String.join("\n", cxmbOmitList));
        vo.setCxmbAll(String.join("\n", cxmbList));
        String jsonStr = JSON.toJSONString(vo);
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        Map<String, Object> map = new HashMap<>();
        jsonObject.keySet().forEach(key -> {
            map.put(key, jsonObject.getString(key));
        });
        return map;
    }

    private void addApprovalInfo(CollaborationVO vo, Map<String, Object> map) {
        List<ReviewInfoVO> review = collaborationService.spbReviewInfo(vo.getId(), null, "spb");
        for (ReviewInfoVO r : review) {
            String prefix = null;
            if ("所领导".equals(r.getType()) || "1".equals(r.getType())) {
                prefix = "approvalOne";
            }
            if ("区县情指".equals(r.getType()) || "2".equals(r.getType())) {
                prefix = "approvalTwo";
            }
            if ("分局领导".equals(r.getType()) || "3".equals(r.getType())) {
                prefix = "approvalThree";
            }
            if ("指挥长审核".equals(r.getType()) || "4".equals(r.getType())) {
                prefix = "approvalFour";
            }
            if (StringUtils.isNotEmpty(prefix)) {
                map.put(prefix + "Content", r.getComment());
                map.put(prefix + "UserName", r.getRelatedUserRealName());
                if (StringUtils.isNotEmpty(r.getReviewTime())) {
                    String s = TimeUtils.stringToString(r.getReviewTime(), YYYYMMDD);
                    String[] split = s.split("-");
                    map.put(prefix + "Year", split[0]);
                    map.put(prefix + "Month", split[1]);
                    map.put(prefix + "Day", split[2]);
                }
                if (Objects.nonNull(r.getRelatedUserId()) && Objects.nonNull(r.getRelatedUserDeptId())) {
                    CurrentUser currentUser = permissionService.findCurrentUser(r.getRelatedUserId(), r.getRelatedUserDeptId());
                    log.info("签名图片：{}", currentUser.getDept().getSignet());
                    if (!StringUtils.isEmpty(currentUser.getDept().getSignet())) {
                        List<FileInfoVO> file = JSONArray.parseArray(currentUser.getDept().getSignet(), FileInfoVO.class);
                        if (!file.isEmpty()) {
                            FileInfoVO fileInfoVO = file.get(0);
                            ResponseEntity<byte[]> download = ossService.download(fileInfoVO.getId().toString());
                            // 转换成InputStream
                            try (ByteArrayInputStream b = new ByteArrayInputStream(download.getBody())) {
                                PictureRenderData pic = Pictures.ofStream(b, PictureType.PNG).size(100, 120).create();
                                map.put(prefix + "UserPic", pic);
                                map.put(prefix + "DeptPic", pic);
                            } catch (Exception e) {
                                log.error("下载签章失败:[{}]！", e.getMessage(), e);
                            }
                        }
                    }
                }
            }
        }
    }
}