package com.trs.police.fight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.EnvEnum;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.fight.domain.vo.Collaboration.ReviewInfoVO;
import com.trs.police.fight.service.SpbService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 实现
 */
@Service
public class SpbServiceImpl implements SpbService {

    @Autowired
    private DictService dictService;

    @Override
    public List<ReviewInfoVO> generateSpb(List<ReviewInfoVO> vos, String virtualNodeKey) {
        String envCode = dictService.getEnvCode();
        // 自贡环境特殊处理
        if (EnvEnum.ZG.equals(EnvEnum.ofCode(envCode).orElse(null))) {
            vos.forEach(v ->  {
                if ("3".equals(v.getType())) {
                    v.setType(null);
                }
            });
        }
        // 配置的类型，类型配置的json格式 key: 指令单 审批表 value : 实际的业务类型 1 2 3 4
        vos.forEach(v -> {
            if (StringUtils.isNotEmpty(v.getType()) && v.getType().contains("spb")) {
                String spbType = JSON.parseObject(v.getType()).getString("spb");
                v.setType(spbType);
            }
        });
        // 添加虚拟的审批节点
        String virtualNode = StringUtils.isEmpty(virtualNodeKey)
                ? null
                : BeanFactoryHolder.getEnv().getProperty(virtualNodeKey);
        if (StringUtils.isNotEmpty(virtualNode)) {
            List<ReviewInfoVO> virtual = JSONArray.parseArray(virtualNode, ReviewInfoVO.class);
            // 添加时间
            String time = CollectionUtils.isEmpty(vos) ? null : vos.get(0).getReviewTime();
            virtual.forEach(vo -> {
                vo.setApplyTime(time);
                vo.setReviewTime(time);
            });
            vos.addAll(virtual);
        }
        return vos;
    }

    @Override
    public List<ReviewInfoVO> generateZld(List<ReviewInfoVO> vos, String virtualNodeKey) {
        // 配置的类型，类型配置的json格式 key: 指令单 审批表 value : 实际的业务类型 1 2 3 4
        vos.forEach(v -> {
            if (StringUtils.isNotEmpty(v.getType()) && v.getType().contains("zld")) {
                String spbType = JSON.parseObject(v.getType()).getString("zld");
                v.setType(spbType);
            }
        });
        return vos;
    }
}
