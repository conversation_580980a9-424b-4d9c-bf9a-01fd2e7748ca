package com.trs.police.fight.service;

import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.fight.domain.dto.LabelListDTO;
import com.trs.police.fight.domain.entity.PlanEntity;
import com.trs.police.fight.domain.vo.PlaPoliceVO;
import com.trs.police.fight.domain.vo.PlanVO;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 * 突发处置服务
 *
 * <AUTHOR>
 * @since 2024/6/18 10:21
 **/
public interface PlanService {



    /**
     * 预案列表检索
     *
     * @param dto dto
     * @return PlanVO
     */
    RestfulResultsV2<PlanVO> labelList(LabelListDTO dto)throws ServiceException;

    /**
     * 新增或编辑预案
     *
     * @param dto 参数
     * @return 结果
     */
    Report<String> saveOrUpdateLabel(LabelListDTO dto)throws ServiceException;

    /**
     * 启用或禁用标签
     *
     * @param ids  ids
     * @param status status
     * @return 结果
     */
    Report<String> enableLabel(String ids, Integer status)throws ServiceException;

    /**
     * 删除预案
     *
     * @param ids ids
     * @return 结果
     */
    Report<String> removePlan(String ids)throws ServiceException;

    /**
     * 查看预案详情
     *
     * @param planId 预案id
     * @param id 处突id
     * @return 结果
     */
    PlanVO checkPlanContent(Long planId,Long id);

    /**
     * 根据名称获取预案
     *
     * @param dto 参数
     * @return 结果
     */
    RestfulResultsV2<PlanVO> getPlan(LabelListDTO dto);

    /**
     * 获取数量
     *
     * @param code 参数
     * @return 结果
     */
    Long getPlanNum(Long code);

    /**
     * 根据id 查询启用的预案
     *
     * @param ids id
     * @return 启用的预案
     */
    List<PlanEntity> findEnablePlanById(List<Long> ids);

    /**
     * 根据预案查询调度的人员
     *
     * @param planeId 预案id
     * @param geometry 坐标
     * @return 相关人员
     */
    List<PlaPoliceVO> planUser(Long planeId, String geometry);

    /**
     * 预案发起作战
     *
     * @param dto dto
     * @return {@link FightComposite}
     */
    FightComposite planCreateComposite(LabelListDTO dto);

    /**
     * 编辑任务
     *
     * @param dto dto
     */
     void editTask(LabelListDTO dto);

    /**
     * 编辑任务牵头单位
     *
     * @param dto dto
     */
    void editTaskLeadDept(LabelListDTO dto);

    /**
     * 删除任务
     *
     * @param id id
     */
    void delTask(Long id);
}
