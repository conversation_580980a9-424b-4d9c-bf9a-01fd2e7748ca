package com.trs.police.fight.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName CluePoolProperties
 * @Description 线索池相关配置项
 * <AUTHOR>
 * @Date 2024/3/21 11:51
 **/
@Data
@Component
@ConfigurationProperties(prefix = "fight.clue-pool")
public class CluePoolProperties {

    /**
     * 警种编号与名称简写的json映射关系
     * {"2":"ZB","12":"FK","5":"XZ","10":"JD","4":"ZA","6":"CRJ","9":"SYHZ","16":"SJ","14":"JJ","7":"WA"}
     */
    private String policeKindCodeNameMapping = "{}";

    /**
     * 涉稳默认警种类别
     */
    private String defaultSheWenPoliceKind;

}
