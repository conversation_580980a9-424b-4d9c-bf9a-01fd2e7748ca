package com.trs.police.fight.domain.params;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 案事件查询参数
 *
 * <AUTHOR>
 * @since 2022/4/2 14:09
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CaseEventParams {

    /**
     * 分页参数
     */
    private PageParams pageParams;
    /**
     * 查询参数
     */
    private SearchParams searchParams;
    /**
     * 其他参数
     */
    private List<KeyValueTypeVO> filterParams;
}
