package com.trs.police.fight.service;

import com.trs.police.fight.domain.dto.CollaborationDto;
import com.trs.police.common.core.entity.Collaboration;

/**
 * @author: dingkeyu
 * @date: 2024/03/07
 * @description: 协作关联关系处理
 */
public interface CollaborationRelationService {

    /**
     * 新建关联关系
     *
     * @param dto 协作dto
     * @param collaboration 协作DO
     */
    void createRelation(CollaborationDto dto, Collaboration collaboration);

    /**
     * 删除关联关系
     *
     * @param collaborationId 协作id
     */
    void deleteRelation(Long collaborationId);
}
