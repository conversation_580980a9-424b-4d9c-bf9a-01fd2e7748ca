package com.trs.police.fight.service.compositePartner;

import com.trs.police.fight.constant.enums.CompositePartnerWay;
import com.trs.police.fight.domain.dto.CompositePartnerDTO;
import com.trs.police.fight.domain.vo.CompositeUserInfo;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/12/23
 * @description:
 */
public interface ICompositePartnerService {

    /**
     * 获取作战参与人员
     *
     * @param dto dto
     * @return {@link List}<{@link CompositeUserInfo}>
     */
    List<CompositeUserInfo> getCompositePartner(CompositePartnerDTO dto);

    /**
     * 获取作战参与人员方式
     *
     * @return {@link CompositePartnerWay}
     */
    CompositePartnerWay way();
}
