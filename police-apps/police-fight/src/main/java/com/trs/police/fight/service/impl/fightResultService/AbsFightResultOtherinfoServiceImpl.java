package com.trs.police.fight.service.impl.fightResultService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.fight.converter.FightResultConvert;
import com.trs.police.fight.domain.dto.FightResultDto;
import com.trs.police.fight.domain.entity.FightResultOtherInfoRelation;
import com.trs.police.fight.service.FightResultFillingService;
import com.trs.police.fight.service.FightResultOtherinfoRelationService;
import com.trs.police.fight.task.analysis.vo.FightResultCombination;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/03/21
 * @description: 战果其他信息填报
 */
@Service
public abstract class AbsFightResultOtherinfoServiceImpl implements FightResultFillingService {

    @Resource
    private FightResultOtherinfoRelationService fightResultOtherinfoRelationService;

    @Override
    public void fightResultFilling(FightResultDto fightResultDto) {
        List<FightResultDto.OtherInfo> list = otherInfoFunction().apply(fightResultDto);
        if (!CollectionUtils.isEmpty(list)) {
            List<FightResultOtherInfoRelation> relations = list.stream().map(e -> {
                FightResultOtherInfoRelation relation = new FightResultOtherInfoRelation();
                relation.setFightResultId(fightResultDto.getFightResultId());
                relation.setTitle(e.getTitle());
                relation.setLevel(e.getLevel());
                relation.setInfoType(operateType());
                relation.setAttachments(e.getAttachments());
                return relation;
            }).collect(Collectors.toList());
            fightResultOtherinfoRelationService.saveBatch(relations);
        }
    }

    @Override
    public void fightResultDelete(FightResultDto fightResultDto) {
        QueryWrapper<FightResultOtherInfoRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("fight_result_id", fightResultDto.getFightResultId());
        queryWrapper.eq("info_type", operateType());
        fightResultOtherinfoRelationService.remove(queryWrapper);
    }

    @Override
    public void queryFightResult(FightResultDto fightResultDto) {
        if (Objects.isNull(fightResultDto.getFightResultId())) {
            return;
        }
        List<FightResultOtherInfoRelation> list = fightResultOtherinfoRelationService.lambdaQuery()
                .eq(FightResultOtherInfoRelation::getFightResultId, fightResultDto.getFightResultId())
                .eq(FightResultOtherInfoRelation::getInfoType, operateType())
                .list();
        List<FightResultDto.OtherInfo> otherInfos = list.stream().map(FightResultConvert.CONVERTER::otherInfo2Dto).collect(Collectors.toList());
        otherInfoBiConsumer().accept(fightResultDto, otherInfos);
    }

    @Override
    public void queryFightResult(List<Long> fightResultIds, FightResultCombination fightResultCombination) {
        List<FightResultOtherInfoRelation> list = fightResultOtherinfoRelationService.lambdaQuery()
                .in(FightResultOtherInfoRelation::getFightResultId, fightResultIds)
                .eq(FightResultOtherInfoRelation::getInfoType, operateType())
                .list();
        combinationBiConsumer().accept(fightResultCombination, list);
    }

    /**
     * otherInfoFunction
     *
     * @return Function
     */
    public abstract Function<FightResultDto, List<FightResultDto.OtherInfo>> otherInfoFunction();

    /**
     * otherInfoConsumer
     *
     * @return Consumer
     */
    public abstract BiConsumer<FightResultDto, List<FightResultDto.OtherInfo>> otherInfoBiConsumer();

    /**
     * combinationBiConsumer
     *
     * @return BiConsumer
     */
    public abstract BiConsumer<FightResultCombination, List<FightResultOtherInfoRelation>> combinationBiConsumer();

}
