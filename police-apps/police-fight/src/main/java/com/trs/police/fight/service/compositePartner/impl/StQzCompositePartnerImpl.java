package com.trs.police.fight.service.compositePartner.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.CompositeRoleEnum;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.enums.CompositePartnerWay;
import com.trs.police.fight.domain.dto.CompositePartnerDTO;
import com.trs.police.fight.domain.entity.BigScreenDutyUserEntity;
import com.trs.police.fight.domain.vo.CompositeUserInfo;
import com.trs.police.fight.helper.DutyUserHelper;
import com.trs.police.fight.mapper.BigScreenDutyUserMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.TimeUtils.YYYYMMDD;

/**
 * 省厅情指作战人员
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ys.fight.fight.approval.version", havingValue = "stqz", matchIfMissing = false)
public class StQzCompositePartnerImpl extends AbsCompositePartnerImpl {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private BigScreenDutyUserMapper bigScreenDutyUserMapper;

    @Autowired
    private DictService dictService;

    @Autowired
    private DutyUserHelper dutyUserHelper;

    @Override
    public List<CompositeUserInfo> getCompositePartner(CompositePartnerDTO dto) {
        // 过滤出协作人员需要的值班人员 （根据作战子类别）
        //（1）突发案件类：本单位刑侦、网安、技侦、科信岗今日值班人员+上一级单位指挥长主副班；
        //（2）突发事故类：本单位治安、网安、宣传、警保岗今日值班人员+上一级单位指挥长主副班；
        //（3）突发灾害类：本单位治安、网安、宣传、警保岗今日值班人员+上一级单位指挥长主副班；
        String mapStr = BeanFactoryHolder.getEnv().getProperty("ys.fight.fight.partner.defaultChildTypeJzMap", "{}");
        Map<String, String> map = JSON.parseObject(mapStr, HashMap.class);
        if (!map.containsKey(String.valueOf(dto.getSubtypeId()))) {
            return new ArrayList<>();
        }
        Set<Integer> set = Stream.of(map.get(String.valueOf(dto.getSubtypeId())).split(",|:"))
                .filter(StringUtils::isNotEmpty)
                .map(Integer::valueOf)
                .collect(Collectors.toSet());
        // 获取大屏值班人员
        List<BigScreenDutyUserEntity> list = new LambdaQueryChainWrapper<>(bigScreenDutyUserMapper)
                .eq(BigScreenDutyUserEntity::getDutyTime, TimeUtils.getCurrentDate(YYYYMMDD) + " 00:00:00")
                .eq(BigScreenDutyUserEntity::getDistrictCode, AuthHelper.getCurrentUser().getDept().getDistrictCode())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        Map<Long, DictDto> postMap = dictService.getDictByType("post_type").stream().collect(Collectors.toMap(DictDto::getCode, Function.identity()));
        List<String> deptAndUserIdList = new ArrayList<>();
        BigScreenDutyUserEntity dutyUserEntity = list.get(0);
        for (Integer postCode : set) {
            DictDto dictDto = postMap.get(Long.valueOf(postCode));
            if (StringUtils.isEmpty(dictDto.getDictDesc())) {
                continue;
            }
            try {
                Field field = dutyUserEntity.getClass().getDeclaredField(dictDto.getDictDesc());
                field.setAccessible(true);
                String value = (String) field.get(dutyUserEntity);
                if (!StringUtils.isEmpty(value)) {
                    List<String> collect = Stream.of(value.split(","))
                            .map(e -> e + "-" + CompositeRoleEnum.ASSISTANT.getCode())
                            .collect(Collectors.toList());
                    deptAndUserIdList.addAll(collect);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // 获取上一级单位指挥长
        DistrictListDto districtByCode = dictService.getDistrictByCode(AuthHelper.getCurrentUser().getDept().getDistrictCode());
        List<BigScreenDutyUserEntity> list2 = new LambdaQueryChainWrapper<>(bigScreenDutyUserMapper)
                .eq(BigScreenDutyUserEntity::getDutyTime, TimeUtils.getCurrentDate(YYYYMMDD) + " 00:00:00")
                .eq(BigScreenDutyUserEntity::getNature, "指挥长")
                .eq(BigScreenDutyUserEntity::getDistrictCode, districtByCode.getPCode())
                .list();
        List<String> collect = list2.stream().map(e -> {
            if ("主班".equals(e.getLevel())) {
                return e.getUniqueSign() + "-" + CompositeRoleEnum.ORGANIZER.getCode();
            } else {
                return e.getUniqueSign() + "-" + CompositeRoleEnum.ASSISTANT.getCode();
            }
        }).collect(Collectors.toList());
        deptAndUserIdList.addAll(collect);
        return dutyUserHelper.buildCompositePartner(deptAndUserIdList, "type");
    }

    @Override
    public CompositePartnerWay way() {
        return CompositePartnerWay.SUB_TYPE;
    }
}
