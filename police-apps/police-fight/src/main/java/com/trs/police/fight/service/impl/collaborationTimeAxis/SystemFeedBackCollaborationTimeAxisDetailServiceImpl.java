package com.trs.police.fight.service.impl.collaborationTimeAxis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.fight.constant.enums.CollaborationOperateEnum;
import com.trs.police.fight.converter.CollaborationFeedbackConvert;
import com.trs.police.fight.domain.entity.CollaborationFeedback;
import com.trs.police.fight.domain.vo.Collaboration.CollaborationFeedbackVO;
import com.trs.police.fight.mapper.CollaborationFeedbackMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.function.Function;

/**
 * 反馈详细
 *
 * <AUTHOR>
 * @date 2024/3/8
 */
@Service
public class SystemFeedBackCollaborationTimeAxisDetailServiceImpl extends AbsCollaborationTimeAxisDetailServiceImpl<CollaborationFeedback, CollaborationFeedbackVO> {
    @Autowired
    private CollaborationFeedbackMapper mapper;

    @Override
    public Integer operateType() {
        return CollaborationOperateEnum.AUTO_FEEDBACK.getCode();
    }

    @Override
    protected BaseMapper<CollaborationFeedback> mapper() {
        return mapper;
    }

    @Override
    protected Function<CollaborationFeedback, CollaborationFeedbackVO> doToVO() {
        return CollaborationFeedbackConvert.CONVERTER::doToVO;
    }
}
