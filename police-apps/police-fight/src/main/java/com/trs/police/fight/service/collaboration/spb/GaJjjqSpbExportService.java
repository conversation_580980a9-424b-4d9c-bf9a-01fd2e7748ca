package com.trs.police.fight.service.collaboration.spb;

import com.deepoove.poi.config.ConfigureBuilder;
import com.trs.police.common.core.constant.enums.EnvEnum;
import com.trs.police.fight.constant.enums.CollaborationType;
import com.trs.police.fight.domain.vo.Collaboration.CollaborationJqVO;
import com.trs.police.fight.domain.vo.CollaborationVO;
import com.trs.police.fight.service.collaboration.SpbExportService;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.trs.police.common.core.constant.enums.EnvEnum.GA;
import static com.trs.police.fight.constant.enums.CollaborationType.URGENT_ALERT;

/**
 * 广安紧急警情
 *
 * <AUTHOR>
 */
@Component
public class GaJjjqSpbExportService implements SpbExportService {

    @Override
    public String getTemplatePath(EnvEnum env, CollaborationType collaborationType, String type) {
        return "collaboration_export_gajjjq.docx";
    }

    @Override
    public Boolean support(EnvEnum env, CollaborationType collaborationType) {
        return GA.equals(env) && URGENT_ALERT.equals(collaborationType);
    }

    @Override
    public Map<String, Object> buildMap(CollaborationVO vo, CollaborationType type, Map<String, Object> common) {
        Map<String, Object> result = new HashMap<>();

        if (Objects.nonNull(vo.getCollaborationJq())) {
            CollaborationJqVO jq = vo.getCollaborationJq();
            result.put("jqbh", jq.getJqbh());
            result.put("jqlbmc", jq.getJqlbmc());
            result.put("jqJyQk", jq.getJqJyQk());
            result.put("findPersonOrTarget", jq.getFindPersonOrTarget());
        } else {
            result.put("jqbh", "");
            result.put("jqlbmc", "");
            result.put("jqJyQk", "");
            result.put("findPersonOrTarget", "");
        }
        result.put("gzfs", "数据查询");
        result.put("bz", "");
        result.put("tableName", "公安技侦部门参与处置紧急警情审批表");
        return result;
    }

    @Override
    public void addBinder(ConfigureBuilder builder) {

    }
}
