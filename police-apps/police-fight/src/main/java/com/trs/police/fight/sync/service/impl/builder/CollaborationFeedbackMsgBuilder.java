package com.trs.police.fight.sync.service.impl.builder;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.constant.enums.YsModule;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.fight.domain.entity.CollaborationFeedback;
import com.trs.police.fight.mapper.CollaborationMapper;
import com.trs.police.fight.sync.bean.Message;
import com.trs.police.fight.sync.bean.SyncDeptInfo;
import com.trs.police.fight.sync.bean.SyncUserInfo;
import com.trs.police.fight.sync.constant.DataSource;
import com.trs.police.fight.sync.service.MsgBuilder;
import com.trs.police.fight.sync.service.YsPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.trs.police.fight.sync.constant.FightMessageType.COLLABORATION_FEEDBACK;

/**
 * 协作反馈消息构造
 *
 * <AUTHOR>
 */
@Component
public class CollaborationFeedbackMsgBuilder implements MsgBuilder<CollaborationFeedback> {

    @Autowired
    private YsPermissionService ysPermissionService;

    @Autowired
    private CollaborationMapper collaborationMapper;

    @Override
    public Message buildMsg(CollaborationFeedback feedback) {
        Collaboration collaboration = collaborationMapper.selectById(feedback.getCollaborationId());
        DataSource dataSource = DataSource.findByCode(collaboration.getDataSource()).orElse(null);
        Message message = new Message(ysPermissionService.getLocalDataSource().getSystemFlag(), YsModule.COLLABORATION.getType(),
                COLLABORATION_FEEDBACK.getType(), dataSource.getCode());
        message.setSourcePrimaryKey(collaboration.getSourcePrimaryKey());
        message.setMessageBody(JSON.toJSONString(Arrays.asList(feedback)));
        List<SyncUserInfo> userInfo = ysPermissionService.getUserInfo(feedback.getCreateUserId());
        List<SyncDeptInfo> deptInfo = ysPermissionService.getDeptInfo(feedback.getCreateDeptId());
        message.setUserInfoList(JSON.toJSONString(userInfo));
        message.setDeptInfoList(JSON.toJSONString(deptInfo));
        return message;
    }
}
