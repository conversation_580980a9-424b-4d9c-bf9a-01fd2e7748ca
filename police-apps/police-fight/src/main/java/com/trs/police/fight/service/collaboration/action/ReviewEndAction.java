package com.trs.police.fight.service.collaboration.action;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.CollaborationStatusEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.service.MessageService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.enums.CollaborationEvent;
import com.trs.police.fight.constant.enums.CollaborationFormType;
import com.trs.police.fight.constant.enums.CollaborationOperateEnum;
import com.trs.police.fight.constant.enums.CollaborationPersonnelTypeEnum;
import com.trs.police.fight.domain.bean.JqAssistancePerson;
import com.trs.police.fight.domain.dto.FeedbackRefreshDTO;
import com.trs.police.fight.domain.entity.CollaborationJq;
import com.trs.police.fight.domain.entity.CollaborationUserRelation;
import com.trs.police.fight.domain.params.collaboration.EndReviewParams;
import com.trs.police.fight.helper.FightSendDwdMessageHelper;
import com.trs.police.fight.mapper.CollaborationJqMapper;
import com.trs.police.fight.mapper.CollaborationMapper;
import com.trs.police.fight.service.CollaborationService;
import com.trs.police.fight.service.CollaborationUserRelationService;
import com.trs.police.fight.service.collaboration.CollaborationEventAction;
import com.trs.police.fight.service.impl.relation.CollaborationUserRelationImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.police.fight.constant.enums.CollaborationPersonnelTypeEnum.DEALT_WITH_BEFORE;
import static com.trs.police.fight.constant.enums.CollaborationPersonnelTypeEnum.ME_PROCESSED;

/**
 * 审核结束
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReviewEndAction implements CollaborationEventAction<EndReviewParams> {

    @Autowired
    private CollaborationUserRelationImpl collaborationUserRelation;

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    private CollaborationUserRelationService collaborationUserRelationService;

    @Autowired
    private CollaborationMapper collaborationMapper;

    @Autowired
    private MessageService messageService;

    @Autowired
    private FightSendDwdMessageHelper fightSendDwdMessageHelper;

    @Autowired
    private CollaborationService collaborationService;

    @Resource
    private CollaborationJqMapper collaborationJqMapper;

    @Override
    public void action(CollaborationStatusEnum from, CollaborationStatusEnum to, CollaborationEvent event, Collaboration collaboration, EndReviewParams endReviewParams) {
        // 建立目标单位待反馈人员的关联关系
        List<CollaborationUserRelation> relations = Boolean.TRUE.equals(endReviewParams.getNeedSelect())
                ? buildProcessRelationsBySelect(collaboration, endReviewParams)
                : buildProcessRelations(collaboration);
        if (Objects.nonNull(relations) && !relations.isEmpty()) {
            // 如果之前有待反馈的人员，修改状态
            collaborationUserRelationService.lambdaUpdate()
                    .eq(CollaborationUserRelation::getPersonnelType, ME_PROCESSED.getCode())
                    .eq(CollaborationUserRelation::getCollaborationId, collaboration.getId())
                    .set(CollaborationUserRelation::getPersonnelType, DEALT_WITH_BEFORE.getCode())
                    .update();
            // 保存
            collaborationUserRelationService.saveBatch(relations);
        }
        // 如果是紧急警情协作，添加反馈
        Integer type = collaborationService.formOrder(collaboration.getCollaborationType());
        if (CollaborationFormType.SIX.getCode().equals(type)) {
            refreshFeedback(collaboration);
        }
    }

    private List<CollaborationUserRelation> buildProcessRelationsBySelect(Collaboration collaboration, EndReviewParams endReviewParams) {
        List<CollaborationUserRelation> list = Optional.ofNullable(endReviewParams.getSelectImplementers())
                .orElse(new ArrayList<>())
                .stream()
                .map(user -> buildRelation(collaboration, user, ME_PROCESSED))
                .collect(Collectors.toList());
        return list;
    }

    /**
     * 根据协作添加待处理人员的关联关系
     *
     * @param collaboration 协作
     * @return 关联关系
     */
    private List<CollaborationUserRelation> buildProcessRelations(Collaboration collaboration) {
        Objects.requireNonNull(collaboration.getCollaborationDeptId(), "协作目标部门不存在");
        final PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        // 指定到个人级别
        if (Objects.nonNull(collaboration.getCollaborationUserId())) {
            UserDto user = permissionService.getUserById(collaboration.getCollaborationUserId());
            CollaborationUserRelation relation = buildRelation(collaboration, user.getId(), ME_PROCESSED);
            return Arrays.asList(relation);
        }
        // 指定到部门级别 (查询部门下有这个权限的人)
        String permissionModule = applicationContext.getEnvironment().getProperty(
                "ys.composite.collaboration.permission", "警务协作_我处理的"
        );
        List<Long> roleIds = permissionService.rolesWithPermission(permissionModule, null);
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new TRSException("没有协作权限");
        }
        String idsStr = roleIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        List<SimpleUserVO> userDeptVOList = permissionService.getDeptUsersByRoles(collaboration.getCollaborationDeptId(), idsStr);
        if (CollectionUtils.isEmpty(userDeptVOList)) {
            DeptDto deptDto = permissionService.getDeptById(collaboration.getCollaborationDeptId());
            log.warn(String.format("无法提交审核。部门：%s 下无法找有反馈权限的用户", deptDto.getName()));
            return new ArrayList<>();
        }
        List<CollaborationUserRelation> relations = CollectionUtils.isEmpty(userDeptVOList)
                ? new ArrayList<>()
                : userDeptVOList.stream()
                .map(vo -> collaborationUserRelation.buildRelationBySimpleUser(collaboration.getId(), vo, ME_PROCESSED))
                .collect(Collectors.toList());
        return relations;
    }

    /**
     * 根部门构造relation
     *
     * @param collaboration 协作id
     * @param userId 部门vo
     * @param type 类型
     * @return relation
     */
    public CollaborationUserRelation buildRelation(Collaboration collaboration, Long userId, CollaborationPersonnelTypeEnum type) {
        UserDeptVO userDeptVO = new UserDeptVO();
        userDeptVO.setUserId(userId);
        userDeptVO.setDeptId(collaboration.getCollaborationDeptId());
        return buildRelation(collaboration, userDeptVO, type);
    }

    /**
     * 根部门构造relation
     *
     * @param collaboration 协作id
     * @param userDeptVO 部门vo
     * @param type 类型
     * @return relation
     */
    public CollaborationUserRelation buildRelation(Collaboration collaboration, UserDeptVO userDeptVO, CollaborationPersonnelTypeEnum type) {
        CollaborationUserRelation relation = new CollaborationUserRelation();
        relation.setCollaborationId(collaboration.getId());
        relation.setRelatedUserId(userDeptVO.getUserId());
        relation.setRelatedUserDeptId(userDeptVO.getDeptId());
        relation.setPersonnelType(type.getCode());
        return relation;
    }

    /**
     * 刷新反馈
     *
     * @param collaboration 协作
     */
    public void refreshFeedback(Collaboration collaboration) {
        try {
            Integer type = collaborationService.formOrder(collaboration.getCollaborationType());
            log.info("协作表单类型：{}", type);
            FeedbackRefreshDTO dto = new FeedbackRefreshDTO();
            dto.setOperateType(CollaborationOperateEnum.AUTO_FEEDBACK);
            CollaborationJq collaborationJq = collaborationJqMapper.selectOne(
                    Wrappers.lambdaQuery(CollaborationJq.class)
                            .eq(CollaborationJq::getCollaborationId, collaboration.getId())
            );
            List<JqAssistancePerson> personList = JSONArray.parseArray(collaborationJq.getAssistancePerson(), JqAssistancePerson.class)
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(p -> StringUtils.isNotEmpty(p.getTel()))
                    .collect(Collectors.toList());
            dto.setAssistancePersonList(personList);
            collaborationService.feedbackRefresh(collaboration.getId(), dto);
        } catch (Exception e) {
            log.error("自动刷新反馈失败", e);
        }
    }

}
