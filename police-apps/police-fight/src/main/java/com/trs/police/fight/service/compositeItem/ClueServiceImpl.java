package com.trs.police.fight.service.compositeItem;

import com.trs.police.common.core.vo.FightClueVO;
import com.trs.police.fight.domain.dto.CompositeItemSearchDTO;
import com.trs.police.fight.service.FightCompositeClueRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 线索检索
 *
 * <AUTHOR>
 */
@Component
public class ClueServiceImpl implements ICompositeItemSearchService{

    @Autowired
    private FightCompositeClueRelationService fightCompositeClueRelationService;

    /**
     * 检索合成子项
     *
     * @param compositeItemSearchDTO CompositeItemSearchDTO
     * @return CompositeItemSearchDTO
     */
    @Override
    public CompositeItemSearchDTO searchCompositeItems(CompositeItemSearchDTO compositeItemSearchDTO) {
        List<Long> compositeItemIds = compositeItemSearchDTO.getCompositeItemIds();
        Map<Long, List<FightClueVO>> compositeIdAndFightClueListMap = new HashMap<>(0);
        if (!CollectionUtils.isEmpty(compositeItemIds)) {
            List<FightClueVO> fightClueVoList = fightCompositeClueRelationService.getRelationList(compositeItemIds);
            compositeIdAndFightClueListMap = fightClueVoList.stream().collect(Collectors.groupingBy(FightClueVO::getCompositeId));
        }
        compositeItemSearchDTO.setCompositeIdAndFightClueListMap(compositeIdAndFightClueListMap);
        return null;
    }
}
