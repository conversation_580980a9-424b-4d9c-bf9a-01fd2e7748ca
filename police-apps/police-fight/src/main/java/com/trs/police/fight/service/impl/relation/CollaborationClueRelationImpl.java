package com.trs.police.fight.service.impl.relation;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.fight.constant.enums.CollaborationRelatedTypeEnum;
import com.trs.police.fight.domain.dto.CollaborationDto;
import com.trs.police.fight.domain.entity.CollaborationSceneRelation;
import com.trs.police.fight.service.CollaborationRelationService;
import com.trs.police.fight.service.CollaborationSceneRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/03/07
 * @description: 协作-线索关联
 */
@Service
@Slf4j
public class CollaborationClueRelationImpl implements CollaborationRelationService {

    @Resource
    private CollaborationSceneRelationService collaborationSceneRelationService;

    @Override
    public void createRelation(CollaborationDto dto, Collaboration collaboration) {
       if (CollectionUtils.isNotEmpty(dto.getClues())) {
           List<CollaborationSceneRelation> entities = dto.getClues().stream().map(clue -> {
               CollaborationSceneRelation entity = new CollaborationSceneRelation();
               entity.setCollaborationId(collaboration.getId());
               entity.setRelatedType(CollaborationRelatedTypeEnum.RELATED_CLUE.getCode());
               entity.setRelatedId(clue.getId());
               entity.setRelatedStrId(clue.getCode());
               entity.setRelatedInfo(JSON.toJSONString(clue));
               return entity;
           }).collect(Collectors.toList());
           collaborationSceneRelationService.saveBatch(entities);
       }
    }

    @Override
    public void deleteRelation(Long collaborationId) {
        QueryWrapper<CollaborationSceneRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("collaboration_id", collaborationId);
        queryWrapper.eq("related_type", CollaborationRelatedTypeEnum.RELATED_CLUE.getCode());
        collaborationSceneRelationService.remove(queryWrapper);
    }
}
