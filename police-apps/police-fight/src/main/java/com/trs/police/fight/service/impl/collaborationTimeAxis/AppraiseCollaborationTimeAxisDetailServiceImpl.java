package com.trs.police.fight.service.impl.collaborationTimeAxis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.fight.constant.enums.CollaborationOperateEnum;
import com.trs.police.fight.converter.CollaborationAppraiseConvert;
import com.trs.police.fight.domain.entity.CollaborationAppraise;
import com.trs.police.fight.domain.vo.Collaboration.CollaborationAppraiseVO;
import com.trs.police.fight.mapper.CollaborationAppraiseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.function.Function;

/**
 * 评价详细
 *
 * <AUTHOR>
 * @date 2024/3/8
 */
@Service
public class AppraiseCollaborationTimeAxisDetailServiceImpl extends AbsCollaborationTimeAxisDetailServiceImpl<CollaborationAppraise, CollaborationAppraiseVO> {
    @Autowired
    private CollaborationAppraiseMapper mapper;

    @Override
    public Integer operateType() {
        return CollaborationOperateEnum.APPRAISE.getCode();
    }

    @Override
    protected BaseMapper mapper() {
        return mapper;
    }

    @Override
    protected Function<CollaborationAppraise, CollaborationAppraiseVO> doToVO() {
        return CollaborationAppraiseConvert.CONVERTER::doToVO;
    }
}
