package com.trs.police.fight.service;

import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.fight.constant.enums.CompositeType;
import com.trs.police.fight.domain.vo.CreateCompositeVO;
import com.trs.police.fight.domain.vo.MergeCompositeVO;

import java.util.Set;

/**
 * 关系处理
 *
 * <AUTHOR>
 */
public interface RelationService {

    /**
     * 可以处理的类型
     *
     * @return 可以处理的类型
     */
    Set<CompositeType> acceptType();

    /**
     * 新建关系
     *
     * @param createCompositeVO 创建作战的vo
     * @param fightComposite 作战
     */
    void createRelation(CreateCompositeVO createCompositeVO, FightComposite fightComposite);

    /**
     * 转移关系
     *
     * @param mergeCompositeVO 转移时的条件
     * @param source 被转移对象
     * @param target 目标对象
     */
    void rebuildRelation(MergeCompositeVO mergeCompositeVO, FightComposite source, FightComposite target);
}
