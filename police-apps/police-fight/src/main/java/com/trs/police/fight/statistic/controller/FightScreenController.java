package com.trs.police.fight.statistic.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.fight.statistic.CountStatisticServiceFactory;
import com.trs.police.fight.statistic.DTO.*;
import com.trs.police.fight.statistic.DTO.NoParamsDTO;
import com.trs.police.statistic.domain.VO.CountStatisticVO;
import com.trs.police.statistic.domain.bean.NoOtherValue;
import com.trs.police.fight.statistic.domain.vo.*;
import com.trs.police.fight.statistic.service.CountStatisticService;
import com.trs.police.fight.statistic.service.FightScreenStatisticService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 作战大屏统计
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping({"statistic"})
public class FightScreenController {

    @Autowired
    private CountStatisticServiceFactory statisticServiceFactory;

    @Autowired
    private FightScreenStatisticService fightScreenStatisticService;

    /**
     * 数据概览
     *
     * @param commonParams 公共参数
     * @param params 数据概览特有参数
     * @return 统计结果
     */
    @PostMapping("fightAreaMap")
    public CountStatisticVO<NoOtherValue> fightAreaMap(CommonDTO commonParams, FightAreaStatisticDTO params) {
        CountStatisticService<FightAreaStatisticDTO, NoOtherValue> service = statisticServiceFactory.getProxyService(new StatisticContext<>(params, commonParams), NoOtherValue.class);
        return service.doStatisticWithParams(commonParams, params);
    }

    /**
     * 数据统计
     *
     * @param commonParams 公共参数
     * @return 统计结果
     */
    @PostMapping("dataOverview")
    public List<DataOverviewCountStatisticVO> dataOverview(@RequestBody CommonDTO commonParams) {
        return fightScreenStatisticService.dataOverview(commonParams);
    }
    /**
     * 在办热线电话
     *
     * @param commonParams 公共参数
     * @return 统计结果
     */
    @PostMapping("handleHotline")
    public Page<HandleHotlineVO> handleHotline(@RequestBody CommonDTO commonParams) {
        return fightScreenStatisticService.handleHotline(commonParams);
    }

    /**
     * 线索发起情况
     *
     * @param dto dto
     * @return 统计结果
     */
    @PostMapping("clueInitiationSituation")
    public List<ClueInitiationSituationVO> clueInitiationSituation(@RequestBody ClueInitiationSituationDTO dto) {
        return fightScreenStatisticService.clueInitiationSituation(dto);
    }

    /**
     * 线索流转情况
     *
     * @param dto dto
     * @return 统计结果
     */
    @PostMapping("clueCurrentSituation")
    public ClueCurrentSituationVO clueCurrentSituation(@RequestBody ClueInitiationSituationDTO dto) {
        return fightScreenStatisticService.clueCurrentSituation(dto);
    }


    /**
     * 获取再办协作
     *
     * @param commonParams dto
     * @return jieguo
     */
    @PostMapping("/inProcessCorporation")
    public RestfulResultsV2<CorporationVO> inProcessCorporation(@RequestBody CommonDTO commonParams){
        return fightScreenStatisticService.inProcessCorporation(commonParams);
    }

    /**
     * 数据概览
     *
     * @param commonParams 公共参数
     * @param noParamsDTO no
     * @return 统计结果
     */
    @PostMapping("policeKindFightList")
    public CountStatisticVO<TotalPageInfoVO> policeKindFightList(CommonDTO commonParams, @RequestBody NoParamsDTO noParamsDTO) {
        commonParams = new CommonDTO();
        BeanUtils.copyProperties(noParamsDTO, commonParams);
        CountStatisticService<NoParamsDTO, TotalPageInfoVO> service = statisticServiceFactory.getProxyService(new StatisticContext<>(noParamsDTO, commonParams), TotalPageInfoVO.class);
        return service.doStatisticWithParams(commonParams, noParamsDTO);
    }

    /**
     * 警务协作统计
     *
     * @param commonParams 公共参数
     * @param noParamsDTO no
     * @return 统计结果
     */
    @PostMapping("policeCorporationList")
    public CountStatisticVO<NoOtherValue> policeCorporationList(CommonDTO commonParams, @RequestBody NoParamsDTO noParamsDTO) {
        commonParams = new CommonDTO();
        BeanUtils.copyProperties(noParamsDTO, commonParams);
        CountStatisticService<NoParamsDTO, NoOtherValue> service = statisticServiceFactory.getProxyService(new StatisticContext<>(noParamsDTO, commonParams), NoOtherValue.class);
        return service.doStatisticWithParams(commonParams, noParamsDTO);
    }

    /**
     * 协作分类统计
     *
     * @param commonParams 公共参数
     * @param noParamsDTO no
     * @return 统计结果
     */
    @PostMapping("corporationKindStatistics")
    public CountStatisticVO<NoOtherValue> corporationKindStatistics(CommonDTO commonParams, @RequestBody NoParamsDTO noParamsDTO) {
        commonParams = new CommonDTO();
        BeanUtils.copyProperties(noParamsDTO, commonParams);
        CountStatisticService<NoParamsDTO, NoOtherValue> service = statisticServiceFactory.getProxyService(
                new StatisticContext<>(noParamsDTO, commonParams),
                NoOtherValue.class
        );
        return service.doStatisticWithParams(commonParams, noParamsDTO);
    }

    /**
     * 警种支撑质效
     *
     * @param commonParams 公共参数
     * @param noParamsDTO no
     * @return 统计结果
     */
    @PostMapping("policeKindZcJx")
    public CountStatisticVO<TotalPageInfoVO> policeKindZcJx(CommonDTO commonParams, @RequestBody NoParamsDTO noParamsDTO) {
        commonParams = new CommonDTO();
        BeanUtils.copyProperties(noParamsDTO, commonParams);
        CountStatisticService<NoParamsDTO, TotalPageInfoVO> service = statisticServiceFactory.getProxyService(
                new StatisticContext<>(noParamsDTO, commonParams),
                TotalPageInfoVO.class
        );
        return service.doStatisticWithParams(commonParams, noParamsDTO);
    }

    /**
     * 协作趋势
     *
     * @param commonParams 公共参数
     * @param noParamsDTO no
     * @return 统计结果
     */
    @PostMapping("corporationTrend")
    public CountStatisticVO<NoOtherValue> corporationTrend(CommonDTO commonParams, @RequestBody NoParamsDTO noParamsDTO) {
        commonParams = new CommonDTO();
        BeanUtils.copyProperties(noParamsDTO, commonParams);
        CountStatisticService<NoParamsDTO, NoOtherValue> service = statisticServiceFactory.getProxyService(
                new StatisticContext<>(noParamsDTO, commonParams),
                NoOtherValue.class
        );
        return service.doStatisticWithParams(commonParams, noParamsDTO);
    }

    /**
     * 协作趋势
     *
     * @param commonParams 公共参数
     * @param noParamsDTO no
     * @return 统计结果
     */
    @PostMapping("policeKindFightTotal")
    public CountStatisticVO<NoOtherValue> policeKindFightTotal(CommonDTO commonParams, @RequestBody NoParamsDTO noParamsDTO) {
        commonParams = new CommonDTO();
        BeanUtils.copyProperties(noParamsDTO, commonParams);
        CountStatisticService<NoParamsDTO, NoOtherValue> service = statisticServiceFactory.getProxyService(
                new StatisticContext<>(noParamsDTO, commonParams),
                NoOtherValue.class
        );
        return service.doStatisticWithParams(commonParams, noParamsDTO);
    }

    /**
     * 作战类型统计
     *
     * @param commonParams 参数
     * @param fightTypeStatisticParams 作战类型统计
     * @return v
     */
    @PostMapping("fightTypeStatistic")
    public CountStatisticVO<NoOtherValue> fightTypeStatistic(CommonDTO commonParams, FightTypeStatisticDTO fightTypeStatisticParams) {
        CountStatisticService<FightTypeStatisticDTO, NoOtherValue>  service = statisticServiceFactory.getProxyService(
                new StatisticContext<>(fightTypeStatisticParams, commonParams),
                NoOtherValue.class
        );
        return service.doStatisticWithParams(commonParams, fightTypeStatisticParams);
    }
}
