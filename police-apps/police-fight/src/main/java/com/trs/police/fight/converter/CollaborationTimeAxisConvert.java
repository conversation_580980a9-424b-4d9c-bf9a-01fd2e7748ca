package com.trs.police.fight.converter;

import com.trs.police.fight.domain.entity.CollaborationTimeAxis;
import com.trs.police.fight.domain.vo.CollaborationTimeAxisVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/3/8
 */
@Mapper
public interface CollaborationTimeAxisConvert {

    CollaborationTimeAxisConvert CONVERTER = Mappers.getMapper(CollaborationTimeAxisConvert.class);

    /**
     * @param timeAxis timeAxis
     * @return {@link CollaborationTimeAxisVO}
     */
    @Mappings({
            @Mapping(target = "operateType", expression = "java(timeAxis.getOperateType().getCode())"),
            @Mapping(target = "createTime", source = "createTime")
    })
    CollaborationTimeAxisVO doToVO(CollaborationTimeAxis timeAxis);
}
