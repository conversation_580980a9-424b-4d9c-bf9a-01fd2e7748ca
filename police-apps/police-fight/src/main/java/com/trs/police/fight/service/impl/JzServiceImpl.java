package com.trs.police.fight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.fight.constant.enums.CollaborationOperateEnum;
import com.trs.police.fight.constant.enums.JzQueryTaskStatusEnum;
import com.trs.police.fight.domain.entity.JzQueryTaskEntity;
import com.trs.police.fight.domain.params.collaboration.FeedbackParams;
import com.trs.police.fight.mapper.CollaborationFeedbackMapper;
import com.trs.police.fight.mapper.JzQueryTaskMapper;
import com.trs.police.fight.properties.FeedbackRefreshProperties;
import com.trs.police.fight.service.CollaborationTimeAxisService;
import com.trs.police.fight.service.JzService;
import com.trs.police.fight.service.collaboration.CollaborationProcessService;
import com.trs.police.fight.sync.service.YsPermissionService;
import com.trs.police.fight.utils.HttpClientUtils;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

import static com.trs.police.common.core.utils.JsonUtil.OBJECT_MAPPER;
import static com.trs.police.fight.constant.enums.JzQueryTaskStatusEnum.*;

/**
 * 技侦服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class JzServiceImpl implements JzService {

    @Autowired
    private JzQueryTaskMapper jzQueryTaskMapper;

    @Autowired
    private CollaborationFeedbackMapper collaborationFeedbackMapper;

    @Autowired
    private CollaborationTimeAxisService collaborationTimeAxisService;

    @Autowired
    private YsPermissionService ysPermissionService;

    @Autowired
    private FeedbackRefreshProperties feedbackRefreshProperties;

    @Autowired
    private CollaborationProcessService collaborationProcessService;

    @Autowired
    private DictService dictService;

    @Override
    public void jzResult(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return;
        }
        log.info("接收到技侦数据：{}", jsonObject.toJSONString());
        String sjpc = jsonObject.getString("sjpc");
        if (StringUtils.isEmpty(sjpc)) {
            throw new IllegalArgumentException("当前数据没有数据批次");
        }
        List<JzQueryTaskEntity> ens = jzQueryTaskMapper.selectList(
                Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                        .eq(JzQueryTaskEntity::getSjpc, sjpc)
        );
        if (CollectionUtils.isEmpty(ens)) {
            throw new IllegalArgumentException("未能匹配到当前数据批次");
        }
        JzQueryTaskEntity en = ens.get(0);
        // 生成反馈记录
        final Boolean needAddFeedBack = StringUtils.isEmpty(en.getResult());
        en.setResult(jsonObject.toJSONString());
        en.setStatus(JzQueryTaskStatusEnum.COMPLETED.getCode());
        en.setFinishTime(LocalDateTime.now());
        jzQueryTaskMapper.updateById(en);
        if (needAddFeedBack) {
            // 解析反馈内容
            JSONArray dataArr = jsonObject.containsKey("dataArr")
                    ? jsonObject.getJSONArray("dataArr")
                    : new JSONArray();
            if (dataArr.isEmpty()) {
                addFeedBack(en, generateEmptyResult(en.getDhhm()));
            } else {
                JSONObject data = dataArr.getJSONObject(0);
                // 张三，14465221874，2024-10-11 12：21：33 出现在自流井区华商国际商贸城1号门附近。
                String content = new StringBuilder()
                        .append(data.get("XM"))
                        .append(",").append(data.get("USERNUM"))
                        .append(",").append(data.get("SUB_INSERT_DATE"))
                        .append(" 出现在").append(data.get("DLYQ_ADDRESS"))
                        .toString();
                addFeedBack(en, content);
            }
        }
    }

    @Override
    public void jzResultV2(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return;
        }
        log.info("接收到技侦数据：{}", jsonObject.toJSONString());
        String sjpc = jsonObject.getString("sjpc");
        if (StringUtils.isEmpty(sjpc)) {
            throw new IllegalArgumentException("当前数据没有数据批次");
        }
        List<JzQueryTaskEntity> ens = jzQueryTaskMapper.selectList(
                Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                        .eq(JzQueryTaskEntity::getSjpc, sjpc)
        );
        if (CollectionUtils.isEmpty(ens)) {
            throw new IllegalArgumentException("未能匹配到当前数据批次");
        }
        JzQueryTaskEntity en = ens.get(0);
        // 生成反馈记录
        final Boolean needAddFeedBack = StringUtils.isEmpty(en.getResult());
        en.setResult(jsonObject.toJSONString());
        en.setStatus(JzQueryTaskStatusEnum.COMPLETED.getCode());
        en.setFinishTime(LocalDateTime.now());
        jzQueryTaskMapper.updateById(en);
        if (needAddFeedBack) {
            if (jsonObject.isEmpty()) {
                addFeedBack(en, generateEmptyResult(en.getDhhm()));
            } else {
                // 张三，14465221874，2024-10-11 12：21：33 出现在自流井区华商国际商贸城1号门附近。
                String content = new StringBuilder()
                        .append(jsonObject.get("XM"))
                        .append(",").append(jsonObject.get("SJHM"))
                        .append(",").append(jsonObject.get("SUB_INSERT_DATE"))
                        .append(" 出现在")
                        .append(getAreaCodeName(jsonObject.getString("CURAREA")))
                        .append(jsonObject.get("DLYQ_ADDRESS"))
                        .toString();
                addFeedBack(en, content);
            }
        }
    }

    @Override
    public void inputData(String dhhm, String sjpc, String name) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("key", feedbackRefreshProperties.getKey());
        bodyMap.put("id", UUID.randomUUID().toString());
        bodyMap.put("type", "1");
        JSONObject dataObject = new JSONObject();
        dataObject.put("SJPC", sjpc);
        dataObject.put("SJHM", dhhm);
        dataObject.put("name", name);
        dataObject.put("XM", name);
        JSONArray dataArray = new JSONArray();
        dataArray.add(dataObject);
        bodyMap.put("data", dataArray);
        log.info("非技侦数据接入技侦接口传参：【{}】", JsonUtil.toJsonString(bodyMap));
        String url = feedbackRefreshProperties.getPreUrl() + feedbackRefreshProperties.getInputDataPath();
        // 构建请求头
        // 构建JSON请求体
        String jsonBody = JsonUtil.toJsonString(bodyMap);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), jsonBody);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("accessToken", feedbackRefreshProperties.getAccessToken())
                .build();
        // 发送请求并获取响应
        try {
            String bodyString = getResponse(request);
            if (StringUtils.isEmpty(bodyString)) {
                log.error("请求非技侦数据接入技侦接口响应内容为空");
                return;
            }
            log.info("请求非技侦数据接入技侦接口响应内容:{}", bodyString);
            String status = OBJECT_MAPPER.readTree(bodyString).get("status").asText();
            String info = OBJECT_MAPPER.readTree(bodyString).get("info").asText();
            if ("0".equals(status)) {
                log.info("非技侦数据接入技侦成功");
            } else {
                log.error(String.format("数据批次【%s】-非技侦数据接入技侦失败，失败原因：%s", sjpc, info));
            }
        } catch (Exception e) {
            throw new TRSException("非技侦数据接入技侦失败", e);
        }
    }

    @Override
    public void noticeRunModel(Long collaborationId, String sjpc, Map<String, Object> bodyMap, Tuple2<String, byte[]> pdfTuple) {
        log.info("通知技侦自动运行模型接口接口传参：【{}】", JsonUtil.toJsonString(bodyMap));
        // pdf手续文件
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("serviceCode", "auto-exe-model")
                .addFormDataPart("systemInfoJson",JsonUtil.toJsonString(bodyMap.get("systemInfoJson")))
                .addFormDataPart("userInfoJson", JsonUtil.toJsonString(bodyMap.get("userInfoJson")))
                .addFormDataPart("caseInfoJson", JsonUtil.toJsonString(bodyMap.get("caseInfoJson")))
                .addFormDataPart("tqspInfoJson", bodyMap.get("tqspInfoJson").toString())
                .addFormDataPart("xmlContent",JsonUtil.toJsonString(bodyMap.get("xmlContent")))
                .addFormDataPart("pdfFiles", pdfTuple._1, RequestBody.create(MediaType.parse("application/octet-stream"), pdfTuple._2));
        // 构造请求
        String url = feedbackRefreshProperties.getPreUrl() + feedbackRefreshProperties.getNoticeRunModelPath();
        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .addHeader("accessToken", feedbackRefreshProperties.getAccessToken())
                .build();
        // 解析请求结果
        try {
            String bodyString =  getResponse(request);
            if (StringUtils.isEmpty(bodyString)) {
                log.error("请求通知技侦自动运行模型接口响应内容为空");
                return;
            }
            log.info("请求通知技侦自动运行模型接口响应内容:{}", bodyString);
            String status = OBJECT_MAPPER.readTree(bodyString).get("status").asText();
            String info = OBJECT_MAPPER.readTree(bodyString).get("info").asText();
            if ("0".equals(status)) {
                log.info("通知技侦自动运行模型成功");
            } else {
                log.error(String.format("数据批次【%s】-通知技侦自动运行模型失败，失败原因：%s", sjpc, info));
            }
        } catch (Exception e) {
            log.error("请求通知技侦自动运行模型接口时发生异常: ", e);
            throw new TRSException("通知技侦自动运行模型接口失败");
        }
    }

    @Override
    public String getRunStatus(String sjpc) {
        // 构建请求URL，jobId固定
        String jobId = feedbackRefreshProperties.getJobId();
        String url = String.format("https://%s:%s/yqDwfnServer/model-service/auto-exe/runStatus?jobId=%s&sjpc=%s", feedbackRefreshProperties.getIp(), feedbackRefreshProperties.getPort(), jobId, sjpc);

        // 创建HttpClient实例
        OkHttpClient client = HttpClientUtils.getHttpClient();

        // 构建请求头
        okhttp3.Request request = new okhttp3.Request.Builder()
                .url(url)
                .header("accessToken", "yourAccessToken") // 替换为实际的accessToken
                .build();

        // 发送请求并获取响应
        try (okhttp3.Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return Objects.requireNonNull(response.body()).string();
            } else {
                throw new TRSException("Failed to get run status: " + response.code());
            }
        } catch (Exception e) {
            throw new TRSException("Error occurred while getting run status", e);
        }
    }

    @Override
    public void syncJzFeedBackResult() {
        // 查询出所有数据批次
        List<Integer> statusList = Arrays.asList(PUSHED.getCode(), RUNNING.getCode());
        List<JzQueryTaskEntity> ens = jzQueryTaskMapper.selectList(
                Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                        .in(JzQueryTaskEntity::getStatus, statusList)
        );
        // 逐个处理
        for (JzQueryTaskEntity en : ens) {
            LocalDateTime createTime = en.getCreateTime();
            Integer overtimeHours = BeanFactoryHolder.getEnv().getProperty("ys.fight.jz.task.ttl", Integer.class, 1);
            if (createTime.plusHours(overtimeHours).isBefore(LocalDateTime.now())) {
                en.setStatus(OVER_TIME.getCode());
                addFeedBack(en, generateEmptyResult(en.getDhhm()));
                jzQueryTaskMapper.updateById(en);
            } else {
                String runStatus = getRunStatus(en.getSjpc());
                log.info("数据批次：{}，jz模型运行状态：{}", en.getSjpc(), runStatus);
                boolean isJson = JSONValidator.from(runStatus).validate();
                if (Boolean.FALSE.equals(isJson)) {
                    log.warn("数据批次：{} 得到了异常返回：{}", en.getSjpc(), runStatus);
                    continue;
                }
                JSONObject status = JSON.parseObject(runStatus);
                String result = status.getString("runStatus");
                if ("正在运行".equals(result)) {
                    en.setStatus(RUNNING.getCode());
                }
                jzQueryTaskMapper.updateById(en);
            }
        }
    }

    @Override
    public void addFeedBack(JzQueryTaskEntity en, String content) {
        FeedbackParams feedback = new FeedbackParams();
        feedback.setCollaborationId(en.getCollaborationId());
        feedback.setOperateUser(ysPermissionService.defaultUser());
        Long ct = jzQueryTaskMapper.selectCount(
                Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                        .eq(JzQueryTaskEntity::getCollaborationId, en.getCollaborationId())
                        .eq(JzQueryTaskEntity::getDhhm, en.getDhhm())
        );
        CollaborationOperateEnum operateType = ct > 0 ? CollaborationOperateEnum.RE_REQUEST : CollaborationOperateEnum.AUTO_FEEDBACK;
        feedback.setCollaborationOperate(operateType.getCode());
        JSONObject json = new JSONObject();
        json.put("content", content);
        json.put("jzResult", en.getResult());
        feedback.setContent(json.toJSONString());
        collaborationProcessService.feedback(feedback);
    }

    /**
     * 获取结果
     *
     * @param request 请求
     * @return 结果
     * @throws Exception e
     */
    public String getResponse(okhttp3.Request request) throws Exception {
        if (Objects.isNull(request)) {
            return null;
        }
        OkHttpClient client = HttpClientUtils.getHttpClient();
        try (Response response = client.newCall(request).execute();
             ResponseBody responseBody = response.body()) {
            if (Objects.isNull(responseBody)) {
                log.error("请求非技侦数据接入技侦接口响应内容为空");
                return null;
            }
            return responseBody.string();
        }
    }

    private String generateEmptyResult(String tel) {
        return String.format("%s，未找到轨迹", tel);
    }

    // 根据区号获取到地域名称
    private String getAreaCodeName(String code) {
        if (StringUtils.isEmpty(code)) {
            return "";
        }
        List<Dict2VO> areaCode = dictService.commonSearch("telephone_area_code", null, null, null);
        Optional<Dict2VO> an = areaCode.stream()
                .filter(d -> d.getName().equalsIgnoreCase(code))
                .findAny();
        return an.map(Dict2VO::getDictDesc).orElse("");
    }
}
