package com.trs.police.fight.sync.service.impl.builder;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.constant.enums.YsModule;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.entity.FightCompositeMessage;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.police.fight.mapper.FightCompositeMapper;
import com.trs.police.fight.mapper.FightCompositeMessageMapper;
import com.trs.police.fight.sync.bean.Message;
import com.trs.police.fight.sync.bean.SyncFightChatMsg;
import com.trs.police.fight.sync.constant.DataSource;
import com.trs.police.fight.sync.service.MsgBuilder;
import com.trs.police.fight.sync.service.YsPermissionService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

import static com.trs.police.fight.sync.constant.FightMessageType.FIGHT_MESSAGE;

/**
 * 聊天消息同步
 *
 * <AUTHOR>
 */
@Component
public class CompositeChatMsgBuilder implements MsgBuilder<FightCompositeMessage> {

    @Autowired
    private YsPermissionService ysPermissionService;

    @Autowired
    private FightCompositeMapper fightCompositeMapper;

    @Resource
    private FightCompositeMessageMapper fightCompositeMessageMapper;

    @Override
    public Message buildMsg(FightCompositeMessage fightCompositeMessage) {

        SyncFightChatMsg syncFightChatMsg = new SyncFightChatMsg();
        BeanUtils.copyProperties(fightCompositeMessage, syncFightChatMsg);
        WebsocketMessageVO ws = JSON.parseObject(fightCompositeMessage.getContent(), WebsocketMessageVO.class);
        if (Objects.nonNull(ws.getReferId())) {
            FightCompositeMessage ref = fightCompositeMessageMapper.selectById(ws.getReferId());
            syncFightChatMsg.setRefDataSource(ref.getDataSource());
            syncFightChatMsg.setRefPrimaryKey(ref.getSourcePrimaryKey());
        }
        syncFightChatMsg.setContent(JSON.toJSONString(ws));
        DataSource dataSource = ysPermissionService.getLocalDataSource();
        FightComposite fightComposite = fightCompositeMapper.selectById(fightCompositeMessage.getCompositeId());
        Message message = new Message(dataSource.getSystemFlag(), YsModule.FIGHT.getType(), FIGHT_MESSAGE.getType())
                .addMainEntitySource(fightComposite.getDataSource(), fightComposite.getSourcePrimaryKey());
        message.setMessageBody(JSON.toJSONString(Arrays.asList(syncFightChatMsg)));
        return message;
    }
}
