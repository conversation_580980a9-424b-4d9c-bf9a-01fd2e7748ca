package com.trs.police.fight.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/29 15:17
 */
@Data
public class JqListVO implements Serializable {

    private static final long serialVersionUID = 5795401357410604045L;

    private Long id;

    /**
     * 警情编号
     */
    private String jqbh;

    /**
     * 发生地点名称
     */
    private String fsddmc;

    /**
     * 报警内容
     */
    private String content;


    /**
     * 报警时间
     */
    private String alarmTime;

    /**
     * 警情类别名称
     */
    private String jqlbmc;

    /**
     * 管辖单位代码
     */
    private String gxdwdm;

    /**
     * 处置单位 管辖单位 名称
     */
    private String gxdwmc;
}
