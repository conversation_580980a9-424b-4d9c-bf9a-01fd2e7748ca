package com.trs.police.fight.domain.vo;

import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.entity.FightCompositeMessage;
import com.trs.police.common.core.entity.FightCompositeMessageUserRelation;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.domain.dto.CompositeItemSearchDTO;
import com.trs.police.fight.mapper.FightCompositeMessageMapper;
import com.trs.police.fight.mapper.FightCompositeMessageUserRelationMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.trs.police.fight.constant.FightCompositeConstant.FIGHT_COMPOSITE_TYPE;

/**
 * 我的合成作战列表
 *
 * <AUTHOR>
 * @date 2022/4/13 14:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MyCompositeListVO extends CompositeInfoVO implements Serializable {

    private static final long serialVersionUID = -4297522673946609339L;

    private static final String USER_ID_PATTERN = "(?<=(@\\{))\\d*(?=})";
    /**
     * 合成id
     */
    private Long id;
    /**
     * 合成类别
     */
    private String compositionType;
    /**
     * 创建合成时间
     */
    private LocalDateTime createTime;
    /**
     * 要求
     */
    private String require;
    /**
     * 最后一条消息id
     */
    private Long lastMessageId;

    /**
     * 最新消息
     */
    private WebsocketMessageVO latestMsg;

    /**
     * 最后一条消息发送时间
     */
    private LocalDateTime lastMessageSendTime;
    /**
     * 最后一条消息内容
     */
    private String lastMessageContent;
    /**
     * 是否有人@我
     */
    private Boolean atMe;
    /**
     * 未读消息数
     */
    private Long unreadCount;
    /**
     * 最早一条未读消息id
     */
    private Long earliestUnreadMessageId;
    /**
     * at我的消息列表
     */
    private List<Long> atMessageIds;

    /**
     * 作战类别 0 合成作战 1 突出
     */
    private Integer compositeType;

    /**
     * 处突类别id
     */
    private String urgentTypeId;

    private String urgentTypeName;

    /**
     * 预案id
     */
    private Long planId;

    /**
     * 预案名称
     */
    private String planName;

    /**
     * 预警详情
     */
    private String warningInfo;

    /**
     * 盯办状态，0：未盯办，1：已盯办
     */
    private Integer watchFlag;

    /**
     * 警情详情
     */
    private String policeIntelligenceInfo;

    /**
     * 最新消息发送时间
     */
    private LocalDateTime lastMsgSendTime;

    /**
     * 搜索词
     */
    private String searchValue;

    /**
     * 事发地点
     */
    private String eventPlace;

    /**
     * 警员范围
     */
    private Double jinYuanRange;

    /**
     * 警车范围
     */
    private Double jinCheRange;

    /**
     * 感知源范围
     */
    private Double sourceRange;

    /**
     * 数据来源
     */
    private Integer dataSource;

    /**
     * 范围
     */
    private List<GeometryVO> geometries;

    /**
     * 表单类型 默认：default_from  省厅情指默认：st_qz_default
     */
    private String formType;

    /**
     * 警情编号
     */
    private String jqbh;

    /**
     * 警情类别
     */
    private String jqlxdm;

    /**
     * 警情类别(明文)
     */
    private String jqlxdmmc;

    /**
     * 简介
     */
    private String jqContent;

    /**
     * 作战目标
     */
    private String target;

    /**
     * 作战要素
     */
    private String element;

    /**
     * 类别
     */
    private Long type;

    /**
     * 子类别
     */
    private Long subType;

    /**
     * 实体类转vo
     *
     * @param fightComposite 实体类
     * @param userId         用户id
     * @return vo
     */
    @Deprecated
    public static MyCompositeListVO of(FightComposite fightComposite, Long userId) {
        final MyCompositeListVO vo = new MyCompositeListVO();
        BeanUtil.copyPropertiesIgnoreNull(fightComposite, vo);

        final DictService dictService = BeanUtil.getBean(DictService.class);
        DictDto type = dictService.getDictByTypeAndCode(FIGHT_COMPOSITE_TYPE, fightComposite.getType());
        DictDto subType =
                Objects.nonNull(fightComposite.getSubType()) ? dictService.getDictByTypeAndCode(FIGHT_COMPOSITE_TYPE,
                        fightComposite.getSubType()) : null;
        vo.setCompositionType(type.getName() + (Objects.nonNull(subType) ? '-' + subType.getName() : ""));
        vo.setCreateTime(fightComposite.getCreateTime());
        vo.setWatchFlag(fightComposite.getWatchFlag());
        final FightCompositeMessageMapper messageMapper = BeanUtil.getBean(FightCompositeMessageMapper.class);
        final FightCompositeMessage fightCompositeMessage = messageMapper.findLastMessageSendTime(
                fightComposite.getId()).orElse(null);
        if (fightCompositeMessage == null) {
            vo.setLastMessageSendTime(fightComposite.getCreateTime());
            vo.setLastMessageId(null);
            vo.setLastMessageContent("");
        } else {
            vo.setLastMessageSendTime(fightCompositeMessage.getSendTime());
            vo.setLastMessageId(fightCompositeMessage.getId());
            vo.setLastMessageContent(vo.patternString(
                    StringUtils.isNotBlank(fightCompositeMessage.getMessage().getMessage().getSimpleContent())
                            ? fightCompositeMessage.getMessage().getMessage().getSimpleContent()
                            : fightCompositeMessage.getMessage().getMessage().getContent()));

        }
        final FightCompositeMessageUserRelationMapper messageUserRelationMapper = BeanUtil.getBean(
                FightCompositeMessageUserRelationMapper.class);
        final List<FightCompositeMessageUserRelation> unreadMessage = messageUserRelationMapper.findUnreadMessageListByUserIdAndCompositeId(
                fightComposite.getId(), userId);
        unreadMessage.sort(Comparator.comparing(FightCompositeMessageUserRelation::getCreateTime));
        vo.setUnreadCount((long) unreadMessage.size());
        vo.setEarliestUnreadMessageId(unreadMessage.isEmpty() ? null : unreadMessage.get(0).getMessageId());

        final List<Long> atMeList = unreadMessage.stream().filter(FightCompositeMessageUserRelation::getIsAt)
                .map(FightCompositeMessageUserRelation::getMessageId).collect(Collectors.toList());
        vo.setAtMe(!atMeList.isEmpty());
        vo.setAtMessageIds(atMeList);
        final CompositeInfoVO compositeInfoVO = CompositeInfoVO.toVO(fightComposite);
        BeanUtil.copyPropertiesIgnoreNull(compositeInfoVO, vo);
        return vo;
    }

    /**
     * 实体类转vo
     *
     * @param fightComposite          合成
     * @param compositeItemSearchDTO compositeItemSearchDTO
     * @return MyCompositeListVO
     */
    public static MyCompositeListVO of(FightComposite fightComposite, CompositeItemSearchDTO compositeItemSearchDTO) {
        final MyCompositeListVO vo = new MyCompositeListVO();

        BeanUtil.copyPropertiesIgnoreNull(fightComposite, vo);

        Map<Long, DictDto> typeAndDict = compositeItemSearchDTO.getCodeAndDictMap();
        Optional<DictDto> type = Optional.ofNullable(typeAndDict.get(fightComposite.getType()));
        Optional<DictDto> subType = Objects.nonNull(fightComposite.getSubType())
                ? Optional.ofNullable(typeAndDict.get(fightComposite.getSubType()))
                : Optional.empty();
        String typeName = String.format(
                "%s%s",
                type.map(DictDto::getName).orElse(""),
                subType.map(DictDto::getName).map(s -> "-" + s).orElse("")
        );
        vo.setCompositionType(typeName);
        vo.setCreateTime(fightComposite.getCreateTime());

        vo.setPoliceIntelligenceInfo(fightComposite.getJqInfo());
        Optional.ofNullable(compositeItemSearchDTO.getJqlxMcMap())
                .ifPresent(m -> vo.setJqlxdmmc(m.get(vo.getJqlxdm())));

        final FightCompositeMessage fightCompositeMessage = compositeItemSearchDTO.getCompositeIdAndLastMessageMap().get(fightComposite.getId());
        if (fightCompositeMessage == null) {
            vo.setLastMessageSendTime(fightComposite.getCreateTime());
            vo.setLastMessageId(null);
            vo.setLastMessageContent("");
        } else {
            vo.setLastMessageSendTime(fightCompositeMessage.getSendTime());
            vo.setLastMessageId(fightCompositeMessage.getId());
            vo.setLastMessageContent(vo.patternString(
                    StringUtils.isNotBlank(fightCompositeMessage.getMessage().getMessage().getSimpleContent())
                            ? fightCompositeMessage.getMessage().getMessage().getSimpleContent()
                            : fightCompositeMessage.getMessage().getMessage().getContent()));
            WebsocketMessageVO message = fightCompositeMessage.getMessage();
            message.setId(fightComposite.getId());
            vo.setLatestMsg(message);
        }
        final List<FightCompositeMessageUserRelation> unreadMessage = compositeItemSearchDTO.getCompositeIdAndUnreadMessageUserRelationMap().get(fightComposite.getId());
        if (!CollectionUtils.isEmpty(unreadMessage)) {
            unreadMessage.sort(Comparator.comparing(FightCompositeMessageUserRelation::getCreateTime));
            vo.setUnreadCount((long) unreadMessage.size());
            vo.setEarliestUnreadMessageId(unreadMessage.isEmpty() ? null : unreadMessage.get(0).getMessageId());
            final List<Long> atMeList = unreadMessage.stream().filter(FightCompositeMessageUserRelation::getIsAt)
                    .map(FightCompositeMessageUserRelation::getMessageId).collect(Collectors.toList());
            vo.setAtMe(!atMeList.isEmpty());
            vo.setAtMessageIds(atMeList);
        }
        final CompositeInfoVO compositeInfoVO = CompositeInfoVO.toVO(fightComposite, compositeItemSearchDTO);
        BeanUtil.copyPropertiesIgnoreNull(compositeInfoVO, vo);
        return vo;
    }

    private String patternString(String content) {
        if (StringUtils.isNotBlank(content)) {
            PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
            Matcher result = Pattern.compile(USER_ID_PATTERN).matcher(content);
            while (result.find()) {
                String userId = result.group();
                String regex = "@\\{(" + userId + ")}";
                content = content.replaceAll(regex,
                        "@" + permissionService.getUserById(Long.valueOf(userId)).getRealName());
            }
            return content;
        } else {
            return "";
        }

    }
}
