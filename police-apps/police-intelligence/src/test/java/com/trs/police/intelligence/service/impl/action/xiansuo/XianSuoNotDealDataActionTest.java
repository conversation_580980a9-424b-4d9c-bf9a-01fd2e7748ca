package com.trs.police.intelligence.service.impl.action.xiansuo;

import com.trs.common.exception.ServiceException;
import com.trs.police.intelligence.PoliceIntelligenceApplication;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.dto.DataActionDTO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceIntelligenceApplication.class)
class XianSuoNotDealDataActionTest {

    @Autowired
    private XianSuoNotDealDataAction action;

    private DataActionDTO dto;

    @BeforeEach
    void setUp() {
        dto = new DataActionDTO();
        dto.setActionEnum(ActionEnum.XIANSUO_NOT_DEAL_DATE);
        dto.setDataId(6L);
    }

    @AfterEach
    void tearDown() {
        dto = null;
    }

    @Test
    void doAction() throws ServiceException {
        action.doAction(dto);
    }

    @Test
    void actionEnum() {
    }

    @Test
    void mergeOneR() {
    }

    @Test
    void check() {
    }

    @Test
    void doOneAction() {
    }

    @Test
    void makeLogContent() {
    }
}