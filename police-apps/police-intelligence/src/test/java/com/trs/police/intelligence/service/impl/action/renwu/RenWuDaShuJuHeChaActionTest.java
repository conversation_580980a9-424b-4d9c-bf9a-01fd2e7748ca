package com.trs.police.intelligence.service.impl.action.renwu;

import com.trs.common.exception.ServiceException;
import com.trs.police.intelligence.PoliceIntelligenceApplication;
import com.trs.police.intelligence.dto.RenWuDaShuJuHeChaActionDTO;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceIntelligenceApplication.class)
class RenWuDaShuJuHeChaActionTest extends BaseTestCase {

    @Autowired
    private RenWuDaShuJuHeChaAction action;

    @Test
    void doAction() throws ServiceException {
        RenWuDaShuJuHeChaActionDTO dto = new RenWuDaShuJuHeChaActionDTO();
        dto.setDataId(20L);
        dto.setPersonId(17L);
        print(action.doAction(dto));
    }

}