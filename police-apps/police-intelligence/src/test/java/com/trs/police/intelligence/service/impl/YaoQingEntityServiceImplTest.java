package com.trs.police.intelligence.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.intelligence.PoliceIntelligenceApplication;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.ExportDTO;
import com.trs.police.intelligence.utils.FieldUtils;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.stream.Collectors;

@SpringBootTest(classes = PoliceIntelligenceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class YaoQingEntityServiceImplTest extends BaseTestCase {

    @Resource
    @InjectMocks
    private YaoQingEntityServiceImpl service;

    @Test
    void makeAttributesFilter() throws ServiceException {
        ListParamsRequest request = JSON.parseObject(
                "{\"pageParams\":{\"pageNumber\":1,\"pageSize\":10},\"searchParams\":{\"searchField\":\"dataTitle\",\"searchValue\":\"\"},\"filterParams\":[{\"key\":\"cr_time\",\"value\":{\"range\":\"0\",\"beginTime\":null,\"endTime\":null},\"type\":\"timeParams\"},{\"key\":\"data_type\",\"type\":\"string\",\"value\":\"zdasj\"},{\"key\":\"data_class\",\"type\":\"string\",\"value\":\"jjsgl\"},{\"key\":\"attributes.happenedTime\",\"value\":{\"range\":\"0\",\"beginTime\":null,\"endTime\":null},\"type\":\"timeParams\"},{\"key\":\"attributes.carHpzl\",\"value\":\"摩托车\",\"type\":\"string\"}]}",
                ListParamsRequest.class
        );
        service.queryList(Constants.DOWN, request);
        var map = request.getFilterParams()
                .stream()
                .collect(Collectors.groupingBy(KeyValueTypeVO::getKey));
        String dataType = Optional.ofNullable(map.get(Constants.DATA_TYPE))
                .map(FieldUtils::parseValueFromFilter)
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        String dataClass = Optional.ofNullable(map.get(Constants.DATA_CLASS))
                .map(FieldUtils::parseValueFromFilter)
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        service.makeAttributesFilter(dataType, dataClass, request);
    }


    @Test
    void export() throws Exception {
        ExportDTO export = new ExportDTO();
        export.setTemplateType(Constants.XXKB);
        export.setDataId(229L);
        var t = service.export(export);
        System.out.println(String.format("%s\n%s", t._1, t._2));

        export.setTemplateType(Constants.DZZB);
        service.export(export);
        System.out.println(String.format("%s\n%s", t._1, t._2));
    }
}