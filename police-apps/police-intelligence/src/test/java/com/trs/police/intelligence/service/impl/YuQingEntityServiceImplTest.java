package com.trs.police.intelligence.service.impl;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.intelligence.PoliceIntelligenceApplication;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.EntityDetailDTO;
import com.trs.police.intelligence.dto.YuQingSaveDTO;
import com.trs.police.intelligence.dto.YuQingVersionDataDTO;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceIntelligenceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class YuQingEntityServiceImplTest extends BaseTestCase {
    @Autowired
    private YuQingEntityServiceImpl service;

    @Test
    void getOrderNo() throws ServiceException {
        System.out.println(service.getMaxNo(Constants.YUQING));
    }

    @Test
    void saveOrUpdate() throws Exception {
        YuQingSaveDTO dto = new YuQingSaveDTO();
        dto.setDataId(0L);
        dto.setDataTitle("测试保存1111");
        dto.setDataContent("<ul><li>测试保存的内容321</li></ul>");
        dto.setDataType(Constants.YUQING);
        dto.setDataClass(Constants.DEFAULT);
        dto.setUrl("http://www.qq.com");
        dto.setYqlxId(48L);
        dto.setYqly("巡查发现");
        dto.setYqdj("一般");
        dto.setSjdyCode("510500000000");
        dto.setYpyj("测试研判意见");
        dto.setAcceptDeptIds("3");
        service.saveOrUpdate(dto);
    }

    @Test
    void detail() throws Exception {
        service.setPermissionService(permissionService);
        EntityDetailDTO dto = new EntityDetailDTO();
        dto.setDataId(2L);
        dto.setFrom("all");
        var vo = service.detail(dto);
        print(vo);
    }

    @Test
    void queryList() throws Exception {
        service.setPermissionService(permissionService);
        ListParamsRequest request = new ListParamsRequest(PageParams.getDefaultPageParams());
        print(service.queryList(Constants.ALL, request));
        request = new ListParamsRequest(new PageParams(2, 1));
        print(service.queryList(Constants.ALL, request));
    }

    @Test
    void versionData() {
        service.setPermissionService(permissionService);
        YuQingVersionDataDTO dto = new YuQingVersionDataDTO();
        dto.setDataId(1L);
        var d = service.versionData(dto);
        print(d);
        dto.setPageSize(1);
        d = service.versionData(dto);
        print(d);
    }
}