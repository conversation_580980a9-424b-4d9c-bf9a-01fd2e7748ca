package com.trs.police.intelligence.mgr;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.vo.PersonLiveInfoVO;
import com.trs.police.intelligence.dto.RelatedPersonSaveDTO;
import com.trs.police.intelligence.dto.RenWuRelatedPersonSaveDTO;
import com.trs.police.intelligence.dto.third.PullDataDTO;
import com.trs.police.intelligence.entity.*;
import com.trs.police.intelligence.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YaoQingEntityConvertToVo
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 16:30
 * @since 1.0
 */
@Mapper(componentModel = "spring", imports = {StringUtils.class})
public interface EntityConvertToVo {

    EntityConvertToVo INSTANCE = Mappers.getMapper(EntityConvertToVo.class);

    /**
     * voToEntity<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 16:20
     */
    @Mapping(
            target = "cp", ignore = true
    )
    @Mapping(
            target = "labels", ignore = true
    )
    @Mapping(
            target = "thxx", ignore = true
    )
    @Mapping(
            target = "lxfs", ignore = true
    )
    RenWuDaShuJuHeChaEntity voToEntity(RenWuDaShuJuHeChaVo vo);


    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:32
     */
    @Mapping(
            target = "cp", ignore = true
    )
    @Mapping(
            target = "labels", ignore = true
    )
    @Mapping(
            target = "thxx", ignore = true
    )
    @Mapping(
            target = "lxfs", ignore = true
    )
    RenWuDaShuJuHeChaVo entityToVo(RenWuDaShuJuHeChaEntity entity);


    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:32
     */
    YaoQingEntityVo entityToVo(YaoQingBaseInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:32
     */
    @Mapping(
            target = "yqlxId", ignore = true
    )
    YuQingEntityVo entityToVo(YuQingBaseInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:32
     */
    @Mapping(
            target = "feedbackInfo", ignore = true
    )
    @Mapping(
            target = "signInfo", ignore = true
    )
    @Mapping(
            target = "acceptInfo", ignore = true
    )
    @Mapping(
            target = "feedbackLimitTime", source = "feedbackTimeLimit"
    )
    ZhiLingEntityVo entityToVo(ZhiLingBaseInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:32
     */
    @Mapping(
            target = "feedbackInfo", ignore = true
    )
    @Mapping(
            target = "signInfo", ignore = true
    )
    @Mapping(
            target = "acceptInfo", ignore = true
    )
    @Mapping(
            target = "showName", expression = "java(entity.makeShowName())"
    )
    @Mapping(
            target = "feedbackLimitTime", source = "feedbackTimeLimit"
    )
    TongZhiTongBaoEntityVo entityToVo(TongZhiTongBaoBaseInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/7 17:05
     */
    AreaVO entityToVo(AreaEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:32
     */
    @Mapping(
            target = "czyqList", expression = "java(StringUtils.getList(entity.getCzyq(), true))"
    )
    XianSuoEntityVo entityToVo(XianSuoBaseInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:32
     */
    RenWuEntityVo entityToVo(RenWuBaseInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 16:32
     */
    @Mapping(
            source = "buKongStatusNew", target = "buKongStatus"
    )
    XianSuoRelatedPersonVo entityToVo(XianSuoRelatedPersonEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/10 14:36
     */
    XianSuoPersonLibraryVo entityToVo(XianSuoPersonLibraryEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/11 18:17
     */
    PreviousVersionVo entityToVo(ZhiLingVersionInfoEntity entity);

    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/12 16:18
     */
    RenWuRelatedPersonVo entityToVo(RenWuRelatedPersonEntity entity);


    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR>
     * @date 创建时间：2024/6/17 18:17
     */
    PreviousVersionVo entityToVo(TongZhiTongBaoVersionInfoEntity entity);


    /**
     * entityToVo<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR>
     * @date 创建时间：2024/6/17 18:17
     */
    @Mapping(
            target = "versionTitle", source = "versionDataTitle"
    )
    @Mapping(
            target = "versionContent", source = "versionDataContent"
    )
    @Mapping(
            target = "versionCrUser", source = "crUser"
    )
    @Mapping(
            target = "versionCrTime", source = "crTime"
    )
    @Mapping(
            target = "versionUpdateUser", source = "updateUser"
    )
    @Mapping(
            target = "lastVersionTime", source = "updateTime"
    )
    YuQingVersionDataVo entityToVo(YuQingVersionInfoEntity entity);

    /**
     * dtoToEntity<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/8 14:09
     */
    XianSuoRelatedPersonEntity dtoToEntity(RelatedPersonSaveDTO dto);

    /**
     * dtoToEntity<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/12 13:45
     */
    RenWuRelatedPersonEntity dtoToEntity(RenWuRelatedPersonSaveDTO dto);

    /**
     * entityToDto<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/12 13:45
     */
    RenWuRelatedPersonSaveDTO entityToDto(RenWuRelatedPersonEntity entity);

    /**
     * entityToDto<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/8 14:09
     */
    RelatedPersonSaveDTO entityToDto(XianSuoRelatedPersonEntity entity);

    /**
     * voToVo<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 17:52
     */
    XianSuoEntityWithRepeatPersonVo voToVo(XianSuoEntityVo vo);

    /**
     * voToVo<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/14 15:47
     */
    @Mappings({
            @Mapping(source = "xzd", target = "jzdDzxz"),
            @Mapping(source = "hjd", target = "hjdDzxz"),
            @Mapping(target = "lxdh", expression = "java(String.join(\",\", vo.getLxfs()))"),
            @Mapping(target = "xbdm", expression = "java(com.trs.police.common.core.constant.SchemaCodeMapping.getCode(com.trs.police.common.core.constant.SchemaCodeMapping.SCHEMA_XB, vo.getXb()).map(Object::toString).orElse(null))"),
            @Mapping(target = "mzdm", expression = "java(com.trs.police.common.core.constant.SchemaCodeMapping.getCode(com.trs.police.common.core.constant.SchemaCodeMapping.SCHEMA_MZ, vo.getMz()).map(Object::toString).orElse(null))"),
            @Mapping(target = "zzmmdm", expression = "java(com.trs.police.common.core.constant.SchemaCodeMapping.getCode(com.trs.police.common.core.constant.SchemaCodeMapping.SCHEMA_ZZMM, vo.getZzmm()).map(Object::toString).orElse(null))"),
            @Mapping(target = "hyztdm", expression = "java(com.trs.police.common.core.constant.SchemaCodeMapping.getCode(com.trs.police.common.core.constant.SchemaCodeMapping.SCHEMA_HYZT, vo.getHyzt()).map(Object::toString).orElse(null))")
    })
    PersonLiveInfoVO voToVo(RenWuDaShuJuHeChaVo vo);

    /**
     * entityToEntity<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 23:59
     */
    @Mapping(
            target = "dataId", ignore = true
    )
    XianSuoPersonLibraryEntity entityToEntity(XianSuoRelatedPersonEntity vo);

    /**
     * clone<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 10:50
     */
    ActionLogEntity clone(ActionLogEntity entity);

    /**
     * clone<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/22 19:26
     */
    PullDataDTO clone(PullDataDTO entity);

}
