package com.trs.police.intelligence.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.intelligence.entity.YuQingBaseInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collections;
import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YuQingBaseInfoMapper
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/17 16:27
 * @since 1.0
 */
@Mapper
public interface YuQingBaseInfoMapper extends BaseEntityMapper<YuQingBaseInfoEntity> {

    /**
     * getMaxNo<BR>
     *
     * @param dataYear 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/11 16:29
     */
    Integer getMaxNo(
            @Param("dataYear") Integer dataYear
    );

    /**
     * 查询舆情列表<BR>
     *
     * @param from         参数
     * @param user         参数
     * @param baseFilter   参数
     * @param searchParams 参数
     * @param sortParams   参数
     * @param page         参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/8 14:57
     */
    default Page<YuQingBaseInfoEntity> doPageSelect(
            @Param("from") String from,
            @Param("user") CurrentUser user,
            @Param("baseFilter") List<KeyValueTypeVO> baseFilter,
            @Param("searchParams") SearchParams searchParams,
            @Param("sortParams") SortParams sortParams,
            Page<YuQingBaseInfoEntity> page
    ) {
        return doPageSelect(
                from,
                user,
                Collections.emptyList(),
                baseFilter,
                null,
                Collections.emptyList(),
                searchParams,
                sortParams,
                page
        );
    }
}
