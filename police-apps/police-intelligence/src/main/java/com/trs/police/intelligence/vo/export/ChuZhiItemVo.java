package com.trs.police.intelligence.vo.export;

import com.trs.common.pojo.BaseVO;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/3 17:42
 * @since 1.0
 */
@Data
public class ChuZhiItemVo extends BaseVO {

    private String date;

    private String time;

    private String crDeptName;

    private String acceptDeptNames;

    private String userName;

    private String actionName;

    private String content;

    /**
     * of<BR>
     *
     * @param date            参数
     * @param time            参数
     * @param crDeptName      参数
     * @param acceptDeptNames 参数
     * @param userName        参数
     * @param actionName      参数
     * @param content         参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/3 18:12
     */
    public static ChuZhiItemVo of(
            String date,
            String time,
            String crDeptName,
            String acceptDeptNames,
            String userName,
            String actionName,
            String content
    ) {
        ChuZhiItemVo vo = new ChuZhiItemVo();
        vo.date = date;
        vo.time = time;
        vo.crDeptName = crDeptName;
        vo.acceptDeptNames = acceptDeptNames;
        vo.userName = userName;
        vo.actionName = actionName;
        vo.content = content;
        return vo;
    }
}
