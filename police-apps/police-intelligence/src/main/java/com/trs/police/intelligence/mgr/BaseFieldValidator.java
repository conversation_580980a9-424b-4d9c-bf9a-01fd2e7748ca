package com.trs.police.intelligence.mgr;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.web.builder.util.KeyMgrFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/26 19:18
 * @since 1.0
 */
@Slf4j
public abstract class BaseFieldValidator implements IFieldValidator<String> {

    @Override
    public String desc() {
        return key() + "校验器";
    }

    /**
     * 空<BR>
     *
     * @param value 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/26 14:04
     */
    @Override
    public boolean isEmpty(String value) {
        return StringUtils.isEmpty(value);
    }

    /**
     * 合法<BR>
     *
     * @param value 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/26 14:04
     */
    @Override
    public boolean isValid(String value) {
        return true;
    }

    /**
     * 非法<BR>
     *
     * @param value 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/26 14:04
     */
    @Override
    final public boolean isNotValid(String value) {
        return !isValid(value);
    }

    /**
     * 非空<BR>
     *
     * @param value 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/26 19:29
     */
    @Override
    final public boolean isNotEmpty(String value) {
        return !isEmpty(value);
    }

    /**
     * findByType<BR>
     *
     * @param type 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 19:31
     */
    public static BaseFieldValidator findByType(String type) {
        try {
            var convert = KeyMgrFactory.findMgrByKey(BaseFieldValidator.class, type);
            if (convert != null) {
                log.info("根据[{}]加载了校验器[{}]", type, convert.getClass().getName());
            } else {
                convert = KeyMgrFactory.findMgrByKey(BaseFieldValidator.class, "common", true);
            }
            return convert;
        } catch (ServiceException e) {
            log.error("根据Type=[{}]获取校验器异常", type, e);
            throw new RuntimeException(e);
        }
    }
}
