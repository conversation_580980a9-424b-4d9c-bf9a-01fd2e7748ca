package com.trs.police.intelligence.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.http2.HttpRequest;
import com.trs.common.http2.util.HttpTools;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.intelligence.ChePaiVo;
import com.trs.police.intelligence.vo.third.RenWuDaShuJuHeChaTokenVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/10/30 16:00
 * @since 1.0
 */
@Slf4j
public class RenWuDaShuJuHeChaUtils {

    private static final HttpRequest HTTP_REQUEST = new HttpRequest.Builder().build();
    private static RenWuDaShuJuHeChaTokenVo token;

    /**
     * convertTime<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 19:06
     */
    public static Function<String, String> convertTime() {
        return convertTime(TimeUtils.YYYYMMDD_HHMMSS);
    }

    /**
     * convertTime<BR>
     *
     * @param timeFormat 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 19:06
     */
    public static Function<String, String> convertTime(String timeFormat) {
        return it -> {
            if (StringUtils.isLong(it) && it.length() >= 8) {
                if (it.length() > 8) {
                    // 时间戳
                    return TimeUtils.stringToString(Objects.toString(Long.parseLong(it) * 1000L), timeFormat);
                } else {
                    // 日期
                    return TimeUtils.dateToString(TimeUtils.stringToDate(it, TimeUtils.YYYYMMDD5), timeFormat);
                }
            } else if (TimeUtils.isValid(it)) {
                // 其他日期格式
                return TimeUtils.stringToString(it, timeFormat);
            }
            return it;
        };
    }

    /**
     * addInfo<BR>
     *
     * @param it       参数
     * @param key      参数
     * @param consumer 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 15:07
     */
    public static void addInfo(JSONObject it, String key, Consumer<String> consumer) {
        addInfo(it, key, i -> i, consumer);
    }

    /**
     * addInfo<BR>
     *
     * @param it       参数
     * @param key      参数
     * @param convert  参数
     * @param consumer 参数
     * @param <T>      参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:31
     */
    public static <T> void addInfo(JSONObject it, String key, Function<String, T> convert, Consumer<T> consumer) {
        Objects.requireNonNull(convert);
        Objects.requireNonNull(consumer);
        String value = it.getString(key);
        if (StringUtils.isEmpty(value)) {
            for (String s : it.keySet()) {
                if (key.equalsIgnoreCase(s)) {
                    value = it.getString(s);
                }
            }
        }
        if (StringUtils.isNotEmpty(value) && !Objects.equals("null", value)) {
            consumer.accept(convert.apply(value));
        }
    }

    /**
     * getToken<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:12
     */
    private static RenWuDaShuJuHeChaTokenVo getToken() throws ServiceException {
        if (Objects.isNull(token) || token.isExpired()) {
            synchronized (RenWuDaShuJuHeChaUtils.class) {
                if (Objects.isNull(token) || token.isExpired()) {
                    token = makeToken();
                }
            }
        }
        return token;
    }

    /**
     * makePoliceNo<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:34
     */
    private static String makePoliceNo() throws ServiceException {
        String info = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.policeNo");
        checkNotEmpty(info, new ParamInvalidException("未配置policeNo"));
        return info;
    }

    /**
     * makePoliceNo<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:34
     */
    private static String makeUserId() throws ServiceException {
        String info = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.userId");
        checkNotEmpty(info, new ParamInvalidException("未配置userId"));
        return info;
    }

    /**
     * makePoliceNo<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:34
     */
    private static String makeAppId() throws ServiceException {
        String info = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.appId");
        checkNotEmpty(info, new ParamInvalidException("未配置appId"));
        return info;
    }

    /**
     * makePoliceNo<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:34
     */
    private static String makeSubId() throws ServiceException {
        String info = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.subId");
        checkNotEmpty(info, new ParamInvalidException("未配置subId"));
        return info;
    }

    /**
     * makePoliceNo<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:34
     */
    private static String makeSenderId() throws ServiceException {
        String info = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.senderId");
        checkNotEmpty(info, new ParamInvalidException("未配置senderId"));
        return info;
    }

    /**
     * makePoliceNo<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:34
     */
    private static String makeGroupId() throws ServiceException {
        String info = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.groupId");
        checkNotEmpty(info, new ParamInvalidException("未配置groupId"));
        return info;
    }

    /**
     * makeAreaCode<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:58
     */
    private static String makeAreaCode() throws ServiceException {
        String info = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.areaCode");
        checkNotEmpty(info, new ParamInvalidException("未配置areaCode"));
        return info;
    }

    /**
     * makePoliceNo<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:34
     */
    private static String makeRequestUserToken() throws ServiceException {
        JSONObject object = new JSONObject();
        object.put("type", "user");
        object.put("policeNo", makePoliceNo());
        object.put("userId", makeUserId());
        return object.toJSONString();
    }

    /**
     * makePoliceNo<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:34
     */
    private static String makeRequestAppToken() throws ServiceException {
        JSONObject object = new JSONObject();
        object.put("appId", makeAppId());
        object.put("userToken", makeRequestUserToken());
        return object.toJSONString();
    }

    /**
     * makeCommonHeaders<BR>
     *
     * @param serviceId 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:41
     */
    private static Headers.Builder makeCommonHeaders(String serviceId) throws ServiceException {
        var token = getToken();
        return new Headers.Builder()
                .add("userToken", token.getUserToken())
                .add("appToken", token.getAppToken())
                .add("userInfo", JsonUtil.toJsonString(Map.of(
                        "userId", makeUserId(),
                        "ip", "127.0.0.1",
                        "mac", "8C-xx-xx-xx-xx-7D"
                )))
                .add("subId", makeSubId())
                .add("serviceId", serviceId)
                .add("userId", makeUserId())
                .add("senderId", makeSenderId())
                .add("groupId", makeGroupId());
    }

    /**
     * makeCommonBody<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:47
     */
    private static JSONObject makeCommonBody() throws ServiceException {
        return new JSONObject();
    }

    /**
     * makeToken<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:12
     */
    private static RenWuDaShuJuHeChaTokenVo makeToken() throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.token.url");
        checkNotEmpty(url, new ParamInvalidException("未配置token url"));
        return Try.of(() -> {
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    new FormBody.Builder()
                            .add("userToken", makeRequestUserToken())
                            .add("appToken", makeRequestAppToken())
                            .build()
            ));
            log.info("接口[{}]响应为:{}", url, json);
            if (!json.containsKey("appTokenId") || !json.containsKey("userTokenId")) {
                throw new ServiceException(StringUtils.showEmpty(json.getString("error"), "未知异常"));
            }
            RenWuDaShuJuHeChaTokenVo vo = new RenWuDaShuJuHeChaTokenVo();
            vo.setAppToken(JSON.parseObject(json.getString("appTokenId")).getString("appToken"));
            vo.setUserToken(JSON.parseObject(json.getString("userTokenId")).getString("userToken"));
            return vo;
        }).getOrElseThrow(e -> new ServiceException("获取Token异常", e));
    }

    /**
     * szqtryxxcx<BR>
     *
     * @param zjhm 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 17:56
     */
    public static JSONArray szqtryxxcx(String zjhm) throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.szqtryxxcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.szqtryxxcx.serviceId")
            ).build();
            var req = makeCommonBody();
            req.put("messageSequence", UUID.randomUUID().toString());
            req.put("ZJHM", zjhm);
            req.put("maxReturnNum", 100);
            req.put("parafs", List.of("SACYLXDM", "XM", "LXDH", "ZJHM"));
            req.put("orderParafs", new JSONObject());
            req.put("areaCode", makeAreaCode());
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("szqtryxxcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            if (Objects.equals(200, json.getInteger("status"))) {
                JSONObject data = json.getJSONObject("data");
                return data.getJSONArray("results");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }

    /**
     * scsryztkxxcx<BR>
     *
     * @param zjhm 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 18:59
     */
    public static JSONArray scsryztkxxcx(String zjhm) throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsryztkxxcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsryztkxxcx.serviceId")
            ).build();
            var req = makeCommonBody();
            req.put("messageSequence", UUID.randomUUID().toString());
            req.put("condition", String.format("( IDNO = '%s')", zjhm));
            req.put("maxReturnNum", 1);
            req.put("parafs", List.of(
                    "PROF",
                    "SEX",
                    "BIRTHDAY",
                    "BPLACE",
                    "HPLACE",
                    "NATION",
                    "VEH",
                    "EDUDEGREE",
                    "MARR",
                    "POLI",
                    "RELI",
                    "ESCU",
                    "DOMPLACE",
                    "HOMEADDR",
                    "WORKADDR",
                    "SERVICEPLACE",
                    "MOBILE",
                    "IDNO",
                    "CHNAME",
                    "HPLACEAREA"
            ));
            req.put("orderParafs", new JSONObject());
            req.put("areaCode", makeAreaCode());
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("scsryztkxxcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            if (Objects.equals(200, json.getInteger("status"))) {
                JSONObject data = json.getJSONObject("data");
                return data.getJSONArray("results");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }

    /**
     * scsjdcjcxxcx<BR>
     *
     * @param zjhm 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 18:59
     */
    public static JSONArray scsjdcjcxxcx(String zjhm) throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsjdcjcxxcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsjdcjcxxcx.serviceId")
            ).build();
            var req = makeCommonBody();
            req.put("messageSequence", UUID.randomUUID().toString());
            req.put("condition", String.format("( CRED_NUM = '%s')", zjhm));
            req.put("maxReturnNum", 100);
            req.put("parafs", List.of(
                    "INS_END_DATE",
                    "CRED_NUM",
                    "MOB",
                    "VEH_LIC_TNAME",
                    "VEH_PLATE_NUM",
                    "VEH_TYPE",
                    "VEH_COLOR_NAME",
                    "ENGINE_NO",
                    "VEH_STAT_CODE",
                    "VEH_STAT_NAME",
                    "INSP_VE_DATE",
                    "FIRST_REG_DATE",
                    "VEH_TNAME",
                    "VEH_LIC_TCODE"
            ));
            req.put("orderParafs", new JSONObject());
            req.put("areaCode", makeAreaCode());
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("scsjdcjcxxcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            if (Objects.equals(200, json.getInteger("status"))) {
                JSONObject data = json.getJSONObject("data");
                return data.getJSONArray("results");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }

    /**
     * scsxdryjbxxcx<BR>
     *
     * @param zjhm 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 18:59
     */
    public static JSONArray scsxdryjbxxcx(String zjhm) throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsxdryjbxxcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsxdryjbxxcx.serviceId")
            ).build();
            var req = makeCommonBody();
            req.put("messageSequence", UUID.randomUUID().toString());
            req.put("SFZHM18", zjhm);
            req.put("XM", "");
            req.put("XBDM", "");
            req.put("HJDZ_XZQHMC", "");
            req.put("maxReturnNum", 100);
            req.put("parafs", List.of(
                    "BMCH",
                    "CSRQ",
                    "HJDZ_XZQHMC",
                    "SJJZD_XZQHMC",
                    "SJJZD_GAJGMC",
                    "LRSJ",
                    "SG",
                    "HJDZ_GAJGMC",
                    "SJJZD_DZMC",
                    "LRDW_DWMC",
                    "XM",
                    "SFZHM18",
                    "HJDZ_DZMC",
                    "XBDM",
                    "MZDM"
            ));
            req.put("orderParafs", new JSONObject());
            req.put("areaCode", makeAreaCode());
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("scsxdryjbxxcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            if (Objects.equals(200, json.getInteger("status"))) {
                JSONObject data = json.getJSONObject("data");
                return data.getJSONArray("results");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }

    /**
     * scsjtwfjlcx<BR>
     *
     * @param cp 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 18:59
     */
    public static JSONArray scsjtwfjlcx(List<ChePaiVo> cp) throws ServiceException {
        if (CollectionUtils.isEmpty(cp)) {
            return new JSONArray();
        }
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsjtwfjlcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsjtwfjlcx.serviceId")
            ).build();
            var req = makeCommonBody();
            req.put("messageSequence", UUID.randomUUID().toString());
            req.put("condition", String.format("( %s )",
                    cp.stream()
                            .map(it -> String.format("(VEH_PLATE_NUM ='%s' and VEH_LIC_TNAME='%s')", it.getHphm(), it.getHpzl()))
                            .collect(Collectors.joining(" or "))
            ));
            req.put("maxReturnNum", 1000);
            req.put("parafs", List.of(
                    "VEH_PLATE_NUM",
                    "VEH_LIC_TNAME",
                    "ILLE_PLAC_ADDR_NAME",
                    "ILLE_TIME",
                    "PROC_TIME",
                    "ILLE_MAN_HAND_RCODE",
                    "VEH_LIC_TCODE"
            ));
            req.put("orderParafs", Map.of("ILLE_TIME", "desc"));
            req.put("areaCode", makeAreaCode());
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("scsjtwfjlcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            if (Objects.equals(200, json.getInteger("status"))) {
                JSONObject data = json.getJSONObject("data");
                return data.getJSONArray("results");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }

    /**
     * scsgmsfyhjgxcx<BR>
     *
     * @param zjhm 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 18:59
     */
    public static JSONArray scsgmsfyhjgxcx(String zjhm) throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsgmsfyhjgxcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsgmsfyhjgxcx.serviceId")
            ).build();
            var req = makeCommonBody();
            req.put("messageSequence", UUID.randomUUID().toString());
            req.put("condition", String.format("( CERT_NUM='%s' )", zjhm));
            req.put("maxReturnNum", 1000);
            req.put("parafs", List.of(
                    "PREDICT",
                    "CERT_NUM",
                    "DOMIC_NUM",
                    "HOU_HEAD_REL",
                    "DOMIC_ADDR",
                    "DOMIC_ADM_DIV",
                    "REL_TYPE",
                    "LAST_TIME"
            ));
            req.put("orderParafs", Map.of("LAST_TIME", "desc"));
            req.put("areaCode", makeAreaCode());
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("scsgmsfyhjgxcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            if (Objects.equals(200, json.getInteger("status"))) {
                JSONObject data = json.getJSONObject("data");
                return data.getJSONArray("results");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }

    /**
     * scsgmsfyajgxcx<BR>
     *
     * @param zjhm 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 18:59
     */
    public static JSONArray scsgmsfyajgxcx(String zjhm) throws ServiceException {
        return scsgmsfyajgxcx(zjhm, null);
    }

    /**
     * scsgmsfyajgxcx<BR>
     *
     * @param zjhm 参数
     * @param ajbh 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:41
     */
    public static JSONArray scsgmsfyajgxcx(String zjhm, String ajbh) throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsgmsfyajgxcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            final var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsgmsfyajgxcx.serviceId")
            ).build();
            final var req = makeCommonBody();
            req.put("messageSequence", UUID.randomUUID().toString());
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("( ");
            stringBuilder.append(String.format(" CERT_NUM='%s' ", zjhm));
            if (StringUtils.isNotEmpty(ajbh)) {
                stringBuilder.append(String.format(" and CASE_NO='%s' ", ajbh));
            }
            stringBuilder.append(" )");
            req.put("condition", stringBuilder.toString());
            req.put("maxReturnNum", 1000);
            req.put("parafs", List.of(
                    "CASE_NAME",
                    "CASE_TIME",
                    "NAME",
                    "CERT_NUM",
                    "CASE_NO"
            ));
            req.put("orderParafs", Map.of("CASE_TIME", "desc"));
            req.put("areaCode", makeAreaCode());
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("scsgmsfyajgxcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            if (Objects.equals(200, json.getInteger("status"))) {
                JSONObject data = json.getJSONObject("data");
                return data.getJSONArray("results");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }

    /**
     * scsajjbxxcx<BR>
     *
     * @param caseNo 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 18:59
     */
    public static JSONArray scsajjbxxcx(String caseNo) throws ServiceException {
        if (StringUtils.isEmpty(caseNo)) {
            return new JSONArray();
        }
        return scsajjbxxcx(List.of(caseNo));
    }

    /**
     * scsajjbxxcx<BR>
     *
     * @param caseNos 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:54
     */
    public static JSONArray scsajjbxxcx(List<String> caseNos) throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsajjbxxcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsajjbxxcx.serviceId")
            ).build();
            var req = makeCommonBody();
            req.put("messageSequence", UUID.randomUUID().toString());
            req.put("condition", String.format(
                    "( %s )",
                    caseNos.stream()
                            .map(it -> String.format("( CASE_NO ='%s' )", it))
                            .collect(Collectors.joining(" or "))
            ));
            req.put("maxReturnNum", caseNos.size());
            req.put("parafs", List.of(
                    "CASE_NO",
                    "CASE_NAME",
                    "CASE_TYPE_CODE",
                    "CASCLA_NAME",
                    "CASE_SOUR_DESC",
                    "ACCEP_UNIT_PSAG",
                    "CASE_DATE",
                    "SET_LAWS_DATE",
                    "REPO_CASE_TIME",
                    "ACCEP_TIME",
                    "DISC_TIME",
                    "CASE_ORGA_PSAG_CODE",
                    "CASE_ORGA_PSAG",
                    "CASE_CLASS_CODE"
            ));
            req.put("orderParafs", Map.of("CASE_DATE", "desc"));
            req.put("areaCode", makeAreaCode());
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("scsajjbxxcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            if (Objects.equals(200, json.getInteger("status"))) {
                JSONObject data = json.getJSONObject("data");
                return data.getJSONArray("results");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }

    /**
     * scsryjcxxcx<BR>
     *
     * @param zjhm 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/30 18:59
     */
    public static JSONArray scsryjcxxcx(String zjhm) throws ServiceException {
        String url = BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsryjcxxcx.url");
        checkNotEmpty(url, new ParamInvalidException("未配置url"));
        return Try.of(() -> {
            var headers = makeCommonHeaders(
                    BeanFactoryHolder.getEnv().getProperty("intelligence.renwu.dsjhc.scsryjcxxcx.serviceId")
            ).build();
            var req = makeCommonBody();
            req.put("idno", zjhm);
            var json = JSON.parseObject(HTTP_REQUEST.doPost(
                    url,
                    HttpTools.createJsonBody(req.toJSONString()),
                    headers
            ));
            log.info("scsryjcxxcx:接口[{}],[{}],[{}]响应为:{}", url, req, headers, json);
            Integer code;
            if (json.containsKey("code")) {
                code = json.getInteger("code");
            } else {
                code = json.getInteger("status");
            }
            if (Objects.equals(200, code)) {
                return json.getJSONArray("data");
            } else {
                throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
            }
        }).getOrElseThrow(e -> new ServiceException("获取数据异常", e));
    }
}
