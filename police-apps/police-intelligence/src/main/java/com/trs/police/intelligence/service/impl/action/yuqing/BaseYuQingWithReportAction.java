package com.trs.police.intelligence.service.impl.action.yuqing;

import com.trs.common.base.Report;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.dto.BaseActionDTO;
import com.trs.police.intelligence.service.BaseYuQingAction;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * @param <T> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/8/15 16:02
 * @since 1.0
 */
public abstract class BaseYuQingWithReportAction<T extends BaseActionDTO>
        extends BaseYuQingAction<T, Report<String>, Report<String>> {

    /**
     * mergeOneR<BR>
     *
     * @param login   参数
     * @param reports 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:36
     */
    @Override
    protected Report<String> mergeOneR(Optional<CurrentUser> login, List<Report<String>> reports) {
        return reports.stream()
                .filter(it -> it.getResult() == Report.RESULT.FAIL)
                .findAny()
                .orElse(new Report<>(desc(), "成功处理"));
    }

}
