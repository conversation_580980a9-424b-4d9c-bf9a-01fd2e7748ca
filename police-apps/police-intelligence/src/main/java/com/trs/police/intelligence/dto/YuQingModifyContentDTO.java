package com.trs.police.intelligence.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.intelligence.constant.ActionEnum;
import lombok.Data;
import lombok.ToString;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/8/15 14:56
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class YuQingModifyContentDTO extends BaseActionDTO {

    private Long versionId;

    private String dataContent;

    private String yqdj;

    public YuQingModifyContentDTO() {
        this.versionId = 0L;
        setActionEnum(ActionEnum.YUQING_ADD_EXT_INFO);
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getDataContent(), new ParamInvalidException("内容不能为空"));
        checkNotEmpty(getYqdj(), new ParamInvalidException("舆情等级不能为空"));
        return super.checkParams();
    }
}
