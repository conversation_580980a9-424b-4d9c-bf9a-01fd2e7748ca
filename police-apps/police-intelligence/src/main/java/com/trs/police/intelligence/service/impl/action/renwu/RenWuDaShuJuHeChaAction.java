package com.trs.police.intelligence.service.impl.action.renwu;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.RenWuDaShuJuHeChaActionDTO;
import com.trs.police.intelligence.entity.DataRelationMappingEntity;
import com.trs.police.intelligence.entity.RenWuBaseInfoEntity;
import com.trs.police.intelligence.entity.RenWuRelatedPersonEntity;
import com.trs.police.intelligence.mgr.RenWuDaShuJuHeChaMgr;
import com.trs.police.intelligence.service.BaseRenWuAction;
import com.trs.police.intelligence.vo.RenWuDaShuJuHeChaVo;
import com.trs.police.intelligence.vo.RenWuRelatedPersonVo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/12 18:03
 * @since 1.0
 */
@Component
@Slf4j
@Getter
public class RenWuDaShuJuHeChaAction extends BaseRenWuAction<RenWuDaShuJuHeChaActionDTO, RenWuDaShuJuHeChaVo, RenWuDaShuJuHeChaVo> {

    @Resource
    private RenWuDaShuJuHeChaMgr renWuDaShuJuHeChaMgr;

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.RENWU_DA_SHU_JU_HE_CHA;
    }

    /**
     * check<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:43
     */
    @Override
    protected void check(Optional<CurrentUser> login, RenWuBaseInfoEntity entity, RenWuDaShuJuHeChaActionDTO dto) throws ServiceException {
        super.check(login, entity, dto);
        checkArgument(
                entity.getStatusCode().equals(Status.JINXINGZHONG.getCode()),
                new ParamInvalidException("只有进行中的数据才能进行大数据核查操作")
        );
        final var map = getDataRelationMappingMgr().getRelationList(
                        module(),
                        entity.getDataId(),
                        Constants.DEPT,
                        Constants.PUSHDOWN
                ).stream()
                .collect(Collectors.toMap(
                        DataRelationMappingEntity::getObjId,
                        DataRelationMappingEntity::getStatusCode,
                        (a, b) -> a
                ));
        final var user = login.orElseThrow(() -> new ServiceException("未登录"));
        Boolean isCrUser = entity.userCreateEntity(login);
        Boolean isAccept = map.containsKey(user.getDeptId());
        Boolean isDesignKd = RenWuRelatedPersonVo.canSeeChildren(login.get());
        checkArgument(
                isCrUser || isAccept || isDesignKd,
                new ParamInvalidException("只有创建人或、指派的单位或指定警种单位才能进行大数据核查操作")
        );
        if (!isCrUser) {
            var person = entity.getRelatedPersons()
                    .stream()
                    .filter(it -> Objects.equals(it.getDataId(), dto.getPersonId()))
                    .findFirst()
                    .orElse(null);
            checkNotNull(
                    person,
                    new ServiceException("任务中没有对应人员")
            );
            var deptIds = person.makeDeptIds();
            if (!isDesignKd) {
                checkArgument(
                        Objects.equals(Status.JINXINGZHONG.getCode(), map.get(user.getDeptId())),
                        new ParamInvalidException("指派的单位签收后才能进行大数据核查操作")
                );
                checkArgument(
                        deptIds.contains(user.getDeptId()),
                        new ServiceException("用户还未被指派给当前单位，无法进行大数据核查")
                );
            }
        }
    }

    /**
     * mergeOneR<BR>
     *
     * @param login 参数
     * @param vos   参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:36
     */
    @Override
    protected RenWuDaShuJuHeChaVo mergeOneR(Optional<CurrentUser> login, List<RenWuDaShuJuHeChaVo> vos) throws ServiceException {
        if (CollectionUtils.isEmpty(vos)) {
            throw new ServiceException("获取数据出错");
        }
        return vos.get(0);
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public RenWuDaShuJuHeChaVo doOneAction(Optional<CurrentUser> login, RenWuBaseInfoEntity entity, RenWuDaShuJuHeChaActionDTO dto) throws ServiceException {
        RenWuRelatedPersonEntity person = entity.getRelatedPersons()
                .stream()
                .filter(it -> Objects.equals(it.getDataId(), dto.getPersonId()))
                .findFirst()
                .orElseThrow(() -> new ServiceException("不存在对应用户"));
        RenWuDaShuJuHeChaVo vo = renWuDaShuJuHeChaMgr.findPerson(person);
        // 记录人员日志
        recordPersonLog(login, entity, person, actionEnum().getLogType(), dto, vo);
        return vo;
    }

    /**
     * makeLogContent<BR>
     *
     * @param login   参数
     * @param entity  参数
     * @param person  参数
     * @param logType 参数
     * @param dto     参数
     * @param one     参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 23:08
     */
    @Override
    protected String makePersonLogContent(Optional<CurrentUser> login, RenWuBaseInfoEntity entity, RenWuRelatedPersonEntity person, String logType, RenWuDaShuJuHeChaActionDTO dto, RenWuDaShuJuHeChaVo one) throws ServiceException {
        return String.format(
                "通过大数据核查查询了“%s(%s)”的信息",
                person.getXm(),
                person.getZjhm()
        );
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(Optional<CurrentUser> login, RenWuBaseInfoEntity entity, RenWuDaShuJuHeChaActionDTO dto, RenWuDaShuJuHeChaVo one) {
        return String.format(
                "通过大数据核查查询了“%s(%s)”的信息",
                one.getXm(),
                one.getZjhm()
        );
    }
}
