package com.trs.police.intelligence.schedule;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.intelligence.service.impl.YaoQingEntityServiceImpl;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/13 17:58
 * @since 1.0
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "intelligence.task.markFieldsFinished", name = "enable", havingValue = "true", matchIfMissing = true)
public class YaoQingFieldsFinishedSchedule extends BaseScheduleWithUser {

    /**
     * 定时进行数据同步
     */
    @Scheduled(cron = "${intelligence.task.markFieldsFinished.cron}")
    public void markFieldsFinished() throws ServiceException {
        try {
            var enable = BeanFactoryHolder.getEnv()
                    .getProperty("intelligence.task.markFieldsFinished.enable", Boolean.class, true);
            if (!enable) {
                log.info("关闭了[要情]字段填写情况标记");
            }
            log.info("开始[要情]字段填写情况标记");
            BeanUtil.getBean(YaoQingEntityServiceImpl.class)
                    .markFieldsFinished(makeUser());
            log.info("完成[要情]字段填写情况标记");
        } catch (Exception e) {
            log.error("[要情]字段填写情况标记执行出错", e);
            throw new ServiceException(e);
        }
    }
}
