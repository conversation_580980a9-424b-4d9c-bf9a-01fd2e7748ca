package com.trs.police.intelligence.dto;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2021</p>
 * <p>Company:      www.trs.com.cn</p>
 * 地域检索DTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2021/7/21 16:59
 * @version 1.0
 * @since 1.0
 */
@Data
public class AreaSearchDTO extends BaseDTO {

    /**
     * 地域级别
     */
    private String level;

    /**
     * 别名
     */
    private String areaAlias;

    /**
     * 地名
     */
    private String areaName;

    /**
     * 父地域编码
     */
    private String parentCode;

    private Boolean searchParentShortCode;

    private Boolean returnList;

    /**
     * 地域编码
     */
    private String areaCode;

    private Boolean onlyLastNodeList;

    private String codePrefix;

    private String parentCodePrefix;

    public AreaSearchDTO() {
        this.onlyLastNodeList = false;
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        return true;
    }
}
