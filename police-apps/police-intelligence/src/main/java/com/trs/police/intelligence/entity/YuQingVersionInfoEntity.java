package com.trs.police.intelligence.entity;

import cn.hutool.http.HtmlUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.CurrentUser;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/8/13 18:08
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
@TableName("tb_intelligence_yuqing_version_info")
public class YuQingVersionInfoEntity extends BaseVersionEntity {

    @TableField
    private String versionDataClearContent;

    @TableField
    private Long updateDeptId;

    @TableField
    private String updateUser;

    @TableField
    private Date updateTime;

    /**
     * addInfoOnCreate<BR>
     *
     * @param user   参数
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 18:52
     */
    public static YuQingVersionInfoEntity addInfoOnCreate(CurrentUser user, YuQingVersionInfoEntity entity) {
        return BaseEntity.addInfoOnCreate(user, addInfoOnUpdate(user, entity));
    }

    /**
     * addInfoOnUpdate<BR>
     *
     * @param user   参数
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/13 18:12
     */
    public static YuQingVersionInfoEntity addInfoOnUpdate(CurrentUser user, YuQingVersionInfoEntity entity) {
        entity.setUpdateDeptId(user.getDeptId());
        entity.setUpdateUser(user.getUsername());
        entity.setUpdateTime(new Date());
        return entity;
    }

    /**
     * of<BR>
     *
     * @param user               参数
     * @param item               参数
     * @param isUpdate           参数
     * @param yqDataId           参数
     * @param versionFlag        参数
     * @param versionDataTitle   参数
     * @param versionDataContent 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 14:12
     */
    public static YuQingVersionInfoEntity of(
            CurrentUser user,
            YuQingVersionInfoEntity item,
            Boolean isUpdate,
            Long yqDataId,
            Integer versionFlag,
            String versionDataTitle,
            String versionDataContent
    ) {
        final YuQingVersionInfoEntity entity;
        if (isUpdate) {
            entity = YuQingVersionInfoEntity.addInfoOnUpdate(
                    user,
                    item
            );
        } else {
            entity = YuQingVersionInfoEntity.addInfoOnCreate(
                    user,
                    item
            );
        }
        entity.setYqDataId(yqDataId);
        entity.setVersionFlag(versionFlag);
        entity.setVersionDataTitle(versionDataTitle);
        entity.setVersionDataContent(versionDataContent);
        entity.setVersionDataClearContent(HtmlUtil.cleanHtmlTag(versionDataContent));
        return entity;
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
