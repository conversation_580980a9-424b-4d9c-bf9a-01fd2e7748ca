package com.trs.police.intelligence.service.impl.action.zhiling;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.ActionCollectionDataDTO;
import com.trs.police.intelligence.entity.CollectionEntity;
import com.trs.police.intelligence.entity.ZhiLingBaseInfoEntity;
import com.trs.police.intelligence.mapper.CollectionMapper;
import com.trs.police.intelligence.service.BaseZhiLingAction;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * TongZhiTongBaoCollectionDataAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/18 13:55
 * @since 1.0
 */
@Component
public class ZhiLingCollectionDataAction
        extends BaseZhiLingAction<ActionCollectionDataDTO, Report<String>, Report<String>> {

    @Resource
    private CollectionMapper collectionMapper;

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.ZHILING_COLLECTION_DATA;
    }

    /**
     * mergeOneR<BR>
     *
     * @param login   参数
     * @param reports 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:36
     */
    @Override
    protected Report<String> mergeOneR(Optional<CurrentUser> login, List<Report<String>> reports) {
        return reports.stream()
                .filter(it -> it.getResult() == RESULT.FAIL)
                .findAny()
                .orElse(new Report<>(desc(), "成功处理"));
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public Report<String> doOneAction(Optional<CurrentUser> login, ZhiLingBaseInfoEntity entity,
                                      ActionCollectionDataDTO dto) throws ServiceException {
        CollectionEntity col = Optional.ofNullable(new LambdaQueryChainWrapper<>(collectionMapper)
                .eq(CollectionEntity::getRelationType, Constants.USER)
                .eq(CollectionEntity::getRelationId, login.get().getId())
                .eq(CollectionEntity::getObjType, module())
                .eq(CollectionEntity::getObjId, entity.getDataId())
                .one()
        ).orElse(CollectionEntity.addInfoOnCreate(login.get(), new CollectionEntity()));
        if (dto.getCollectionFlag() == 1) {
            col.setIsDel(0);
            col.setRelationType(Constants.USER);
            col.setRelationId(login.get().getId());
            col.setObjType(module());
            col.setObjId(entity.getDataId());
            col.setCollectionTime(new Date());
            col.setCollectionTitle(dto.getCollectionTitle());
            if (Optional.ofNullable(col.getDataId()).filter(it -> it > 0L).isPresent()) {
                collectionMapper.updateById(col);
            } else {
                collectionMapper.insert(col);
            }
        } else {
            if (Optional.ofNullable(col.getDataId()).filter(it -> it > 0L).isPresent()) {
                col.setIsDel(1);
                collectionMapper.updateById(col);
            }
        }
        return new Report<>(desc(), "成功操作");
    }

    /**
     * needSendMessage<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:22
     */
    @Override
    protected Boolean needSendChatMessage() {
        return false;
    }

    /**
     * needRecordLog<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:22
     */
    @Override
    protected Boolean needRecordLog() {
        return false;
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(Optional<CurrentUser> login, ZhiLingBaseInfoEntity entity,
                                    ActionCollectionDataDTO dto, Report<String> one) {
        return "";
    }

    /**
     * makeMessageContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:38
     */
    @Override
    protected String makeMessageContent(Optional<CurrentUser> login, ZhiLingBaseInfoEntity entity,
                                        ActionCollectionDataDTO dto, Report<String> one) throws ServiceException {
        return "";
    }

    /**
     * makeMessageSimpleContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 17:07
     */
    @Override
    protected String makeMessageSimpleContent(Optional<CurrentUser> login, ZhiLingBaseInfoEntity entity,
                                              ActionCollectionDataDTO dto, Report<String> one) throws ServiceException {
        return "";
    }
}
