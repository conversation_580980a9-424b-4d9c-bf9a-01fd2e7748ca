package com.trs.police.control.task;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.control.ControlApp;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.service.impl.cloud.control.PushToCloudControlServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * 测试拉取云控布控指令
 *
 * <AUTHOR>
 * @since 2025/1/22 15:32
 */
@Slf4j
@SpringBootTest(classes = ControlApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class CloudControlSubscribeTaskTest {

    @Resource
    private CloudControlSubscribeTask cloudControlSubscribeTask;

    @Resource
    private PushToCloudControlServiceImpl pushToCloudControlService;

    /**
     * 测试正常拉取云控布控指令
     */
    @Test
    public void test() {
        cloudControlSubscribeTask.fetchAndPushMessage();
    }

    /**
     * 测试推送云控布控指令到 云控 mock 接口
     */
    @Test
    public void testPushToCloudControl() {
        ControlInfo controlInfo = new ControlInfo();
        GroupWarningDTO.Track trackVO = JSONObject.parseObject("{\n" +
                "\t\"dataClassification\": \"人像抓拍数据\",\n" +
                "\t\"enName\": \"rxzpsj\",\n" +
                "\t\"eventTime\": \"20240618151755\",\n" +
                "\t\"hitInfo\": {\n" +
                "\t\t\"customInfo\": null,\n" +
                "\t\t\"hitModel\": null,\n" +
                "\t\t\"hitTags\": [],\n" +
                "\t\t\"subscribeCode\": \"care_45\"\n" +
                "\t},\n" +
                "\t\"identifier\": \"510603187698672412\",\n" +
                "\t\"identifierType\": 1,\n" +
                "\t\"name\": \"人像抓拍数据\",\n" +
                "\t\"sensingMessage\": {\n" +
                "\t\t\"address\": \"泸州市合江县501214义园街车站进出口人脸1_H_hjzj_QXZJ\",\n" +
                "\t\t\"district\": null,\n" +
                "\t\t\"id\": \"510501190000043\",\n" +
                "\t\t\"latitude\": 28.807966,\n" +
                "\t\t\"longitude\": 105.834901,\n" +
                "\t\t\"name\": \"泸州市合江县501214义园街车站进出口人脸1_H_hjzj_QXZJ\",\n" +
                "\t\t\"type\": \"face_camera\"\n" +
                "\t},\n" +
                "\t\"trackDetail\": {\n" +
                "\t\t\"spsbgjbh\": \"51052200001190000043\",\n" +
                "\t\t\"mzrlurl\": \"http://ip:port/storage/v1/image/global?cluster_id=GAW_LZ_FP_1652599390&uri_base64=a3Y6Ly9rdi1zdXJ2ZWlsbGFuY2VfaW1hZ2UvNGRiNDY2NzEzNDAyMDAwMDA0\",\n" +
                "\t\t\"bhsj\": \"20240618151755\",\n" +
                "\t\t\"data_digest\": \"new_alert0_6140457\",\n" +
                "\t\t\"wdwgs84_sxj\": 28.807966,\n" +
                "\t\t\"data_id\": 1,\n" +
                "\t\t\"ssbhrlurl\": \"http://ip:port/storage/v1/image/global?cluster_id=GAW_LZ_FP_1652599390&uri_base64=a3Y6Ly9rdi1zdXJ2ZWlsbGFuY2VfaW1hZ2UvNGRiNDY2NzEzNDAyMDAwMDA0MD\",\n" +
                "\t\t\"zjhm_bzr\": \"510921195208028656\",\n" +
                "\t\t\"jszt_bzr\": false,\n" +
                "\t\t\"ssbhtpurl\": \"http://ip:port/storage/v1/image/global?cluster_id=GAW_LZ_FP_1652599390&uri_base64=aHVpanU6Ly9BSUNFTEwxNDlfMTY4OTMyNjYxODoxMTE4MC9zdG9yYWdlL3YxL2ltYWdlP2NsdXN0ZXJfaWQ9QUlDRUxMMTQ5XzE2ODkzMjY2MTgmdXJpX2Jhc2U2ND1hM1k2THk5cmRpMXBi\",\n" +
                "\t\t\"jdwgs84_sxj\": 105.834901,\n" +
                "\t\t\"bdxsd_bzr\": 91.04597556731272,\n" +
                "\t\t\"data_source_id\": 6087,\n" +
                "\t\t\"zjlid\": \"9148d7a85e3097979f6c173916b51713\",\n" +
                "\t\t\"spsbmc\": \"泸州市合江县501214义园街车站进出口人脸1_H_hjzj_QXZJ\"\n" +
                "\t},\n" +
                "\t\"userName\": \"ys\"\n" +
                "}", GroupWarningDTO.Track.class);

        log.info("trackVO:{}", JSONObject.toJSONString(
                trackVO
        ));

        pushToCloudControlService.produceToCloudControl(controlInfo, trackVO);
    }

    @Test
    public void testPushScsmhdpTrack(){
        ControlInfo controlInfo = new ControlInfo();
        GroupWarningDTO.Track trackVO = JSONObject.parseObject("{\"dataClassification\":\"四川省民航订票\",\"enName\":\"scsmhdp\",\"eventTime\":\"20250206085439\",\"hitInfo\":{\"customInfo\":null,\"hitModel\":null,\"hitTags\":[],\"subscribeCode\":\"monitor_72\"},\"identifier\":\"510722197608161805\",\"identifierType\":1,\"name\":\"四川省民航订票\",\"sensingMessage\":{\"address\":\"北京大兴国际机场\",\"district\":null,\"id\":\"PKX\",\"latitude\":39.511571,\"longitude\":116.423796,\"name\":\"北京大兴国际机场\",\"type\":\"airport\"},\"trackDetail\":{\"task\":{\"appId\":\"23************1734593820683\",\"dxywzjbh\":\"************-1736401557318-0000001\",\"rwbh\":\"************-1736401557318-0000001\",\"modelType\":\"singleId\"},\"data\":{\"hdfsddxzqh\":\"131003\",\"gjxxbh\":\"1C8F345445084E43B4BFA1ED546FD9C2\",\"uuid\":\"4ad399f82d11e775df866f96d44ddca3\",\"dxbh\":\"510722197608161805\",\"hdfssj\":\"20250206085439\",\"dxsx\":\"MH\",\"hdfsdssshcsdm\":\"PKX\",\"hdfsdwd\":\"39.511571\",\"hdfsdssshcs\":\"北京大兴国际机场\",\"hdfsdd\":\"北京\",\"hdfsdjd\":\"116.423796\",\"hdxgxx\":{\"hbh\":\"NS8001\",\"zjhm\":\"510722197608161805\",\"qfsj\":\"20250208155500\",\"jpzt\":\"RR\",\"ddsj\":\"20250208185000\",\"mdddm\":\"TFU\",\"mddwd\":\"30.278925\",\"mddxzqh\":\"510185\",\"mdd\":\"天府国际机场\",\"mdddd\":\"四川省成都市简阳市芦葭镇空港大道\",\"mddjd\":\"104.432573\"},\"dtxxlb\":\"\",\"hdfsdssgajg\":\"\",\"hdfsdssgajgdm\":\"\",\"hdxxms\":\"\",\"dtxxtgjg\":\"\",\"dtxxtgjgdm\":\"\"},\"trackSourceFrom\":\"hz\",\"zjlid\":\"fcc6ddb816e3744f943292afd2096a54\"},\"userName\":\"ys\"}", GroupWarningDTO.Track.class);
        pushToCloudControlService.produceToCloudControl(controlInfo, trackVO);
    }

}
