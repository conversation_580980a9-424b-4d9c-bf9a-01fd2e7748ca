package com.trs.police.control.service;

import com.trs.police.common.core.constant.enums.MonitorBaseTypeEnum;
import com.trs.police.common.core.vo.control.AreaVO;
import com.trs.police.control.domain.entity.monitor.CareMonitorEntity;
import com.trs.police.control.domain.entity.monitor.MonitorEntity;
import com.trs.police.control.domain.entity.monitor.RegularMonitorEntity;
import com.trs.police.control.domain.request.PersonAllTrackRequest;
import com.trs.police.control.domain.vo.monitor.TrackPointVO;

import java.util.List;
import java.util.Map;

/**
 * 布控、预警订阅服务
 *
 * <AUTHOR>
 */
public interface SubscribeService {

    /**
     * 预警订阅
     *
     * @param monitor 布控信息
     */
    void sendWarningSubscribe(MonitorEntity monitor);

    /**
     * 预警订阅
     *
     * @param monitor 布控信息
     * @param preInitHeaderMap 预先初始化的头信息
     */
    void sendWarningSubscribe(MonitorEntity monitor, Map<String, String> preInitHeaderMap);

    /**
     * 预警订阅
     *
     * @param regular 常控信息
     */
    void sendWarningSubscribe(RegularMonitorEntity regular);

    /**
     * 关注监控订阅
     *
     * @param entity 关注监控内容；
     * <AUTHOR>
     */
    void sendWarningSubscribe(CareMonitorEntity entity);

    /**
     * 取消订阅
     *
     * @param monitorId 布控id
     * @param type      布控类型
     */
    void cancelWarningSubscribe(Long monitorId, MonitorBaseTypeEnum type);

    /**
     * 取消订阅
     *
     * @param idNumber 证件号码
     * @param monitorId 布控id
     * @return 返回结果
     */
    String cancelWarningSubscribe(String idNumber, Long monitorId);

    /**
     * 常控取消订阅
     *
     * @param regularId 布控id
     * @param type      布控类型
     */
    void cancelRegularWarningSubscribe(Long regularId, MonitorBaseTypeEnum type);

    /**
     * 关注预警取消订阅
     *
     * @param careMonitorEntity 关注预警实体
     * @param type      布控类型
     */
    void cancelCareWarningSubscribe(CareMonitorEntity careMonitorEntity, MonitorBaseTypeEnum type);

    /**
     * 添加区域
     *
     * @param areaId 区域id
     * @param areaVO 区域详情
     */
    void addImportantArea(Long areaId, AreaVO areaVO);

    /**
     * 查询人员全量数据
     *
     * @param request 参数
     * @return 结果
     */
    List<TrackPointVO> getPersonAllTrack(PersonAllTrackRequest request);

    /**
     * 变更人员-布控关联
     *
     * @param idNumber   人员身份证号
     * @param monitorIds 布控id
     */
    void changePersonMonitorRelation(String idNumber, List<Long> monitorIds);
}
