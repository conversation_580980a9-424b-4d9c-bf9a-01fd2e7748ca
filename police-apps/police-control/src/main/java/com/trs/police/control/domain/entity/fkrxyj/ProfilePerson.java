package com.trs.police.control.domain.entity.fkrxyj;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chenhaiyang.plugin.mybatis.sensitive.annotation.EncryptField;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import com.trs.police.common.core.handler.typehandler.JsonToStringListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 人员档案表(ProfilePerson)数据访问类
 *
 */
//@SensitiveEncryptEnabled
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_profile_person", autoResultMap = true)
public class Profile<PERSON><PERSON> extends AbstractBaseEntity {

    private static final long serialVersionUID = 113659934363509467L;

    /**
     * 证件号码
     */
    @EncryptField
    private String idNumber;

    /**
     * 证件类型
     */
    private Integer idType;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 曾用名
     */
    private String formerName;

    /**
     * 绰号
     */
    private String nickName;

    /**
     * 民族
     */
    private Integer nation;

    /**
     * 政治面貌
     */
    private Integer politicalStatus;

    /**
     * 宗教信仰
     */
    private String religiousBelief;

    /**
     * 婚姻状况
     */
    private Integer martialStatus;

    /**
     * 现职业
     */
    private String currentJob;

    /**
     * 目前所在地
     */
    private Integer currentPosition;

    /**
     * 户籍地区域代码
     */
    private String registeredResidence;

    /**
     * 户籍地详细地址
     */
    private String registeredResidenceDetail;

    /**
     * 现住址区域代码
     */
    private String currentResidence;

    /**
     * 现住址详细地址
     */
    private String currentResidenceDetail;

    /**
     * 主要诉求
     */
    private String mainDemand;

    /**
     * 工作措施
     */
    private String workMeasures;

    /**
     * 上访情况
     */
    private String petitionInfo;

    /**
     * 被打击处理情况
     */
    private String punishInfo;

    /**
     * 人员标签
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> personLabel;

    /**
     * 照片
     */
    private String photo;

    /**
     * 关联虚拟身份id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> virtualIdentityIds;

    /**
     * 关联车辆id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> vehicleIds;

    /**
     * 电话号码
     */
    @EncryptField
    @TableField(typeHandler = JsonToStringListHandler.class)
    private List<String> tel;

    /**
     * 家庭关系id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> familyRelationIds;

    /**
     * 社会关系id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> socialRelationIds;

    /**
     * 布控状态
     */
    private Integer monitorStatus;

    /**
     * 管控状态
     */
    private Integer controlStatus;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 完整度统计
     */
    private Double completeRate;

    /**
     * 人员档案类型，普通档案为0(默认值)，FK人员档案：1
     */
    private Integer personType;

    /**
     *  工作目标
     */
    private Integer workTarget;

    /**
     *  人员级别
     */
    private Integer controlLevel;

    /**
     *  人员风险评分
     */
    private Double riskScore;

    /**
     *  累计分
     */
    private Double rawScore;

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 审批code
     */
    private Integer approvalStatueCode;
}