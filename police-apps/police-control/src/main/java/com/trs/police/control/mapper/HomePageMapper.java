package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.CodeNameCountVO;
import com.trs.police.common.core.vo.IdNameVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.control.domain.vo.homepage.HeatMapVO;
import com.trs.police.control.domain.vo.homepage.ModelCountVo;
import com.trs.police.control.domain.vo.homepage.MonitorPersonListVO;
import com.trs.police.control.domain.vo.homepage.MonitorPersonVO;
import com.trs.police.control.domain.vo.homepage.PersonInfoVO;
import com.trs.police.control.domain.vo.homepage.PersonMapVO;
import com.trs.police.control.domain.vo.homepage.PlaceCountVO;
import com.trs.police.control.domain.vo.homepage.TrackVO;
import com.trs.police.control.domain.vo.homepage.WarningHomePageListVO;
import com.trs.police.control.domain.vo.warning.WarningListVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 10/7/2023 下午2:42
 */
public interface HomePageMapper {


    /**
     * 预警处置统计
     *
     * @param request {@link ListParamsRequest}
     * @return {@link KeyValueVO}
     */
    List<KeyValueVO> getWarningHandleStatistic(ListParamsRequest request);

    /**
     * 预警处置统计
     *
     * @param request {@link ListParamsRequest}
     * @return {@link KeyValueVO}
     */
    List<KeyValueVO> getWarningLevelStatistic(ListParamsRequest request);


    /**
     * 布控人员列表
     *
     * @param params 请求参数
     * @param page   分页参数
     * @return {@link MonitorPersonVO}
     */
    Page<MonitorPersonVO> getMonitorPerson(@Param("params") ListParamsRequest params, Page<Object> page);

    /**
     * 获取布控人员的布控发起人
     *
     * @param personId 布控人员Id
     * @return {@link String}
     */
    List<KeyValueVO> getPersonMonitorUsers(@Param("personId") Long personId);

    /**
     * 获取布控人员的布控发起人
     *
     * @param personId 布控人员Id
     * @return {@link String}
     */
    List<IdNameVO> getPersonMonitorUsersByIds(@Param("personId") List<Long> personId);

    /**
     * 获取布控人员的布控发起人
     *
     * @param personId 布控人员Id
     * @return {@link String}
     */
    List<String> getPersonMonitorUsersList(@Param("personId") Long personId);

    /**
     * 获取布控人员的布控发起人
     *
     * @param personId 布控人员Id
     * @return {@link String}
     */
    List<String> getPersonLabels(@Param("personId") Long personId);

    /**
     * 人员信息
     *
     * @param personId 人员id
     * @return {@link PersonInfoVO}
     */
    PersonInfoVO getPersonInfo(Long personId);

    /**
     * 人员预警列表
     *
     * @param personId 人员id
     * @param params   请求参数
     * @param page     分页参数
     * @return {@link WarningEntity}
     */
    Page<WarningEntity> getPersonWarningList(@Param("personId") Long personId,
        @Param("params") ListParamsRequest params, Page<Object> page);

    /**
     * 预警地图数据
     *
     * @param request 请求参数
     * @return {@link WarningListVO}
     */
    List<WarningHomePageListVO> getWarnMapData(@Param("request") ListParamsRequest request);


    /**
     * 管控首页预警列表
     *
     * @param request 请求参数
     * @param page    分页参数
     * @return {@link WarningListVO}
     */
    Page<WarningHomePageListVO> getWarningPageList(@Param("request") ListParamsRequest request,
        Page<WarningHomePageListVO> page);

    /**
     * 人员地图数据
     *
     * @param request 请求参数
     * @return {@link PersonMapVO}
     */
    List<PersonMapVO> getPersonMapData(@Param("request") ListParamsRequest request);

    /**
     * 管控人员数量查询
     *
     * @param params 请求参数
     * @return {@link WarningListVO}
     */
    List<KeyValueVO> getMonitorStatistics(@Param("params") ListParamsRequest params);

    /**
     * 热力图数据
     *
     * @param request 请求参数
     * @return point数组
     */
    List<HeatMapVO> getHeatMapData(@Param("request") ListParamsRequest request);

    /**
     * 布控详情-各级别数量统计
     *
     * @param personId 人员id
     * @param params   筛选参数
     * @return {@link KeyValueVO}
     */
    List<KeyValueVO> getPersonWarningCount(@Param("personId") Long personId, @Param("params") ListParamsRequest params);


    /**
     * 布控级别统计
     *
     * @param params 筛选参数
     * @return {@link KeyValueVO}
     */
    List<KeyValueVO> getLevelStatistics(@Param("params") ListParamsRequest params);

    /**
     * 布控人员标签统计
     *
     * @param topLevelPersonLabel 人员标签
     * @param request             筛选参数
     * @return {@link CodeNameCountVO}
     */
    List<CodeNameCountVO> getLabelStatistics(@Param("personLabels") List<KeyValueTypeVO> topLevelPersonLabel,
        @Param("params") ListParamsRequest request);

    /**
     * 场所统计
     *
     * @param type   场所类型
     * @param params 参数
     * @return 结果
     */
    List<PlaceCountVO> getPlaceCount(@Param("type") String type, @Param("params") List<KeyValueTypeVO> params);

    /**
     * 预警数量
     *
     * @param timeParams 时间参数
     * @param request    请求参数
     * @return 数量
     */
    List<CodeNameCountVO> getWarningLevelCount(@Param("timeParams") TimeParams timeParams,
        @Param("request") ListParamsRequest request);

    /**
     * 预警统计-预警模型统计
     *
     * @param request 请求参数
     * @return {@link KeyValueVO}
     */
    List<ModelCountVo> getModelCount(@Param("request") ListParamsRequest request);

    /**
     * 分布统计
     *
     * @param nextLevelDept 下级部门
     * @param request       筛选参数
     * @return {@link KeyValueVO}
     */
    List<CodeNameCountVO> getDistributionStatistics(@Param("depts") List<DeptDto> nextLevelDept,
        @Param("params") ListParamsRequest request);


    /**
     * 布控人员列表
     *
     * @param params 请求参数
     * @param page   分页参数
     * @return {@link MonitorPersonVO}
     */
    Page<MonitorPersonListVO> getMonitorPersonList(@Param("params") ListParamsRequest params,
        Page<MonitorPersonListVO> page);

    /**
     * 预警处置统计
     *
     * @param deptCode 部门编码
     * @param request  筛选参数
     * @return {@link CodeNameCountVO}
     */
    List<CodeNameCountVO> getDistrictHandle(@Param("deptCode") String deptCode,
        @Param("request") ListParamsRequest request);

    /**
     * 预警处置统计
     *
     * @param userId  用户id
     * @param request 筛选参数
     * @return {@link CodeNameCountVO}
     */
    List<CodeNameCountVO> getUserHandle(@Param("userId") Long userId, @Param("request") ListParamsRequest request);

    /**
     * 根据部门统计有轨迹人员
     *
     * @param deptCodePrefix 下级部门前缀
     * @param request        筛选参数
     * @return 总数
     */
    Integer countHaveTrackPersonByDept(@Param("deptCodePrefix") String deptCodePrefix,
        @Param("request") ListParamsRequest request);

    /**
     * 根据用户统计有轨迹人员
     *
     * @param userId  用户id
     * @param request 筛选参数
     * @return 总数
     */
    Integer countHaveTrackPersonByUser(@Param("userId") Long userId, @Param("request") ListParamsRequest request);

    /**
     * 根据部门统计布控人员
     *
     * @param deptCodePrefix 下级部门前缀
     * @param request        筛选参数
     * @return 总数
     */
    Integer countPersonByDept(@Param("deptCodePrefix") String deptCodePrefix,
        @Param("request") ListParamsRequest request);

    /**
     * 根据用户统计布控人员
     *
     * @param userId  用户id
     * @param request 筛选参数
     * @return 总数
     */
    Integer countPersonByUser(@Param("userId") Long userId, @Param("request") ListParamsRequest request);

    /**
     * 根据用户统计有轨迹人员
     *
     * @param deptCode 下级部门
     * @return {@link IdNameVO}
     */
    List<IdNameVO> selectMonitorUser(@Param("deptCode") String deptCode);

    /**
     * 获取人员最近预警轨迹信息
     *
     * @param personId 人员id
     * @return {@link TrackVO}
     */
    TrackVO getLastTrack(@Param("personId") Long personId);

    /**
     * 批量获取人员最近轨迹信息
     *
     * @param personId 人员id
     * @return {@link TrackVO}
     */
    List<TrackVO> getLastTrackBatch(@Param("personId") List<Long> personId);

    /**
     * 预警列表弹窗
     *
     * @param request 请求参数
     * @param toPage  分页参数
     * @return {@link WarningEntity}
     */
    Page<WarningEntity> getWarningList(@Param("request") ListParamsRequest request, Page<WarningEntity> toPage);

    /**
     * 获取人员标签名称
     *
     * @param labelId 标签id
     * @return 标签名称
     */
    @Select("select name from t_profile_label where id = #{labelId}")
    String getPeronLabelName(@Param("labelId") Long labelId);

    /**
     * 获取模型名称
     *
     * @param modelId 标签id
     * @return 标签名称
     */
    @Select("select title from t_control_monitor_warning_model where id = #{modelId}")
    String getWarningModelName(@Param("modelId") Long modelId);
}
