package com.trs.police.control.service.warning;

import com.trs.police.control.constant.WarningFkrxyjConstant;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 预警服务工厂
 *
 * <AUTHOR>
 */
@Component
public class WarningServiceFactory {

    @Autowired
    private ScrqWarningService scrqWarningService;;

    /**
     * 获取预警服务
     *
     * @param entity 预警数据
     * @return w
     */
    public WarningService of(WarningFkrxyjEntity entity) {

        switch (entity.getWarningModel()) {
            case WarningFkrxyjConstant.FIRST_ENTRY_INTO_THE_ZONE:
                return scrqWarningService;
            default:
                return null;
        }
    }
}
