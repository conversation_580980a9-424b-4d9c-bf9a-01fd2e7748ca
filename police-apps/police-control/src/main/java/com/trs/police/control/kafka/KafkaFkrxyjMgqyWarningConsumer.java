package com.trs.police.control.kafka;

import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.control.constant.WarningFkrxyjConstant;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjDTO;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.mapper.WarningFkrxyjMapper;
import com.trs.police.control.properties.KafkaFkrxyjWarningConsumerProperties;
import com.trs.police.control.service.warning.FkMessageHelper;
import com.trs.police.control.service.warning.FkWarningUpdater;
import com.trs.police.control.service.warning.MgqyWarningService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * 60w数据 敏感区域
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@ConditionalOnBean(value = KafkaFkrxyjWarningConsumerProperties.class)
public class KafkaFkrxyjMgqyWarningConsumer {

    private KafkaConsumer<String, String> consumer;

    private final KafkaFkrxyjWarningConsumerProperties properties;

    private final MgqyWarningService mgqyWarningService;

    private final FkWarningUpdater fkWarningUpdater;

    private final WarningFkrxyjMapper warningFkrxyjMapper;

    public KafkaFkrxyjMgqyWarningConsumer(
            KafkaFkrxyjWarningConsumerProperties properties,
            MgqyWarningService mgqyWarningService,
            FkWarningUpdater fkWarningUpdater,
            WarningFkrxyjMapper warningFkrxyjMapper) {
        this.properties = properties;
        this.mgqyWarningService = mgqyWarningService;
        this.fkWarningUpdater = fkWarningUpdater;
        this.warningFkrxyjMapper = warningFkrxyjMapper;

        if (com.trs.common.utils.StringUtils.isEmpty(properties.getMgqyGroupId())) {
            log.info("fk预警未启动mg区域预警消费，跳过初始化");
            return;
        }
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, properties.getBootStrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, properties.getMgqyGroupId());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, properties.getMaxPollRecords());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, properties.getAutoOffsetReset());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);//关闭自动提交kafka事务，批量处理时控制回滚策略
        consumer = new KafkaConsumer<>(props);
        consumer.subscribe(List.of(properties.getTopic()));
    }

    /**
     * 消费人员预警信息
     */
    @Scheduled(fixedDelay = 1L)
    public void consumer() {
        if (Objects.isNull(consumer)) {
            return;
        }
        final ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(properties.getPollDuration()));
        log.info("本批次数据共消费反恐人像预警（敏感区域）数据：{} 条", records.count());
        List<ConsumerRecord<String, String>> recordList = new ArrayList<>(records.count());
        records.forEach(recordList::add);
        //处理该批次消费到的数据
        List<WarningFkrxyjDTO> list = recordList.stream()
                .map(message -> {
                    if (StringUtils.isBlank(message.value())) {
                        return null;
                    }
                    return JsonUtil.parseObject(message.value(), WarningFkrxyjDTO.class);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        list.forEach(this::consumeMessage);
        //提交kafka事务
        consumer.commitSync();
    }


    /**
     * 消费消息
     *
     * @param dto dto
     */
    public void consumeMessage(WarningFkrxyjDTO dto) {
        try {
            WarningFkrxyjEntity entity = FkMessageHelper.buildEntity(dto);
            if (mgqyWarningService.isMgqy(entity)) {
                entity.setWarningModel(WarningFkrxyjConstant.WANDERING_ON_SENSITIVE);
            }
            fkWarningUpdater.update(entity);
            warningFkrxyjMapper.insert(entity);
        } catch (Exception e) {
            log.error("消费敏感区域数据出错", e);
        }

    }
}
