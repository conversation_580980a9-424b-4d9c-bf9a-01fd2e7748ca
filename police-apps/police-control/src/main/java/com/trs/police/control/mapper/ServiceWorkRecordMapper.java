package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.control.domain.entity.ServiceWorkRecordEntity;
import com.trs.police.control.domain.vo.WorkRecordListVO;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

/**
 * ServiceWorkRecordMapper
 *
 * <AUTHOR>
 * @Date 2023/6/29 15:28
 */
@Mapper
public interface ServiceWorkRecordMapper extends BaseMapper<ServiceWorkRecordEntity> {

    /**
     * 获取常控人员工作记录
     *
     * @param regularId    常控id
     * @param filterParams 筛选参数
     * @param sortParams   排序参数
     * @param page         分页参数
     * @return {@link  WorkRecordListVO}
     */
    Page<ServiceWorkRecordEntity> getRegularWorkRecordList(@Param("regularId") Long regularId,
        @Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("sortParams") SortParams sortParams, Page<Object> page);

    /**
     * 获取时间范围内工作记录数量
     *
     * @param regularId 常控id
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Select("select count(0) from t_service_work_record where regular_id=#{regularId} and create_time >= #{beginTime} and create_time < #{endTime}")
    Integer getCountByTime(@Param("regularId") Long regularId, @Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取常控最后一条工作记录
     *
     * @param regularId 常控id
     * @return {@link ServiceWorkRecordEntity}
     */
    @Select("select * from t_service_work_record where regular_id=#{regularId} order by create_time desc limit 1")
    @ResultMap("mybatis-plus_ServiceWorkRecordEntity")
    Optional<ServiceWorkRecordEntity> getLastRecord(@Param("regularId") Long regularId);

    /**
     * 获取常控最开始的那一条工作记录
     *
     * @return {@link ServiceWorkRecordEntity}
     */
    @Select("select * from t_service_work_record order by create_time  limit 1")
    @ResultMap("mybatis-plus_ServiceWorkRecordEntity")
    Optional<ServiceWorkRecordEntity> getFirstRecord();
}
