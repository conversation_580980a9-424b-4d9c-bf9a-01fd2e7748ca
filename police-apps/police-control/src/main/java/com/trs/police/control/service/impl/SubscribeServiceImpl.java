package com.trs.police.control.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.base.PreConditionCheck;
import com.trs.police.common.core.constant.enums.*;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.MonitorWarningModelEntity;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.CommonMapper;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.control.AreaVO;
import com.trs.police.common.core.vo.control.PersonAllTrackVO;
import com.trs.police.common.core.vo.message.ScheduleMessageVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.UserDeptActionVO;
import com.trs.police.common.core.vo.profile.GroupVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.common.rabbitmq.starter.utils.RabbitmqUtils;
import com.trs.police.common.schedule.starter.annotation.ScheduleResult;
import com.trs.police.control.constant.SubscribeConstant;
import com.trs.police.control.constant.enums.GroupMonitorTypeEnum;
import com.trs.police.control.domain.entity.monitor.*;
import com.trs.police.control.domain.query.AddImportantAreaQuery;
import com.trs.police.control.domain.request.PersonAllTrackRequest;
import com.trs.police.control.domain.vo.care.CareMonitorInitialeVO;
import com.trs.police.control.domain.vo.monitor.MonitorFilterParamsVO;
import com.trs.police.control.domain.vo.monitor.TrackPointVO;
import com.trs.police.control.domain.vo.warning.*;
import com.trs.police.control.domain.vo.warning.GroupSubscribeVO.MonitorParams;
import com.trs.police.control.handler.care.ICareMonitorHandler;
import com.trs.police.control.mapper.*;
import com.trs.police.control.properties.PostConstructProperties;
import com.trs.police.control.properties.SubscribeProperties;
import com.trs.police.control.properties.WarningProperties;
import com.trs.police.control.service.SubscribeService;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.common.schedule.starter.constant.ExchangeConstant.SCHEDULE_EXCHANGE;

/**
 * <AUTHOR>
 * @date 2022/09/12
 */
@Service
@Slf4j
public class SubscribeServiceImpl implements SubscribeService {

    @Resource
    private MonitorTargetRelationMapper monitorTargetRelationMapper;

    @Resource
    private MonitorGroupPersonRelationMapper monitorGroupPersonRelationMapper;

    @Resource
    private MonitorWarningModelRelationMapper monitorWarningModelRelationMapper;
    @Resource
    private MonitorWarningModelMapper monitorWarningModelMapper;
    @Resource
    private ProfileService profileService;
    @Resource
    private SubscribeProperties subscribeProperties;
    @Resource
    private OssService ossService;
    @Resource
    private MonitorTargetFilterParamsMapper monitorTargetFilterParamsMapper;
    @Resource
    private WarningProperties warningProperties;
    @Resource
    private MonitorMapper monitorMapper;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private PostConstructProperties postConstructProperties;
    @Resource
    private RegularMonitorConfigMapper regularMonitorConfigMapper;

    @Resource
    private RegularMonitorMapper regularMonitorMapper;

    @Resource
    private CareMonitorMapper careMonitorMapper;

    @Resource
    private ApprovalService approvalService;

    @Resource
    private PermissionService permissionService;

    OkHttpUtil okHttpUtil = OkHttpUtil.getInstance();

    private static final String MONITOR_PREFIX = "monitor";

    private static final String REGULAR_PREFIX = "regular";

    private static final String SUBSCRIBE_SUCCESS = "moye 比对服务订阅成功：返回信息：{}";
    @Autowired
    private PersonMapper personMapper;

    @Override
    public void sendWarningSubscribe(MonitorEntity monitor) {
        sendWarningSubscribe(monitor, null);
    }


    @Override
    public void sendWarningSubscribe(MonitorEntity monitor, Map<String, String> preInitHeaderMap) {
        try {
            //构建header信息
            Map<String, String> headerMap = buildHeaderMap(monitor.getCreateUserId(), OperateModule.MONITOR, monitor.getId());
            switch (monitor.getMonitorType()) {
                case PERSON:
                    WarningSubscribeVO subscribeVO = getSubscribeVO(monitor);
                    String subscribeStr = JsonUtil.toJsonString(subscribeVO);
                    String response = sendWarningRequest(subscribeProperties.getSubscribeUrl(), subscribeStr, headerMap);
                    log.info(SUBSCRIBE_SUCCESS, response);
                    break;
                case GROUP:
                    handleGroupSubscribeRequest(monitor, headerMap);
                    break;
                case AREA:
                    handleAreaSubscribeRequest(monitor, headerMap);
                    //sendAreaSubscribe(monitor, headerMap);
                    break;
                default:
                    log.info("布控类型:" + monitor.getMonitorType().getCode() + "不存在！");
            }
        } catch (Exception e) {
            log.error("发送布控请求出错", e);
        }
    }

    @Override
    public void sendWarningSubscribe(RegularMonitorEntity regular) {
        WarningSubscribeVO subscribeVO = getSubscribeVO(regular);
        if (subscribeVO == null) {
            return;
        }
        Map<String, String> headerMap = buildHeaderMap(regular.getCreateUserId(), OperateModule.REGULAR, regular.getId());
        String subscribeStr = JsonUtil.toJsonString(subscribeVO);
        String response = sendWarningRequest(subscribeProperties.getSubscribeUrl(), subscribeStr, headerMap);
        log.info(SUBSCRIBE_SUCCESS, response);
    }

    /**
     * 关注监控订阅
     *
     * @param entity entity
     * <AUTHOR>
     */
    @Override
    public void sendWarningSubscribe(CareMonitorEntity entity) {

        PreConditionCheck.checkNotNull(entity);
        PreConditionCheck.checkNotNull(entity.getId());

        //拼装订阅信息，现阶段只支持身份证号，并且没有人像订阅，如果要做，需要扩展CareMonitorInitialeVO内容
        WarningSubscribeVO subscribeVO = new WarningSubscribeVO();

        subscribeVO.setSubscribeCode(String.format("%s_%s", ControlTypeEnum.CARE.getEnName(), entity.getId()));
        subscribeVO.setSubscribeStartTime(StringUtil.isEmpty(entity.getSubscribeStartTime())
                ? TimeUtil.getSubscribeTime(LocalDateTime.now())
                : entity.getSubscribeStartTime());
        subscribeVO.setSubscribeEndTime(StringUtil.isEmpty(entity.getSubscribeEndTime())
                ? TimeUtil.getSubscribeTime(TimeUtil.DEFAULT_END_TIME)
                : entity.getSubscribeEndTime());
        subscribeVO.setSubscribePlatform(entity.getSubscribePlatform());
        subscribeVO.setIdentifiers(getIdentifiersV2(entity));
        Map<String, String> headerMap = buildHeaderMap(entity.getCreateUserId(), null, entity.getId());
        //中台订阅
        String subscribeStr = JsonUtil.toJsonString(subscribeVO);
        log.info("参数为：{}", subscribeStr);
        log.info("请求头为：{}", JSONObject.toJSONString(headerMap));
        log.info("参数为：{}", JSONObject.toJSONString(subscribeStr));
        String response = sendWarningRequest(subscribeProperties.getSubscribeUrl(), subscribeStr, headerMap);
        log.info(SUBSCRIBE_SUCCESS, response);
    }

    /**
     * 处理群体预警
     * 只选择了原有的聚集和分散预警模型 走原有逻辑
     * 只选择了除开聚集预警和分散预警之外的模型  走群体下的每个人的人员预警逻辑
     * 以上两者混合 同时走原有逻辑和群体下每个人的人员预警两种
     *
     * @param monitor   管控参数
     * @param headerMap 请求header
     */
    private void handleGroupSubscribeRequest(MonitorEntity monitor, Map<String, String> headerMap) {
        // 查询走的是什么模型 (聚集预警和分散预警就走原来的聚集计算的请求, 其它的就走人员预警的请求)
        List<MonitorWarningModelRelationEntity> monitorWarningModelRelationEntities = monitorWarningModelRelationMapper.selectRelationByMonitorId(monitor.getId());
        // 不同环境的模型id不固定, 需要在配置文件中精确指定
        if (warningProperties.getGroupSpreadModelId() == null || warningProperties.getGroupAggregateModelId() == null) {
            throw new TRSException("请在配置文件中指定聚集预警和分散预警的模型id");
        }
        List<Long> warnModelIdList = monitorWarningModelRelationEntities.stream().map(MonitorWarningModelRelationEntity::getWarningModelId).collect(Collectors.toList());
        GroupMonitorTypeEnum groupMonitorTypeEnum = determineWarningModelType(warnModelIdList);
        switch (groupMonitorTypeEnum) {
            case AGGREGATE_OR_SPREAD:
                log.info("只包含 聚集预警 或 分散预警，走原有的计算逻辑");
                // 只包含 聚集预警 或 分散预警，走原有的计算逻辑
                sendGroupSubscribe(monitor, headerMap);
                break;
            case GROUP_PER_PERSON:
                log.info("只包含聚集和分散之外的预警逻辑, 先获取群体下的人员，推送人员预警");
                // 只包含聚集和分散之外的预警逻辑, 先获取群体下的人员，推送人员预警
                pushGroupPerPersonSubscribeRequest(monitor, headerMap);
                break;
            case MIXED:
                log.info("含有聚集或分散预警模型, 以及其它预警模型, 同时推送原有群体预警布控 和 群体下人员的人员布控");
                // 原有的群体预警或分散预警逻辑
                sendGroupSubscribe(monitor, headerMap);
                // 人员预警逻辑, 先获取群体下的人员
                pushGroupPerPersonSubscribeRequest(monitor, headerMap);
                break;
            case OTHER:
                log.warn("未知的预警模型组合情况 {}", JsonUtil.toJsonString(warnModelIdList));
                break;
            default:
                break;
        }
    }

    /**
     * 区域布控下每个人的人员预警布控请求
     *
     * @param monitor 监控参数
     * @param headerMap 请求头
     */
    private void handleAreaSubscribeRequest(MonitorEntity monitor, Map<String, String> headerMap) {
        try {
            if (Objects.isNull(monitor.getId())) {
                throw new TRSException("区域布控id");
            }
            final WarningSubscribeVO subscribeVO = getCommonSubscribeVO(monitor);
            // 获取区域布控下的areaid
            List<Long> areaIds = monitorTargetRelationMapper.getAreaIdsByMonitorId(monitor.getId());
            if (areaIds.isEmpty()) {
                throw new TRSException("布控区域不能为空!");
            }
            MonitorTargetFilterParamsEntity targetFilter = monitorTargetFilterParamsMapper.selectByMonitorId(
                    monitor.getId());
            MonitorFilterParamsVO filter = JsonUtil.parseSpecificObject(targetFilter.getFilterParams(),
                    MonitorFilterParamsVO.class);
            List<String> districts = filter.getDistrict().stream().map(StringUtil::getPrefixCode).collect(Collectors.toList());
            // 根据filter获取人员id
            List<Long> personIds = personMapper.selectPersonIdsByFiter(filter.getDept(),filter.getPersonLabel()
                    ,districts);
            if (CollectionUtils.isEmpty(personIds)) {
                throw new TRSException("未关联人员!");
            }
            List<IdentifierVO> identifierList = profileService.getPersonByIds(personIds).stream()
                    .map(target -> getIdentifierVos(target)).flatMap(Collection::stream)
                    .collect(Collectors.toList());

            subscribeVO.setIdentifiers(identifierList);
            String subscribeStr = JsonUtil.toJsonString(subscribeVO);
            String response = sendWarningRequest(subscribeProperties.getSubscribeUrl(), subscribeStr, headerMap);
            log.info("发送区域布控下每个人的人员预警布控请求, 请求参数 {}  请求响应 {}", subscribeStr, response);
        } catch (Exception e){
            log.error("发送区域布控下每个人的人员预警布控请求", e);
        }
    }

    private List<IdentifierVO> getIdentifierVos(PersonVO target) {
        List<IdentifierVO> identifiers = new ArrayList<>();
        //身份证号、人像
        IdentifierVO idNumber = new IdentifierVO(target.getCertificateNumber(),
                IdentifierTypeEnum.idTypeToIdentityType(target.getCertificateType()),
                getPhotoEncodeString(target.getCertificateNumber(), target.getImgs()));
        idNumber.setCustomInfo(new CustomInfo());
        identifiers.add(idNumber);
        //车牌号
        if (target.getCarNumber() != null) {
            identifiers.addAll(getCarNumber(target.getCarNumber()));
        }
        //手机号
        if (target.getTel() != null) {
            identifiers.addAll(getTel(target.getTel()));
        }
        //虚拟身份
        if (target.getVirtualIdentity() != null) {
            List<IdentifierVO> collect = getIdentifiers(target.getVirtualIdentity());
            identifiers.addAll(collect);
        }

        return identifiers;
    }

    /**
     * 判断选择了那些模型，对应什么类别、
     *
     * @param warnModelIdList 选择的模型的集合
     * @return 群体预警类型枚举
     */
    private GroupMonitorTypeEnum determineWarningModelType(List<Long> warnModelIdList) {
        boolean containsAggOrSpread = warnModelIdList.contains(warningProperties.getGroupAggregateModelId()) || warnModelIdList.contains(warningProperties.getGroupSpreadModelId());
        boolean containsOther = warnModelIdList.stream().anyMatch(item -> !item.equals(warningProperties.getGroupAggregateModelId()) && !item.equals(warningProperties.getGroupSpreadModelId()));
        if (containsAggOrSpread && containsOther) {
            // 同时包含 聚集预警 或 分散预警 和其他模型，走两个计算逻辑
            return GroupMonitorTypeEnum.MIXED;
        } else if (containsAggOrSpread) {
            // 只包含 聚集预警 或 分散预警，走原有的计算逻辑
            return GroupMonitorTypeEnum.AGGREGATE_OR_SPREAD;
        } else if (containsOther) {
            // 只包含聚集和分散之外的预警逻辑, 先获取群体下的人员，推送人员预警
            return GroupMonitorTypeEnum.GROUP_PER_PERSON;
        } else {
            return GroupMonitorTypeEnum.OTHER;
        }
    }

    private void pushGroupPerPersonSubscribeRequest(MonitorEntity monitor, Map<String, String> headerMap) {
        try {
            if (Objects.isNull(monitor.getId())) {
                throw new TRSException("群体布控id为null");
            }
            final WarningSubscribeVO subscribeVO = getCommonSubscribeVO(monitor);
            // 找到本次群体布控下的群体id集合
            List<Long> groupIds = monitorTargetRelationMapper.getIdsByMonitorId(monitor.getId());
            if (CollectionUtils.isEmpty(groupIds)) {
                throw new TRSException("群体布控下群体id为空");
            }
            // 找到群体集合下所有人的id集合
            List<Long> personIds = monitorGroupPersonRelationMapper.selectPersonIdInGroupIds(groupIds, monitor.getId());
            List<IdentifierVO> identifierList = profileService.getPersonByIds(personIds).stream()
                    .map(target -> {
                        return getIdentifierVos(target);
                    }).flatMap(Collection::stream).collect(Collectors.toList());

            subscribeVO.setIdentifiers(identifierList);
            String subscribeStr = JsonUtil.toJsonString(subscribeVO);
            String response = sendWarningRequest(subscribeProperties.getSubscribeUrl(), subscribeStr, headerMap);
            log.info("发送群体下每个人的人员预警布控请求, 请求参数 {}  请求响应 {}", subscribeStr, response);
        } catch (Exception e) {
            log.error("发送群体下每个人的人员预警布控请求出错", e);
        }
    }

    private CustomInfo buildCustomInfo(CareMonitorEntity entity) throws ClassNotFoundException {
        //设置customInfo，需要将关注监控的主键（com.trs.police.control.service.impl.CareMonitorServiceImpl.getCareMonitorKey）也传递到订阅信息当中
        //通过moye的透传功能，在消费时可以获得订阅信息。此外还需要将CareMonitorHandler的className也传递进入，供消费时使用
        CustomInfo customInfo = new CustomInfo();
        //虽然有点丑和牵强，没有办法。。。
        customInfo.setMonitorName(String.format("%s-%s-%s", entity.getCreateDeptId(), entity.getCertificateType(), entity.getCertificateValue()));
        customInfo.setModelName(entity.getHandlerClassName().stream().collect(Collectors.joining(";")));

        List<Long> monitorConfigId = PreConditionCheck.checkNotNull(getMonitorConfigId(entity));
        customInfo.setModelId(monitorConfigId);

        return customInfo;
    }

    private List<Long> getMonitorConfigId(CareMonitorEntity entity) throws ClassNotFoundException {
        if (entity.getWarningConfigIds() != null && !entity.getWarningConfigIds().isEmpty()) {
            return entity.getWarningConfigIds();
        }
        //如果没有配置预警配置，则通过传入的第一个handler的配置的默认预警配置来
        if (entity.getHandlerClassName() != null && entity.getHandlerClassName().size() > 0) {
            //根据handler配置的默认常控配置id
            ICareMonitorHandler handler = CareMonitorInitialeVO.getCareMonitorHandlerInstance(entity.getHandlerClassName().get(0));
            Long defaultMonitorConfigId = handler.getDefaultMonitorConfigId();
            RegularMonitorConfigEntity rmcEntity = regularMonitorConfigMapper.selectById(defaultMonitorConfigId);
            if (!com.trs.common.utils.StringUtils.isNullOrEmpty(rmcEntity.getName())) {
                return List.of(defaultMonitorConfigId);
            } else {
                throw new ClassNotFoundException(String.format("[%s]配置的defaultMonitorConfigId在数据库中无法查找到！",
                        entity.getHandlerClassName().get(0)));
            }
        } else {
            return null;
        }
    }

    /**
     * 构建请求头
     * XMKFB-2021 要求布控时，传递发起人和审批的信息，便于第三方系统的审计功能
     * 这里需要去审批服务捞取审批人信息
     *
     * @param createUserId  发起人信息
     * @param operateModule 操作模块
     * @param relatedId     关联ID
     * @return header
     */
    private Map<String, String> buildHeaderMap(Long createUserId, OperateModule operateModule, Long relatedId) {
        Map<String, String> headerMap = new HashMap<>(subscribeProperties.getHeadersMap());
        try {
            //获取发起人信息
            UserDto createUser = permissionService.getUserById(createUserId);
            if (createUser != null) {
                headerMap.put("endUserName", URLEncoder.encode(createUser.getRealName(), StandardCharsets.UTF_8));
                headerMap.put("endUserIdentifier", createUser.getIdNumber());
            }
            if (operateModule != null) {
                //获取审批人信息
                UserDeptActionVO userDeptAction = approvalService.getApprover(operateModule.getCode(), relatedId);
                UserDto approvalUser = permissionService.getUserById(userDeptAction.getUserInfo().getUserId());
                if (!StringUtils.isEmpty(approvalUser.getRealName())) {
                    headerMap.put("endUserName2", URLEncoder.encode(approvalUser.getRealName(), StandardCharsets.UTF_8));
                    headerMap.put("endUserIdentifier2", approvalUser.getIdNumber());
                }
            }
        } catch (Exception e) {
            log.info("获取创建人/审批人信息发生异常", e);
        }
        return headerMap;
    }

    @Override
    public void cancelWarningSubscribe(Long monitorId, MonitorBaseTypeEnum type) {
        switch (type) {
            case PERSON:
                List<String> idNumbers = monitorMapper.selectIdNumberByMonitorId(monitorId);
                for (String idNumber : idNumbers) {
                    cancelWarningSubscribe(idNumber, monitorId);
                }
                break;
            case GROUP:
                cancelGroupSubscribe(monitorId);
                break;
            case AREA:
                cancelAreaSubscribe(monitorId);
                break;
            default:
        }

    }

    @Override
    public String cancelWarningSubscribe(String idNumber, Long monitorId) {
        WarningCancelVO cancelVO = new WarningCancelVO(generateSubscribeCode(MONITOR_PREFIX, monitorId));
        cancelVO.setIdentifier(idNumber);
        cancelVO.setIdentifierType(IdentifierTypeEnum.ID_NUMBER.getCode());
        String subscribeString = JsonUtil.toJsonString(cancelVO);
        String response = sendWarningRequest(subscribeProperties.getCancelUrl(), subscribeString);
        log.info("moye 尝试取消人员布控比对服务订阅成功：返回信息：{}", response);
        return response;
    }

    /**
     * 常控取消订阅
     *
     * @param regularId 布控id
     * @param type      布控类型
     */
    @Override
    public void cancelRegularWarningSubscribe(Long regularId, MonitorBaseTypeEnum type) {
        switch (type) {
            case PERSON:
                List<String> personIdCardByRegularId = regularMonitorMapper.getPersonIdCardByRegularId(regularId);
                for (String idCard : personIdCardByRegularId) {
                    WarningCancelVO cancelVO = new WarningCancelVO(generateSubscribeCode(REGULAR_PREFIX, regularId));
                    cancelVO.setIdentifier(idCard);
                    cancelVO.setIdentifierType(IdentifierTypeEnum.ID_NUMBER.getCode());
                    String subscribeString = JsonUtil.toJsonString(cancelVO);
                    String response = sendWarningRequest(subscribeProperties.getCancelUrl(), subscribeString);
                    log.info("moye 尝试取消人员常控比对服务订阅：返回信息：{}", response);
                }
                break;
            case GROUP:

                break;
            case AREA:

                break;
            default:
        }
    }

    /**
     * 关注预警取消订阅
     *
     * @param careMonitorEntity 关注预警实体
     * @param type              布控类型
     */
    @Override
    public void cancelCareWarningSubscribe(CareMonitorEntity careMonitorEntity, MonitorBaseTypeEnum type) {
        switch (type) {
            case PERSON:
                try {
                    WarningCancelVO cancelVO = new WarningCancelVO(generateSubscribeCode(ControlTypeEnum.CARE.getEnName(), careMonitorEntity.getId()));
                    cancelVO.setIdentifier(careMonitorEntity.getCertificateValue());
                    cancelVO.setIdentifierType(careMonitorEntity.getCertificateType());
                    String subscribeString = JsonUtil.toJsonString(cancelVO);
                    String response = sendWarningRequest(subscribeProperties.getCancelUrl(), subscribeString);
                    log.info("moye 尝试取消人员关注预警比对服务订阅：返回信息：{}", response);
                } catch (Exception e) {
                    log.error("取消人员关注预警比对服务订阅失败！", e);
                }
                break;
            case GROUP:

                break;
            case AREA:

                break;
            default:
        }
    }

    private void cancelGroupSubscribe(Long monitorId) {
        monitorTargetRelationMapper.getIdsByMonitorId(monitorId).forEach(groupId -> {
            Map<String, String> map = Map.of("monitorId", generateSubscribeCode(monitorId, groupId));
            String groupStr = JsonUtil.toJsonString(map);
            String groupResponse = sendWarningRequest(subscribeProperties.getGroupCancelUrl(), groupStr);
            log.info("moye 取消群体布控订阅成功：返回信息：{}", groupResponse);
        });
    }

    private void cancelAreaSubscribe(Long monitorId) {
        WarningCancelVO cancelVO = new WarningCancelVO();
        cancelVO.setSubscribeCode(generateSubscribeCode(MONITOR_PREFIX, monitorId));
        String subscribeString = JsonUtil.toJsonString(cancelVO);
        String response = sendWarningRequest(subscribeProperties.getCancelUrl(), subscribeString);
        log.info("moye 取消区域布控比对服务订阅成功：返回信息：{}", response);
    }

    @Override
    public void changePersonMonitorRelation(String idNumber, List<Long> monitorIds) {
        CancelPersonVO cancelPersonVO = new CancelPersonVO();
        cancelPersonVO.setId(idNumber);
        cancelPersonVO.setMonitorIds(monitorIds);
        String groupStr = JsonUtil.toJsonString(cancelPersonVO);
        String groupResponse = sendWarningRequest(subscribeProperties.getGroupPersonCancelUrl(), groupStr);
        log.info("moye 取消群体布控订阅成功：返回信息：{}", groupResponse);
    }

    private void sendGroupSubscribe(MonitorEntity monitor, Map<String, String> headerMap) {
        try {
            List<GroupSubscribeVO> groupSubs = getGroupSubscribe(monitor);

            groupSubs.forEach(vo -> {
                String groupStr = JsonUtil.toJsonString(vo);
                String groupResponse = sendWarningRequest(subscribeProperties.getGroupSubscribeUrl(), groupStr, headerMap);
                log.info("moye 群体布控订阅! 订阅id：{} 返回信息：{}", vo.getMonitorId(), groupResponse);
            });

            List<String> idNumbers = getAllIdNumberByMonitor(monitor);
            List<GroupPersonSubVO> personList = getGroupPersonSubscribe(idNumbers, monitor.getId());
            String groupPersonStr = JsonUtil.toJsonString(personList);
            String personResponse = sendWarningRequest(subscribeProperties.getGroupPersonSubscribeUrl(), groupPersonStr);
            log.info("moye 群体人员更新! 返回信息：{}", personResponse);
        } catch (Exception e) {
            log.error("发送原有群体布控请求出错", e);
        }
    }

    private void sendAreaSubscribe(MonitorEntity monitor, Map<String, String> headerMap) {
        //拼接spel表达式
        List<Long> areaIds = monitorTargetRelationMapper.getAreaIdsByMonitorId(monitor.getId());
        if (areaIds.isEmpty()) {
            throw new TRSException("布控区域不能为空!");
        }

        MonitorTargetFilterParamsEntity targetFilter = monitorTargetFilterParamsMapper.selectByMonitorId(
                monitor.getId());
        MonitorFilterParamsVO filter = JsonUtil.parseSpecificObject(targetFilter.getFilterParams(),
                MonitorFilterParamsVO.class);
        String expression = getExpression(filter, areaIds);

        final WarningSubscribeVO subscribeVO = getCommonSubscribeVO(monitor);
        subscribeVO.setIdentifiers(List.of(new IdentifierVO(expression, IdentifierTypeEnum.EXPRESSION.getCode())));

        String response = sendWarningRequest(subscribeProperties.getSubscribeUrl(), JsonUtil.toJsonString(subscribeVO), headerMap);
        log.info("moye 区域布控订阅成功! 订阅id：{} 返回信息：{}", monitor.getId(), response);
    }

    /**
     * 根据筛选参数生成SpEL表达式
     *
     * @param filter  人员参数
     * @param areaIds 区域id
     * @return 表达式
     */
    public String getExpression(MonitorFilterParamsVO filter, List<Long> areaIds) {

        StringBuilder expression = new StringBuilder();

        //区域
        expression.append(toExpression(areaIds, "areaId_"));

        if (Objects.nonNull(filter)) {
            //人员标签
            if (CollectionUtils.isNotEmpty(filter.getPersonLabel())) {
                List<Long> personLabels = commonMapper.getPersonLabelChildren(filter.getPersonLabel());
                expression.append(" and ").append(toExpression(personLabels, "personLabel_"));
            }
            //户籍地标签
            if (CollectionUtils.isNotEmpty(filter.getDistrict())) {
                List<Long> districts = commonMapper.getDistrictChildren(filter.getDistrict());
                expression.append(" and ").append(toExpression(districts, "district_"));
            }
            //责任派出所
            if (CollectionUtils.isNotEmpty(filter.getDept())) {
                List<Long> depts = commonMapper.getDeptChildren(filter.getDept());
                expression.append(" and ").append(toExpression(depts, "dept_"));
            }
        }

        return StringUtils.isBlank(expression) ? Boolean.TRUE.toString() : expression.toString();
    }

    private String toExpression(List<Long> ids, String prefix) {
        List<String> tags = StringUtil.addPrefix(ids, prefix);
        return "!T(java.util.Collections).disjoint(#tagSet?:{},{#tags})".replaceAll("#tags",
                StringUtil.listToString(tags));
    }

    private WarningSubscribeVO getCommonSubscribeVO(MonitorEntity monitor) {
        final WarningSubscribeVO subscribeVO = new WarningSubscribeVO();
        subscribeVO.setSubscribeCode(generateSubscribeCode(MONITOR_PREFIX, monitor.getId()));
        subscribeVO.setSubscribeStartTime(TimeUtil.getSubscribeTime(LocalDateTime.now()));
        subscribeVO.setSubscribeEndTime(TimeUtil.getSubscribeTime(monitor.getExpirationDate()));
        // 设置布控平台
        if (CollectionUtils.isNotEmpty(monitor.getMonitorPlatform())) {
            List<String> collect = monitor.getMonitorPlatform().stream().map(String::valueOf).collect(Collectors.toList());
            subscribeVO.setSubscribePlatform(String.join(",", collect));
        }
        return subscribeVO;
    }

    private WarningSubscribeVO getCommonSubscribeVO(RegularMonitorEntity regular) {
        final WarningSubscribeVO subscribeVO = new WarningSubscribeVO();
        subscribeVO.setSubscribeCode(generateSubscribeCode(REGULAR_PREFIX, regular.getId()));
        subscribeVO.setSubscribeStartTime(TimeUtil.getSubscribeTime(LocalDateTime.now()));
        subscribeVO.setSubscribeEndTime(TimeUtil.getSubscribeTime(TimeUtil.DEFAULT_END_TIME));
        return subscribeVO;
    }


    private List<String> getAllIdNumberByMonitor(MonitorEntity monitor) {
        return monitorTargetRelationMapper.getIdsByMonitorId(monitor.getId()).stream().flatMap(groupId -> {
                    GroupVO groupVO = profileService.getById(groupId);
                    if (groupVO != null && !groupVO.getMonitorPerson().isEmpty()) {
                        return groupVO.getMonitorPerson().stream().map(PersonVO::getCertificateNumber);
                    } else {
                        return Stream.empty();
                    }
                }).distinct()
                .collect(Collectors.toList());
    }

    private String sendWarningRequest(String url, String requestStr) {
        return sendWarningRequest(url, requestStr, subscribeProperties.getHeadersMap());
    }

    private String sendWarningRequest(String url, String requestStr, Map<String, String> headersMap) {
        String response = okHttpUtil.postData(url, requestStr, headersMap);
        if (StringUtils.isNotBlank(response)) {
            return response;
        } else {
            log.info("比对服务订阅发生错误！");
            return "";
        }
    }

    /**
     * 构造布控下的人员特征值等信息
     *
     * @param monitor 布控对象
     * @return 布控对象
     */
    private WarningSubscribeVO getSubscribeVO(MonitorEntity monitor) {
        final WarningSubscribeVO subscribeVO = getCommonSubscribeVO(monitor);
        List<Long> personIds = monitorTargetRelationMapper.getIdsByMonitorId(monitor.getId());
        List<IdentifierVO> identifierList = profileService.getPersonByIds(personIds).stream()
                .map(target -> {
                    return getIdentifierVos(target);
                }).flatMap(Collection::stream).collect(Collectors.toList());

        subscribeVO.setIdentifiers(identifierList);
        return subscribeVO;
    }

    private WarningSubscribeVO getSubscribeVO(RegularMonitorEntity regular) {
        final WarningSubscribeVO subscribeVO = getCommonSubscribeVO(regular);
        PersonVO personVO = profileService.findById(regular.getTargetId());
        //发现存在数据库的人档被删了的情况，这里做下兼容
        if (personVO == null) {
            return null;
        }

        List<IdentifierVO> identifiers = new ArrayList<>();
        //身份证号、人像
        IdentifierVO idNumber = new IdentifierVO(personVO.getCertificateNumber(),
                IdentifierTypeEnum.idTypeToIdentityType(personVO.getCertificateType()),
                getPhotoEncodeString(personVO.getCertificateNumber(), personVO.getImgs()));

        identifiers.add(idNumber);
        //车牌号
        if (personVO.getCarNumber() != null) {
            identifiers.addAll(getCarNumber(personVO.getCarNumber()));
        }
        //手机号
        if (personVO.getTel() != null) {
            identifiers.addAll(getTel(personVO.getTel()));
        }
        //虚拟身份
        if (personVO.getVirtualIdentity() != null) {
            List<IdentifierVO> virtualIdentities = personVO.getVirtualIdentity().stream()
                    .map(vo -> new IdentifierVO(vo.getVirtualNumber(),
                            IdentifierTypeEnum.virtualTypeToIdentityType(vo.getType())))
                    .distinct()
                    .collect(Collectors.toList());
            identifiers.addAll(virtualIdentities);
        }
        subscribeVO.setIdentifiers(identifiers);
        return subscribeVO;
    }

    /**
     * 构造布控下的人员特征值等信息
     *
     * @param monitor 布控对象
     * @return 布控对象
     */
    private WarningSubscribeVO getGroupPerPersonSubscribeVO(MonitorEntity monitor) {
        final WarningSubscribeVO subscribeVO = getCommonSubscribeVO(monitor);
        List<Long> groupIds = monitorTargetRelationMapper.getIdsByMonitorId(monitor.getId());
        List<Long> personIds = monitorGroupPersonRelationMapper.selectPersonIdInGroupIds(groupIds, monitor.getId());
        List<IdentifierVO> identifierList = profileService.getPersonByIds(personIds).stream()
                .map(target -> {
                    return getIdentifierVos(target);
                }).flatMap(Collection::stream).collect(Collectors.toList());

        subscribeVO.setIdentifiers(identifierList);
        return subscribeVO;
    }


    @NotNull
    private static List<IdentifierVO> getIdentifiers(List<PersonVirtualIdentityVO> virtualIdentity) {
        return virtualIdentity.stream().map(e -> {
            IdentifierVO identifierVO = new IdentifierVO();
            identifierVO.setIdentifier(e.getVirtualNumber());
            Integer identifierType = IdentifierTypeEnum.virtualTypeToIdentityType(e.getType());
            identifierVO.setIdentifierType(identifierType);
            return identifierVO;
        }).collect(Collectors.toList());
    }

    private String getPhotoEncodeString(String idNumber) {
        byte[] file = ossService.getPhoto(idNumber);
        return file != null ? Base64.getEncoder().encodeToString(file) : null;
    }

    /**
     * 获取人像信息  base64编码
     *
     * @param idNumber 身份证号码
     * @param imgs     用户上传头像信息
     * @return 图片的base64编码
     */
    private String getPhotoEncodeString(String idNumber, List<FileInfoVO> imgs) {
        try {
            byte[] file = ossService.getPhoto(idNumber);
            if (file != null) {
                return Base64.getEncoder().encodeToString(file);
            }
            if (!CollectionUtils.isEmpty(imgs)) {
                ResponseEntity<byte[]> response = ossService.download(imgs.get(imgs.size() - 1).getId().toString());
                if (response.getBody() != null) {
                    return Base64.getEncoder().encodeToString(response.getBody());
                }
                log.error("身份证号码={}的人没有头像", idNumber);
            }
        } catch (Exception e) {
            log.error("身份证号码={}的人获取照片异常", idNumber, e);
        }
        return null;
    }

    private IdentifierVO getPhoto(String idNumber) {
        byte[] file = ossService.getPhoto(idNumber);
        if (file != null) {
            IdentifierVO photo = new IdentifierVO();
            photo.setIdentifierType(IdentifierTypeEnum.PHOTO.getCode());
            photo.setIdentifier(Base64.getEncoder().encodeToString(file));
            return photo;
        }
        return null;
    }

    private String getNewPhoto(String idNumber) {
        byte[] file = ossService.getPhoto(idNumber);
        if (file != null) {
            return Base64.getEncoder().encodeToString(file);
        }
        return "";
    }

    private List<IdentifierVO> getCarNumber(List<String> carNumbers) {
        return carNumbers.stream()
                .filter(number -> !StringUtil.isEmpty(number))
                .map(carNumber -> new IdentifierVO(carNumber, IdentifierTypeEnum.CAR_NUMBER.getCode()))
                .collect(Collectors.toList());
    }

    private List<IdentifierVO> getTel(List<String> carNumbers) {
        return carNumbers.stream()
                .map(tel -> new IdentifierVO(tel, IdentifierTypeEnum.PHONE_NUMBER.getCode()))
                .collect(Collectors.toList());
    }

    private List<GroupSubscribeVO> getGroupSubscribe(MonitorEntity monitor) {
        List<GroupSubscribeVO> voList = new ArrayList<>();
        List<Long> groupIds = monitorTargetRelationMapper.getIdsByMonitorId(monitor.getId());
        groupIds.forEach(groupId -> {
            String subscriptionCode = generateSubscribeCode(monitor.getId(), groupId);
            GroupSubscribeVO vo = new GroupSubscribeVO();
            vo.setMonitorId(subscriptionCode);
            vo.setMonitorName(monitor.getMonitorTitle());
            MonitorParams params = new MonitorParams();
            final List<MonitorWarningModelRelationEntity> modelRelations =
                    monitorWarningModelRelationMapper.selectRelationByMonitorId(monitor.getId());
            modelRelations.forEach(relation -> {
                MonitorWarningModelEntity model = monitorWarningModelMapper.selectById(relation.getWarningModelId());
                //聚集预警
                if (model.getId().equals(warningProperties.getGroupAggregateModelId())) {
                    if (!relation.getAggregationArea().isEmpty()) {
                        List<String> collect = relation.getAggregationArea().stream().map(e -> "areaId_" + e)
                                .collect(Collectors.toList());
                        params.setAreaIds(collect);
                    }

                    Integer allGroupCount = profileService.getById(groupId).getPersonCount();
                    double personPercentage = allGroupCount * relation.getPersonCount() * 0.01;
                    params.setMemberGatherThreshold((int) personPercentage);
                    params.setMemberGatherTime(relation.getTimeCount());
                    if (CollectionUtils.isEmpty(params.getAreaIds())) {
                        params.setAreaIds(new ArrayList<>());
                    }
                }
            });
            vo.setParams(params);
            voList.add(vo);
        });
        return voList;
    }

    /**
     * 群体布控人员身份证号整理为人员订阅请求
     *
     * @param idNumbers 身份证号
     * @param monitorId 布控id
     * @return {@link GroupPersonSubVO}
     */
    private List<GroupPersonSubVO> getGroupPersonSubscribe(List<String> idNumbers, Long monitorId) {
        return idNumbers.stream()
                .map(idNumber -> {
                    GroupPersonSubVO vo = new GroupPersonSubVO();
                    vo.setId(idNumber);

                    List<IdentifierVO> identifiers = new ArrayList<>();
                    //身份证号
                    identifiers.add(new IdentifierVO(idNumber, IdentifierTypeEnum.ID_NUMBER.getCode()));

                    PersonVO person = profileService.getPersonByIdNumber(idNumber);

                    identifiers.addAll(getCarNumber(person.getCarNumber()));
                    identifiers.addAll(getIdentifiers(person.getVirtualIdentity()));
                    IdentifierVO photo = getPhoto(person.getCertificateNumber());
                    if (photo != null) {
                        identifiers.add(photo);
                    }

                    vo.setIdentifiers(identifiers);

                    List<String> subscribeCodes = new ArrayList<>();
                    //当前布控中，该人员所在群体拼接subscribeCode
                    subscribeCodes.addAll(getMonitorSubscribeCode(monitorId, idNumber));
                    //该人员所在的所有群体-每个群体所关联的布控中布控
                    subscribeCodes.addAll(getAllActiveSubscribeCode(idNumber));
                    vo.setMonitorIds(subscribeCodes.stream().distinct().collect(Collectors.toList()));

                    return vo;
                }).collect(Collectors.toList());

    }

    private String generateSubscribeCode(Long monitorId, Long groupId) {
        return "monitor_" + monitorId + "_" + groupId;
    }

    private String generateSubscribeCode(String prefix, Long monitorId) {
        return prefix + "_" + monitorId;
    }

    private List<String> getMonitorSubscribeCode(Long monitorId, String idNumber) {
        List<Long> monitorGroupIds = monitorTargetRelationMapper.getIdsByMonitorId(monitorId);
        List<Long> personInGroupIds = profileService.selectGroupIdsByIdNumber(idNumber);
        monitorGroupIds.retainAll(personInGroupIds);
        return monitorGroupIds.stream().map(id -> generateSubscribeCode(monitorId, id)).collect(Collectors.toList());
    }

    private List<String> getAllActiveSubscribeCode(String idNumber) {
        List<Long> groupIds = profileService.selectGroupIdsByIdNumber(idNumber);
        return groupIds.stream()
                .flatMap(id -> monitorTargetRelationMapper.getActiveMonitorIdByGroupId(id).stream()
                        .map(monitorId -> generateSubscribeCode(monitorId, id)))
                .collect(Collectors.toList());
    }

    @Override
    public void addImportantArea(Long areaId, AreaVO areaVO) {
        // request body
        AddImportantAreaQuery query = new AddImportantAreaQuery();
        query.setAreaId(areaId);
        query.setTags(List.of("areaId_" + areaId, "areaLabel_" + areaVO.getCategory()));

        List<String> graph = Arrays.stream(areaVO.getGeometries()).map(GeometryVO::toMoyeString).flatMap(List::stream)
                .collect(Collectors.toList());
        query.setGeometries(graph);
        String subscribeString = JsonUtil.toJsonString(query);

        addImportantArea(subscribeString);
    }

    private void addImportantArea(String subscribeString) {
        String response = okHttpUtil.postData(
                subscribeProperties.getAddAreaUrl(),
                subscribeString,
                subscribeProperties.getHeadersMap());
        if (StringUtils.isNotBlank(response)) {
            log.info("创建重点区域成功。moye 返回信息：{}", response);
        } else {
            throw new TRSException("创建重点区域失败。moye 接口返回为空! ");
        }
    }

    /**
     * 初始化自动布控省会和首都区域用于进京赴省预警
     */
    @PostConstruct
    public void init() {
        if (postConstructProperties.isInitImportantArea()) {
            try {
                // request body
                AddImportantAreaQuery query = new AddImportantAreaQuery();
                query.setAreaId(-1L);
                query.setTags(List.of("areaId_-1"));
                query.setGeometries(
                        new GeometryVO("GEOMETRYCOLLECTION", SubscribeConstant.BEIJING, null).toMoyeString());
                addImportantArea(JsonUtil.toJsonString(query));

                query = new AddImportantAreaQuery();
                query.setAreaId(-2L);
                query.setTags(List.of("areaId_-2"));
                query.setGeometries(
                        new GeometryVO("GEOMETRYCOLLECTION", SubscribeConstant.CHENGDU, null).toMoyeString());
                addImportantArea(JsonUtil.toJsonString(query));
            } catch (Exception exception) {
                log.error("初始化首都省会区域信息失败！请联系moye管理员！", exception);
            }
        }
    }

    @Resource
    private PersonAllTrackVOToTrackPointVOMapper mapper;

    @Override
    public List<TrackPointVO> getPersonAllTrack(PersonAllTrackRequest request) {
        String requestStr = JsonUtil.toJsonString(request);
        try {
            String response = okHttpUtil.postData(subscribeProperties.getGeneralSearchUrl(), requestStr,
                    subscribeProperties.getHeadersMap());
            Map<String, Object> responseMap = JsonUtil.parseMap(response, Object.class);
            Map<String, Object> page = JsonUtil.parseMap(JsonUtil.toJsonString(responseMap.get("data")), Object.class);
            List<PersonAllTrackVO> result = JsonUtil.parseArray(JsonUtil.toJsonString(page.get("data")),
                    PersonAllTrackVO.class);
            return result.stream().map(mapper).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询人员全量轨迹失败！");
        }
        return Collections.emptyList();
    }


    /**
     * PersonAllTrackVO 转 TrackPointVO 的 Mapper
     *
     * <AUTHOR>
     * @date 2023/03/12
     */
    @Component
    private static class PersonAllTrackVOToTrackPointVOMapper implements Function<PersonAllTrackVO, TrackPointVO> {

        @Resource
        private WarningSourceTypeMapper sourceTypeMapper;

        @Override
        public TrackPointVO apply(PersonAllTrackVO track) {
            TrackPointVO pointVO = new TrackPointVO();
            Long typeCode = sourceTypeMapper.getSourceTypeByGzylx(track.getGjlx());
            pointVO.setType(typeCode != null ? typeCode : 1L);
            pointVO.setTime(TimeUtil.timeStringFormatter(track.getHdsj(), TimeUtil.DEFAULT_TIME_PATTERN));
            pointVO.setLocation(track.getHddz());
            pointVO.setLng(track.getJdwgs84());
            pointVO.setLat(track.getWdwgs84());
            pointVO.setIdNumber(track.getZjlid());
            return pointVO;
        }
    }

    /**
     * 接收定时任务消息
     *
     * @param message 消息字符串
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(autoDelete = "true"),
            exchange = @Exchange(value = SCHEDULE_EXCHANGE, type = ExchangeTypes.FANOUT)))
    @Transactional(rollbackFor = RuntimeException.class)
    @ScheduleResult
    public void receiveScheduleMessage(String message) {
        ScheduleMessageVO messageVO = RabbitmqUtils.getMessage(message, ScheduleMessageVO.class);
        if (OperateModule.MONITOR.equals(messageVO.getModule())
                && DelayMessageTypeEnum.SYNCHRONIZE.equals(messageVO.getOperation())) {
            updateMonitorSubscriptions();
        }
    }

    private void updateMonitorSubscriptions() {
        List<String> result = getMoyeSubscriptions();
        monitorMapper.selectList(Wrappers.lambdaQuery(MonitorEntity.class)
                .eq(MonitorEntity::getMonitorStatus, MonitorStatusEnum.MONITORING)).forEach(monitor -> {
            String monitorCode = generateSubscribeCode(MONITOR_PREFIX, monitor.getId());
            //若布控未在moye布控列表中找到，重新布控
            if (!checkMonitorSubscriptionExist(monitorCode, result)) {
                sendWarningSubscribe(monitor);
                log.info("更新布控：id:{} title:{}", monitor.getId(), monitor.getMonitorTitle());
            }
        });
    }

    private List<String> getMoyeSubscriptions() {
        String response = okHttpUtil.postData(subscribeProperties.getSubscribeQueryUrl(),
                JsonUtil.toJsonString(PageParams.getAll()),
                subscribeProperties.getHeadersMap());
        Map<String, Object> responseMap = JsonUtil.parseMap(response, Object.class);
        Map<String, Object> page = JsonUtil.parseMap(JsonUtil.toJsonString(responseMap.get("data")), Object.class);
        return JsonUtil.parseArray(JsonUtil.toJsonString(page.get("data")), WarningSubscribeVO.class).stream()
                .map(WarningSubscribeVO::getSubscribeCode).distinct().collect(Collectors.toList());
    }

    private boolean checkMonitorSubscriptionExist(String monitorCode, List<String> result) {
        return result.stream().anyMatch(code -> code.equals(monitorCode));
    }

    private List<IdentifierVO> getIdentifiersV2(CareMonitorEntity entity) {
        List<IdentifierVO> identifierVOList = new ArrayList<>();
        // 拼接身份信息
        IdentifierVO idNumber = new IdentifierVO(entity.getCertificateValue(),
                entity.getCertificateType(), StringUtils.isNotEmpty(entity.getImageUrl()) ? ImageUtil.getBase64FromImageUrl(
                entity.getImageUrl()) : getNewPhoto(entity.getCertificateValue()));
        idNumber.setCustomInfo(Try.of(() -> buildCustomInfo(entity)).getOrElseThrow(cls -> new RuntimeException(cls)));
        identifierVOList.add(idNumber);
        // 车牌号
        if (CollectionUtils.isNotEmpty(entity.getCarNumber())) {
            List<IdentifierVO> carNumberList = entity.getCarNumber().stream()
                    .filter(number -> !StringUtil.isEmpty(number))
                    .map(carNumber -> new IdentifierVO(carNumber, IdentifierTypeEnum.CAR_NUMBER.getCode()))
                    .collect(Collectors.toList());
            identifierVOList.addAll(carNumberList);
        }
        // 手机号
        if (CollectionUtils.isNotEmpty(entity.getTel())) {
            List<IdentifierVO> telList = entity.getTel().stream()
                    .map(tel -> new IdentifierVO(tel, IdentifierTypeEnum.PHONE_NUMBER.getCode()))
                    .collect(Collectors.toList());
            identifierVOList.addAll(telList);
        }
        // 虚拟身份
        if (CollectionUtils.isNotEmpty(entity.getImei())) {
            List<IdentifierVO> imeiList = entity.getImei().stream()
                    .map(tel -> new IdentifierVO(tel, IdentifierTypeEnum.IMEI.getCode()))
                    .collect(Collectors.toList());
            identifierVOList.addAll(imeiList);
        }
        if (CollectionUtils.isNotEmpty(entity.getImsi())) {
            List<IdentifierVO> imsiList = entity.getImsi().stream()
                    .map(tel -> new IdentifierVO(tel, IdentifierTypeEnum.IMSI.getCode()))
                    .collect(Collectors.toList());
            identifierVOList.addAll(imsiList);
        }
        if (CollectionUtils.isNotEmpty(entity.getMac())) {
            List<IdentifierVO> macList = entity.getMac().stream()
                    .map(tel -> new IdentifierVO(tel, IdentifierTypeEnum.MAC.getCode()))
                    .collect(Collectors.toList());
            identifierVOList.addAll(macList);
        }
        return identifierVOList;
    }
}
