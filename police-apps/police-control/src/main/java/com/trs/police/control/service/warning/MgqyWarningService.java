package com.trs.police.control.service.warning;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.common.core.utils.GeoUtils;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.mapper.ImportantAreaMapper;
import com.trs.police.control.mapper.PersonRelateToFkMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 敏感区域
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MgqyWarningService implements WarningService {

    private WKTReader wktReader = new WKTReader();

    private List<Geometry> mgqyList;

    @Resource
    private ImportantAreaMapper importantAreaMapper;

    @Autowired
    private PersonRelateToFkMapper personRelateToFkMapper;

    /**
     * 初始化
     */
    @PostConstruct
    public void initMgqyList() {
        List<ImportantAreaEntity> area = importantAreaMapper.selectList(
                Wrappers.lambdaQuery(ImportantAreaEntity.class)
                        .eq(ImportantAreaEntity::getPoliceCategory, 12)
                        .eq(ImportantAreaEntity::getDeleted, 0)
        );
        mgqyList = area.stream()
                .map(
                        a -> Stream.of(a.getGeometries())
                                .map(GeometryVO::getGeometry)
                                .map(g -> Try.of(() -> wktReader.read(g)).onFailure(e -> log.error("解析重点区域失败", e)).getOrNull())
                                .collect(Collectors.toList())
                )
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public MonitorLevelEnum getWarningLevel(WarningFkrxyjEntity entity) {
        PersonRelateToFkEntity fkEntity = personRelateToFkMapper.selectOne(
                Wrappers.lambdaQuery(PersonRelateToFkEntity.class)
                        .eq(PersonRelateToFkEntity::getIdCard, entity.getIdCard())
        );
        if (Objects.isNull(fkEntity)) {
            return MonitorLevelEnum.ORANGE;
        }
        Long level = BeanFactoryHolder.getEnv().getProperty("control.fkry.tgph.control.level", Long.class, 1L);
        return level.equals(fkEntity.getControlLevel()) ? MonitorLevelEnum.YELLOW : MonitorLevelEnum.ORANGE;
    }

    /**
     * 是否是敏感区域
     *
     * @param entity 预警
     * @return 结果
     */
    public Boolean isMgqy(WarningFkrxyjEntity entity) {
        // 根据经纬度生成Geometry
        try {
            String latitude = entity.getLatitude();
            String longitude = entity.getLongitude();
            Geometry point = wktReader.read(GeoUtils.makePointByLongitudeLatitude(Double.parseDouble(longitude), Double.parseDouble(latitude)));
            return mgqyList.stream().anyMatch(ge -> checkInTopDistrict(ge, point));
        } catch (Exception e) {
            log.error("判断敏感区域失败", e);
            return false;
        }
    }

    private static boolean checkInTopDistrict(Geometry topDistrict, Geometry point) {
        return IntStream.range(0, topDistrict.getNumGeometries()).anyMatch(i -> {
            Geometry geometryN = topDistrict.getGeometryN(i);
            try {
                return point.within(geometryN);
            } catch (Exception ignore) {
                return false;
            }
        });
    }
}
