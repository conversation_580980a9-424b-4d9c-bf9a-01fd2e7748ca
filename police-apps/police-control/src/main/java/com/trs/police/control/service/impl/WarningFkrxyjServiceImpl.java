package com.trs.police.control.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.constant.enums.DelayMessageTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.constant.enums.WarningFeedbackTypeEnum;
import com.trs.police.common.core.constant.enums.WarningStatusEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.OperateVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.CategoryVO;
import com.trs.police.common.core.vo.control.WarningPhotoVO;
import com.trs.police.common.core.vo.message.ScheduleMessageVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ScheduleService;
import com.trs.police.control.config.WarningFkrxyjConfig;
import com.trs.police.control.config.WarningModalConfig;
import com.trs.police.control.constant.WarningFkrxyjConstant;
import com.trs.police.control.constant.enums.WarningNotifyEnum;
import com.trs.police.control.domain.dto.fkrxyj.SignWarningDTO;
import com.trs.police.control.domain.entity.fkrxyj.*;
import com.trs.police.common.core.request.WarningDoneRequest;
import com.trs.police.control.domain.vo.WarningTrackPointVO;
import com.trs.police.control.domain.vo.fkrxyj.FkPersonPoliceControlVO;
import com.trs.police.control.domain.vo.warning.*;
import com.trs.police.control.mapper.WarningFeedbackFkrxyjMapper;
import com.trs.police.control.mapper.WarningFkrxyjMapper;
import com.trs.police.control.mapper.WarningNotifyFkrxyjMapper;
import com.trs.police.control.mapper.WarningProcessFkrxyjMapper;
import com.trs.police.control.properties.WarningProperties;
import com.trs.police.control.service.WarningFkrxyjService;
import com.trs.police.control.service.warning.FkWarningUpdater;
import com.trs.police.control.utils.WarningModelUtil;
import net.sf.json.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/10/26 10:12
 */
@Service
public class WarningFkrxyjServiceImpl implements WarningFkrxyjService {

    @Resource
    private WarningFkrxyjMapper warningFkrxyjMapper;

    @Resource
    private SourceServiceImpl sourceService;

    @Autowired
    private WarningFkrxyjConfig warningFkrxyjConfig;

    @Resource
    private WarningFeedbackFkrxyjMapper warningFeedbackFkrxyjMapper;

    @Resource
    private WarningProcessFkrxyjMapper warningProcessFkrxyjMapper;

    @Resource
    private WarningNotifyFkrxyjMapper warningNotifyFkrxyjMapper;

    @Resource
    private ScheduleService scheduleService;

    @Resource
    private WarningProperties warningProperties;

    @Autowired
    private WarningModalConfig warningModalConfig;

    @Resource
    private PermissionService permissionService;

    @Resource
    private FkWarningUpdater fkWarningUpdater;

    @Override
    public PageResult<SpecialWarningListVO> getWarningList(ListParamsRequest request) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();

        //需要进行权限判断，如果没有权限返回空列表
        Set<Long> roleIds = currentUser.getRoleIds();

        if (!roleIds.contains(warningFkrxyjConfig.fkRoleId)
                && !roleIds.contains(warningFkrxyjConfig.fkPcsRoleId)) {
            //没有任何FK权限直接返回空集合
            return PageResult.empty(request.getPageParams());
        } else if (roleIds.contains(warningFkrxyjConfig.fkPcsRoleId)
                && !roleIds.contains(warningFkrxyjConfig.fkRoleId)) {
            //有fk派出所的权限但是没有fk的权限，只能看自己所在派出所的杆体
            KeyValueTypeVO keyValueTypeVO = new KeyValueTypeVO();
            keyValueTypeVO.setKey("group");
            keyValueTypeVO.setValue(currentUser.getDept().getShortName());
            request.getFilterParams().add(keyValueTypeVO);
        }
        // 有fk权限，可以看所有
        final Page<WarningFkrxyjEntity> page = request.getPageParams().toPage();
        Page<WarningFkrxyjEntity> result = warningFkrxyjMapper.selectPageList(request, page);

        return PageResult.of(getWarningVoList(result.getRecords()), request.getPageParams().getPageNumber(), result.getTotal(),
                request.getPageParams().getPageSize());
    }

    @Override
    public List<SpecialWarningListVO> getWarningListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<WarningFkrxyjEntity> warningFkrxyjEntities = warningFkrxyjMapper.selectBatchIds(ids);
        return getWarningVoList(warningFkrxyjEntities);
    }

    @Override
    public WarningStxfDetailVO getWarningDetail(Long warningId) {
        final SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        WarningFkrxyjEntity entity = warningFkrxyjMapper.selectById(warningId);
        WarningStxfDetailVO vo = new WarningStxfDetailVO();
        vo.setId(warningId);
        vo.setTrackId(warningId);
        MonitorLevelEnum level = entity.getWarningLevel();
        if (Objects.nonNull(level)) {
            vo.setWarningLevel(new CodeNameVO(level.getCode(), level.getName()));
        }
        //如果是静默预警，则显示内容要改变
        if (entity.getWarningModel().equalsIgnoreCase(WarningFkrxyjConstant.WANDERING_ON_SILENCE)) {
            vo.setContent("静默预警:连续10天未采集到轨迹数据");
        } else {
            vo.setContent(TimeUtil.getSimpleTime(entity.getCaptureTime()) + " " + entity.getSuspectName() + " 出现在" + entity.getCaptureAddress());
        }

        vo.setWarningTime(TimeUtil.getSimpleTime(entity.getCaptureTime()));
        vo.setWarningType("人像");
        vo.setIdNumber(entity.getIdCard());
        vo.setIdType("身份证");
        vo.setPersonType("重点人员-涉疆");
        vo.setControlType(1L);
        vo.setTargetType(1L);
        WarningProcessFkrxyjEntity process = warningProcessFkrxyjMapper.getFkrxyjUserProcessByWarningId(warningId, currentUser.getUserId(),
                currentUser.getDeptId());
        if (Objects.nonNull(process)) {
            vo.setWarningStatus(new CodeNameVO(process.getStatus().getCode(), process.getStatus().getName()));
        } else {
            if (Boolean.TRUE.equals(warningProcessFkrxyjMapper.getWarningDoneStatus(warningId))) {
                vo.setWarningStatus(new CodeNameVO(4, "已完结"));
            } else {
                vo.setWarningStatus(new CodeNameVO(5, "处置中"));
            }
        }
        return vo;
    }

    @Override
    public List<WarningTrackPointVO> getTrackPoint(Long warningId) {
        WarningFkrxyjEntity entity = warningFkrxyjMapper.selectById(warningId);
        if (entity.getLatitude() == null || entity.getLongitude() == null) {
            return null;
        }
        if (sourceService.checkPoint(Double.parseDouble(entity.getLatitude()),
                Double.parseDouble(entity.getLongitude()))) {
            WarningTrackPointVO vo = new WarningTrackPointVO();
            vo.setDateTime(entity.getCaptureTime());
            vo.setPersonName(entity.getSuspectName());
            vo.setPersonIdCard(entity.getIdCard());
            vo.setLatitude(Double.parseDouble(entity.getLatitude()));
            vo.setLongitude(Double.parseDouble(entity.getLongitude()));
            String suspectPhoto = entity.getSuspectPhoto();
            //本来就是我们自己的图片，就不需要转了
            if (suspectPhoto.startsWith("/oss")) {
                vo.setImg(suspectPhoto);
            } else {
                vo.setImg("/oss/photo/forward?base64Url=" + Base64.getEncoder().encodeToString(entity.getSuspectPhoto().getBytes()));
            }
            vo.setAddress(entity.getCaptureAddress());
            return List.of(vo);
        } else {
            return null;
        }

    }

    @Override
    public WarningProcessVO getWarningProcess(Long id) {
        WarningProcessVO warningProcessVO = new WarningProcessVO();
        warningProcessVO.setDispatch(getWarningDispatchByWarningId(id));
        warningProcessVO.setDisposal(warningProcessFkrxyjMapper.getWarningDisposalByWarningId(id));
        warningProcessFkrxyjMapper.getWarningDone(id).ifPresent(process -> {
            WarningDoneFkrxyjVO done = process.getDone();
            warningProcessVO.setDone(new WarningProcessVO.Done(TimeUtil.getSimpleTime(done.getTime()), done.getUserInfo()));
        });
        //蓝色预警，默认完结
        WarningFkrxyjEntity warning = warningFkrxyjMapper.selectById(id);
        if (warning.getWarningLevel().equals(MonitorLevelEnum.BLUE)) {
            warningProcessVO.setDone(new WarningProcessVO.Done(TimeUtil.getSimpleTime(warning.getCreateTime()), new SimpleUserVO()));
        }
        return warningProcessVO;
    }

    /**
     * 获取预警推送
     *
     * @param id 预警id
     * @return 推送
     */
    WarningDispatchVO getWarningDispatchByWarningId(Long id) {
        WarningDispatchVO warningDispatchVO = new WarningDispatchVO();
        WarningFkrxyjEntity warning = warningFkrxyjMapper.selectById(id);
        warningDispatchVO.setTime(warning.getCreateTime());
        List<WarningNotifyFkrxyjEntity> warningNotify = warningNotifyFkrxyjMapper.selectByWarningId(id);
        warningDispatchVO.setNotifyPerson(
                warningNotify.stream().filter(item -> WarningNotifyEnum.NOTIFY_PERSON.equals(item.getNotifyType()))
                        .map(item -> permissionService.findSimpleUser(item.getUserId(), item.getDeptId())).collect(
                        Collectors.toList()));

        //通知部门要进行排重
        StringBuilder notifyDept = new StringBuilder();
        for (WarningNotifyFkrxyjEntity curWarningNotify : warningNotify) {
            if (!WarningNotifyEnum.NOTIFY_PERSON.equals(curWarningNotify.getNotifyType())) {
                DeptDto dept = permissionService.getDeptById(curWarningNotify.getDeptId());
                String appendNotifyDept = dept.getShortName()
                        + "(" + curWarningNotify.getNotifyType().getName() + ")";
                if (!notifyDept.toString().contains(appendNotifyDept)) {
                    notifyDept.append(appendNotifyDept).append("、");
                }
            }
        }
        if (notifyDept.length() > 0) {
            warningDispatchVO.setNotifyDept(notifyDept.toString());
        }
        WarningDispatchVO.RegularInfo regularInfo = new WarningDispatchVO.RegularInfo();
        regularInfo.setName(warning.getName());
        regularInfo.setTitle(warning.getWarningModel());
        warningDispatchVO.setRegularInfo(regularInfo);

        return warningDispatchVO;
    }

    @Override
    public WarningActivityVO getWarningActivity(Long warningId) {
        WarningFkrxyjEntity entity = warningFkrxyjMapper.selectById(warningId);
        WarningActivityVO vo = new WarningActivityVO();
        vo.setActivityPlace(entity.getCaptureAddress());
        vo.setActivityTime(entity.getCaptureTime());
        return vo;
    }

    @Override
    public WarningPhotoVO getWarningPhoto(Long warningId) {
        WarningFkrxyjEntity entity = warningFkrxyjMapper.selectById(warningId);
        WarningPhotoVO vo = new WarningPhotoVO();

        NumberFormat percentFormat = NumberFormat.getPercentInstance();
        if (entity.getSimilarity() != null) {
            String percent = percentFormat.format(Double.parseDouble(entity.getSimilarity()));
            vo.setSimilarity(percent);
        }

        List<String> photo = new ArrayList<>();

        String facePhoto = entity.getFacePhoto();
        String curPhoto = entity.getPhoto();

        if (StringUtils.isNotBlank(facePhoto)) {
            //本来就是我们自己的图片，就不需要转了
            if (facePhoto.startsWith("/oss")) {
                photo.add(facePhoto);
            } else {
                photo.add("/oss/photo/forward?base64Url=" + Base64.getEncoder().encodeToString(entity.getFacePhoto().getBytes()));
            }
        }
        if (StringUtils.isNotBlank(curPhoto)) {
            //本来就是我们自己的图片，就不需要转了
            if (curPhoto.startsWith("/oss")) {
                photo.add(curPhoto);
            } else {
                photo.add("/oss/photo/forward?base64Url=" + Base64.getEncoder().encodeToString(entity.getPhoto().getBytes()));
            }
        }
        vo.setPhotos(photo);
        return vo;
    }

    @Override
    public PersonCardVO getWarningPerson(Long warningId) {
        WarningFkrxyjEntity entity = warningFkrxyjMapper.selectById(warningId);
        PersonCardVO vo = new PersonCardVO();
        vo.setIdNumber(entity.getIdCard());
        vo.setName(entity.getSuspectName());
        if (StringUtils.isNotBlank(entity.getFacePhoto())) {
            FileInfoVO fileInfoVO = new FileInfoVO();
            fileInfoVO.setId(-1L);
            //本来就是我们自己的图片，就不需要转了
            if (entity.getFacePhoto().startsWith("/oss")) {
                fileInfoVO.setUrl(entity.getFacePhoto());
            } else {
                fileInfoVO.setUrl("/oss/photo/forward?base64Url=" + Base64.getEncoder().encodeToString(entity.getSuspectPhoto().getBytes()));
            }

            vo.setImgs(List.of(fileInfoVO));
        }
        return vo;
    }

    @Override
    public List<CategoryVO> getWarningCategory(Long trackId) {
        return JsonUtil.parseArray("[{\"code\": 2, \"name\": \"活动时间、地点\"}, {\"code\": 3, \"name\": \"活动人员\"}, {\"code\": 4, \"name\": \"活动照片\"}]", CategoryVO.class);
    }

    @Override
    public boolean dealFK4ProcessWarning(Long timeFrameHour) {
        List<WarningFkrxyjEntity> resultList = getFK4ProcessWarning(timeFrameHour);
        for (WarningFkrxyjEntity curWarning : resultList) {
            if (StringUtils.isEmpty(curWarning.getIdCard())) {
                continue;
            }
            curWarning.setWarningLevel(MonitorLevelEnum.YELLOW);
            warningFkrxyjMapper.updateById(curWarning);
        }
        return false;
    }

    /**
     * 指定时间，出现超过指定次数的数据
     *
     * @param timeFrameHour timeFrameHour
     * @return 结果
     */
    public List<WarningFkrxyjEntity> getFK4ProcessWarning(Long timeFrameHour) {
        if (timeFrameHour == null || timeFrameHour == 0) {
            timeFrameHour = warningFkrxyjConfig.timeFrameHour;
        }
        //将筛选时间范围转换成具体时间
        LocalDateTime startTime = TimeUtil
                .getTargetTime(TimeUtil.OFFSET_UNIT_HOUR, LocalDateTime.now(), timeFrameHour);

        return warningFkrxyjMapper
                .getFK4ProcessWarning(startTime, warningFkrxyjConfig.warningThreshold);
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void signWarning(Long id) {
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        WarningProcessFkrxyjEntity process = warningProcessFkrxyjMapper.getFkrxyjUserProcessByWarningId(id, currentUser.getUserId(),
                currentUser.getDeptId());
        //Fk没有处理process，直接新建
        if (process == null) {
            process = initFkrxyjProcess(id, currentUser);
        }
        OperateVO operateVO = OperateVO.newInstance();
        process.setSign(operateVO);

        //签收时需要记录到反馈表
        WarningFeedbackFkrxyjEntity warningFeedbackFkrxyjEntity = new WarningFeedbackFkrxyjEntity();
        warningFeedbackFkrxyjEntity.setType(WarningFeedbackTypeEnum.SIGN);
        warningFeedbackFkrxyjEntity.setProcessId(process.getId());
        warningFeedbackFkrxyjEntity.setWarningId(id);
        warningFeedbackFkrxyjMapper.insert(warningFeedbackFkrxyjEntity);

        //已签收，开始研判过期计时，不知道是否需要，先注释
//        WarningFkrxyjEntity warningFkrxyjEntity = warningFkrxyjMapper.selectById(id);
//        pushWarningActionMessage(process.getId(), warningFkrxyjEntity.getWarningLevel(), WarningStatusEnum.SIGN_FINISH);
    }

    @Override
    public void signWarnings(SignWarningDTO dto) {
        if (CollectionUtils.isEmpty(dto.getIds())){
            return;
        }
        dto.getIds().forEach(id -> signWarning(id));
    }

    @Override
    public void signHistoryWarnings(String createTime) {
        long total;
        do {
            Page<Long> pageList = warningFkrxyjMapper.pageSignHistoryWarnings(createTime, new Page(1, 100));
            total = pageList.getTotal();
            batchSignWarning(pageList.getRecords());
        } while (total > 0);
    }

    private void batchSignWarning(List<Long> warningIds) {
        if (CollectionUtils.isEmpty(warningIds)) {
            return;
        }
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        QueryWrapper<WarningNotifyFkrxyjEntity> processWrapper = new QueryWrapper<WarningNotifyFkrxyjEntity>()
                .eq("user_id", currentUser.getUserId())
                .eq("dept_id", currentUser.getDeptId())
                .in("warning_id", warningIds);
        List<WarningNotifyFkrxyjEntity> notifyFkrxyjEntities = warningNotifyFkrxyjMapper.selectList(processWrapper);
        List<Long> processIds = notifyFkrxyjEntities.stream().map(WarningNotifyFkrxyjEntity::getProcessId).collect(Collectors.toList());
        List<WarningProcessFkrxyjEntity> process = new ArrayList<>();
        if (!CollectionUtils.isEmpty(processIds)) {
            process = warningProcessFkrxyjMapper.selectList(new QueryWrapper<WarningProcessFkrxyjEntity>().in("id", processIds));
        }
        List<Long> existWarningIds = process.stream().map(WarningProcessFkrxyjEntity::getWarningId).collect(Collectors.toList());
        List<Long> initWarningIds = warningIds.stream().filter(id -> !existWarningIds.contains(id)).collect(Collectors.toList());
        List<WarningProcessFkrxyjEntity> initFkrxyjProcess = initFkrxyjProcess(initWarningIds, currentUser);
        initFkrxyjProcess.addAll(process);
        Map<Long, Long> warningIdAndPeocrssIdMap = process.stream().collect(Collectors.toMap(WarningProcessFkrxyjEntity::getWarningId, WarningProcessFkrxyjEntity::getId, (i1, i2) -> i1 < i2 ? i2 : i1));
        for (Long warningId : warningIds) {
            WarningFeedbackFkrxyjEntity warningFeedbackFkrxyjEntity = new WarningFeedbackFkrxyjEntity();
            warningFeedbackFkrxyjEntity.setType(WarningFeedbackTypeEnum.SIGN);
            warningFeedbackFkrxyjEntity.setProcessId(warningIdAndPeocrssIdMap.get(warningId));
            warningFeedbackFkrxyjEntity.setWarningId(warningId);
            warningFeedbackFkrxyjMapper.insert(warningFeedbackFkrxyjEntity);
        }
    }

    @Override
    public void pushWarningActionMessage(Long processId, MonitorLevelEnum level, WarningStatusEnum status) {
        //除蓝色预警外，开始签收计时
        if (level.equals(MonitorLevelEnum.BLUE)) {
            return;
        }
        ScheduleMessageVO message = new ScheduleMessageVO();
        message.setRelatedId(processId);
        message.setModule(OperateModule.WARNING);
        //待签收状态，计时签收
        if (status.equals(WarningStatusEnum.WAITING_SIGN)) {
            message.setOperation(DelayMessageTypeEnum.WARNING_SIGN);
            message.setTimeLimit(LocalDateTime.now().plusHours(warningProperties.getSignLimit()));
            scheduleService.subscribeDelayJob(message);
        }
        //已签收状态，计时反馈
        else if (status.equals(WarningStatusEnum.SIGN_FINISH)) {
            message.setOperation(DelayMessageTypeEnum.WARNING_REPLY);
            message.setTimeLimit(LocalDateTime.now().plusHours(warningProperties.getReplyLimit()));
            scheduleService.subscribeDelayJob(message);
        }
    }

    /**
     * 初始化process
     *
     * @param warnId      warnId
     * @param currentUser 当前登录用户
     * @return process
     */
    private WarningProcessFkrxyjEntity initFkrxyjProcess(Long warnId, SimpleUserVO currentUser) {
        WarningProcessFkrxyjEntity process = new WarningProcessFkrxyjEntity();
        process.setWarningId(warnId);
        process.setStatus(WarningStatusEnum.SIGN_FINISH);
        process.setDeptId(currentUser.getDeptId());
        warningProcessFkrxyjMapper.insert(process);
        WarningNotifyFkrxyjEntity relation = new WarningNotifyFkrxyjEntity(warnId, currentUser.getUserId(),
                currentUser.getDeptId(), true, WarningNotifyEnum.NOTIFY_PERSON, process.getId());
        warningNotifyFkrxyjMapper.insert(relation);
        return process;
    }

    private List<WarningProcessFkrxyjEntity> initFkrxyjProcess(List<Long> warningIds, SimpleUserVO currentUser) {
        List<WarningProcessFkrxyjEntity> processList = new ArrayList<>();
        for (Long warnId : warningIds) {
            WarningProcessFkrxyjEntity process = new WarningProcessFkrxyjEntity();
            process.setWarningId(warnId);
            process.setStatus(WarningStatusEnum.SIGN_FINISH);
            process.setDeptId(currentUser.getDeptId());
            warningProcessFkrxyjMapper.insert(process);
            WarningNotifyFkrxyjEntity relation = new WarningNotifyFkrxyjEntity(warnId, currentUser.getUserId(),
                    currentUser.getDeptId(), true, WarningNotifyEnum.NOTIFY_PERSON, process.getId());
            warningNotifyFkrxyjMapper.insert(relation);
            processList.add(process);
        }
        return processList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void feedbackWarning(Long id, WarningFeedbackDetailFkrxyjVO feedbackVO) {
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        WarningProcessFkrxyjEntity process = warningProcessFkrxyjMapper.getFkrxyjUserProcessByWarningId(id, currentUser.getUserId(),
                currentUser.getDeptId());
        //只有已签收状态反馈才会更改process值
        if (process.getStatus().equals(WarningStatusEnum.SIGN_FINISH)) {
            process.setStatus(WarningStatusEnum.REPLY_FINISH);
            warningProcessFkrxyjMapper.updateById(process);
        }
        WarningFeedbackFkrxyjEntity warningFeedbackFkrxyjEntity = new WarningFeedbackFkrxyjEntity();
        warningFeedbackFkrxyjEntity.setWarningId(id);
        warningFeedbackFkrxyjEntity.setProcessId(process.getId());
        warningFeedbackFkrxyjEntity.setType(WarningFeedbackTypeEnum.FEEDBACK);
        warningFeedbackFkrxyjEntity.setFeedback(feedbackVO);
        warningFeedbackFkrxyjMapper.insert(warningFeedbackFkrxyjEntity);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteWarningFeedback(Long id) {
        warningFeedbackFkrxyjMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateWarningFeedback(Long id, WarningFeedbackDetailFkrxyjVO feedbackVO) {
        WarningFeedbackFkrxyjEntity warningFeedbackFkrxyjEntity = warningFeedbackFkrxyjMapper.selectById(id);
        if (Objects.nonNull(warningFeedbackFkrxyjEntity)) {
            warningFeedbackFkrxyjEntity.setFeedback(feedbackVO);
            warningFeedbackFkrxyjMapper.updateById(warningFeedbackFkrxyjEntity);
        }
    }

    @Override
    public List<WarningFeedbackFkrxyjListVo> getFeedbackList(Long warningId) {
        return warningFeedbackFkrxyjMapper.getFeedbackListByWarningId(warningId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void doneWarning(Long id, WarningDoneRequest request) {
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        final WarningProcessFkrxyjEntity process = warningProcessFkrxyjMapper.getFkrxyjUserProcessByWarningId(id,
                currentUser.getUserId(), currentUser.getDeptId());
        WarningDoneFkrxyjVO warningDoneVO = new WarningDoneFkrxyjVO();
        warningDoneVO.setTime(LocalDateTime.now());
        warningDoneVO.setUserInfo(currentUser);
        warningDoneVO.setAttachments(request.getAttachments());
        warningDoneVO.setIsHandle(request.getIsHandle());
        warningDoneVO.setNotHandleReason(request.getNotHandleReason());
        warningDoneVO.setNotHandleDescription(request.getNotHandleDescription());
        warningDoneVO.setComment(request.getComment());
        process.setDone(warningDoneVO);
        process.setStatus(WarningStatusEnum.PROCESS_FINISH);
        warningProcessFkrxyjMapper.updateById(process);
        warningProcessFkrxyjMapper.updateFkrxyjProcessDoneByWarningId(id);

    }

    @Override
    public WarningDoneFkrxyjVO getWarningDone(Long id) {
        return warningProcessFkrxyjMapper.getFkrxyjWarningDone(id).orElse(new WarningProcessFkrxyjEntity()).getDone();
    }

    @Override
    public boolean silenceWarning() {
        //查询出所有的已经建档的fk人员
        List<ProfilePerson> fkPersons = warningFkrxyjMapper.getFkOnRecord(warningFkrxyjConfig.fkLabel);

        //遍历查询fk预警表和全部预警表，如果超过10天没有预警信息，则生成一条静默预警信息
        LocalDateTime searchTime = LocalDateTime.now().minusDays(warningFkrxyjConfig.silentTime);
        for (ProfilePerson profilePerson : fkPersons) {
            QueryWrapper<WarningFkrxyjEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.ge("capture_time", searchTime);
            queryWrapper.eq("id_card", profilePerson.getIdNumber());
            long count = warningFkrxyjMapper.selectCount(queryWrapper);

            long normalCount = warningFkrxyjMapper.getCount4NormalWarning(profilePerson.getIdNumber(), searchTime);

            //增加静默模型的调用次数
            WarningModelUtil.asyncCallModalCount(warningModalConfig.silenceModalId);

            if (count > 0 || normalCount > 0) {
                continue;
            }
            //有多个管控派出所需要生成多条静默预警信息
            List<FkPersonPoliceControlVO> fkPersonPoliceControlVOList
                    = warningFkrxyjMapper.getPersonPoliceControl(profilePerson.getId());
            for (FkPersonPoliceControlVO curPoliceControl : fkPersonPoliceControlVOList) {
                //生成一条新的静默预警信息
                WarningFkrxyjEntity entity = new WarningFkrxyjEntity();
                String photo = "";
                String photos = profilePerson.getPhoto();
                if (!StringUtils.isEmpty(photos)) {
                    JSONArray fileInfoVoS = JSONArray.fromObject(photos);
                    if (!fileInfoVoS.isEmpty()){
                        photo = fileInfoVoS.optJSONObject(0).optString("url");
                    }
                }
                entity.setFacePhoto(photo);
                entity.setSuspectPhoto(photo);
                entity.setSimilarity("1");
                entity.setWarningModel(WarningFkrxyjConstant.WANDERING_ON_SILENCE);
                entity.setName(profilePerson.getName());
                entity.setWarningLevel(MonitorLevelEnum.YELLOW);
                fkWarningUpdater.update(entity);
                entity.setIdCard(profilePerson.getIdNumber());
                entity.setCaptureTime(LocalDateTime.now());
                entity.setCreateTime(LocalDateTime.now());
                //需要处理预警信息所属派出所：人员所属的管控派出所的简称
                entity.setGroup(curPoliceControl.getShortName());
                //处理民族
                entity.setNation(entity.getName().contains("·")
                        ? WarningFkrxyjConstant.NATION_CODE_WZ : WarningFkrxyjConstant.NATION_CODE_HZ);
                warningFkrxyjMapper.insert(entity);
                WarningModelUtil.pushModelStatisticsMessage(warningModalConfig.silenceModalId);
            }

        }

        return true;
    }

    private List<SpecialWarningListVO> getWarningVoList(List<WarningFkrxyjEntity> entities) {
        List<SpecialWarningListVO> collect = entities.stream().map(item -> {
            SpecialWarningListVO warningListVO = new SpecialWarningListVO();
            warningListVO.setId(item.getId());
            if (StringUtils.isNotBlank(item.getFacePhoto())) {
                String suspectPhoto = item.getSuspectPhoto();
                //本来就是我们自己的图片，就不需要转了
                if (suspectPhoto.startsWith("/oss")) {
                    warningListVO.setPhoto(suspectPhoto);
                } else {
                    warningListVO.setPhoto("/oss/photo/forward?base64Url=" + Base64.getEncoder().encodeToString(item.getSuspectPhoto().getBytes()));
                }
            }
            warningListVO.setIdType("身份证");
            warningListVO.setWarningType("人像");
            String name = StringUtils.isEmpty(item.getName()) ? item.getSuspectName() : item.getName();
            warningListVO.setActivityPerson(name);
            warningListVO.setIdNumber(item.getIdCard());
            warningListVO.setPersonType("重点人员-涉疆");
            warningListVO.setSourceType("人像");
            warningListVO.setActivityAddress(item.getCaptureAddress());
            warningListVO.setWarningTime(item.getCreateTime());
            warningListVO.setActivityTime(item.getCaptureTime());
            warningListVO.setWarningModel(item.getWarningModel());
            MonitorLevelEnum level = item.getWarningLevel();
            if (Objects.nonNull(level)) {
                warningListVO.setWarningLevel(new CodeNameVO(level.getCode(), level.getName()));
            }
            List<WarningProcessFkrxyjEntity> processList = warningProcessFkrxyjMapper.getFkrxyjUserProcessByWarningIdV2(item.getId());
            if (!CollectionUtils.isEmpty(processList)) {
                Optional<WarningProcessFkrxyjEntity> first = processList.stream()
                        .filter(v -> v.getStatus() != null)
                        .sorted(Comparator.comparing(v -> -v.getStatus().getCode()))
                        .findFirst();
                if(first.isPresent()){
                    warningListVO.setStatus(new CodeNameVO(first.get().getStatus().getCode(), first.get().getStatus().getName()));
                }
            }
            return warningListVO;
        }).collect(Collectors.toList());
        return collect;
    }
}
