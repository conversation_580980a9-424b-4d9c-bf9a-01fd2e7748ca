package com.trs.police.control.service.warning;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.mapper.WarningFkrxyjMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.police.control.constant.WarningFkrxyjConstant.WANDERING_ON_GATHER;

/**
 *  聚集
 *
 * <AUTHOR>
 */
@Component
public class JjWarningService implements WarningService {

    @Resource
    private WarningFkrxyjMapper warningFkrxyjMapper;

    @Override
    public MonitorLevelEnum getWarningLevel(WarningFkrxyjEntity entity) {
        return MonitorLevelEnum.YELLOW;
    }

    /**
     * 获取到聚集列表
     *
     * @param warningList 新轨迹列表
     * @return 聚集预警列表
     */
    public List<WarningFkrxyjEntity> jjList(List<WarningFkrxyjEntity> warningList) {
        // warningList 按照时间分组 每5分钟一组
        List<List<WarningFkrxyjEntity>> entitiesGroup = jjListGroup(warningList);
        List<WarningFkrxyjEntity> jjResult = entitiesGroup.stream()
                .map(this::buildJjList)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        return jjResult;
    }

    private List<WarningFkrxyjEntity> buildJjList(List<WarningFkrxyjEntity> entities) {
        if (entities.isEmpty()) {
            return new ArrayList<>();
        }
        WarningFkrxyjEntity first = entities.get(0);
        // 重数据库中找出相同感知源到5分钟前到现在的所有数据
        List<WarningFkrxyjEntity> warningFkrxyjEntities = warningFkrxyjMapper.selectList(
                Wrappers.lambdaQuery(WarningFkrxyjEntity.class)
                        .eq(WarningFkrxyjEntity::getDeviceCode, first.getDeviceCode())
                        .between(WarningFkrxyjEntity::getCaptureTime, first.getCaptureTime().minusMinutes(5), first.getCaptureTime())
        );
        // 合并所有轨迹
        List<WarningFkrxyjEntity> mg = new ArrayList<>();
        mg.addAll(entities);
        mg.addAll(warningFkrxyjEntities);
        // 根据身份证分组
        Map<String, List<WarningFkrxyjEntity>> group = mg.stream().collect(Collectors.groupingBy(WarningFkrxyjEntity::getIdCard));
        // 找出次数大于三次的人员
        Integer personWarningCount = BeanFactoryHolder.getEnv().getProperty("control.fkry.jz.count.person", Integer.class, 3);
        List<List<WarningFkrxyjEntity>> jj = group.entrySet()
                .stream()
                .filter(en -> en.getValue().size() >= personWarningCount)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        // 群体数量大于xxxx
        Integer groupSize = BeanFactoryHolder.getEnv().getProperty("control.fkry.jz.count.group", Integer.class, 3);
        if (jj.size() >= groupSize) {
            // 构造聚集轨迹预警
            List<WarningFkrxyjEntity> jjWarningList = jj.stream()
                    .map(ens -> ens.get(ens.size() - 1))
                    .map(en -> {
                        WarningFkrxyjEntity jjEntity = new WarningFkrxyjEntity();
                        BeanUtils.copyProperties(en, jjEntity);
                        jjEntity.setId(null);
                        jjEntity.setWarningLevel(getWarningLevel(en));
                        jjEntity.setWarningModel(WANDERING_ON_GATHER);
                        return jjEntity;
                    })
                    .collect(Collectors.toList());
            return jjWarningList;
        }
        return new ArrayList<>();
    }

    /**
     * 获取到聚集列表
     *
     * @param warningList 新轨迹列表
     * @return 聚集预警列表
     */
    private List<List<WarningFkrxyjEntity>> jjListGroup(List<WarningFkrxyjEntity> warningList) {
        Integer jz = BeanFactoryHolder.getEnv().getProperty("control.fkry.jz.time", Integer.class, 5);
        if (warningList == null || warningList.isEmpty()) {
            return Collections.emptyList();
        }

        // 按照时间排序
        List<WarningFkrxyjEntity> sortedList = warningList.stream()
                .sorted(Comparator.comparing(WarningFkrxyjEntity::getCaptureTime))
                .collect(Collectors.toList());

        // 存储分组后的结果
        List<List<WarningFkrxyjEntity>> groupedBy5Minutes = new ArrayList<>();
        List<WarningFkrxyjEntity> currentGroup = new ArrayList<>();

        for (int i = 0; i < sortedList.size(); i++) {
            WarningFkrxyjEntity current = sortedList.get(i);
            LocalDateTime currentTime = current.getCaptureTime();

            if (currentGroup.isEmpty()) {
                // 开始一个新的分组
                currentGroup.add(current);
            } else {
                // 计算当前元素与当前分组第一个元素的时间差（分钟）
                LocalDateTime firstTimeInGroup = currentGroup.get(0).getCaptureTime();
                long minutesDiff = java.time.Duration.between(firstTimeInGroup, currentTime).toMinutes();
                if (minutesDiff <= jz) {
                    // 如果时间差小于等于5分钟，继续留在当前分组
                    currentGroup.add(current);
                } else {
                    // 否则结束当前分组，开始新的分组
                    groupedBy5Minutes.add(currentGroup);
                    currentGroup = new ArrayList<>();
                    currentGroup.add(current);
                }
            }

            // 如果是最后一个元素，确保当前分组被添加到结果中
            if (i == sortedList.size() - 1) {
                groupedBy5Minutes.add(currentGroup);
            }
        }

        // 返回每个分组的第一个和最后一个元素都在5分钟内的所有分组的合并结果
        return groupedBy5Minutes;
    }
}
