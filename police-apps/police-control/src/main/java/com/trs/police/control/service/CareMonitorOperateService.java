package com.trs.police.control.service;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.MonitorListVO;
import com.trs.police.common.core.vo.control.TrackVO;
import com.trs.police.control.domain.vo.monitor.TrackStatisticsVO;
import com.trs.police.control.domain.vo.warning.WarningDetailVO;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 *
 * 布控数据服务
 */
public interface CareMonitorOperateService {


    /**
     * 获取关注监控列表
     *
     * @param request request
     * @return 列表
     */
    PageResult<MonitorListVO> monitorList(ListParamsRequest request);

    /**
     * 根据身份证号和相关数据id获取轨迹列表
     *
     * @param idNumber       idNumber
     * @param relatedDataId    relatedDataId
     * @return 列表
     */
    List<TrackVO> getTrackListByIdNumber(String idNumber,
                                         Long relatedDataId);

    /**
     * 根据身份证号统计轨迹个数
     *
     * @param idNumber       idNumber
     * @param relatedDataId    relatedDataId
     * @param relatedType    relatedType
     * @return 列表
     */
    List<TrackStatisticsVO> statisticsTrackByIdNumber(String idNumber,Long relatedDataId,String relatedType);

    /**
     * 删除监控
     *
     * @param monitorId monitorId
     */
    void removeMonitor(Long monitorId);

    /**
     * 撤控
     *
     * @param personIds personIds
     * @param relatedDataId relatedDataId
     * @param relatedType relatedType
     * @return 结果
     */
    RestfulResultsV2<Object> cancelMonitor(String personIds,
                                           Long relatedDataId,
                                           String relatedType);

    /**
     * 获取预警轨迹详情
     *
     * @param warningId warningId
     * @return WarningDetailVO
     */
    WarningDetailVO getWarningTrackDetail(String warningId);
}
