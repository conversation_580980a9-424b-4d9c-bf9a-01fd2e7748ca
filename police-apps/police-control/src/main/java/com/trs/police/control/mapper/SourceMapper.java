package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.District;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.control.SourceListVO2;
import com.trs.police.control.domain.dto.SourceDto;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.vo.CameraVO;
import com.trs.police.control.domain.vo.HaiKanCamerasDeviceInfoVO;
import com.trs.police.control.domain.vo.basic.SourceBasicVO;
import com.trs.police.control.domain.vo.basic.SourceBatchRequestVO;
import com.trs.police.control.domain.vo.basic.SourceExportRequestVO;
import com.trs.police.control.domain.vo.basic.SourceListVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 感知源(Source)持久层
 *
 * <AUTHOR>
 * @date 2022-08-11 14:03:48
 */
@Mapper
public interface SourceMapper extends BaseMapper<SourceEntity> {

    /**
     * 获取感知源信息
     *
     * @param sourceId 感知源id
     * @return {@link SourceBasicVO}
     */
    SourceBasicVO getSourceBasicInfo(Long sourceId);

    /**
     * 获取感知源
     *
     * @param sourceId 感知源id
     * @return {@link  SourceEntity}
     */
    SourceEntity selectById(Long sourceId);

    /**
     * 更新感知源
     *
     * @param sourceEntity {@link  SourceEntity}
     * @return 结果
     */
    int updateSourceById(SourceEntity sourceEntity);

    /**
     * 更新感知源
     *
     * @param sourceEntity 感知源
     * @return 结果
     */
    int updateSourceByIdWithOutUserInfo(SourceEntity sourceEntity);


    /**
     * 获取在区域内的感知源
     *
     * @param geometryVO {@link  GeometryVO}
     * @param isLocalSource 是否是本地引擎
     * @return {@link  SourceEntity}
     */
    List<SourceListVO> getSourceInArea(@Param("geometry") GeometryVO geometryVO,
                                       @Param("isLocalSource") Boolean isLocalSource);

    /**
     * getSourceInAreaCount<BR>
     *
     * @param geometryVO 参数
     * @param isLocalSource 是否是本地引擎
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/24 14:30
     */
    Long getSourceInAreaCount(@Param("geometry") GeometryVO geometryVO,
                              @Param("isLocalSource") Boolean isLocalSource);

    /**
     * 获取在区域内的感知源
     *
     * @param geometryVO   {@link  GeometryVO}
     * @param filterParams 过滤参数
     * @param searchParams 检索参数
     * @param isLocalSource 是否是本地引擎
     * @return {@link  SourceEntity}
     */
    List<SourceListVO> getSourcePointInArea(@Param("geometry") GeometryVO geometryVO,
                                            @Param("filterParams") List<KeyValueTypeVO> filterParams,
                                            @Param("searchParams") SearchParams searchParams,
                                            @Param("isLocalSource") Boolean isLocalSource);

    /**
     * getSourcePointInAreaForCount<BR>
     *
     * @param geometryVO   参数
     * @param filterParams 参数
     * @param searchParams 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/24 14:19
     */
    Long getSourcePointInAreaForCount(@Param("geometry") GeometryVO geometryVO,
                                      @Param("filterParams") List<KeyValueTypeVO> filterParams,
                                      @Param("searchParams") SearchParams searchParams);

    /**
     * 批量更新
     *
     * @param sourceVO {@link SourceBatchRequestVO}
     */
    void batchUpdate(SourceBatchRequestVO sourceVO);

    /**
     * 获取感知源列表
     *
     * @param filterParams 检索参数
     * @param searchParams 模糊检索参数
     * @param geometry     坐标参数
     * @param isLocalSource 是否是本地引擎
     * @return {@link  SourceListVO}
     */
    List<SourceListVO> selectList(
            @Param("filterParams") List<KeyValueTypeVO> filterParams,
            @Param("searchParams") SearchParams searchParams,
            @Param("geometry") GeometryVO geometry,
            @Param("isLocalSource") Boolean isLocalSource
    );

    /**
     * selectCount<BR>
     *
     * @param filterParams 参数
     * @param searchParams 参数
     * @param geometry gt
     * @param isLocalSource 是否是本地引擎
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/12 21:20
     */
    Long selectCount(
            @Param("filterParams") List<KeyValueTypeVO> filterParams,
            @Param("searchParams") SearchParams searchParams,
            @Param("geometry") GeometryVO geometry,
            @Param("isLocalSource") Boolean isLocalSource
    );

    /**
     * 列表页查询
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param geometry     坐标参数
     * @param isLocalSource 是否是本地引擎
     * @param pageParams   分页参数
     * @return 分页查询结果
     */
    Page<SourceListVO> selectPageList(
            @Param("filterParams") List<KeyValueTypeVO> filterParams,
            @Param("searchParams") SearchParams searchParams,
            @Param("geometry") GeometryVO geometry,
            @Param("isLocalSource") Boolean isLocalSource,
            Page<SourceListVO> pageParams
    );

    /**
     * 批量查询感知源
     *
     * @param sourceExportRequestVO 请求参数
     * @return {@link  SourceListVO}
     */
    List<SourceListVO> selectByExportRequest(SourceExportRequestVO sourceExportRequestVO);

    /**
     * 从moye同步感知源
     *
     * @param sourceDtos 同步的数据
     * @return {@link  SourceListVO}
     */
    Integer synchronizeSource(@Param("sourceDtos") List<SourceDto> sourceDtos);

    /**
     * 插入感知源
     *
     * @param sourceList 同步的数据
     * @return {@link  SourceListVO}
     */
    Integer synchronizeSourceV2(@Param("sourceDtos") List<SourceDto> sourceList);

    /**
     * 根据unique_key查询感知源id
     *
     * @param uniqueKey 主键
     * @return 感知源id
     */
    @Select("select id, create_user_id, create_dept_id, create_time, update_user_id, update_dept_id, update_time, name, code, `type`, category, nesting_category, district_name, district_code, dept, address, if(st_srid(t1.point) = 0,ST_AsText(t1.point),ST_AsText(ST_SwapXY(t1.point))) as point, unique_key, is_legal" +
            " from t_control_warning_source t1 where unique_key = #{uniqueKey} limit 1")
    @ResultMap("mybatis-plus_SourceEntity")
    SourceEntity selectByUniqueKey(@Param("uniqueKey") String uniqueKey);

    /**
     * 批量查询感知源数据
     *
     * @param uniqueKeys 唯一键集合
     * @return 感知源列表
     */
    List<SourceEntity> selectBatchByUniqueKey(@Param("uniqueKeys") List<String> uniqueKeys);

    /**
     * 根据unique_key查询感知源id
     *
     * @param uniqueKey 主键
     * @return 感知源id
     */
    @Select("select id,`name`,`code`,type,unique_key,ST_AsText(ST_SwapXY(`point`)) AS `point` from t_control_warning_source where unique_key = #{uniqueKey} limit 1")
    @ResultMap("mybatis-plus_SourceEntity")
    SourceEntity selectPointByUniqueKey(@Param("uniqueKey") String uniqueKey);

    /**
     * 根据name查询感知源id
     *
     * @param name 主键
     * @return 感知源id
     */
    @Select("select id from t_control_warning_source where name = #{name} limit 1")
    Long selectByName(@Param("name") String name);

    /**
     * 根据id查询感知源类型
     *
     * @param id 感知源id
     * @return 感知源类型
     */
    @Select("WITH recursive t1 AS ("
            + "                   SELECT id,p_id,1 as level,name FROM t_dict WHERE type = 'control_warning_source_type'  AND CODE = (select type from t_control_warning_source where id = #{id})"
            + "                   UNION ALL SELECT t2.id ,t2.p_id, (t1.level+1) as level,t2.name FROM t_dict t2 INNER JOIN t1 ON t2.id = t1.p_id  where  type = 'control_warning_source_type' "
            + "               ) SELECT name FROM t1  ORDER BY  level desc limit 1")
    String getSourceTopType(Long id);

    /**
     * 根据区域范围查询视频监控感知源列表
     *
     * @param geometry {@link GeometryVO}
     * @return {@link CameraVO}
     */
    List<CameraVO> selectCameraList(@Param("geometry") GeometryVO geometry);

    /**
     * 根据id查询视频监控感知源
     *
     * @param id 感知源id
     * @return {@link CameraVO}
     */
    CameraVO selectCamera(Long id);

    /**
     * 根据预警id查询感知源
     *
     * @param warningId 预警id
     * @return {@link SourceEntity}
     */
    @ResultMap("mybatis-plus_SourceEntity")
    @Select("select * from t_control_warning_source "
            + "where unique_key = (select source_id from t_warning_track where warning_id = #{warningId} limit 1)")
    SourceEntity selectByWarningId(@Param("warningId") Long warningId);

    /**
     * 获取码表路径
     *
     * @param type 类型
     * @param code code
     * @return path
     */
    @Select("with recursive t1 as(\n"
            + "                    select id,code,p_id ,1 as level from t_dict where type=#{type} and code=#{code}\n"
            + "                    union all\n"
            + "                    select t2.id,t2.code,t2.p_id,t1.level+1 as level from t_dict t2 inner join t1 on t2.id = t1.p_id where type=#{type}\n"
            + "                    )select code from t1 ORDER BY t1.level desc")
    List<Long> getDictPathByCode(@Param("type") String type, @Param("code") Long code);

    /**
     * @param id              id
     * @param nestingCategory nestingCategory
     */
    @Update("update t_control_warning_source set nesting_category=#{nestingCategory} where id =#{id}")
    void updateSourceCategory(@Param("id") Long id, @Param("nestingCategory") String nestingCategory);

    /**
     * 插入hk
     *
     * @param vo vo
     */
    void insertHkCamera(@Param("vo") HaiKanCamerasDeviceInfoVO vo);

    /**
     * 根据id查询大华监控源
     *
     * @param id id
     * @return {@link CameraVO}
     */
    CameraVO selectDhCamera(@Param("id") Long id);

    /**
     * 批量遍历查询
     *
     * @param limit 批量条数
     * @param offset 偏移量
     * @return {@link SourceEntity}
     */
    @Select("SELECT id, " +
            "if(st_srid(point) = 0,ST_AsText(point),ST_AsText(ST_SwapXY(point))) as point " +
            "FROM t_control_warning_source ORDER BY id LIMIT #{limit} OFFSET #{offset}")
    List<SourceEntity> batchSelect(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据区划代码查询GEO信息
     *
     * @param code 区划代码
     * @return GEO信息
     */
    @Select("SELECT id, code, name, short_name, main_name, p_code, level, " +
            "if(st_srid(center) = 0,ST_AsText(center),ST_AsText(ST_SwapXY(center))) as center, " +
            "if(st_srid(contour) = 0,ST_AsText(contour),ST_AsText(ST_SwapXY(contour))) as contour, " +
            "path FROM t_district WHERE code = #{code} LIMIT 1")
    District selectGeoByCode(@Param("code") String code);

    /**
     * 根据区划代码查询GEO信息
     *
     * @param code 区划代码
     * @return GEO信息
     */
    @Select("SELECT id, code, name, short_name, main_name, p_code, level, " +
            "if(st_srid(center) = 4326,ST_AsText(center),ST_AsText(ST_SwapXY(center))) as center, " +
            "if(st_srid(contour) = 4326,ST_AsText(contour),ST_AsText(ST_SwapXY(contour))) as contour, " +
            "path FROM t_district WHERE code = #{code} LIMIT 1")
    District selectGeo4326ByCode(@Param("code") String code);

    /**
     * 更新行政区划信息
     *
     * @param id 主键
     * @param code 行政区划代码
     * @param name 行政区划名称
     * @return 更新条数
     */
    @Update("UPDATE t_control_warning_source SET district_code = #{code}, district_name = #{name} WHERE id = #{id}")
    int updateDistrictInfo(@Param("id") Long id, @Param("code") String code, @Param("name") String name);

    /**
     * 根据部门代码查询感知源列表
     *
     * @param deptCode 部门代码
     * @param searchParams 搜索参数
     * @return 感知源列表
     */
    List<SourceListVO2> getSourceListByDept(@Param("deptCode") String deptCode,
                                            @Param("searchParams") SearchParams searchParams);
}