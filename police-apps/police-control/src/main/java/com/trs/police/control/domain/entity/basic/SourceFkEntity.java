package com.trs.police.control.domain.entity.basic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 感知源(Source)数据访问类
 *
 * <AUTHOR>
 * @since 2022-08-11 14:03:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_control_warning_source_fk",autoResultMap = true)
public class SourceFkEntity implements Serializable {
    
    private static final long serialVersionUID = -33259842359515744L;

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 国标编码
     */
    private String deviceCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 所属区域
     */
    private String regionId;

    /**
     * MAC地址
     */
    private String devMac;

    /**
     * 行政区域
     */
    private String administrativeDivision;

    /**
     * 通道能力集
     */
    private String cameraFunction;

    /**
     * 监控点位类型
     */
    private String monitorType;

    /**
     * 通道主能力集
     */
    private String cameraMainFunction;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 设备厂商
     */
    private String manufacturer;

    /**
     * 设备型号
     */
    private String devModel;

    /**
     * 通道号
     */
    private String channelNo;

    /**
     * 摄像机类型
     */
    private String cameraType;

    /**
     * 平台编码
     */
    private String platCode;

    /**
     * 所属设备编码
     */
    private String parentDeviceCode;

    /**
     * 所属杆件
     */
    private String rodId;

    /**
     * 数据归属
     */
    private String dataLevel;

    /**
     * 摄像机安装高度
     */
    private String deviceHeight;

    /**
     * 云哨所属单位
     */
    private Long ysDept;
}