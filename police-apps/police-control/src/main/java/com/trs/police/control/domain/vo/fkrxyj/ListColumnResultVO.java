package com.trs.police.control.domain.vo.fkrxyj;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 动态列表列查询结果
 *
 * <AUTHOR>
 * @date 2022/11/14 9:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListColumnResultVO {

    /**
     * 字段名
     */
    private String key;
    /**
     * 字段类型
     */
    private String type;
    /**
     * 显示值
     */
    private Object value;
    /**
     * 修改值
     */
    private Object editValue;
    /**
     * 其他配置
     */
    private JsonNode properties;
    /**
     * 列表标题
     */
    private String title;


    private String backgroundColor;
}
