package com.trs.police.control.kafka;

import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjDTO;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.mapper.WarningFkrxyjMapper;
import com.trs.police.control.properties.KafkaFkrxyjWarningConsumerProperties;
import com.trs.police.control.service.warning.FkMessageHelper;
import com.trs.police.control.service.warning.FkWarningUpdater;
import com.trs.police.control.service.warning.JjWarningService;
import com.trs.police.control.service.warning.MgqyWarningService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * 60w数据 聚集
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@ConditionalOnBean(value = KafkaFkrxyjWarningConsumerProperties.class)
public class KafkaFkrxyjJjWarningConsumer {

    private KafkaConsumer<String, String> consumer;

    private final KafkaFkrxyjWarningConsumerProperties properties;

    private final MgqyWarningService mgqyWarningService;

    private final FkWarningUpdater fkWarningUpdater;

    private final WarningFkrxyjMapper warningFkrxyjMapper;

    private JjWarningService jjWarningService;

    public KafkaFkrxyjJjWarningConsumer(
            KafkaFkrxyjWarningConsumerProperties properties,
            MgqyWarningService mgqyWarningService,
            FkWarningUpdater fkWarningUpdater,
            WarningFkrxyjMapper warningFkrxyjMapper,
            JjWarningService jjWarningService) {
        this.properties = properties;
        this.mgqyWarningService = mgqyWarningService;
        this.fkWarningUpdater = fkWarningUpdater;
        this.warningFkrxyjMapper = warningFkrxyjMapper;
        this.jjWarningService = jjWarningService;

        if (com.trs.common.utils.StringUtils.isEmpty(properties.getJjGroupId())) {
            log.info("fk预警未启动聚集预警消费，跳过初始化");
            return;
        }
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, properties.getBootStrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, properties.getJjGroupId());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, properties.getMaxJjPollRecords());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, properties.getAutoOffsetReset());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);//关闭自动提交kafka事务，批量处理时控制回滚策略
        consumer = new KafkaConsumer<>(props);
        consumer.subscribe(List.of(properties.getTopic()));
    }

    /**
     * 消费人员预警信息
     */
    @Scheduled(fixedDelay = 1L)
    public void consumer() {
        if (Objects.isNull(consumer)) {
            return;
        }
        final ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(properties.getPollDuration()));
        log.info("本批次数据共消费反恐人像预警（聚集）数据：{} 条", records.count());
        List<ConsumerRecord<String, String>> recordList = new ArrayList<>(records.count());
        records.forEach(recordList::add);

        // 处理该批次消费到的数据
        List<WarningFkrxyjEntity> list = recordList.stream()
                .map(message -> {
                    if (StringUtils.isBlank(message.value())) {
                        return null;
                    }
                    return JsonUtil.parseObject(message.value(), WarningFkrxyjDTO.class);
                })
                .filter(Objects::nonNull)
                .map(FkMessageHelper::buildEntity)
                .collect(Collectors.toList());
        List<WarningFkrxyjEntity> jjList = jjWarningService.jjList(list);
        // 更新预警等级
        jjList.forEach(fkWarningUpdater::update);

        jjList.forEach(warningFkrxyjMapper::insert);

        //提交kafka事务
        consumer.commitSync();
    }
}
