package com.trs.police.control.converter;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.entity.ThemeGjxxbEntity;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.dwd.WarningPersonClueEntity;
import com.trs.police.control.domain.dto.BzWarningDTO;
import com.trs.police.control.domain.entity.BzWarningTrackEntity;
import com.trs.police.control.domain.entity.FxWarningEntity;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.domain.vo.BzWarningTrackListVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 预警实体转换器
 *
 * <AUTHOR>
 */
@Mapper
public interface WarningEntityConverter {

    WarningEntityConverter INSTANCE = Mappers.getMapper(WarningEntityConverter.class);

    /**
     * 预警实体转换成fx实体
     *
     * @param warning 预警基础信息
     * @return fx
     */
    @Mappings({
            @Mapping(target = "id", source = "id"),
            @Mapping(target = "createTime", source = "createTime"),
            @Mapping(target = "updateUserId", source = "updateUserId"),
            @Mapping(target = "updateDeptId", source = "updateDeptId"),
            @Mapping(target = "updateTime", source = "updateTime"),
            @Mapping(target = "warningType", source = "warningType"),// 已在公告设置属性中设置了
            @Mapping(target = "warningLevel", source = "warningLevel"),
            @Mapping(target = "content", source = "content"),
            @Mapping(target = "warningTime", source = "warningTime"),// 已在公告设置属性中设置了
            @Mapping(target = "monitorId", source = "monitorId"),// 已在公告设置属性中设置了
            @Mapping(target = "modelId", source = "modelId"),// 已在公告设置属性中设置了
            @Mapping(target = "groupId", source = "groupId"),
            @Mapping(target = "controlType", source = "controlType"),// 已在公告设置属性中设置了
            @Mapping(target = "activityTime", source = "activityTime"),
            @Mapping(target = "activityAddress", source = "activityAddress"),
            @Mapping(target = "personLabel", source = "personLabel"),
            @Mapping(target = "areaId", source = "areaId"),// 已在公告设置属性中设置了
            @Mapping(target = "placeCode", source = "placeCode"),// 已在公告设置属性中设置了
            // fx特有字段
            @Mapping(target = "warningTags", ignore = true),
            @Mapping(target = "warningScore", ignore = true),
            @Mapping(target = "warningStatus", ignore = true)
    })
    FxWarningEntity warningToFx(WarningEntity warning);

    /**
     * 预警对象拷贝
     *
     * @param warningEntity 预警实体
     * @return warningEntity
     */
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    WarningEntity warningEntityToWarningEntity(WarningEntity warningEntity);

    /**
     * 轨迹对象拷贝
     *
     * @param warningTrackEntity 预警实体
     * @return warningTrackEntity
     */
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    WarningTrackEntity warningEntityToWarningTrackEntity(WarningTrackEntity warningTrackEntity);

    /**
     * 预警实体转换人员线索预警实体
     *
     * @param warningEntity 预警实体
     * @param idCard 身份证
     * @return personClueEntity
     */
    WarningPersonClueEntity warningEntityToWarningPersonClueEntity(WarningEntity warningEntity, String idCard);

    /**
     * 轨迹信息表数据转换预警数据
     *
     * @param themeGjxxbEntity 轨迹信息
     * @return 预警数据
     */
    @Mappings({
            @Mapping(target = "idCard", source = "tzzhm"),
            @Mapping(target = "warningTime", source = "hdsj", dateFormat = TimeUtils.YYYYMMDD_HHMMSS)
    })
    WarningPersonClueEntity gjxxbEntityToWarningEntity(ThemeGjxxbEntity themeGjxxbEntity);

    /**
     * 比中轨迹转换es实体
     *
     * @param bzWarningDTO 比中数据
     * @return 轨迹数据
     */
    @Mappings({
            @Mapping(target = "sbsj", source = "bzWarningDTO.record.sbsj", dateFormat = TimeUtils.YYYYMMDD_HHMMSS2),
            @Mapping(target = "djzjhm", source = "bzWarningDTO.record.djzjhm"),
            @Mapping(target = "dtxxtgdwjgdm", source = "bzWarningDTO.record.dtxxtgdwjgdm"),
            @Mapping(target = "hdfssj", source = "bzWarningDTO.record.hdfssj", dateFormat = TimeUtils.YYYYMMDD_HHMMSS2),
            @Mapping(target = "dtxxtgdw", source = "bzWarningDTO.record.dtxxtgdw"),
            @Mapping(target = "djxm", source = "bzWarningDTO.record.djxm"),
            @Mapping(target = "xxbdsj", source = "bzWarningDTO.record.xxbdsj", dateFormat = TimeUtils.YYYYMMDD_HHMMSS2),
            @Mapping(target = "bzmbhm", source = "bzWarningDTO.record.bzmbhm"),
            // hdxxms 代替 hdxgxx
            @Mapping(target = "hdxgxx", source = "bzWarningDTO.record.hdxxms"),
            @Mapping(target = "xxbddw", source = "bzWarningDTO.record.xxbddw"),
            @Mapping(target = "hdfsddqh", source = "bzWarningDTO.record.hdfsddqh"),
            @Mapping(target = "hdfsddshcsdm", source = "bzWarningDTO.record.hdfsddshcsdm"),
            @Mapping(target = "hdfsddJd", source = "bzWarningDTO.record.hdfsddJd"),
            @Mapping(target = "bzmblx", source = "bzWarningDTO.record.bzmblx"),
            @Mapping(target = "djcsrq", source = "bzWarningDTO.record.djcsrq", dateFormat = TimeUtils.YYYYMMDD5),
            @Mapping(target = "xxbddwjgdm", source = "bzWarningDTO.record.xxbddwjgdm"),
            @Mapping(target = "hdfsddshcs", source = "bzWarningDTO.record.hdfsddshcs"),
            @Mapping(target = "djxb", source = "bzWarningDTO.record.djxb"),
            @Mapping(target = "dtgjxxbh", source = "bzWarningDTO.record.dtgjxxbh"),
            @Mapping(target = "djclsbdh", source = "bzWarningDTO.record.djclsbdh"),
            @Mapping(target = "hdfsddssgajg", source = "bzWarningDTO.record.hdfsddssgajg"),
            @Mapping(target = "hdfsddWd", source = "bzWarningDTO.record.hdfsddWd"),
            @Mapping(target = "djfdjh", source = "bzWarningDTO.record.djfdjh"),
            @Mapping(target = "djhpzl", source = "bzWarningDTO.record.djhpzl"),
            @Mapping(target = "bkdxbh", source = "bzWarningDTO.record.bkdxbh"),
            @Mapping(target = "hdfsddxz", source = "bzWarningDTO.record.hdfsddxz"),
            @Mapping(target = "djzjlx", source = "bzWarningDTO.record.djzjlx"),
            @Mapping(target = "djhphm", source = "bzWarningDTO.record.djhphm"),
            @Mapping(target = "dtxxlb", source = "bzWarningDTO.record.dtxxlb"),
            @Mapping(target = "hdfsddssgajgjgdm", source = "bzWarningDTO.record.hdfsddssgajgjgdm"),
            @Mapping(target = "hdlx", source = "bzWarningDTO.hdlx"),
            @Mapping(target = "id", source = "ldxxbh"),
            @Mapping(target = "ldxxType", source = "type")
    })
    BzWarningTrackEntity bzWarningDtoToBzWarningTrackEntity(BzWarningDTO bzWarningDTO);

    /**
     * 比中轨迹es实体转vo
     *
     * @param bzWarningTrackEntity 实体
     * @return vo
     */
    @Mappings({
            @Mapping(target = "sbsj", source = "sbsj", dateFormat = TimeUtils.YYYYMMDD_HHMMSS),
            @Mapping(target = "hdfssj", source = "hdfssj", dateFormat = TimeUtils.YYYYMMDD_HHMMSS),
            @Mapping(target = "createTime", source = "createTime", dateFormat = TimeUtils.YYYYMMDD_HHMMSS)
    })
    BzWarningTrackListVO bzWarningTrackEntityToBzWarningTrackListVO(BzWarningTrackEntity bzWarningTrackEntity);

}
