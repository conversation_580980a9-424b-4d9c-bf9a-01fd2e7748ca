package com.trs.police.control.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 模型的配置类，为了实现gx模型数据的相关统计
 * @date 2024/3/21 11:29
 */
@Component
public class WarningModalConfig {

    /**
     * 首次入区模型的id
     */
    @Value("${com.trs.warningModalConfig.firstInfoZoneModalId:240}")
    public long firstInfoZoneModalId;

    /**
     * 同感徘徊模型的id
     */
    @Value("${com.trs.warningModalConfig.theSamePoleModalId:241}")
    public long theSamePoleModalId;

    /**
     * 静默模型的id
     */
    @Value("${com.trs.warningModalConfig.silenceModalId:242}")
    public long silenceModalId;

    /**
     * fk人员关注监控的使用的预警配置id
     */
    @Value("${com.trs.warningModalConfig.fkCareMonitorConfigId:0}")
    public long fkCareMonitorConfigId;

    /**
     * 敏感区域模型的id集合，以逗号进行分割
     */
    @Value("${com.trs.warningModalConfig.sensitiveAreaModalIds:0}")
    public String sensitiveAreasModalIds;

    /**
     * 聚集预警模型的id集合，以逗号进行分割
     */
    @Value("${com.trs.warningModalConfig.gatherAreaModalIds:0}")
    public String gatherModalIds;


    /**
     * 离开的属地 地域码，兼容多个环境 广安 5116
     */
    @Value("${com.trs.warningModalConfig.leaveDependencyPlaceCode:511600}")
    public String leaveDependencyPlaceCode;

    /**
     * 区域模型的类型
     */
    @Value("${com.trs.warningModalConfig.areaModelType:7}")
    public Long areaModelType;

    /**
     * 是否启用离开属地的模型
     */
    @Value("${com.trs.warningModalConfig.activeLeaveDependencyModel:false}")
    public Boolean activeLeaveDependencyModel = Boolean.FALSE;
}
