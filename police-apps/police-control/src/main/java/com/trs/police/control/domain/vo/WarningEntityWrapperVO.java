package com.trs.police.control.domain.vo;

import com.trs.police.common.core.entity.WarningEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 预警表包装
 *
 * <AUTHOR>
 * @since 2022-08-11 14:04:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarningEntityWrapperVO extends WarningEntity {

    /**
     * 预警平台
     */
    private List<Long> monitorPlatform;

    /**
     * 布控单位代码
     */
    private Long monitorPersonUnit;

    /**
     * 预警状态
     */
    private Integer warningStatus;

    /**
     * 布控级别
     */
    private Integer monitorLevel;

    /**
     * 常控级别
     */
    private Integer regularLevel;
}