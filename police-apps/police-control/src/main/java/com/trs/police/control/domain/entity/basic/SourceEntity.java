package com.trs.police.control.domain.entity.basic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.control.WarningSourceVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.control.domain.vo.basic.MoyeSourceVO;
import com.trs.police.control.mapper.SourceMapper;
import com.trs.police.control.utils.GeoUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Coordinate;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 感知源(Source)数据访问类
 *
 * <AUTHOR>
 * @since 2022-08-11 14:03:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_control_warning_source",autoResultMap = true)
public class SourceEntity extends AbstractBaseEntity implements Serializable {
    
    private static final long serialVersionUID = -33259842359515744L;

    /**
     * 名称 
     */ 
    private String name;
                                                                                            
    /**
     * 编号 
     */ 
    private String code;
                                                                                            
    /**
     * 类型
     */ 
    private Long type;

    /**
     * 类别
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> category;

    /**
     * 类别
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Long[][] nestingCategory;
    /**
     * 行政区划 
     */ 
    private String districtName;
    /**
     * 行政区划
     */
    private String districtCode;
                                                                                            
    /**
     * 所属辖区 
     */ 
    private Long dept;
                                                                                            
    /**
     * 地址 
     */ 
    private String address;
    /**
     * wkt 格式数据
     */
    private String point;
    /**
     * 主记录id
     */
    private String uniqueKey;

    /**
     * 经纬度是否合法
     */
    private Boolean isLegal;

    /**
     * 是否本地
     * 0 否
     * 1 是
     */
    private int isLocal;

    /**
     * 感知源提供方
     */
    private String sourceProvider;

    /**
     * 转MoyeSourceVO
     *
     * @return {@link MoyeSourceVO}
     */
    public MoyeSourceVO toMoyeSourceVO(){
        MoyeSourceVO moyeSourceVO = new MoyeSourceVO();
        String[] split = this.getUniqueKey().split("-");
        moyeSourceVO.setZjlid(split[0]);
        moyeSourceVO.setGzymc(name);
        moyeSourceVO.setGzybh(code);
        moyeSourceVO.setGzylx(split[1]);
        moyeSourceVO.setXzqhmc(districtName);
        moyeSourceVO.setXzqhdm(districtCode);
        moyeSourceVO.setDzmc(address);
        Coordinate coordinate = GeoUtil.wktToCoordinate(point);
        if (Objects.nonNull(coordinate)){
            moyeSourceVO.setJdwgs84(String.valueOf(coordinate.getX()));
            moyeSourceVO.setWdwgs84(String.valueOf(coordinate.getY()));
        }
        return moyeSourceVO;
    }

    /**
     * 转换vo
     *
     * @return vo
     */
    public WarningSourceVO toVO() {
        WarningSourceVO vo = new WarningSourceVO();
        vo.setId(this.getId());
        vo.setSourceCode(this.getCode());
        vo.setSourceName(this.getName());
        DictService dictService = BeanUtil.getBean(DictService.class);
        vo.setType(this.getType() != null
            ? dictService.getNameByTypeAndCode("control_warning_source_type", this.getType()) : null);
        vo.setCategory(this.getCategory() != null
            ? this.getCategory().stream().map(item->dictService.getNameByTypeAndCode("control_warning_source_category",item)).collect(
            Collectors.joining("、")) : null);
        PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        vo.setControlStation(this.getDept() != null
            ? permissionService.getDeptById(this.getDept()).getName() : null);
        vo.setAddress(this.getAddress());
        SourceMapper mapper = BeanUtil.getBean(SourceMapper.class);
        vo.setTopType(mapper.getSourceTopType(this.getId()));
        // 经纬度
        vo.setPoint(this.getPoint());
        return vo;
    }
}