package com.trs.police.control.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WorkRecordVO
 *
 * <AUTHOR>
 * @Date 2023/6/29 16:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkRecordVO implements Serializable {

    private static final long serialVersionUID = -1181913030651861709L;

    /**
     * 常控id
     */
    private Long regularId;
    /**
     * 人员id
     */
    private Long personId;
    /**
     * 工作方式
     */
    private Long workMethod;
    /**
     * 人员状态
     */
    @NotNull(message = "人员状态不能为空")
    private Integer status;
    /**
     * 失控时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime outOfControlTime;
    /**
     * 稳控时间
     */
    private Long inControlTime;
    /**
     * 轨迹id
     */
    private List<Long> trackId;
    /**
     * 工作详情
     */
    @NotBlank(message = "工作详情不能为空")
    private String workDetail;
    /**
     * 当前去向
     */
    private String destination;
    /**
     * 附件
     */
    private List<FileInfoVO> attachments;

    /**
     * 工作民警
     */
    private List<SimpleUserVO> workPolice;

    /**
     * app使用的轨迹
     */
    private List<TrackPointAppVO> workTrack;
}
