package com.trs.police.control.service.impl;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.common.utils.TimeUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.constant.enums.*;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.dto.*;
import com.trs.police.common.core.dto.GroupWarningDTO.Track;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.IdNameVO;
import com.trs.police.common.core.vo.message.PhoneMessageVO;
import com.trs.police.common.core.vo.profile.GroupVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.MessageService;
import com.trs.police.common.openfeign.starter.service.OperationLogService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.config.WarningFkrxyjConfig;
import com.trs.police.control.config.WarningModalConfig;
import com.trs.police.control.constant.CareMonitorConstant;
import com.trs.police.control.constant.TrackSourceFromEnum;
import com.trs.police.control.constant.WarningFkrxyjConstant;
import com.trs.police.control.constant.enums.WarningNotifyEnum;
import com.trs.police.control.converter.WarningEntityConverter;
import com.trs.police.control.domain.dto.BzWarningDTO;
import com.trs.police.control.domain.dto.ProfessionalWarningDTO;
import com.trs.police.control.domain.dto.ProfessionalWarningDTO.JzsdData;
import com.trs.police.control.domain.entity.BzWarningTrackEntity;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjDTO;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.domain.entity.monitor.MonitorEntity;
import com.trs.police.control.domain.entity.monitor.RegularMonitorEntity;
import com.trs.police.control.domain.entity.stxf.WarningStxfDto;
import com.trs.police.control.domain.entity.stxf.WarningStxfEntity;
import com.trs.police.control.domain.entity.stxf.WarningStxfEntity.PersonStxf;
import com.trs.police.control.domain.entity.warning.WarningHistory;
import com.trs.police.control.domain.entity.warning.WarningNoconfigEntity;
import com.trs.police.control.domain.entity.warning.WarningNotifyEntity;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.domain.vo.ControlInfo.WarningInfo;
import com.trs.police.control.domain.vo.warning.CareMonitorResumeResult;
import com.trs.police.control.helper.CloudControlPictureHelper;
import com.trs.police.control.mapper.*;
import com.trs.police.control.properties.WarningProperties;
import com.trs.police.control.proxy.WarningOperateProxy;
import com.trs.police.control.repository.BzWarningTrackRepository;
import com.trs.police.control.service.*;
import com.trs.police.control.service.warning.FkMessageHelper;
import com.trs.police.control.service.warning.FkWarningUpdater;
import com.trs.police.control.utils.GeoUtil;
import com.trs.police.control.utils.WarningModelUtil;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.Strings;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.control.constant.BzWarningTrackConstant.HDLX_MAP;

/**
 * 预警入库处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WarningProcessServiceImpl implements WarningProcessService {

    OkHttpUtil okHttpUtil = OkHttpUtil.getInstance();
    @Resource
    private WarningMapper warningMapper;
    @Resource
    private WarningSourceTypeMapper warningSourceTypeMapper;
    @Resource
    private WarningNotifyMapper warningNotifyMapper;
    @Resource
    private MonitorWarningModelMapper monitorWarningModelMapper;
    @Resource
    private SourceMapper sourceMapper;
    @Resource
    private WarningService warningService;
    @Resource
    private WarningTrackMapper warningTrackMapper;
    @Resource
    private ImportantAreaMapper importantAreaMapper;

    @Resource
    private ProfileService profileService;
    @Resource
    private WarningProperties warningProperties;
    @Resource
    private WarningProcessMapper warningProcessMapper;
    @Resource
    private WarningHistoryMapper warningHistoryMapper;
    @Resource
    private MonitorMapper monitorMapper;
    @Resource
    private RegularMonitorMapper regularMonitorMapper;
    @Resource
    private PermissionService permissionService;

    @Resource
    private MessageService messageService;

    @Resource
    private WarningStxfMapper warningStxfMapper;


    @Resource
    private WarningNoconfigMapper warningNoconfigMapper;
    @Resource
    private WarningFkrxyjMapper warningFkrxyjMapper;

    @Autowired
    private PersonRelate2FkHelper personRelate2FkHelper;

    @Autowired
    private WarningModalConfig warningModalConfig;

    @Resource
    CareMonitorService careMonitorService;
    @Resource
    private BzWarningTrackRepository bzWarningTrackRepository;

    @Resource
    private CloudControlPictureHelper cloudControlPictureHelper;

    private CommonMapValueReader mapValueReader = new CommonMapValueReader();


    private static final String GROUP_DETAIL_TEMPLATE = "#eventTime+ '，'+#sourceList+'等'+#sourceCount+' 个感知源监测到 '+ #groupName+ ' 群体'+ #aggregationCount +' 人聚集 '+#areaName";

    /**
     * 重点区域预警模型id
     */
    @Getter
    private List<Long> monitorAreaModelId;

    /**
     * 有轨迹即预警模型id
     **/
    @Getter
    private Long anyTrackModelId;

    /**
     * 进京模型id
     */
    @Getter
    private Long beijingArrivalModelId;

    /**
     * 赴省模型id
     */
    @Getter
    private Long provincialCapitalArrivalModelId;

    @Autowired
    private WarningFkrxyjConfig warningFkrxyjConfig;

    @Autowired
    private WarningContentGenerator warningContentGenerator;

    @Value("${com.trs.webhook.url:}")
    private String webhookUrl;

    @Autowired
    private List<SceneImportantAreaWarningService> sceneImportantAreaWarningServices;

    @Autowired
    private WarningOperateProxy warningOperateProxy;

    @Resource
    private OperationLogService operationLogService;

    @Autowired
    private FkWarningUpdater fkWarningUpdater;

    @PostConstruct
    void init() {
        monitorAreaModelId = monitorWarningModelMapper.getByType(WarningModelTypeEnum.AREA.getCode()).stream()
                .map(IdNameVO::getId)
                .collect(Collectors.toList());
        anyTrackModelId = monitorWarningModelMapper.getIdByTitle("有轨迹即预警");
        beijingArrivalModelId = monitorWarningModelMapper.getIdByTitle("进京预警");
        provincialCapitalArrivalModelId = monitorWarningModelMapper.getIdByTitle("赴省预警");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void receivePersonWarningMessage(WarningDTO dto) {
        final HitInfo hitInfo = dto.getHitInfo();
        Track trackVO = warningDto2Track(dto);
        if (!isValidCoordinates(trackVO.getSensingMessage().getLongitude(),
                trackVO.getSensingMessage().getLatitude())) {
            log.info("预警轨迹经纬度不合法，丢弃该条预警! 轨迹内容：{}", dto);
            return;
        }
        final ControlInfo controlInfo = ControlInfo.getControlInfo(hitInfo.getSubscribeCode(), List.of(trackVO));
        //如果是关注监测，后续逻辑走关注监测
        if (controlInfo.getType().equals(ControlTypeEnum.CARE)) {
            List<CareMonitorResumeResult> results = doCareMonitorWarningMessage(dto, controlInfo, trackVO);
            boolean hasFx = results.stream()
                    .filter(r -> Objects.nonNull(r.getEntity().getHandlerClassName()))
                    .anyMatch(r -> r.getEntity().getHandlerClassName().contains(CareMonitorConstant.CMH_FX));
            // 反邪的需要保留预警信息并存放轨迹表
            if (hasFx) {
                List<String> hitTags = hitInfo.getHitTags();
                List<Long> areaIds = Objects.nonNull(hitTags) ? getAreaIdByHitTag(hitTags) : Collections.emptyList();
                List<Long> placeCodes = Objects.nonNull(hitTags) ? getPlaceCodeByHitTag(hitTags) : Collections.emptyList();
                controlInfo.getWarningInfo().forEach(warningInfo -> {
                    processWarningInfo(warningInfo, dto, controlInfo, trackVO, areaIds, placeCodes);
                });
            }
            return;
        }
        if (Boolean.FALSE.equals(controlInfo.getIsInMonitor())) {
            log.info("布控不存在或状态不为布控中，丢弃该条预警! 预警内容：{}", dto);
            return;
        }
        if (Objects.isNull(trackVO.getPerson())) {
            log.info("预警轨迹找不到对应的布控人员，丢弃该条预警! 轨迹内容：{}", dto);
            return;
        }
        List<String> hitTags = hitInfo.getHitTags();
        List<Long> areaIds = Objects.nonNull(hitTags) ? getAreaIdByHitTag(hitTags) : Collections.emptyList();
        List<Long> placeCodes = Objects.nonNull(hitTags) ? getPlaceCodeByHitTag(hitTags) : Collections.emptyList();
        controlInfo.getWarningInfo().forEach(warningInfo -> {
            processWarningInfo(warningInfo, dto, controlInfo, trackVO, areaIds, placeCodes);

        });
    }

    private void processWarningInfo(WarningInfo warningInfo, WarningDTO dto, ControlInfo controlInfo, Track trackVO,
                                    List<Long> areaIds, List<Long> placeCodes) {
        final LocalDateTime now = LocalDateTime.now();
        final Long monitorId = controlInfo.getId();
        WarningEntity warning = new WarningEntity();
        warning.setControlType(controlInfo.getType());
        warning.setWarningLevel(warningInfo.getLevel());
        warning.setWarningType(controlInfo.getTargetType().getEnName());
        warning.setWarningTime(now);
        warning.setMonitorId(monitorId);
        // 默认带有有轨迹即预警
        List<Long> warningModelIds = new ArrayList<>(List.of(anyTrackModelId));
        // 重点区域预警模型
        if (!areaIds.isEmpty()) {
            List<Long> areaModelIds = monitorWarningModelMapper.selectModelIdsByAreaId(areaIds);
            warningModelIds.addAll(areaModelIds);
            warningModelIds.addAll(monitorAreaModelId);
        }
        // 场所预警模型
        if (!placeCodes.isEmpty()) {
            List<Long> placeModelIds = monitorWarningModelMapper.selectModelIdsByPlaceCodes(placeCodes);
            warningModelIds.addAll(placeModelIds);
        }
        warningModelIds.retainAll(warningInfo.getModelIds());
        //增加预警模型的调用次数
        WarningModelUtil.asyncCallModalCount(warningModelIds);

        if (warningModelIds.isEmpty()) {
            log.info("布控模型id和预警模型id无交集，丢弃该条预警! 预警内容：{}", dto);
            return;
        } else {
            warning.setModelId(warningModelIds);
        }
        //拼接预警内容，这里比较复杂主要使用了通用模版
        WarningSourceTypeEntity config = warningSourceTypeMapper.selectByWarningType(dto.getEnName());
        if (Objects.isNull(config)) {
            log.info("没有轨迹类型为：{} 的配置，丢弃该条预警！预警内容: {}", dto.getEnName(), dto);
            return;
        }
        String content = warningContentGenerator.generateContent(dto, trackVO.getPerson(), config);
        warning.setContent(content);
        // -- end
        String address = dto.getSensingMessage().getAddress();
        warning.setActivityAddress(StringUtils.isBlank(address) ? dto.getSensingMessage().getName() : address);
        warning.setActivityTime(LocalDateTime.parse(trackVO.getEventTime(), TimeUtil.WARNING_MESSAGE_PATTERN));
        warning.setPersonLabel(Objects.nonNull(trackVO.getPerson()) ? trackVO.getPerson().getTargetType() : new ArrayList<>());
        //设置预警命中的重点区域和场所
        List<WarningModelVO> hitModel = monitorWarningModelMapper.getByModelIds(warning.getModelId());
        List<ImportantAreaEntity> importantAreaEntityList =
                areaIds.isEmpty() ? Collections.emptyList() : importantAreaMapper.selectBatchIds(areaIds);
        List<Long> areaIdList = hitModel.stream().map(WarningModelVO::getAreaId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> hitAreaIds = importantAreaEntityList.stream().map(ImportantAreaEntity::getId).distinct().collect(Collectors.toList());
        areaIdList.addAll(hitAreaIds);
        areaIdList = areaIdList.stream().distinct().collect(Collectors.toList());
        //地域设置去掉匹配模型限制，只要命中了就赋值
        warning.setAreaId(areaIdList);
        warning.setPlaceCode(hitModel.stream().map(WarningModelVO::getPlaceCode).filter(Objects::nonNull)
                .collect(Collectors.toList()));

        //云控相关字段入库
        Map<String, Object> trackDetail = trackVO.getTrackDetail();
        if (Objects.nonNull(trackDetail)) {
            if (TrackSourceFromEnum.CLOUD_CONTROL.getCode().equalsIgnoreCase(String.valueOf(trackDetail.get("trackSourceFrom")))) {
                //设置云控相关字段
                cloudControlColumnSet(warning, trackDetail);
            } else {
                //感知引擎
                warning.setWarningPlatform(1);
            }
        } else {
            //感知引擎
            warning.setWarningPlatform(1);
        }

        warningMapper.insert(warning);

        //如果是云控的预警，需要记录签收日志
        if (Objects.nonNull(trackDetail)) {
            if (TrackSourceFromEnum.CLOUD_CONTROL.getCode().equalsIgnoreCase(String.valueOf(trackDetail.get("trackSourceFrom")))) {
                createOperationLog(warning.getId());
            }
        }
        WarningModelUtil.pushModelStatisticsMessage(warningModelIds);
        //更新布控最后预警时间
        if (controlInfo.getType().equals(ControlTypeEnum.MONITOR)) {
            monitorMapper.updateMonitorLastWarningTime(monitorId, now);
        } else {
            regularMonitorMapper.updateRegularLastWarningTime(monitorId, now);
        }
        WarningTrackEntity track = setTrack(trackVO, warning.getId());
        track.setMonitorId(controlInfo.getId());
        track.setControlType(controlInfo.getType());
        warningTrackMapper.insert(track);
        log.info("预警入库：{}", warning);
        List<WarningNotifyEntity> notifyEntityList = setWarningUserRelation(warningInfo, warning.getId());

        //根据需要将预警信息存入es，以加快预警列表检索速度
        warningOperateProxy.esInsert(track, warning, notifyEntityList);

        //记录原始预警轨迹信息
        CompletableFuture.runAsync(
                () -> warningHistoryMapper.insert(new WarningHistory(JsonUtil.toJsonString(dto), warning.getId())));
        // 当区域id包含fx的重点区域时，保存额外的预警消息
        sceneImportantAreaWarningServices.forEach(s -> {
            Long warningId = s.saveSceneImportantAreaWarning(importantAreaEntityList, warning, track);
            Optional.ofNullable(warningId).ifPresent(id -> setWarningUserRelation(warningInfo, id));
        });
        //TODO 消息中心推消息
        //短信通知
        if (warningInfo.getPhoneMessageUser() != null
                && !warningInfo.getPhoneMessageUser().isEmpty()) {
            sendPhoneMessage(warningInfo, warning);
        }
    }

    /**
     * 云控相关字段设置
     *
     * @param warning 预警信息实体
     * @param trackDetail 轨迹信息
     */
    private void cloudControlColumnSet(WarningEntity warning, Map<String, Object> trackDetail) {
        JsonNode detailRecord = JsonUtil.objectToJsonNode(trackDetail.get("record"));
        //发文字号
        Optional.ofNullable(detailRecord.get("fwzh")).filter(n -> !n.isNull()).map(JsonNode::asText).ifPresent(warning::setFwzh);
        //首次处置反馈时限
        Optional.ofNullable(detailRecord.get("scczfksx"))
                .filter(jsonNode -> !jsonNode.isNull())
                .map(jsonNode -> LocalDateTime.parse(jsonNode.asText(), DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS2)))
                .ifPresent(warning::setScczfksx);

        //预警指令ID
        Optional.ofNullable(detailRecord.get("ywzjid")).filter(n -> !n.isNull()).map(JsonNode::asText).ifPresent(warning::setYwzjid);
        //布控指令编号
        Optional.ofNullable(detailRecord.get("lkzlbh")).filter(n -> !n.isNull()).map(JsonNode::asText).ifPresent(warning::setLkzlbh);
        //签收时限
        Optional.ofNullable(detailRecord.get("qssx"))
                .filter(jsonNode -> !jsonNode.isNull())
                .map(jsonNode -> LocalDateTime.parse(jsonNode.asText(), DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS2)))
                .ifPresent(warning::setQssx);
        //活动类型
        warning.setHdlx(String.valueOf(trackDetail.get("hdlx")));
        //处置措施要求
        Optional.ofNullable(detailRecord.get("czcsyq")).filter(n -> !n.isNull()).map(JsonNode::asText).ifPresent(warning::setCzcsyq);
        //发布责任单位名称
        Optional.ofNullable(detailRecord.get("fbzrdw")).filter(n -> !n.isNull()).map(JsonNode::asText).ifPresent(warning::setFbzrdw);
        //发布责任单位代码
        Optional.ofNullable(detailRecord.get("fbzrdwjgdm")).filter(n -> !n.isNull()).map(JsonNode::asText).ifPresent(warning::setFbzrdwjgdm);
        //活动发起地点区划
        Optional.ofNullable(detailRecord.get("hdfsddqh")).filter(n -> !n.isNull()).map(JsonNode::asText).ifPresent(warning::setHdfsddqh);
        //活动发生地所属社会场所
        Optional.ofNullable(detailRecord.get("hdfsddssshcs")).filter(n -> !n.isNull()).map(JsonNode::asText).ifPresent(warning::setHdfsddshcs);
        //云控
        warning.setWarningPlatform(2);
    }

    private void createOperationLog(long id) {
        try {
            String detail = BeanFactoryHolder.getEnv().getProperty("com.trs.warning.cloud.control.detail", "四川省公安厅签收了预警指令");
            operationLogService.createOperationLog(id,
                    null,
                    OperateModule.CLOUD_CONTROL,
                    Operation.CLOUD_CONTROL_SIGN,
                    null,
                    detail);
        } catch (Exception e) {
            log.error("记录省厅签收日志发生异常", e);
        }
    }

    /**
     * 当预警是fx的重点区域时，保存额外的预警消息
     *
     * @param hitSubject      专题
     * @param hitSubjectScene 场景
     * @param warningInfo     warningInfo
     * @param warning         预警信息
     * @param track           轨迹信息
     */
    private void saveSceneImportantAreaWarning(Long hitSubject, Long hitSubjectScene, WarningInfo warningInfo, WarningEntity warning, WarningTrackEntity track) {
        // 补充专题，场景码值，并记录新的预警数据
        WarningEntity warningEntity = WarningEntityConverter.INSTANCE.warningEntityToWarningEntity(warning);
        warningEntity.setHitSubject(hitSubject);
        warningEntity.setHitSubjectScene(hitSubjectScene);
        warningMapper.insert(warningEntity);
        // 记录预警对应的轨迹数据
        track.setWarningId(warningEntity.getId());
        WarningTrackEntity trackEntity = WarningEntityConverter.INSTANCE.warningEntityToWarningTrackEntity(track);
        warningTrackMapper.insert(trackEntity);
        // 记录预警对应的人员相关信息
        setWarningUserRelation(warningInfo, warningEntity.getId());
    }

    /**
     * 处理关注预警
     *
     * @param dto                    dto
     * @param careMonitorControlInfo careMonitorControlInfo
     * @param trackVO                trackVO
     * @return 消费结果
     */
    private List<CareMonitorResumeResult> doCareMonitorWarningMessage(WarningDTO dto, ControlInfo careMonitorControlInfo, Track trackVO) {
        List<CareMonitorResumeResult> result = new ArrayList<>();
        //逐一消费预警内容
        careMonitorControlInfo.getWarningInfo().forEach(warningInfo -> {
            //封装预警对象
            WarningEntity warning = new WarningEntity();
            HitInfo hitInfo = dto.getHitInfo();
            initDefaultWarningEntity(warning, careMonitorControlInfo, warningInfo, hitInfo, null);
            CareMonitorResumeResult resumeResult = careMonitorService.consumerMonitorMessage(careMonitorControlInfo, warningInfo, trackVO, warning, dto);
            result.add(resumeResult);
        });
        return result;
    }

    /**
     * 初始WarningEntity。因为关注监测和常控共用绝大多数的逻辑，因此WarningEntity的初始将共有的
     * 抽取出来。
     * 初始内容有：
     * 1，管控类型
     * 2，管控级别
     * 3，预警类型
     * 4，预警时间
     * 5，预警id；（关注监测没有）
     * 6，命中模型ids
     * 7，命中重点区域
     * 8，命中场所
     *
     * @param warning     warning
     * @param controlInfo controlInfo
     * @param warningInfo warningInfo
     * @param hitInfo     hitInfo
     * @param monitorId   monitorId
     */
    private void initDefaultWarningEntity(WarningEntity warning, ControlInfo controlInfo, WarningInfo warningInfo, HitInfo hitInfo, Long monitorId) {

        List<String> hitTags = hitInfo.getHitTags();

        final List<Long> areaIds = Objects.nonNull(hitTags) ? getAreaIdByHitTag(hitTags) : Collections.emptyList();
        final List<Long> placeCodes = Objects.nonNull(hitTags) ? getPlaceCodeByHitTag(hitTags) : Collections.emptyList();
        LocalDateTime now = LocalDateTime.now();

        warning.setControlType(controlInfo.getType());
        warning.setWarningLevel(warningInfo.getLevel());
        warning.setWarningType(controlInfo.getTargetType().getEnName());
        warning.setWarningTime(now);
        //对于关注监测，warning.setMonitorId是不存在的
        if (monitorId != null && monitorId > 0L) {
            warning.setMonitorId(monitorId);
        }

        //--加载所有预警模型，有两个来源：通用区域（anyTrackModelId，monitorAreaModelId）和中台返回区域
        // 默认带有有轨迹即预警
        List<Long> warningModelIds = new ArrayList<>(List.of(anyTrackModelId));
        // 重点区域预警模型
        if (!areaIds.isEmpty()) {
            List<Long> areaModelIds = monitorWarningModelMapper.selectModelIdsByAreaId(areaIds);
            warningModelIds.addAll(areaModelIds);
            warningModelIds.addAll(monitorAreaModelId);
        }
        // 场所预警模型
        if (!placeCodes.isEmpty()) {
            List<Long> placeModelIds = monitorWarningModelMapper.selectModelIdsByPlaceCodes(placeCodes);
            warningModelIds.addAll(placeModelIds);
        }
        //--end
        //加载的模型与命中的预警配置的模型进行交际，得到最终命中模型
        warningModelIds.retainAll(warningInfo.getModelIds());

        //增加预警模型的调用次数
        WarningModelUtil.asyncCallModalCount(warningModelIds);

        if (warningModelIds.isEmpty()) {
            log.warn("布控模型id和预警模型id无交集，丢弃该条预警! 预警内容：{}");
            return;
        } else {
            warning.setModelId(warningModelIds);
        }

        //设置预警命中的重点区域和场所
        //这里程序这么绕根本原因是：模型配置的地点可能是画的区域（import_area_id），也可能是感知源的类型（place_code）
        List<WarningModelVO> hitModel = monitorWarningModelMapper.getByModelIds(warning.getModelId());
        List<ImportantAreaEntity> importantAreaEntityList =
                areaIds.isEmpty() ? Collections.emptyList() : importantAreaMapper.selectBatchIds(areaIds);
        warning.setAreaId(hitModel.stream().flatMap(model -> {
            if (Objects.nonNull(model.getAreaId())) {
                return Stream.of(model.getAreaId());
            } else {
                return importantAreaEntityList.stream()
                        .filter(area -> Objects.equals(area.getCategory(), model.getPlaceCode()))
                        .map(ImportantAreaEntity::getId);
            }
        }).collect(Collectors.toList()));
        warning.setPlaceCode(hitModel.stream().map(WarningModelVO::getPlaceCode).filter(Objects::nonNull)
                .collect(Collectors.toList()));
        // --end

    }

    private List<String> getPhoto(List<String> imageColumns, Map<String, Object> trackDetail) {
        return imageColumns.stream()
                .map(key -> {
                    if (StringUtils.isEmpty(key)) {
                        return null;
                    }
                    return key.contains("#") ? warningContentGenerator.generateContent(key, trackDetail) : trackDetail.get(key);
                })
                .filter(Objects::nonNull)
                .map(Objects::toString)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
    }

    private Double getSimilarity(String similarity, Map<String, Object> trackDetail) {
        try {
            Double value =  mapValueReader.readValueFromMap(similarity, trackDetail)
                    .map(Double::valueOf)
                    // 如果小于1，则乘以100
                    .map(v -> v <= 1.0D ? v * 100 : v)
                    // 保留两位小数
                    .map(v -> BigDecimal.valueOf(v).setScale(2, RoundingMode.HALF_UP).doubleValue())
                    .orElse(null);
            return value;
        } catch (RuntimeException e) {
            log.error("处理相似度异常", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void receiveGroupWarningMessage(GroupWarningDTO dto) {
        final HitInfo hitInfo = dto.getHitInfo();
        List<Track> tracks = dto.getTracks();
        final ControlInfo controlInfo = ControlInfo.getControlInfo(hitInfo.getSubscribeCode(), tracks);
        if (controlInfo.getGroupId() == null) {
            log.error("群体预警布控订阅主键格式不为[管控类型_布控id_群体id]，解析失败！预警信息：{}", dto);
            return;
        }
        if (Boolean.FALSE.equals(controlInfo.getIsInMonitor())) {
            log.info("布控不存在或状态不为布控中，丢弃该条预警! 预警内容：{}", dto);
            return;
        }
        //通过证件号码反查档案信息
        tracks.forEach(e -> {
            String identifier = e.getIdentifier();
            if (e.getPerson() != null) {
                return;
            }
            PersonVO personVO = profileService.getPersonByIdNumber(identifier);
            e.setPerson(personVO);
        });
        if (tracks.stream().anyMatch(item -> Objects.isNull(item.getPerson()))) {
            log.info("预警轨迹找不到对应的布控人员，丢弃该条预警! 轨迹内容：{}", dto);
            return;
        }
        final Long monitorId = controlInfo.getId();
        final Long groupId = controlInfo.getGroupId();
        LocalDateTime now = LocalDateTime.now();
        List<String> hitTags = hitInfo.getHitTags();
        List<Long> areaIds = Objects.nonNull(hitTags) ? getAreaIdByHitTag(hitTags) : Collections.emptyList();
        List<Long> placeCodes = Objects.nonNull(hitTags) ? getPlaceCodeByHitTag(hitTags) : Collections.emptyList();
        controlInfo.getWarningInfo().forEach(warningInfo -> {
            WarningEntity warning = new WarningEntity();
            warning.setControlType(controlInfo.getType());
            warning.setWarningLevel(warningInfo.getLevel());
            warning.setWarningType(MonitorBaseTypeEnum.GROUP.getEnName());
            warning.setWarningTime(now);
            warning.setMonitorId(monitorId);
            warning.setGroupId(groupId);
            warning.setActivityTime(
                    LocalDateTime.parse(tracks.get(0).getEventTime(), TimeUtil.WARNING_MESSAGE_PATTERN));
            warning.setActivityAddress(
                    tracks.stream().map(
                                    item -> StringUtils.isBlank(item.getSensingMessage().getAddress()) ? item.getSensingMessage()
                                            .getName() : item.getSensingMessage().getAddress()).distinct()
                            .collect(Collectors.joining("、")));
            //写死的群体聚集模型id
            warning.setModelId(List.of(warningProperties.getGroupAggregateModelId()));

            //增加群体聚集模型的调用次数
            WarningModelUtil.asyncCallModalCount(warningProperties.getGroupAggregateModelId());


            Map<String, Object> paramsMap = getGroupWarningMap(groupId, dto);
            String content = warningContentGenerator.generateContent(GROUP_DETAIL_TEMPLATE, paramsMap);
            warning.setContent(content);
            warning.setAreaId(areaIds);
            warning.setPlaceCode(placeCodes);
            warningMapper.insert(warning);
            WarningModelUtil.pushModelStatisticsMessage(Arrays.asList(warningProperties.getGroupAggregateModelId()));
            if (controlInfo.getType().equals(ControlTypeEnum.MONITOR)) {
                monitorMapper.updateMonitorLastWarningTime(monitorId, now);
            } else {
                regularMonitorMapper.updateRegularLastWarningTime(monitorId, now);
            }
            tracks.forEach(track -> {
                final WarningTrackEntity trackEntity = setTrack(track, warning.getId());
                trackEntity.setControlType(controlInfo.getType());
                trackEntity.setMonitorId(monitorId);
                warningTrackMapper.insert(trackEntity);
            });
            log.info("预警入库：{}", warning);
            setWarningUserRelation(warningInfo, warning.getId());
            CompletableFuture.runAsync(
                    () -> warningHistoryMapper.insert(new WarningHistory(JsonUtil.toJsonString(dto), warning.getId())));

            //TODO 消息中心推消息

            //短信通知
            if (warningInfo.getPhoneMessageUser() != null
                    && !warningInfo.getPhoneMessageUser().isEmpty()) {
                sendPhoneMessage(warningInfo, warning);
            }
        });
    }

    private void sendPhoneMessage(WarningInfo warningInfo, WarningEntity warning) {
        try {
            PhoneMessageVO message = new PhoneMessageVO();
            message.setContent(
                    String.format("%s 您有一条布控预警信息，请及时签收",
                            TimeUtil.getSubscribeTime(warning.getWarningTime())));
            message.setPhoneNumbers(warningInfo.getPhoneMessageUser().stream()
                    .map(user -> permissionService.findSimpleUser(user.getUserId(), user.getDeptId()).getTel())
                    .filter(StringUtil::isLegalMobilePhone)
                    .toArray(String[]::new));
            message.setModuleId(2L);
            message.setRelatedId(warning.getId());
            log.info("预警短信通知：{}", JsonUtil.toJsonString(message));
            messageService.sendPhoneMessage(message);
        } catch (Exception e) {
            log.info("预警短信通知失败！", e);
        }

    }


    /**
     * 设置用户关联关系
     *
     * @param warningInfo 预警信息
     * @param warningId   预警ID
     * @return WarningNotifyEntity
     */
    public List<WarningNotifyEntity> setWarningUserRelation(WarningInfo warningInfo, Long warningId) {
        List<WarningNotifyEntity> warningNotifyList = new ArrayList<>();

        WarningStatusEnum status = getWarningStatusByWarningLevel(warningInfo.getLevel());
        //通知人员
        warningInfo.getNotifyPerson().forEach(notifyPerson -> {
            if (Objects.isNull(notifyPerson)) {
                return;
            }
            WarningProcessEntity processEntity = new WarningProcessEntity();
            processEntity.setWarningId(warningId);
            processEntity.setStatus(status);
            processEntity.setDeptId(notifyPerson.getDeptId());
            warningProcessMapper.insert(processEntity);
            WarningNotifyEntity relation = new WarningNotifyEntity(warningId, notifyPerson.getUserId(),
                    notifyPerson.getDeptId(), WarningNotifyEnum.NOTIFY_PERSON, processEntity.getId());
            warningNotifyList.add(relation);
            warningNotifyMapper.insert(relation);
            //除蓝色预警外，开始签收计时
            warningService.pushWarningActionMessage(processEntity.getId(), warningInfo.getLevel(),
                    WarningStatusEnum.WAITING_SIGN);
        });

        //人员管控单位
        if (Objects.nonNull(warningInfo.getNotifyControlDeptPerson())
                && !warningInfo.getNotifyControlDeptPerson().isEmpty()) {
            //活动地区派出所通知人员共用一个process
            WarningProcessEntity notifyControlDeptProcess = new WarningProcessEntity();
            notifyControlDeptProcess.setWarningId(warningId);
            notifyControlDeptProcess.setStatus(status);
            notifyControlDeptProcess.setDeptId(warningInfo.getNotifyControlDeptPerson().get(0).getDeptId());
            warningProcessMapper.insert(notifyControlDeptProcess);

            //重复通知时，取通知人
            warningInfo.getNotifyControlDeptPerson()
                    .forEach(notifyPerson -> {
                        WarningNotifyEntity relation = new WarningNotifyEntity(warningId, notifyPerson.getUserId(),
                                notifyPerson.getDeptId(), WarningNotifyEnum.NOTIFY_CONTROL_DEPT,
                                notifyControlDeptProcess.getId());
                        warningNotifyList.add(relation);
                        warningNotifyMapper.insert(relation);
                        //除蓝色预警外，开始签收计时
                        warningService.pushWarningActionMessage(notifyControlDeptProcess.getId(), warningInfo.getLevel(),
                                WarningStatusEnum.WAITING_SIGN);
                    });
        }

        //活动地区
        if (Objects.nonNull(warningInfo.getNotifyWarningAreaPerson())
                && !warningInfo.getNotifyWarningAreaPerson().isEmpty()) {
            //活动地区派出所通知人员共用一个process
            WarningProcessEntity notifyWarningAreaProcess = new WarningProcessEntity();
            notifyWarningAreaProcess.setWarningId(warningId);
            notifyWarningAreaProcess.setStatus(status);
            notifyWarningAreaProcess.setDeptId(warningInfo.getNotifyWarningAreaPerson().get(0).getDeptId());
            warningProcessMapper.insert(notifyWarningAreaProcess);

            warningInfo.getNotifyWarningAreaPerson()
                    .forEach(notifyPerson -> {
                        WarningNotifyEntity relation = new WarningNotifyEntity(warningId, notifyPerson.getUserId(),
                                notifyPerson.getDeptId(), WarningNotifyEnum.NOTIFY_WARNING_AREA,
                                notifyWarningAreaProcess.getId());
                        warningNotifyList.add(relation);
                        warningNotifyMapper.insert(relation);
                        //除蓝色预警外，开始签收计时
                        warningService.pushWarningActionMessage(notifyWarningAreaProcess.getId(), warningInfo.getLevel(),
                                WarningStatusEnum.WAITING_SIGN);
                    });
        }
        return warningNotifyList;
    }

    /**
     * 蓝色预警不需要处置，置为已完结状态
     *
     * @param monitorLevelEnum 预警级别
     * @return 预警状态
     */
    private WarningStatusEnum getWarningStatusByWarningLevel(MonitorLevelEnum monitorLevelEnum) {
        return MonitorLevelEnum.BLUE.equals(monitorLevelEnum)
                ? WarningStatusEnum.PROCESS_FINISH
                : WarningStatusEnum.WAITING_SIGN;
    }

    private Track warningDto2Track(WarningDTO dto) {
        Track track = new Track();
        track.setSensingMessage(dto.getSensingMessage());
        track.setTrackDetail(dto.getTrackDetail());
        track.setEnName(dto.getEnName());
        track.setName(dto.getName());
        track.setIdentifier(dto.getIdentifier());
        track.setIdentifierType(dto.getIdentifierType());
        track.setEventTime(dto.getEventTime());
        return track;
    }

    /**
     * 设置轨迹信息
     *
     * @param track     轨迹
     * @param warningId 预警ID
     * @return WarningTrackEntity
     */
    public WarningTrackEntity setTrack(Track track, Long warningId) {
        Source source = track.getSensingMessage();
        WarningTrackEntity trackEntity = new WarningTrackEntity();
        trackEntity.setWarningId(warningId);
        trackEntity.setIdentifier(track.getIdentifier());
        trackEntity.setIdentifierType(track.getIdentifierType());
        trackEntity.setDatasourceType(track.getEnName());
        trackEntity.setSourceId(getSourceId(source));
        trackEntity.setActivityTime(LocalDateTime.parse(track.getEventTime(), TimeUtil.WARNING_MESSAGE_PATTERN));
        trackEntity.setInputSourceType(track.getTrackDetail().containsKey("inputSourceType") ? track.getTrackDetail().get("inputSourceType").toString() : null);

        trackEntity.setDistrict(source.getDistrict());
        trackEntity.setPlace(source.getName());
        trackEntity.setAddress(StringUtils.isBlank(source.getAddress()) ? source.getName() : source.getAddress());
        trackEntity.setLongitude(source.getLongitude());
        trackEntity.setLatitude(source.getLatitude());
        trackEntity.setTrackDetail(JsonUtil.toJsonString(track.getTrackDetail()));
        trackEntity.setCreateTime(LocalDateTime.now());
        trackEntity.setPersonId(Objects.nonNull(track.getPerson()) ? track.getPerson().getId() : null);
        WarningSourceTypeEntity config = warningSourceTypeMapper.selectByWarningType(track.getEnName());
        if (Objects.nonNull(config.getImageColumns())) {
            Map<String, Object> map = new HashMap<>();
            Optional.ofNullable(track.getTrackDetail())
                    .ifPresent(trackDetailMap -> trackDetailMap.forEach((key, value) -> map.put(key, value)));
            map.put(WarningContentGenerator.TRACK_DETAIL, JSON.toJSONString(track.getTrackDetail()));
            trackEntity.setPhotos(getPhoto(Arrays.asList(config.getImageColumns()), map));
        }
        if (Objects.nonNull(config.getSimilarity())) {
            trackEntity.setSimilarity(getSimilarity(config.getSimilarity(), track.getTrackDetail()));
        }
        final SourceEntity sourceEntity = sourceMapper.selectPointByUniqueKey(trackEntity.getSourceId());
        if (Objects.nonNull(sourceEntity)) {
            trackEntity.setSourceType(sourceEntity.getType());
            // 当没有预警经纬度，感知源有经纬度时做个兜底操作，取感知源的经纬度
            if ((trackEntity.getLatitude() == null || trackEntity.getLongitude() == null) && StringUtils.isNotEmpty(sourceEntity.getPoint())) {
                Coordinate coordinate = GeoUtil.wktToCoordinate(sourceEntity.getPoint());
                if (Objects.nonNull(coordinate)) {
                    trackEntity.setLongitude(coordinate.getX());
                    trackEntity.setLatitude(coordinate.getY());
                }
            }
        }
        //下载图片地址，目前只有云控的需要下载
        trackEntity.setPhotos(cloudControlPictureHelper.downloadPicture(track, trackEntity.getPhotos()));
        return trackEntity;
    }

    private String getSourceId(Source source) {
        return source.getId() + "-" + source.getType();
    }

    private Map<String, Object> getGroupWarningMap(Long groupId, GroupWarningDTO dto) {
        Map<String, Object> warningMap = JsonUtil.parseMap(JsonUtil.toJsonString(dto), Object.class);
        final HitInfo hitInfo = dto.getHitInfo();
        final GroupVO groupVO = profileService.getById(groupId);
        final List<Track> tracks = dto.getTracks();
        final Long gatherPersonCount = tracks.stream().map(Track::getIdentifier).filter(Objects::nonNull).distinct().count();
        List<String> sourceNames = tracks.stream().map(track -> {
            Source source = track.getSensingMessage();
            SourceEntity sourceEntity = sourceMapper.selectByUniqueKey(source.getId() + "-" + source.getType());
            return sourceEntity.getName();
        }).distinct().collect(Collectors.toList());
        //群体预警
        String eventTime = TimeUtil.timeStringFormatter(tracks.get(0).getEventTime());
        warningMap.put("eventTime", eventTime);
        warningMap.put("sourceList",
                String.join("、", sourceNames.size() < 3 ? sourceNames : sourceNames.subList(0, 3)));
        warningMap.put("sourceCount", sourceNames.size());
        warningMap.put("groupName", groupVO != null ? groupVO.getGroupName() : "");
        warningMap.put("aggregationCount", gatherPersonCount);
        List<String> areaIds = hitInfo.getHitModel().getAreaIds();
        if (areaIds.isEmpty()) {
            warningMap.put("areaName", "");
        } else {
            warningMap.put("areaName",
                    "在" + importantAreaMapper.selectBatchIds(areaIds).stream().map(ImportantAreaEntity::getName)
                            .collect(Collectors.joining("、")));
        }
        return warningMap;
    }

    /**
     *消费信令预警消息
     *
     * @param warningDTO 预警消息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveXlWarningMessage(ProfessionalWarningDTO warningDTO) {
        JzsdData jzsdData = JsonUtil.parseObject(warningDTO.getData(), JzsdData.class);
        if (Objects.isNull(jzsdData)) {
            log.error("接收到的预警信息为空");
            return;
        }

        String phoneNumber = jzsdData.getTarget();
        if (StringUtils.isBlank(phoneNumber)) {
            log.error("接收到的预警信息手机号为空");
            return;
        }

        // 转换geoHash
        convertGeoHash(jzsdData);

        //查询号码关联人员，并生成常控与临控预警
        List<PersonVO> personByTel = profileService.getPersonByTel(phoneNumber);
        if (CollectionUtils.isEmpty(personByTel)) {
            log.info("手机号{}未查到相关人员", phoneNumber);
            return;
        }
        List<Long> personIds = personByTel.stream().map(PersonVO::getId).collect(Collectors.toList());
        // 一次查询多值避免多次查询数据库
        List<RegularMonitorEntity> regularMonitorList = regularMonitorMapper.getMonitorByPersonIds(personIds);
        Map<Long, List<RegularMonitorEntity>> regularMonitorGroupByPersonId = regularMonitorList.stream().collect(Collectors.groupingBy(RegularMonitorEntity::getTargetId));

        personByTel.forEach(person -> {
            // 从map中获取该人员关联的常控列表
            List<RegularMonitorEntity> regularMonitorEntities = regularMonitorGroupByPersonId.get(person.getId());
            ArrayList<MonitorIdAndType> list = new ArrayList<>();
            if (regularMonitorEntities != null) {
                List<Long> regularMonitorIds = regularMonitorEntities.stream().map(RegularMonitorEntity::getId).collect(Collectors.toList());
                regularMonitorIds.forEach(regularMonitorId -> list.add(new MonitorIdAndType(regularMonitorId, ControlTypeEnum.REGULAR)));
            }
            // 查询临控列表, 一个人可能被多次临控
            List<MonitorEntity> monitorEntities = monitorMapper.selectMonitorsByPersonId(String.valueOf(person.getId()));
            if (monitorEntities != null) {
                List<Long> monitorIds = monitorEntities.stream().map(MonitorEntity::getId).collect(Collectors.toList());
                monitorIds.forEach(monitorId -> list.add(new MonitorIdAndType(monitorId, ControlTypeEnum.MONITOR)));
            }
            list.forEach(monitorIdAndType -> {
                String code = monitorIdAndType.getType().getEnName() + "_" + monitorIdAndType.monitorId;
                Track track = xlDataToTrack(jzsdData, person);
                ControlInfo controlInfo = ControlInfo.getControlInfo(code, List.of(track));
                LocalDateTime now = LocalDateTime.now();
                controlInfo.getWarningInfo().forEach(warningInfo -> {
                    WarningEntity warning = new WarningEntity();
                    warning.setControlType(controlInfo.getType());
                    //固定为橙色预警
                    warningInfo.setLevel(MonitorLevelEnum.ORANGE);
                    warning.setWarningLevel(warningInfo.getLevel());
                    warning.setWarningType(controlInfo.getTargetType().getEnName());
                    warning.setWarningTime(now);
                    warning.setMonitorId(monitorIdAndType.monitorId);
                    // 默认带有有轨迹即预警
                    List<Long> warningModelIds = new ArrayList<>(List.of(anyTrackModelId));
                    warningModelIds.retainAll(warningInfo.getModelIds());
                    //028对应赴省，010对应进京
                    if ("028".equals(jzsdData.getCurArea())) {
                        warningModelIds.add(provincialCapitalArrivalModelId);
                    } else if ("010".equals(jzsdData.getCurArea())) {
                        warningModelIds.add(beijingArrivalModelId);
                    }
                    if (warningModelIds.isEmpty()) {
                        log.info("布控模型id和预警模型id无交集，丢弃该条预警! 预警内容。");
                        return;
                    } else {
                        warning.setModelId(warningModelIds);
                    }
                    warning.setModelId(List.of(anyTrackModelId));
                    warning.setContent(String.format("%s,信令数据监测到：%s", now.format(TimeUtil.CN_TIME_PATTERN),
                            jzsdData.getResultDesc()));
                    warning.setActivityAddress(track.getSensingMessage().getAddress());
                    warning.setActivityTime(
                            LocalDateTime.parse(jzsdData.getWarningTime(), TimeUtil.DEFAULT_TIME_PATTERN));
                    warning.setPersonLabel(person.getTargetType());
                    warningMapper.insert(warning);
                    //更新布控最后预警时间
                    if (controlInfo.getType().equals(ControlTypeEnum.MONITOR)) {
                        monitorMapper.updateMonitorLastWarningTime(monitorIdAndType.monitorId, now);
                    } else {
                        regularMonitorMapper.updateRegularLastWarningTime(monitorIdAndType.monitorId, now);
                    }
                    WarningTrackEntity te = setTrack(track, warning.getId());
                    te.setMonitorId(controlInfo.getId());
                    te.setControlType(controlInfo.getType());
                    warningTrackMapper.insert(te);
                    log.info("预警入库：{}", warning);
                    List<WarningNotifyEntity> notifyEntityList = setWarningUserRelation(warningInfo, warning.getId());

                    //记录原始预警轨迹信息
                    CompletableFuture.runAsync(
                            () -> warningHistoryMapper.insert(
                                    new WarningHistory(JsonUtil.toJsonString(warningDTO), warning.getId())));
                    //TODO 消息中心推消息

                    //强制短信通知相关人员
                    if (Objects.isNull(warningInfo.getPhoneMessageUser())
                            || warningInfo.getPhoneMessageUser().isEmpty()) {
                        warningInfo.setPhoneMessageUser(warningInfo.getNotifyPerson());
                    }
                    //根据需要将预警信息存入es，以加快预警列表检索速度
                    warningOperateProxy.esInsert(te, warning, notifyEntityList);
                    sendPhoneMessage(warningInfo, warning);
                });
            });
        });
    }

    /**
     * 接收信令预警
     *
     * @param warningDTO 预警数据
     */
    public void receiveXlWarningMessageOld(ProfessionalWarningDTO warningDTO) {
        JzsdData jzsdData = JsonUtil.parseObject(warningDTO.getData(), JzsdData.class);
        if (Objects.isNull(jzsdData)) {
            log.error("接收到的预警信息为空");
            return;
        }

        String phoneNumber = jzsdData.getTarget();
        if (StringUtils.isBlank(phoneNumber)) {
            log.error("接收到的预警信息手机号为空");
            return;
        }

        // 转换geoHash
        convertGeoHash(jzsdData);

        //查询号码关联人员，并生成常控预警
        profileService.getPersonByTel(phoneNumber).forEach(person -> {
            //查询人员关联的常控
            regularMonitorMapper.getMonitorByPersonId(person.getId()).forEach(monitor -> {
                Long monitorId = monitor.getId();
                String code = ControlTypeEnum.REGULAR.getEnName() + "_" + monitorId;
                Track track = xlDataToTrack(jzsdData, person);
                ControlInfo controlInfo = ControlInfo.getControlInfo(code, List.of(track));
                LocalDateTime now = LocalDateTime.now();
                controlInfo.getWarningInfo().forEach(warningInfo -> {
                    WarningEntity warning = new WarningEntity();
                    warning.setControlType(controlInfo.getType());
                    //固定为橙色预警
                    warningInfo.setLevel(MonitorLevelEnum.ORANGE);
                    warning.setWarningLevel(warningInfo.getLevel());
                    warning.setWarningType(controlInfo.getTargetType().getEnName());
                    warning.setWarningTime(now);
                    warning.setMonitorId(monitorId);
                    // 默认带有有轨迹即预警
                    List<Long> warningModelIds = new ArrayList<>(List.of(anyTrackModelId));
                    warningModelIds.retainAll(warningInfo.getModelIds());
                    if (warningModelIds.isEmpty()) {
                        log.info("布控模型id和预警模型id无交集，丢弃该条预警! 预警内容。");
                        return;
                    } else {
                        warning.setModelId(warningModelIds);
                    }
                    warning.setModelId(List.of(anyTrackModelId));
                    warning.setContent(String.format("%s,信令数据监测到：%s", now.format(TimeUtil.CN_TIME_PATTERN),
                            jzsdData.getResultDesc()));
                    warning.setActivityAddress(track.getSensingMessage().getAddress());
                    warning.setActivityTime(
                            LocalDateTime.parse(jzsdData.getWarningTime(), TimeUtil.DEFAULT_TIME_PATTERN));
                    warning.setPersonLabel(person.getTargetType());
                    warningMapper.insert(warning);
                    //更新布控最后预警时间
                    if (controlInfo.getType().equals(ControlTypeEnum.MONITOR)) {
                        monitorMapper.updateMonitorLastWarningTime(monitorId, now);
                    } else {
                        regularMonitorMapper.updateRegularLastWarningTime(monitorId, now);
                    }
                    WarningTrackEntity te = setTrack(track, warning.getId());
                    te.setMonitorId(controlInfo.getId());
                    te.setControlType(controlInfo.getType());
                    warningTrackMapper.insert(te);
                    log.info("预警入库：{}", warning);
                    setWarningUserRelation(warningInfo, warning.getId());

                    //记录原始预警轨迹信息
                    CompletableFuture.runAsync(
                            () -> warningHistoryMapper.insert(
                                    new WarningHistory(JsonUtil.toJsonString(warningDTO), warning.getId())));
                    //TODO 消息中心推消息

                    //强制短信通知相关人员
                    if (Objects.isNull(warningInfo.getPhoneMessageUser())
                            || warningInfo.getPhoneMessageUser().isEmpty()) {
                        warningInfo.setPhoneMessageUser(warningInfo.getNotifyPerson());
                    }
                    sendPhoneMessage(warningInfo, warning);
                });
            });
        });
    }

    private void convertGeoHash(JzsdData jzsdData) {
        String geoHash = jzsdData.getGeoHash();
        if (!StringUtils.isBlank(geoHash)) {
            double[] decode = GeoHashUtils.decode(geoHash);
            jzsdData.setLatitude(String.valueOf(decode[0]));
            jzsdData.setLongitude(String.valueOf(decode[1]));
        }
    }

    private Track xlDataToTrack(JzsdData data, PersonVO person) {
        Track track = new Track();
        track.setPerson(person);
        track.setTrackDetail(JsonUtil.parseMap(JsonUtil.toJsonString(data), Object.class));
        track.setEnName("xl");
        track.setName("信令");
        Source source = new Source();
        source.setAddress(data.address());
        source.setName("");
        source.setType("electric_fence");
        source.setId("null");
        if (Objects.nonNull(data.getLongitude())) {
            source.setLongitude(Double.valueOf(data.getLongitude()));
        }
        if (Objects.nonNull(data.getLatitude())) {
            source.setLatitude(Double.valueOf(data.getLatitude()));
        }
        track.setSensingMessage(source);
        track.setIdentifier(data.getTarget());
        track.setIdentifierType(2);
        String eventTime = LocalDateTime.parse(data.getWarningTime(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                .format(TimeUtil.WARNING_MESSAGE_PATTERN);
        track.setEventTime(eventTime);
        return track;
    }

    /**
     * 获取地域ID
     *
     * @param hitTag hitTag
     * @return List
     */
    public List<Long> getAreaIdByHitTag(List<String> hitTag) {
        if (Objects.isNull(hitTag) || hitTag.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return hitTag.stream().map(item -> {
                        final String[] subscribeInfo = Strings.split(item, '_');
                        return (subscribeInfo.length > 0 && "areaId".equals(subscribeInfo[0])) ? Long.parseLong(
                                subscribeInfo[1]) : null;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (RuntimeException e) {
            log.info("解析areaId失败,默认处理为有轨迹即预警，tag={}", hitTag);
            return Collections.emptyList();
        }
    }

    /**
     * 获取场所CODE
     *
     * @param hitTag 标签
     * @return code
     */
    public List<Long> getPlaceCodeByHitTag(List<String> hitTag) {
        if (Objects.isNull(hitTag) || hitTag.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return hitTag.stream().map(item -> {
                        final String[] subscribeInfo = Strings.split(item, '_');
                        return (subscribeInfo.length > 0 && "areaLabel".equals(subscribeInfo[0])) ? Long.parseLong(
                                subscribeInfo[1]) : null;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (RuntimeException e) {
            log.info("解析placeCode失败,默认处理为有轨迹即预警，tag={}", hitTag);
            return Collections.emptyList();
        }
    }

    boolean isValidCoordinates(Double longitude, Double latitude) {
        if (Objects.isNull(longitude) || Objects.isNull(latitude)) {
            return true;
        }
        return longitude >= -180.0 && longitude <= 180.0 && latitude >= -90.0 && latitude <= 90.0;
    }

    @Override
    public void receiveStxfWarningMessage(WarningStxfDto warningDTO) {
        WarningStxfEntity entity = new WarningStxfEntity();
        BeanUtil.copyPropertiesIgnoreNull(warningDTO, entity);
        List<PersonStxf> personStxfs = JsonUtil.parseArray(warningDTO.getAlarmHumans(), PersonStxf.class);
        entity.setAlarmHumans(personStxfs.get(0));
        warningStxfMapper.insert(entity);
    }

    @Override
    public void receiveFkrxyjWarningMessage(WarningFkrxyjDTO warningDTO) {
        //如果是已经建档的人，不需要入库
        if (isInProfile(warningDTO.getIdCard())) {
            log.info("被预警人已经非涉恐建档，丢掉此次预警消息！");
            return;
        }

        WarningFkrxyjEntity entity = FkMessageHelper.buildEntity(warningDTO);
        boolean isImproveWarning = isImproveWarningLevel(entity);
        boolean isFirstIntoModel = false;
        if (isImproveWarning) {
            entity.setWarningModel(WarningFkrxyjConstant.WANDERING_ON_THE_SAME_POLE);
        } else {
            entity.setWarningModel(WarningFkrxyjConstant.EARLY_WARNING_WITH_TRAJECTORY);
            isFirstIntoModel = isFirstIntoModel(entity);
        }

        // 构造预警等级
        fkWarningUpdater.update(entity);

        // 写入数据库
        warningFkrxyjMapper.insert(entity);

        // 初始化fk人员
        personRelate2FkHelper.fkWarning2Person(entity);
        if (isImproveWarning) {
            WarningModelUtil.pushModelStatisticsMessage(warningModalConfig.theSamePoleModalId);
        }
        if (isFirstIntoModel) {
            WarningModelUtil.pushModelStatisticsMessage(warningModalConfig.firstInfoZoneModalId);
        }
    }

    /**
     * 是否首次入区
     *
     * @param entity entity
     * @return true/false
     */
    private boolean isFirstIntoModel(WarningFkrxyjEntity entity) {
        String idCard = entity.getIdCard();
        if (StringUtils.isEmpty(idCard)) {
            return false;
        }
        //一周三天出现
        LocalDateTime startTime = TimeUtil
                .getTargetTime(TimeUtil.OFFSET_UNIT_HOUR, LocalDateTime.now(),
                        warningFkrxyjConfig.timeFrameHour);

        Long resultCount = warningFkrxyjMapper.getCountDayOfWeeks(startTime, idCard);
        //增加首次入区的模型调用次数
        WarningModelUtil.asyncCallModalCount(warningModalConfig.firstInfoZoneModalId);
        if (resultCount >= warningFkrxyjConfig.warningThreshold) {
            entity.setWarningModel(WarningFkrxyjConstant.FIRST_ENTRY_INTO_THE_ZONE);
            return true;
        }
        return false;
    }

    /**
     * 是否已经建档
     *
     * @param idCard 身份证号
     * @return 结果
     */
    private boolean isInProfile(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return false;
        }

        return warningFkrxyjMapper.getCountFromProfileByIdCard(idCard) > 0;
    }

    /**
     * 是否提高预警等级
     *
     * @param warningFkrxyj w
     * @return 结果
     */
    public boolean isImproveWarningLevel(WarningFkrxyjEntity warningFkrxyj) {
        String idCard = warningFkrxyj.getIdCard();
        if (StringUtils.isEmpty(idCard)) {
            return false;
        }

        //同一点位,两周出现3次
        String captureAddress = warningFkrxyj.getCaptureAddress();
        LocalDateTime sameAddressStartTime = TimeUtil
                .getTargetTime(TimeUtil.OFFSET_UNIT_HOUR, LocalDateTime.now(),
                        warningFkrxyjConfig.sameAddressTimeFrameHour);
        Long sameAddressResultCount = warningFkrxyjMapper
                .getCountSameAdressDayOfWeeks(sameAddressStartTime, idCard, captureAddress);

        //增加同杆徘徊的模型调用次数
        WarningModelUtil.asyncCallModalCount(warningModalConfig.theSamePoleModalId);
        if (sameAddressResultCount >= warningFkrxyjConfig.sameAddressWarningThreshold) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void receiveNoConfigWarningMessage(WarningDTO warningDTO, String message) {
        WarningNoconfigEntity entity = new WarningNoconfigEntity();
        entity.setCnName(warningDTO.getName());
        entity.setEnName(warningDTO.getEnName());
        entity.setData(message);
        warningNoconfigMapper.insert(entity);
    }

    @Override
    public void sendWebHookForNoConfigMessage(WarningDTO warningDTO, String message) {
        if (StringUtils.isEmpty(webhookUrl)) {
            log.info("webhookUrl未配置!");
        } else {
            Map<String, String> headersMap = new HashMap<>();
            headersMap.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
            Map<String, String> request = new HashMap<>();
            request.put("enName", warningDTO.getEnName());
            request.put("cnName", warningDTO.getName());
            request.put("message", message);
            log.info("开始发送webhook，参数：{}", request);
            okHttpUtil.postData(webhookUrl, JsonUtil.toJsonString(request), headersMap);
        }
    }

    @Override
    public void receiveBzWarningMessage(BzWarningDTO warningDTO) {
        BzWarningTrackEntity entity = WarningEntityConverter.INSTANCE.bzWarningDtoToBzWarningTrackEntity(warningDTO);
        entity.setHdlxdm(HDLX_MAP.getOrDefault(entity.getHdlx(), 3));
        List<String> list = StringUtil.isEmpty(entity.getBkdxbh())
                ? Collections.emptyList()
                : List.of(entity.getBkdxbh().split("[,|，]"));
        entity.setBkdxbh(JSON.toJSONString(list));
        entity.setId(entity.getLdxxbh());
        if (Objects.isNull(entity.getSbsj()) && !StringUtil.isEmpty(entity.getDjzjhm())) {
            MonitorEntity monitor = monitorMapper.selectMaxByIdNumber(entity.getDjzjhm());
            entity.setSbsj(Objects.nonNull(monitor) && Objects.nonNull(monitor.getCreateTime())
                    ? TimeUtils.stringToDate(TimeUtil.localDateTimeToString(monitor.getCreateTime(), "yyyy-MM-dd HH:mm:ss"), TimeUtils.YYYYMMDD_HHMMSS)
                    : entity.getSbsj());
        }
        Expression expression = ExpressionBuilder.Condition("ywzjid", Operator.Equal, entity.getYwzjid());
        BzWarningTrackEntity first = bzWarningTrackRepository.findFirst(expression);
        Date date = new Date();
        if (Objects.nonNull(first)) {
            entity.setCreateTime(first.getCreateTime());
            entity.setUpdateTime(date);
            bzWarningTrackRepository.update(entity);
        } else {
            entity.setCreateTime(date);
            entity.setUpdateTime(date);
            bzWarningTrackRepository.insert(entity);
        }
    }

    /**
     * 常控或临控
     */
    @Getter
    @Setter
    @AllArgsConstructor
    static class MonitorIdAndType {
        private Long monitorId;
        private ControlTypeEnum type;
    }
}
