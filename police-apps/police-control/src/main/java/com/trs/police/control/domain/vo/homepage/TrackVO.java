package com.trs.police.control.domain.vo.homepage;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.json.serializer.SimpleTimeSerializer;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/12 09:23
 */
@Data
@NoArgsConstructor
public class TrackVO {

    private String address;

    private String point;

    @JsonSerialize(using = SimpleTimeSerializer.class, nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime time;

    private Long personId;

    public TrackVO(String address, String point, LocalDateTime time, Long personId) {
        this.address = address;
        this.point = point;
        this.time = time;
        this.personId = personId;
    }

    /**
     * 带有默认子的轨迹
     *
     * @return {@link TrackVO}
     */
    public static TrackVO ofDefaultValue() {
        return new TrackVO("- -", "", null, null);
    }
}
