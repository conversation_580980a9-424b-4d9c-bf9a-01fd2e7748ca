package com.trs.police.control.constant.enums;

import com.trs.common.base.PreConditionCheck;

/**
 * @description 证件类型的枚举
 * @date 2024/4/22
 * @author: cy
 */
public enum CertificateTypeEnum {

    IDCard("sfzh", "身份证"),


    ;

    private String code;

    private String desc;

    CertificateTypeEnum(String code, String desc) {
        this.code = PreConditionCheck.checkNotNull(code);
        this.desc = PreConditionCheck.checkNotNull(desc);
    }
}
