package com.trs.police.control.kafka.v2.flow.processor.warning.model;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.dto.Source;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.common.core.entity.District;
import com.trs.police.common.core.entity.MonitorWarningModelEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.mapper.MonitorWarningModelMapper;
import com.trs.police.control.mapper.SourceMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.io.WKTReader;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

import static com.trs.police.control.service.impl.SourceServiceImpl.checkInTopDistrict;

/**
 * 离开属地预警模型
 *
 * <AUTHOR>
 * @since 2025/5/27 10:18
 */
@Slf4j
@Component
public class LevelDependencyWarningModelProcessor implements WarningModelProcessor {

    @Resource
    protected MonitorWarningModelMapper monitorWarningModelMapper;

    WKTReader wktReader = new WKTReader(new GeometryFactory(new PrecisionModel(), 4326));

    @Resource
    protected SourceMapper sourceMapper;

    /**
     * 收集modelId
     *
     * @param context 预警信息上下文
     * @return modelId列表
     */
    @Override
    public List<Long> collect(WarningMessageContext context) {
        Environment env = BeanFactoryHolder.getEnv();
        boolean active = Boolean.parseBoolean(env.getProperty("com.trs.warningModalConfig.activeLeaveDependencyModel", "false"));
        if (active) {
            try {
                if(context == null || context.getControlInfo() == null) {
                    log.error("没有布控信息");
                    return List.of();
                }
                // 是否选中了离开属地模型
                List<ControlInfo.WarningInfo> warningInfoList = context.getControlInfo().getWarningInfo();
                if (CollectionUtils.isEmpty(warningInfoList)) {
                    log.error("没有命中的预警");
                    // 没有命中的轨迹
                    return List.of();
                }
                ControlInfo.WarningInfo warningInfo = warningInfoList.get(0);
                if (warningInfo == null) {
                    log.error("没有命中的预警");
                    return List.of();
                }
                List<Long> modelIds = warningInfo.getModelIds();
                if (CollectionUtils.isEmpty(modelIds)) {
                    log.error("未配置匹配的预警模型");
                    return List.of();
                }
                String modelName = BeanFactoryHolder.getEnv().getProperty("com.trs.warningConsume.leaveDependencyModelName", "离开属地模型");
                List<MonitorWarningModelEntity> list = monitorWarningModelMapper.selectList(new QueryWrapper<MonitorWarningModelEntity>().eq("title", modelName));
                if (CollectionUtils.isEmpty(list)) {
                    log.error("未找到模型:{}", modelName);
                    return List.of();
                }
                if (!modelIds.contains(list.get(0).getId())) {
                    log.info("布控未关联离开属地模型");
                    return List.of();
                }
                //  忽略没有经纬度的轨迹
                WarningDTO warningDTO = context.getWarningDTO();
                if (warningDTO == null) {
                    log.error("预警信息为空, 忽略当前数据");
                    return List.of();
                }
                Source sensingMessage = warningDTO.getSensingMessage();
                if (sensingMessage == null) {
                    log.error("感知源信息为空, 忽略当前数据");
                    return List.of();
                }
                Double longitude = sensingMessage.getLongitude();
                Double latitude = sensingMessage.getLatitude();

                if (longitude == null || latitude == null) {
                    log.error("轨迹数据没有经纬度：{}", warningDTO);
                    return List.of();
                }
                //  查询当前环境匹配的属地
                String placeCode = env.getProperty("com.trs.warningModalConfig.leaveDependencyPlaceCode", "511600");
                District district = sourceMapper.selectGeo4326ByCode(placeCode);
                if (district == null) {
                    log.error("未找到区域：{}, 离开属地模型无法进行", placeCode);
                    return List.of();
                }
                log.info("离开属地模型当前配置的属地为 {}", district.getName());
                String contour = district.getContour();
                String point = String.format("POINT(%s %s)", longitude, latitude);
                if (checkInTopDistrict(wktReader.read(contour), wktReader.read(point))) {
                    return List.of();
                }
                log.info("轨迹数据在区域外：{}", warningDTO);
                //不在区域内就命中模型, 返回模型id
                return List.of(list.get(0).getId());
            } catch (Exception e) {
                log.error("LevelDependencyWarningModelProcessor.collect error: {}", e.getMessage(), e);
                return List.of();
            }
        }
        return List.of();
    }
}
