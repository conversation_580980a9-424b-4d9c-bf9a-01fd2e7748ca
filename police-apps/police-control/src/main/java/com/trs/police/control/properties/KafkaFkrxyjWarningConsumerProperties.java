package com.trs.police.control.properties;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 反恐人像预警（高新）
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Data
@Component
@ConfigurationProperties(prefix = "kafka.warning.fkrxyj")
@ConditionalOnProperty(value = "kafka.warning.fkrxyj.autoStartup", havingValue = "true")
public class KafkaFkrxyjWarningConsumerProperties {

    /**
     * 拉取消息间隔 单位：秒
     */
    private Long pollDuration;

    /**
     * 每次拉取最大记录数
     */
    private int maxPollRecords = 500;

    /**
     * kafka服务器地址
     */
    private String bootStrapServers;

    /**
     * 消费者组id
     */
    private String groupId;

    /**
     * 消费的topic
     */
    private String topic;

    /**
     * 消费策略：earliest or latest
     */
    private String autoOffsetReset;

    /**
     * 敏感区域消费组id
     */
    private String mgqyGroupId;

    /**
     * 聚集消费组id
     */
    private String jjGroupId;

    /**
     * 聚集每次拉取最大记录数
     */
    private int maxJjPollRecords = 100;
}
