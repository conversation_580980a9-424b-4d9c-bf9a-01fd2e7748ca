package com.trs.police.control.controller;

import com.trs.common.base.PreConditionCheck;
import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.vo.control.CareMonitorVO;
import com.trs.police.control.aspect.OperationLog;
import com.trs.police.control.domain.vo.care.CareMonitorInitByIdCardVO;
import com.trs.police.control.domain.vo.care.CareMonitorInitialeVO;
import com.trs.police.control.service.CareMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 提供给业务模块使用的关注监控服务，该服务旨在提供一个通用的能力，能够让业务系统能够快速实现自己的关注监控能力。
 * 关注监控的含义：
 * 1，能够对指定的人直接触发中台的订阅，并保存相关数据到数据库当中；
 * 2，关注监控的优先级是最低的，简单将：常控>=临控>关注监控。因此，如果一个人被关注监控过，之后设置为了临控或者常控，关注监控不会再生效，如果这个人从常控或者临控中撤控，该人会被继续关注监控；
 * 3，关注监控只有在被取消关注后，才会放弃监控；
 * 4，关注监控本质上调用的是常控，系统需要默认设置一个预警模型，关注监控也可指定自己的监控模型；
 *
 * @date 2024/4/22
 * @author: cy
 */
@Slf4j
@RestController
@RequestMapping({"/care-monitor", "/public/care-monitor"})
public class CareMonitorController {

    @Resource
    private CareMonitorService careMonitorService;

    /**
     * 发起关注监控
     *
     * @param initiatesVOs 关注监控实体
     * @return true/false
     */
    @PostMapping("/initiate")
    @OperationLog(operation = Operation.CARE_MONITOR_INITIATE, operateModule = OperateModule.REGULAR, newObj = "#initiateVOs")
    public Boolean initiate(@RequestBody List<CareMonitorInitialeVO> initiatesVOs) {
        try {
            careMonitorService.initiate(PreConditionCheck.checkNotNull(initiatesVOs));
            return true;
        } catch (Exception ex) {
            log.error("发起关注监控失败，原因为{}, 传入的关注监控内容长度为：{}", ex.getMessage(), initiatesVOs.size());
            log.error("发起关注监控失败堆栈信息", ex);
            return false;
        }
    }

    /**
     * 更新所有关注布控到moye
     *
     * @param ids ids
     */
    @PostMapping("/careMonitor/monitorByHand")
    public void careMonitor(@RequestBody List<Long> ids) {
        careMonitorService.careMonitorByHand(ids);
    }


    /**
     * 发起关注监控
     *
     * @param vo 关注监控实体
     * @return true/false
     */
    @PostMapping("/initiateByIdCard")
    @OperationLog(operation = Operation.CARE_MONITOR_INITIATE, operateModule = OperateModule.REGULAR, newObj = "#initiateVOs")
    public Boolean initiateByIdCard(@RequestBody CareMonitorInitByIdCardVO vo) {
        try {
            List<CareMonitorInitialeVO> initVo = vo.toInitVo();
            careMonitorService.initiate(PreConditionCheck.checkNotNull(initVo));
            return true;
        } catch (Exception ex) {
            log.error("发起关注监控失败，原因为:{}", ex.getMessage());
            log.error("发起关注监控失败堆栈信息", ex);
            return false;
        }
    }

    /**
     * 发起关注监控
     *
     * @param vo vo
     * @return 是否成功
     */
    @PostMapping("/initiateSingle")
    @OperationLog(operation = Operation.CARE_MONITOR_INITIATE, operateModule = OperateModule.REGULAR, newObj = "#vo")
    public Boolean initiateSingle(@NotNull @RequestBody CareMonitorVO vo) {
        final CareMonitorInitialeVO careMonitorInitialeVO = new CareMonitorInitialeVO();
        careMonitorInitialeVO.setCertificateType(IdentifierTypeEnum.codeOf(vo.getCertificateType()));
        careMonitorInitialeVO.setCertificateValue(PreConditionCheck.checkNotNull(vo.getCertificateValue()));
        careMonitorInitialeVO.setModuleCareMonitorHandlerClassName(PreConditionCheck.checkNotNull(vo.getModuleCareMonitorHandlerClassName()));
        careMonitorInitialeVO.setMonitorConfigId(vo.getMonitorConfigId());
        careMonitorInitialeVO.setCreateUserId(PreConditionCheck.checkNotNull(vo.getCreateUserId()));
        careMonitorInitialeVO.setCreateDeptId(PreConditionCheck.checkNotNull(vo.getCreateDeptId()));
        return initiate(List.of(careMonitorInitialeVO));
    }


}
