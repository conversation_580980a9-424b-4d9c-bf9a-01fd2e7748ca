package com.trs.police.control.service.approval.impl.comon;

import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.control.constant.enums.MonitorTypeEnum;
import com.trs.police.control.domain.vo.monitor.MonitorGroupVO;
import com.trs.police.control.service.approval.GroupMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 群体布控
 *
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(name = "control.approval.version", havingValue = "v1", matchIfMissing = true)
public class DefaultGroupMonitorApprovalService implements GroupMonitorService<MonitorGroupVO> {

    @Autowired
    private CommonMonitorApprovalService commonMonitorApprovalService;

    @Override
    public String approval(ApprovalRequest approvalRequest) {
        return commonMonitorApprovalService.approval(approvalRequest);
    }

    @Override
    public ApprovalRequest buildRequest(MonitorGroupVO vo, Long monitorId, String title, MonitorTypeEnum typeEnum, Operation operation) {
        return commonMonitorApprovalService.buildRequest(vo, monitorId, title, typeEnum, operation);
    }
}
