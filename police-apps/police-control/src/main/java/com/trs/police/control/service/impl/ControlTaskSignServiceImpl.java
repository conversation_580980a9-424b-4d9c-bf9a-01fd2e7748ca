package com.trs.police.control.service.impl;

import com.trs.police.common.core.constant.enums.PositionEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.control.aspect.OperationLog;
import com.trs.police.control.mapper.ControlTaskReceiveMapper;
import com.trs.police.control.service.ControlTaskSignService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 布控任务签收服务实现
 */
@Service
public class ControlTaskSignServiceImpl implements ControlTaskSignService {

    @Autowired
    private ControlTaskReceiveMapper controlTaskReceiveMapper;

    @Override
    @OperationLog(operateModule = OperateModule.CONTROL_TASK, operation = Operation.CONTROL_TASK_SIGN, idPosition = PositionEnum.PARAM, relatedId = "#taskId")
    public RestfulResultsV2<String> taskSignSingle(Long deptId, Long taskId) {
        controlTaskReceiveMapper.signByTaskIdAndReceiveDeptId(taskId, deptId);
        return RestfulResultsV2.ok("success");
    }

    @Override
    public Boolean checkSign(Long deptId, Long taskId) {
        Integer signStatus = controlTaskReceiveMapper.checkSign(taskId, deptId);
        //没查到算未签收，查到数据且状态为已签收返回true
        return !(Objects.nonNull(signStatus) && signStatus == 0);
    }

}
