package com.trs.police.control.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.WarningSourceVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.profile.LabelVO;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.core.vo.search.KeyValueTypeVoForSearch;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.common.openfeign.starter.service.SearchService;
import com.trs.police.control.config.WarningModalConfig;
import com.trs.police.control.constant.CareMonitorConstant;
import com.trs.police.control.constant.CommonConstants;
import com.trs.police.control.converter.PersonRelateToFkNoRecordConverter;
import com.trs.police.control.converter.PersonRelateToFkRecordConverter;
import com.trs.police.control.domain.dto.fkrxyj.FkCheckRegularDTO;
import com.trs.police.control.domain.dto.fkrxyj.InitFkDataDTO;
import com.trs.police.control.domain.dto.fkrxyj.PersonRelateToFkDTO;
import com.trs.police.control.domain.dto.fkrxyj.PersonRelateToFkListDTO;
import com.trs.police.control.domain.dto.fkzt.RyypDTO;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity;
import com.trs.police.control.domain.entity.fkrxyj.RyypEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningProcessFkrxyjEntity;
import com.trs.police.control.domain.vo.TrackPointVO;
import com.trs.police.control.domain.vo.care.CareMonitorInitialeVO;
import com.trs.police.control.domain.vo.fkrxyj.*;
import com.trs.police.control.domain.vo.fkzt.PersonLabel;
import com.trs.police.control.domain.vo.fkzt.PersonVO;
import com.trs.police.control.mapper.*;
import com.trs.police.control.service.CareMonitorService;
import com.trs.police.control.service.PersonRelate2FkService;
import com.trs.police.control.service.WarningFkrxyjService;
import com.trs.police.control.utils.ConverterHelper;
import com.trs.police.control.utils.EasyExcelUtil;
import com.trs.police.control.utils.FileUtil;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/01/24 10:12
 */
@Service
@Slf4j
public class PersonRelate2FkServiceImpl implements PersonRelate2FkService {

    @Resource
    private PersonRelateToFkMapper personRelateToFkMapper;

    @Resource
    private WarningFkrxyjMapper warningFkrxyjMapper;
    @Autowired
    private SearchService searchService;

    @Autowired
    private PersonRelate2FkHelper personRelate2FkHelper;

    @Resource
    private WarningProcessFkrxyjMapper warningProcessFkrxyjMapper;

    @Resource
    private WarningFeedbackFkrxyjMapper warningFeedbackFkrxyjMapper;

    @Autowired
    private PersonRelateToFkNoRecordConverter noRecordConverter;

    @Resource
    private SourceMapper sourceMapper;

    @Autowired
    private PersonRelateToFkRecordConverter recordConverter;

    @Resource
    private CareMonitorService careMonitorService;

    @Autowired
    private WarningModalConfig warningModalConfig;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private DictService dictService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private FxPersonYpMapper fxPersonYpMapper;

    @Autowired
    private WarningFkrxyjService warningFkrxyjService;

    @Override
    public PageResult<PersonVO> personList(ListParamsRequest params) {
        PageParams pageParams = params.getPageParams();
        Page<PersonRelateToFkEntity> ps = personRelateToFkMapper.personList(
                params, new Page<>(pageParams.getPageNumber(), pageParams.getPageSize())
        );
        // 转换成vo
        List<PersonVO> personVo = toPersonVo(ps.getRecords());
        PageResult<PersonVO> result = PageResult.of(personVo, pageParams.getPageNumber(), ps.getTotal(), pageParams.getPageSize());
        return result;
    }


    @Override
    public void addByPersonId(Long personId) {
        // 已经有了的话就不新建
        if (personRelateToFkMapper.selectOne(
                Wrappers.lambdaQuery(PersonRelateToFkEntity.class)
                        .eq(PersonRelateToFkEntity::getPersonProfileId, personId)
        ) != null) {
            return;
        }
        Optional<PersonRelateToFkEntity> entity = personRelate2FkHelper.buildByPersonId(personId);
        entity.ifPresent(personRelateToFkEntity -> personRelateToFkMapper.insert(personRelateToFkEntity));
    }

    @Override
    public void ryyp(RyypDTO dto) {
        // 根据id找到所有fk人员
        List<PersonRelateToFkEntity> ens = personRelateToFkMapper.selectBatchIds(dto.getRelatedId());
        for (PersonRelateToFkEntity en : ens) {
            // 找到所有的蓝色以上（不包含蓝色）的预警
            List<WarningFkrxyjEntity> warningFkrxyjEntities = warningFkrxyjMapper.selectList(
                    Wrappers.lambdaQuery(WarningFkrxyjEntity.class)
                            .eq(WarningFkrxyjEntity::getIdCard, en.getIdCard())
                            .lt(WarningFkrxyjEntity::getWarningLevel, MonitorLevelEnum.BLUE.getCode())
                            .notExists("select 1 from t_warning_fk_person_ryrp where warning_id = t_warning_fkrxyj.id")
            );

            if (warningFkrxyjEntities.isEmpty()) {
                continue;
            }
            String labelId = CollectionUtils.isEmpty(dto.getLabelId()) ? "[]" : JSON.toJSONString(dto.getLabelId());
            for (WarningFkrxyjEntity warningFkrxyjEntity : warningFkrxyjEntities) {
                RyypEntity yp = new RyypEntity();
                yp.setFkPersonId(en.getId());
                yp.setYpjl(dto.getComment());
                yp.setNation(dto.getNation());
                yp.setLabelId(labelId);
                yp.setIdCard(en.getIdCard());
                yp.setWarningId(warningFkrxyjEntity.getId());
                fxPersonYpMapper.insert(yp);
            }

            // 更新人员档案信息
            en.setWorkSituation(1);
            JSONArray jsonArray = JSON.parseArray(labelId);
            List<Long> lbIds = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                List<Long> lbId = jsonArray.getJSONArray(i).toJavaList(Long.class);
                if (!lbId.isEmpty()) {
                    lbIds.add(lbId.get(lbId.size() - 1));
                }
            }
            en.setLabelId(JSON.toJSONString(lbIds));
            personRelateToFkMapper.updateById(en);
        }
    }

    @Override
    public FileInfoVO getFkryImportTemplate() {
        Long property = BeanFactoryHolder.getEnv().getProperty("control.fkry.import.file.id", Long.class);
        FileInfoVO info = new FileInfoVO();
        info.setId(property);
        return info;
    }

    @Override
    public Long getFkryLabelRoot() {
        Long property = BeanFactoryHolder.getEnv().getProperty("control.fkry.label.root.id", Long.class);
        return property;
    }

    @Override
    public void importFkry(Long fileId) {

    }

    @Override
    public void exportFkry(List<Long> ids) {
        List<PersonRelateToFkEntity> ps = personRelateToFkMapper.selectBatchIds(ids);
        List<PersonVO> vo = toPersonVo(ps);
        try {
            EasyExcelUtil.commonWebExport(
                    Arrays.asList(
                            Arrays.asList("姓名"),
                            Arrays.asList("性别"),
                            Arrays.asList("居民身份证号"),
                            Arrays.asList("人员级别"),
                            Arrays.asList("预警"),
                            Arrays.asList("标签"),
                            Arrays.asList("工作情况"),
                            Arrays.asList("责任派出所"),
                            Arrays.asList("建档情况"),
                            Arrays.asList("通联号码"),
                            Arrays.asList("户籍地址"),
                            Arrays.asList("基础摸排地址"),
                            Arrays.asList("十类人员类别"),
                            Arrays.asList("流入时间"),
                            Arrays.asList("相关警情")
                    ),
                    vo.stream().map(this::getExportYrData).collect(Collectors.toList()),
                    "人员列表导出"
            );
        } catch (Exception e) {
            log.error("导出人员列表失败", e);
        }
    }

    private List getExportYrData(PersonVO vo) {
        List result = new ArrayList();
        result.add(vo.getName());
        result.add(Objects.equals(1, vo.getXb()) ? "男" : Objects.equals(2, vo.getXb()) ? "女" : "");
        result.add(vo.getIdCard());
        result.add(vo.getPersonLevelName());
        if (Objects.nonNull(vo.getLastWarning())) {
            String title = String.format("%s, %s", vo.getLastWarning().getWarningModel(), vo.getLastWarning().getWarningTime());
            result.add(title);
        } else {
            result.add("");
        }
        result.add(Optional.ofNullable(vo.getPerLabelList()).orElse(new ArrayList<>()).stream().map(PersonLabel::getName).collect(Collectors.joining(",")));
        if (Objects.equals(0, vo.getGzqk())) {
            result.add("需要研判");
        } else if (Objects.equals(1, vo.getGzqk())) {
            result.add("已研判");
        } else {
            result.add("无需研判");
        }
        result.add(Optional.ofNullable(vo.getControlStationList()).orElse(new ArrayList<>()).stream().map(DeptDto::getName).collect(Collectors.joining(",")));
        result.add(Objects.equals(1, vo.getJdqk()) ? "已建档" : "未建档");
        result.add(Optional.ofNullable(vo.getTelList()).orElse(new ArrayList<>()).stream().map(String::valueOf).collect(Collectors.joining(",")));
        result.add(vo.getHjdName());
        result.add(vo.getMpdzName());
        result.add(Objects.equals(1, vo.getTenPersonType()) ? "是" : "否");
        result.add(vo.getInflowTime());
        result.add(vo.getWarningCount());
        return result;
    }

    @Override
    public PageResult<PersonRelateToFkNoRecordVo> getNotRecordList(PersonRelateToFkListDTO dto) {
        dto.setOnRecord(CommonConstants.RECORD_STATUS_0);
        Page<PersonRelateToFkEntity> page = Page.of(dto.getPageNum(), dto.getPageSize());
        Page<PersonRelateToFkEntity> pageResult = personRelateToFkMapper.getNotRecordList(dto, page);
        List<PersonRelateToFkNoRecordVo> listResult = noRecordConverter.toVoList(pageResult.getRecords());
        listResult.forEach(e->e.setPhoto("/oss/photo/" + e.getIdCard()));
        return PageResult.of(listResult, dto.getPageNum(), pageResult.getTotal(), dto.getPageSize());
    }

    /**
     * 设置photo
     *
     * @param listResult 反馈列表数据
     * @param list 基座数据
     */
    private static void setPhoto(List<PersonRelateToFkNoRecordVo> listResult, List<ArchivesVO> list) {
        //获取身份证
        Function<ArchivesVO,String> idCardFunction = (vo)->{
            KeyValueTypeVoForSearch vo2 = vo.getFields().stream().filter(e -> e.getKey().equals("证件号码") && e.getFieldName().equals("zjhm"))
                    .findFirst().orElse(null);
            if (Objects.isNull(vo2)){
                return "无身份证信息人员";
            }
            Matcher matcher = Pattern.compile("\\d{17}[\\d|x|X]|\\d{15}").matcher(vo2.getValue().toString());
            return matcher.find() ? matcher.group() : "默认身份证";
        };
        //获取标签信息
        Function<ArchivesVO,String> labelFunction =  (vo)->{
            KeyValueTypeVoForSearch vo2 = vo.getFields().stream().filter(e -> e.getKey().equals("标签信息") && e.getFieldName().equals("tags"))
                    .findFirst().orElse(null);
            return Objects.isNull(vo2) ? "": JSONObject.parseArray(JSONObject.toJSONString(vo2.getValue()),JSONObject.class)
                    .stream().map(e->e.getString("tag_name")).collect(Collectors.joining(","));
        };
        Map<String, String> map = CollectionUtils.isEmpty(list)
                ? new HashMap<>() : list.stream().filter(e -> !CollectionUtils.isEmpty(e.getFields()))
                .collect(Collectors.toMap(idCardFunction, labelFunction, (e1, e2) -> e1));
        listResult.forEach(e->e.setPhoto(map.get(e.getIdCard())));
    }

    @Override
    public PageResult<PersonRelateToFkRecordVo> getRecordList(PersonRelateToFkListDTO dto) {
        dto.setOnRecord(CommonConstants.RECORD_STATUS_1);
        Page<PersonRelateToFkRecordVo> page = Page.of(dto.getPageNum(), dto.getPageSize());
        List<String> stations = dto.getStations();
        if (!CollectionUtils.isEmpty(stations)) {
            List<String> preFixCodes = stations.stream().map(StringUtil::getPrefixCode).distinct().collect(Collectors.toList());
            dto.setStations(preFixCodes);
        }
        Page<PersonRelateToFkRecordVo> pageResult = personRelateToFkMapper.getRecordList(dto, page);

        List<PersonRelateToFkRecordVo> listResult = pageResult.getRecords().stream()
                .map(this::dealOtherInfo).collect(Collectors.toList());

        return PageResult.of(listResult, dto.getPageNum(), pageResult.getTotal(), dto.getPageSize());
    }

    /**
     * 处理责任民警字段
     *
     * @param personRelateToFkRecordVo 原始数据
     * @return 处理后数据
     */
    private PersonRelateToFkRecordVo dealOtherInfo(PersonRelateToFkRecordVo personRelateToFkRecordVo) {

        //预警信息和24小时轨迹
        Long trackCount = ConverterHelper.getTrackCount(personRelateToFkRecordVo.getIdCard());
        Long warningCount = ConverterHelper.getWarningCount(personRelateToFkRecordVo.getIdCard());
        personRelateToFkRecordVo.setTrackCount(trackCount);
        personRelateToFkRecordVo.setWarningCount(warningCount);
        //处理图片
        personRelateToFkRecordVo.setPhoto(ConverterHelper.getPhoto(personRelateToFkRecordVo.getPhoto()));

        //责任民警
        List<Long> userIds = JsonUtil.objectToArray(personRelateToFkRecordVo.getDutyPolice(), Long.class);
        if (userIds != null && !userIds.isEmpty()) {
            List<UserDto> users = BeanUtil.getBean(PermissionService.class).getUserListById(userIds);
            String dutyPoliceName = users.stream().map(UserDto::getRealName).collect(Collectors.joining(";"));
            personRelateToFkRecordVo.setDutyPoliceName(dutyPoliceName);
        }

        //标签
        List<Long> labelIds = JsonUtil.objectToArray(personRelateToFkRecordVo.getPersonLabel(), Long.class);
        if (labelIds != null && !labelIds.isEmpty()) {
            String labelName = personRelateToFkMapper.getLabelNames(labelIds);
            personRelateToFkRecordVo.setPersonLabelName(labelName);
        }

        //户籍所在地
        String code = personRelateToFkRecordVo.getRegisteredResidence();
        if (!StringUtils.isEmpty(code)) {
            DistrictListDto districtDto = BeanUtil.getBean(DictService.class).getDistrictByCode(code);
            if (null != districtDto) {
                personRelateToFkRecordVo.setRegisteredResidence(districtDto.getName());
            }
        }
        return personRelateToFkRecordVo;
    }

    @Override
    public boolean saveRelationOfProfile(PersonRelateToFkDTO dto) {
        PersonRelateToFkEntity personRelateToFkEntity = personRelateToFkMapper.selectById(dto.getPersonRelateToFkId());
        if (personRelateToFkEntity == null) {
            log.error("根据人员id{}无法获取对应fk人员", dto.getPersonRelateToFkId());
            return false;
        }
        personRelateToFkEntity.setOnRecord(CommonConstants.RECORD_STATUS_1);
        personRelateToFkEntity.setPersonProfileId(dto.getPersonProfileId());
        personRelateToFkMapper.updateById(personRelateToFkEntity);
        return true;
    }

    @Override
    public boolean manualInitPersonRelate2Fk(InitFkDataDTO dto) {
        QueryWrapper<WarningFkrxyjEntity> queryWrapper = new QueryWrapper<>();
        //身份证的处理
        String idCard = dto.getIdCard();
        if (!StringUtils.isEmpty(idCard)) {
            queryWrapper.eq("id_card", dto.getIdCard());
        }

        LocalDateTime start = dto.getCaptureTimeStart();
        if (null != start) {
            queryWrapper.ge("capture_time", start);
        }
        LocalDateTime end = dto.getCaptureTimeEnd();
        if (null != end) {
            queryWrapper.le("capture_time", end);
        }
        //默认以常见时间排序
        queryWrapper.orderByAsc("create_time");

        long totalCount = warningFkrxyjMapper.selectCount(queryWrapper);
        //是否需要分页
        if (totalCount <= 0) {
            return true;
        }

        // 分批进行
        Page<WarningFkrxyjEntity> page;
        if (CommonConstants.SYNC_PAGE_SIZE <= totalCount) {
            long count = (totalCount + CommonConstants.SYNC_PAGE_SIZE - 1) / CommonConstants.SYNC_PAGE_SIZE;
            for (long i = 1L; i <= count; i++) {
                page = Page.of(i, CommonConstants.SYNC_PAGE_SIZE);
                initFkPerson(queryWrapper, page);
            }
            return true;
        }
        // 一次处理完
        page = Page.of(1L, totalCount);

        initFkPerson(queryWrapper, page);

        return true;
    }

    /**
     * 根据预警信息初始化人员数据
     *
     * @param queryWrapper 检索条件
     * @param page         分页
     */
    private void initFkPerson(QueryWrapper<WarningFkrxyjEntity> queryWrapper, Page<WarningFkrxyjEntity> page) {
        Page<WarningFkrxyjEntity> pageResult = warningFkrxyjMapper.selectPage(page, queryWrapper);
        List<WarningFkrxyjEntity> listResult = pageResult.getRecords();
        for (WarningFkrxyjEntity entity : listResult) {
            try {
                //必须捕获异常，否则会营销后续的数据初始化
                personRelate2FkHelper.fkWarning2Person(entity);
            } catch (Exception e) {
                log.error("初始化fk人员【{}】出错", entity.getIdCard());
            }

        }
    }


    @Override
    public PageResult<FkPersonWarningVO> getFkWarning(Long fkPersonId, PageParams pageParams) {
        PersonRelateToFkEntity personRelateToFkEntity = personRelateToFkMapper.selectById(fkPersonId);
        if (personRelateToFkEntity == null) {
            return PageResult.empty(pageParams.getPageNumber(), pageParams.getPageSize());
        }
        Page<WarningFkrxyjEntity> page = pageParams.toPage();
        Page<WarningFkrxyjEntity> warningPage = warningFkrxyjMapper.getFkWarningByIdCard(personRelateToFkEntity.getIdCard(), null, null, page);
        List<FkPersonWarningVO> finalWarningVoList = warningPage.getRecords().stream()
                .map(this::toWarningVO).collect(Collectors.toList());
        VoParameterConstructor
                .of(finalWarningVoList)
                .singleValueMatcher(this::getPersonByWarning, (w, p) -> Objects.equals(w.getIdCard(), p.getIdCard()))
                .consumer((w, p) -> {
                    w.setControlStationList(p.getControlStationList());
                })
                .singleValueMatcher(this::getYpListByWarningId, (w, yp) -> Objects.equals(w.getId(), yp.getWarningId()))
                .consumer((w, yp) -> w.setGzqk(1))
                .build();
        return PageResult.of(finalWarningVoList, pageParams.getPageNumber(), warningPage.getTotal(), pageParams.getPageSize());
    }

    private FkPersonWarningVO toWarningVO(WarningFkrxyjEntity entity) {
        if (entity == null) {
            return null;
        }
        final SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        FkPersonWarningVO warningVO = new FkPersonWarningVO();
        warningVO.setId(entity.getId());
        warningVO.setContent(TimeUtil.getSimpleTime(entity.getCaptureTime()) + " " + entity.getSuspectName() + " 出现在" + entity.getCaptureAddress());
        warningVO.setModel("人像");
        warningVO.setControlType("1");
        warningVO.setWarningType("人员");
        warningVO.setWarningTime(TimeUtil.getSimpleTime(entity.getCaptureTime()));
        MonitorLevelEnum level = entity.getWarningLevel();
        if (Objects.nonNull(level)) {
            warningVO.setWarningLevel(level.getName());
        }
        WarningProcessFkrxyjEntity process = warningProcessFkrxyjMapper.getFkrxyjUserProcessByWarningId(entity.getId(), currentUser.getUserId(),
                currentUser.getDeptId());
        if (Objects.nonNull(process)) {
            warningVO.setStatus(process.getStatus().getName());
        } else {
            if (Boolean.TRUE.equals(warningProcessFkrxyjMapper.getWarningDoneStatus(entity.getId()))) {
                warningVO.setStatus("已完结");
            } else {
                warningVO.setStatus("处置中");
            }
        }
        int feedbackNum = warningFeedbackFkrxyjMapper.countFeedbackByWarningId(entity.getId());
        warningVO.setFeedbackNum(feedbackNum);
        warningVO.setWarningModel(entity.getWarningModel());
        if (Objects.nonNull(entity.getWarningLevel())) {
            warningVO.setWarningLevelInfo(new CodeNameVO(entity.getWarningLevel().getCode(), entity.getWarningLevel().getName()));
        }
        warningVO.setIdCard(entity.getIdCard());
        // 如果是蓝色以上的，没有研判记录，就需要研判
        if (entity.getWarningLevel().getCode() >= MonitorLevelEnum.BLUE.getCode()) {
           warningVO.setGzqk(2);
        } else {
            warningVO.setGzqk(0);
        }
        return warningVO;
    }

    private List<PersonVO> getPersonByWarning(List<FkPersonWarningVO> finalWarningVoList) {
        // 构造人员信息
        final List<String> ids = finalWarningVoList.stream()
                .map(FkPersonWarningVO::getIdCard)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        final List<PersonRelateToFkEntity> personRelateToFkEntities = CollectionUtils.isEmpty(ids) ? new ArrayList<>() : personRelateToFkMapper.selectList(
                Wrappers.lambdaQuery(PersonRelateToFkEntity.class)
                        .in(PersonRelateToFkEntity::getIdCard, ids)
        );
        final List<PersonVO> ps = toPersonVo(personRelateToFkEntities);
        return ps;
    }

    private List<RyypEntity> getYpListByWarningId(List<FkPersonWarningVO> finalWarningVoList) {
        List<Long> id = finalWarningVoList.stream()
                .map(FkPersonWarningVO::getId)
                .collect(Collectors.toList());
        List<RyypEntity> ryypEntities = CollectionUtils.isEmpty(id) ? new ArrayList<>() : fxPersonYpMapper.selectList(
                Wrappers.lambdaQuery(RyypEntity.class)
                        .in(RyypEntity::getWarningId, id)
        );
        return ryypEntities;
    }

    @Override
    public PageResult<TrackPointVO> getFkTrackList(Long fkPersonId, ListParamsRequest listParamsRequest) {
        PersonRelateToFkEntity personRelateToFkEntity = personRelateToFkMapper.selectById(fkPersonId);
        PageParams pageParams = listParamsRequest.getPageParams();
        if (personRelateToFkEntity == null) {
            return PageResult.empty(pageParams.getPageNumber(), pageParams.getPageSize());
        }
        Page<WarningFkrxyjEntity> page = pageParams.toPage();
        Page<WarningFkrxyjEntity> warningPage = warningFkrxyjMapper.getFkWarningByIdCard(personRelateToFkEntity.getIdCard(),
                listParamsRequest.getFilterParams(), listParamsRequest.getSearchParams(), page);
        List<TrackPointVO> trackPointVoList = warningPage.getRecords().stream()
                .map(this::toTrackPointVO).collect(Collectors.toList());
        return PageResult.of(trackPointVoList, pageParams.getPageNumber(), warningPage.getTotal(), pageParams.getPageSize());
    }

    private TrackPointVO toTrackPointVO(WarningFkrxyjEntity entity) {
        if (entity == null) {
            return null;
        }
        TrackPointVO vo = new TrackPointVO();
        vo.setId(String.valueOf(entity.getId()));
        vo.setTime(TimeUtil.getSimpleTime(entity.getCaptureTime()));
        vo.setLat(Double.parseDouble(entity.getLatitude()));
        vo.setLng(Double.parseDouble(entity.getLongitude()));
        vo.setName(entity.getName());
        vo.setLocation(entity.getCaptureAddress());
        vo.setFacePhoto(ConverterHelper.getPhoto(entity.getFacePhoto()));
        vo.setImg(ConverterHelper.getPhoto(entity.getPhoto()));
        //人像识别
        vo.setType(5L);
        return vo;
    }

    @Override
    public List<FkCheckRegularVO> checkRegular(FkCheckRegularDTO dto) {
        List<Long> fkPersonIds = dto.getFkPersonIds();
        List<PersonRelateToFkEntity> fkEntityList = personRelateToFkMapper.selectBatchIds(fkPersonIds);
        if (CollectionUtils.isEmpty(fkEntityList)) {
            return new ArrayList<>();
        }
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        return fkEntityList.stream().
                map(e -> {
                    FkCheckRegularVO vo = new FkCheckRegularVO();
                    vo.setFkPersonId(e.getPersonProfileId());
                    vo.setName(e.getName());
                    vo.setIdCard(e.getIdCard());
                    vo.setCheckStatus(!personRelateToFkMapper.selectIsRegularControlByUser(e.getPersonProfileId(), currentUser.getUserId()));
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public void notRecordedExport(HttpServletResponse httpServletResponse, ExportParams params) throws Exception {
        List<ExportPersonRelateToFkVo> notRecordListVo = getExportNotRecordVoList(params);
        FileUtil.downloadExcel(httpServletResponse, ExportPersonRelateToFkVo.class,
                "未建档关注人员列表", "工作表1", params.getFieldNames(), notRecordListVo);
    }

    private List<ExportPersonRelateToFkVo> getExportNotRecordVoList(ExportParams params) {
        List<Long> ids = params.getIds();
        List<PersonRelateToFkEntity> fkEntities = personRelateToFkMapper.selectBatchIds(ids);
        List<PersonRelateToFkNoRecordVo> fkNoRecordVos = noRecordConverter.toVoList(fkEntities);
        return fkNoRecordVos.stream().map(e -> {
            ExportPersonRelateToFkVo vo = new ExportPersonRelateToFkVo();
            vo.setName(e.getName());
            vo.setIdCard(e.getIdCard());
            vo.setPersonStatus(CommonConstants.SCRQ.equals(e.getPersonStatus()) ? "首次入区" : "入区三天");
            vo.setWarningCount(e.getWarningCount());
            vo.setTrackPoint(e.getTrackCount());
            vo.setFirstIntoTime(TimeUtil.getSimpleTime(e.getFirstIntoTime()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public void recordedExport(HttpServletResponse httpServletResponse, ExportParams params) throws Exception {
        List<ExportPersonRelateToFkVo> recordListVo = getExportRecordVoList(params);
        FileUtil.downloadExcel(httpServletResponse, ExportPersonRelateToFkVo.class,
                "已建档关注人员列表", "工作表1", params.getFieldNames(), recordListVo);
    }

    private List<ExportPersonRelateToFkVo> getExportRecordVoList(ExportParams params) {
        List<Long> ids = params.getIds();
        List<PersonRelateToFkRecordVo> fkRecordVos = personRelateToFkMapper.getRecordListByIds(ids);
        List<PersonRelateToFkRecordVo> finalFkRecordVos = fkRecordVos.stream()
                .map(this::dealOtherInfo).collect(Collectors.toList());
        return finalFkRecordVos.stream().map(e -> {
            ExportPersonRelateToFkVo vo = new ExportPersonRelateToFkVo();
            vo.setName(e.getName());
            vo.setIdCard(e.getIdCard());
            vo.setPersonStatus(CommonConstants.SCRQ.equals(e.getPersonStatus()) ? "首次入区" : "入区三天");
            vo.setWarningCount(e.getWarningCount());
            vo.setTrackPoint(e.getTrackCount());
            vo.setFirstIntoTime(TimeUtil.getSimpleTime(e.getFirstIntoTime()));
            vo.setPersonLabel(e.getPersonLabel());
            vo.setControlLevel(e.getControlLevel());
            vo.setDutyPoliceName(e.getDutyPoliceName());
            vo.setDutyPoliceStation(e.getDutyPoliceStation());
            vo.setRegisteredResidence(e.getRegisteredResidence());
            vo.setCreateTime(TimeUtil.getSimpleTime(e.getCreateTime()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public PersonRelateToFkNoRecordVo getNoRecordDetail(Long fkPersonId) {
        PersonRelateToFkEntity entity = personRelateToFkMapper.selectById(fkPersonId);
        if (entity == null) {
            return null;
        }
        return noRecordConverter.toVo(entity);
    }

    @Override
    public List<ListColumnResultVO> getNoRecordHeaderDetail(Long fkPersonId) {
        PersonRelateToFkEntity entity = personRelateToFkMapper.selectById(fkPersonId);
        if (entity == null) {
            return null;
        }
        final List<ListColumnResultVO> list = new ArrayList<>();
        ListColumnResultVO photoVo = new ListColumnResultVO();
        photoVo.setKey("photo");
        photoVo.setTitle("照片");
        photoVo.setType("photo");
        JSONArray photoArray = new JSONArray();
        JSONObject photo = new JSONObject();
        photo.put("url", ConverterHelper.getPhoto(entity.getPhoto()));
        photoArray.add(photo);
        photoVo.setValue(photoArray);
        list.add(photoVo);

        ListColumnResultVO nameVo = new ListColumnResultVO();
        nameVo.setKey("name");
        nameVo.setTitle("姓名");
        nameVo.setType("string");
        nameVo.setValue(entity.getName());
        list.add(nameVo);

        ListColumnResultVO idCardVo = new ListColumnResultVO();
        idCardVo.setKey("idNumber");
        idCardVo.setTitle("证件号码");
        idCardVo.setType("string");
        idCardVo.setValue(entity.getIdCard());
        list.add(idCardVo);

        return list;
    }

    @Override
    public DynamicTableResultVO getNoRecordPersonInfoDetail(Long fkPersonId) {
        PersonRelateToFkEntity entity = personRelateToFkMapper.selectById(fkPersonId);
        if (entity == null) {
            return null;
        }
        DynamicTableResultVO resultVO = new DynamicTableResultVO();
        resultVO.setBordered(true);
        resultVO.setColumn(4);

        final List<ItemResult> list = new ArrayList<>();
        ItemResult idCard = new ItemResult();
        idCard.setSpan(1);
        idCard.setCopyable(true);
        idCard.setTitle("证件号码");
        idCard.setType("string");
        idCard.setValue(entity.getIdCard());
        list.add(idCard);

        ItemResult name = new ItemResult();
        name.setSpan(1);
        name.setCopyable(false);
        name.setTitle("姓名");
        name.setType("string");
        name.setValue(entity.getName());
        list.add(name);

        ItemResult cardType = new ItemResult();
        cardType.setSpan(1);
        cardType.setCopyable(false);
        cardType.setTitle("证件类型");
        cardType.setType("string");
        cardType.setValue("身份证号");
        list.add(cardType);

        resultVO.setItems(list);
        return resultVO;
    }

    @Override
    public void careMonitor(String fkPersonIds) {
        //根据fkPersonIds转为关注对象
        String[] arrFkPersonId = fkPersonIds.split(",");
        List<CareMonitorInitialeVO> careMonitorVoS = new ArrayList<>();
        for (String fkPersonId : arrFkPersonId) {
            PersonRelateToFkEntity personRelateToFk = personRelateToFkMapper.selectById(fkPersonId);

            CareMonitorInitialeVO careMonitorInitialeVO = new CareMonitorInitialeVO();
            careMonitorInitialeVO.setCertificateType(IdentifierTypeEnum.ID_NUMBER);
            careMonitorInitialeVO.setCertificateValue(personRelateToFk.getIdCard());
            //配置了要特殊使用的预警配置id
            if (warningModalConfig.fkCareMonitorConfigId > 0) {
                careMonitorInitialeVO.setMonitorConfigId(warningModalConfig.fkCareMonitorConfigId);
            }
            careMonitorInitialeVO.setModuleCareMonitorHandlerClassName(CareMonitorConstant.CMH_FK);
            careMonitorInitialeVO.setCreateUserId(AuthHelper.getNotNullUser().getId() + "");
            careMonitorInitialeVO.setCreateDeptId(AuthHelper.getNotNullUser().getDeptId() + "");
            careMonitorVoS.add(careMonitorInitialeVO);
        }

        try {
            careMonitorService.initiate(PreConditionCheck.checkNotNull(careMonitorVoS));
        } catch (Exception ex) {
            log.error("发起关注监控失败，原因为{}, 传入的关注监控内容长度为：{}", ex.getMessage(), careMonitorVoS.size());
            ex.printStackTrace();
        }
    }

    @Override
    public void careMonitorAll() {
        //查询所有的fk人员
        QueryWrapper<PersonRelateToFkEntity> emptyWrapper = new QueryWrapper<>();
        List<PersonRelateToFkEntity> allPerson = personRelateToFkMapper.selectList(emptyWrapper);
        //遍历进行关注
        List<CareMonitorInitialeVO> careMonitorVoS = new ArrayList<>();
        for (PersonRelateToFkEntity curPerson : allPerson) {
            CareMonitorInitialeVO careMonitorInitialeVO = new CareMonitorInitialeVO();
            careMonitorInitialeVO.setCertificateType(IdentifierTypeEnum.ID_NUMBER);
            careMonitorInitialeVO.setCertificateValue(curPerson.getIdCard());
            //配置了要特殊使用的预警配置id
            if (warningModalConfig.fkCareMonitorConfigId > 0) {
                careMonitorInitialeVO.setMonitorConfigId(warningModalConfig.fkCareMonitorConfigId);
            }
            careMonitorInitialeVO.setModuleCareMonitorHandlerClassName(CareMonitorConstant.CMH_FK);
            careMonitorInitialeVO.setCreateUserId(AuthHelper.getNotNullUser().getId() + "");
            careMonitorInitialeVO.setCreateDeptId(AuthHelper.getNotNullUser().getDeptId() + "");
            careMonitorVoS.add(careMonitorInitialeVO);
        }

        try {
            careMonitorService.initiate(PreConditionCheck.checkNotNull(careMonitorVoS));
        } catch (Exception ex) {
            log.error("发起关注监控失败，原因为{}, 传入的关注监控内容长度为：{}", ex.getMessage(), careMonitorVoS.size());
            ex.printStackTrace();
        }

    }

    @Override
    public WarningSourceVO getFkSource(String id) {
        WarningFkrxyjEntity warningFkrxyjEntity = warningFkrxyjMapper.selectById(id);
        SourceEntity sourceEntity = sourceMapper.selectByUniqueKey(warningFkrxyjEntity.getCaptureAddress() + "-face_camera");
        SourceEntity vehicleSource = sourceMapper.selectByUniqueKey(warningFkrxyjEntity.getCaptureAddress() + "-vehicle_camera");
        if (Objects.nonNull(sourceEntity)){
            return sourceEntity.toVO();
        } else if (Objects.nonNull(vehicleSource)) {
            return vehicleSource.toVO();
        }
        return new WarningSourceVO();
    }

    private BaseArchivesSearchDTO buildDto(List<PersonRelateToFkNoRecordVo> listResult) {
        BaseArchivesSearchDTO dto = new BaseArchivesSearchDTO();
        String uuid = searchService.uuid();
        JSONObject object = JSONObject.parseObject(uuid);
        dto.setUuid((String) object.getJSONArray("data").get(0));
        String idCards = listResult.stream().filter(e -> com.trs.common.utils.StringUtils.isNotEmpty(e.getIdCard()))
                .map(PersonRelateToFkNoRecordVo::getIdCard).collect(Collectors.joining("|"));
        dto.setArchivesType("person");
        dto.setIdCard(idCards);
        dto.setLabelsType("ALL");
        dto.setPageNum(1);
        dto.setPageSize(listResult.size());
        dto.setSearchValue("证件号码：" + idCards);
        return dto;
    }

    private List<PersonVO> toPersonVo(List<PersonRelateToFkEntity> entity) {
        if (Objects.isNull(entity)) {
            return new ArrayList<>();
        }
        List<PersonVO> list = entity.stream()
                .map(this::toPersonBaseVo)
                .collect(Collectors.toList());
        // 统计预警数量，先直接填写1
        list.forEach(e -> e.setWarningCount(1));
        // 人员标签码表
        final List<LabelVO> regular = profileService.allLabelList("person");
        // 部门名称
        final List<Long> did = list.stream()
                .map(PersonVO::getControlStationIdList)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        List<DeptDto> deptByIds = did.isEmpty() ? new ArrayList<>() : permissionService.getDeptByIds(did);
        // 最后的预警信息
        final List<Long> warningId = list.stream()
                .map(PersonVO::getLastWarningId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 警情信息
        final String idNumber = list.stream()
                .map(PersonVO::getIdCard)
                .collect(Collectors.joining(","));
        final String tel = list.stream()
                .map(PersonVO::getTelList)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.joining(","));
        // 地域
        final List districtCodes = list.stream()
                .map(PersonVO::getPlaceCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<DistrictDto> byDistrictCodes = CollectionUtils.isEmpty(districtCodes) ? new ArrayList<>() : dictService.getByDistrictCodes(districtCodes);
        VoParameterConstructor
                .of(list)
                .multiValueMatcher(d -> regular, (d, v) ->d.getLabelId().contains(v.getId()))
                .consumer((d, v) -> d.setPerLabelList(v.stream().map(PersonLabel::new).collect(Collectors.toList())))
                .multiValueMatcher(d -> deptByIds, (d, v) -> d.getControlStationIdList().contains(v.getId()))
                .consumer(PersonVO::setControlStationList)
                .singleValueMatcher(d -> dictService.commonSearch("profile_person_control_level", null, null, null), (d, v) -> Objects.equals(d.getPersonLevel(), v.getCode()))
                .mapAndConsumer(Dict2VO::getName, PersonVO::setPersonLevelName)
                .singleValueMatcher(d -> warningFkrxyjService.getWarningListByIds(warningId), (d, v) -> Objects.equals(d.getLastWarningId(), v.getId()))
                .consumer(PersonVO::setLastWarning)
                .multiValueMatcher(d -> profileService.findByTelAndIdCard(idNumber, tel), (p, v) -> p.getTelList().contains(Optional.of(v.getBjdh()).orElse("-1")) || p.getIdCard().equals(v.getIdNumber()))
                .consumer((p, j) -> p.setJqCount(j.size()))
                .singleValueMatcher(d -> byDistrictCodes, (d, v) -> Objects.equals(d.getPlaceCode(), v.getCode()))
                .consumer((p, v) -> p.setHjdName(v.getName()))
                .build();
        return list;
    }

    private PersonVO toPersonBaseVo(PersonRelateToFkEntity entity) {
        PersonVO person = new PersonVO();
        person.setId(entity.getId());
        person.setPhoto(entity.getPhoto());
        person.setName(entity.getName());
        person.setIdCard(entity.getIdCard());
        person.setPersonLevel(entity.getControlLevel());
        if (Objects.nonNull(entity.getLastWarningTime())) {
            person.setWarningTime(entity.getLastWarningTime().format(DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS)));
        }
        person.setLastWarningId(entity.getLastWarningId());
        // 预警数量重新统计
        person.setWarningCount(0);
        person.setLabelId(StringUtil.isEmpty(entity.getLabelId()) ? new ArrayList<>() : JSON.parseArray(entity.getLabelId(), Long.class));
        person.setGzqk(entity.getWorkSituation());
        person.setJdqk(Objects.isNull(entity.getPersonProfileId()) ? 0 : 1);
        person.setTelList(StringUtil.isEmpty(entity.getTel()) ? new ArrayList<>() : JSON.parseArray(entity.getTel(), String.class));
        person.setPlaceCode(entity.getPlaceCode());
        person.setMpdzName(null);
        person.setTenPersonType(entity.getTenPersonType());
        if (Objects.nonNull(entity.getFirstIntoTime())) {
            person.setInflowTime(entity.getFirstIntoTime().format(DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS)));
        }
        person.setControlStationIdList(StringUtil.isEmpty(entity.getZrpcs()) ? new ArrayList<>() : JSON.parseArray(entity.getZrpcs(), Long.class));
        person.setXb(entity.getXb());
        person.setMpdzName(entity.getMpdzCode());
        person.setJqCount(0);
        return person;
    }
}
