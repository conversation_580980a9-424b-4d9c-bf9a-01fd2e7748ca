package com.trs.police.control.kafka.v2.flow.processor;

import com.trs.data.processor.IDataProcessor;
import com.trs.mq.utils.CollectionUtils;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.kafka.v2.flow.processor.warning.attrubute.IWarningAttributeBuilder;
import com.trs.police.control.kafka.v2.flow.processor.warning.postprocess.IWarningPostProcessor;
import com.trs.police.control.mapper.WarningMapper;
import com.trs.police.control.mapper.WarningSourceTypeMapper;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 布控/常控预警消息处理器
 * *@author:wen.wen
 * *@create 2025-01-23 17:01
 **/
@Slf4j
public abstract class AbstractMonitorWarningMessageProcessor extends BasicWarningMessageProcessor {

    @Resource
    private WarningMapper warningMapper;
    @Resource
    private WarningSourceTypeMapper warningSourceTypeMapper;

    @Resource
    private List<IWarningAttributeBuilder> warningAttributeBuilders;

    @Resource
    private List<IWarningPostProcessor> warningPostProcessors;

    private List<IWarningPostProcessor> sortedWarningPostProcessors;

    @Override
    public IDataProcessor<WarningMessageContext, WarningMessageContext> dataProcessor() {
        return this::doProcess;
    }

    /**
     * 执行操作行为
     *
     * @param context WarningMessageContext
     * @return WarningMessageContext
     */
    protected WarningMessageContext doProcess(WarningMessageContext context) {
        ControlInfo controlInfo = context.getControlInfo();
        if (controlInfo == null || CollectionUtils.isEmpty(controlInfo.getWarningInfo())) {
            log.warn("控制信息为空或无预警信息，丢弃该条预警! 预警内容：{}", context.getMessage());
            return null;
        }
        WarningSourceTypeEntity warningSourceType = getWanringConfig(context);
        //当没有匹配到预警模板，则丢弃
        if (warningSourceType == null) {
            log.info("没有轨迹类型为：{} 的配置，丢弃该条预警！预警内容: {}", context.getWarningDTO().getEnName(), context.getMessage());
            return null;
        }
        context.setWarningConfig(warningSourceType);
        processWarningInfo(context, controlInfo);
        return context;
    }

    /**
     * 插入数据
     *
     * @param context 上下文对象
     * @param controlInfo 管控信息
     */
    public void processWarningInfo(WarningMessageContext context, ControlInfo controlInfo) {
        for (ControlInfo.WarningInfo warningInfo : controlInfo.getWarningInfo()) {
            WarningMessageContext.WarningResultContext warningResultContext = new WarningMessageContext.WarningResultContext();
            warningResultContext.setWarningInfo(warningInfo);
            //3.执行插入预警信息
            insertWarningEntity(context, warningResultContext);
            //其他额外的操作:例如：执行插入预警轨迹信息、添加云控接收日志、设置通知对象、插入数据到ES 等等
            for (IWarningPostProcessor dataProcessor : dataProcessors()) {
                warningResultContext = dataProcessor.process(context, warningResultContext);
            }
        }
    }

    /**
     * 执行预警实体插入
     *
     * @param context              WarningMessageContext
     * @param warningResultContext WarningResultContext
     */
    private void insertWarningEntity(WarningMessageContext context,
                                     WarningMessageContext.WarningResultContext warningResultContext) {
        log.info("插入数据成功");
        warningResultContext.setWarningEntity(new WarningEntity());
        for (IWarningAttributeBuilder warningAttributeBuilder : warningAttributeBuilders) {
            warningAttributeBuilder.build(context, warningResultContext);
        }
        warningMapper.insert(warningResultContext.getWarningEntity());
    }

    /**
     * 数据操作行为
     *
     * @return IWarningPostProcessor
     */
    protected List<IWarningPostProcessor> dataProcessors() {
        if (sortedWarningPostProcessors == null && warningPostProcessors != null) {
            sortedWarningPostProcessors = warningPostProcessors.stream()
                    .sorted(Comparator.comparing(IWarningPostProcessor::order))
                    .collect(Collectors.toList());
        }
        return sortedWarningPostProcessors;
    }


    /**
     * 获取预警配置
     *
     * @param context WarningMessageContext
     * @return WarningSourceTypeEntity
     */
    protected WarningSourceTypeEntity getWanringConfig(WarningMessageContext context) {
        WarningDTO dto = context.getWarningDTO();
        //拼接预警内容，这里比较复杂主要使用了通用模版
        return warningSourceTypeMapper.selectByWarningType(dto.getEnName());
    }
}
