package com.trs.police.control.domain.vo.fkzt;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.control.domain.vo.warning.SpecialWarningListVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 反恐人员vo
 */
@Data
@NoArgsConstructor
public class PersonVO {

    /**
     * 人员id
     */
    private Long id;

    /**
     * 照片
     */
    private String photo;

    /**
     * 人员名称
     */
    private String name;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 人员级别
     */
    private Long personLevel;

    /**
     * 人员级别名称
     */
    private String personLevelName;

    /**
     * 预警时间
     */
    private String warningTime;

    /**
     * 最后的预警id
     */
    private Long lastWarningId;


    /**
     * 预警数量
     */
    private Integer warningCount;

    /**
     * 标签编码
     */
    private List<Long> labelId;

    /**
     * 人员标签
     */
    private List<PersonLabel> perLabelList;

    /**
     * 工作情况
     * 0: 需要研判
     * 1: 已研判
     * 2: 无需研判
     */
    private Integer gzqk;

    /**
     * 建档案情况
     */
    private Integer jdqk;

    /**
     * 电话列表
     */
    private List<String> telList;

    /**
     * 户籍地代码
     */
    private String placeCode;

    /**
     * 户籍地名称
     */
    private String hjdName;

    /**
     * 摸排地址
     */
    private String mpdzName;

    /**
     * 十类人员类型
     */
    private Integer tenPersonType;

    /**
     * 流入时间
     */
    private String inflowTime;

    /**
     * 责任派出所id列表
     */
    private List<Long> controlStationIdList;

    /**
     * 责任派出所
     */
    private List<DeptDto> controlStationList;

    /**
     * 性别
     */
    private Integer xb;

    /**
     * 相关警情数量
     */
    private Integer jqCount;

    /**
     * 最后的预警信息
     */
    private SpecialWarningListVO lastWarning;
}
