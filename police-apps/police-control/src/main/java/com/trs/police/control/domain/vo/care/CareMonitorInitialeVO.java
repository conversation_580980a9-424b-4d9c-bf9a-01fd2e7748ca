package com.trs.police.control.domain.vo.care;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.control.domain.entity.monitor.CareMonitorEntity;
import com.trs.police.control.domain.vo.warning.IdentifierVO;
import com.trs.police.control.handler.care.ICareMonitorHandler;
import io.vavr.control.Try;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.common.utils.TimeUtils.YYYYMMDD_HHMMSS2;

/**
 * 发起监控vo
 */
@Data
public class CareMonitorInitialeVO {

    //证件类型
    private IdentifierTypeEnum certificateType;

    //证件号码
    private String certificateValue;

    //名称
    private String certificateName;

    private Long personId;

    //关联id
    private Long relatedDataId;

    //关联类型，线索：xiansuo
    private String relatedType;

    //关联，线索：xiansuo
    private String relatedDataTitle;

    //人像图片url
    private String imageUrl;

    //所用预警配置，这里的预警配置和常控的预警配置是相通的
    private Long monitorConfigId;

    //调用关注监控的服务配套的对应Handler的className。如果给的字符串名非CareMonitorHandler，关注布控将失败
    private String moduleCareMonitorHandlerClassName;

    private String createUserId;

    private String createDeptId;

    /**
     * 布控开始时间
     */
    private String subscribeStartTime;

    /**
     * 布控结束时间
     */
    private String subscribeEndTime;

    /**
     * 布控的平台，不传表示全部
     * 1 表示本地感知引擎
     * 2 表示云控平台
     */
    private String subscribePlatform;

    private List<IdentifierVO> identifiers;

    /**
     * 获取关注监控handler
     *
     * @param handlerClassName handlerClassName
     * @return handler
     * @throws ClassNotFoundException 异常
     */
    public static ICareMonitorHandler getCareMonitorHandlerInstance(String handlerClassName) throws ClassNotFoundException {
        return Try.of(() -> {
            String className = PreConditionCheck.checkNotNull(handlerClassName);
            Class<?> careMonitorHandlerClazz = Class.forName(className);
            return (ICareMonitorHandler) careMonitorHandlerClazz.getDeclaredConstructor().newInstance();
        }).getOrElseThrow(() -> new ClassNotFoundException(String.format("[%s]创建实例失败", handlerClassName)));
    }

    /**
     * 获取关注监控handler
     *
     * @return handler
     * @throws ClassNotFoundException ClassNotFoundException
     */
    public ICareMonitorHandler getCareMonitorHandlerInstance() throws ClassNotFoundException {
        return getCareMonitorHandlerInstance(this.moduleCareMonitorHandlerClassName);
    }

    /**
     * 对象转换
     *
     * @return 转换后对象
     */
    public CareMonitorEntity toCareMonitorEntity() {
        CareMonitorEntity entity = new CareMonitorEntity();
        entity.setCertificateType(certificateType.getCode());
        entity.setCertificateValue(certificateValue);
        entity.setCertificateName(certificateName);
        entity.setWarningConfigIds(List.of(monitorConfigId));
        entity.setDeleted(false);
        entity.setHandlerClassName(List.of(moduleCareMonitorHandlerClassName));
        if (StringUtils.isEmpty(createUserId) || StringUtils.isEmpty(createDeptId)) {
            CurrentUser currentUser = AuthHelper.getCurrentUser();
            entity.setCreateUserId(currentUser.getId());
            entity.setCreateDeptId(currentUser.getDeptId());
        } else {
            entity.setCreateUserId(Long.valueOf(createUserId));
            entity.setCreateDeptId(Long.valueOf(createDeptId));
        }
        entity.setCreateTime(LocalDateTime.now());
        entity.setImageUrl(imageUrl);
        entity.setSubscribeStartTime(TimeUtils.stringToString(subscribeStartTime,YYYYMMDD_HHMMSS2));
        entity.setSubscribeEndTime(TimeUtils.stringToString(subscribeEndTime,YYYYMMDD_HHMMSS2));
        entity.setSubscribePlatform(subscribePlatform);
        entity.setPersonId(personId);
        entity.setRelatedDataId(relatedDataId);
        entity.setRelatedDataName(relatedDataTitle);
        entity.setRelatedType(relatedType);
        if (!CollectionUtils.isEmpty(identifiers)) {
            List<String> carNumberList = identifiers.stream()
                    .filter(e -> IdentifierTypeEnum.CAR_NUMBER.getCode().equals(e.getIdentifierType()))
                    .map(IdentifierVO::getIdentifier)
                    .collect(Collectors.toList());
            entity.setCarNumber(carNumberList);
            List<String> imsiList = identifiers.stream()
                    .filter(e -> IdentifierTypeEnum.IMSI.getCode().equals(e.getIdentifierType()))
                    .map(IdentifierVO::getIdentifier)
                    .collect(Collectors.toList());
            entity.setCarNumber(imsiList);
            List<String> imeiList = identifiers.stream()
                    .filter(e -> IdentifierTypeEnum.IMEI.getCode().equals(e.getIdentifierType()))
                    .map(IdentifierVO::getIdentifier)
                    .collect(Collectors.toList());
            entity.setCarNumber(imeiList);
            List<String> macList = identifiers.stream()
                    .filter(e -> IdentifierTypeEnum.MAC.getCode().equals(e.getIdentifierType()))
                    .map(IdentifierVO::getIdentifier)
                    .collect(Collectors.toList());
            entity.setCarNumber(macList);
        }
        return entity;
    }

}
