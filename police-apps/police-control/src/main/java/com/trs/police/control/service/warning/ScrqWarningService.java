package com.trs.police.control.service.warning;

import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import org.springframework.stereotype.Component;

/**
 * 首次入区
 *
 * <AUTHOR>
 */
@Component
public class ScrqWarningService implements WarningService {

    @Override
    public MonitorLevelEnum getWarningLevel(WarningFkrxyjEntity entity) {
        // 首次入区直接返回蓝色
        return MonitorLevelEnum.BLUE;
    }
}
