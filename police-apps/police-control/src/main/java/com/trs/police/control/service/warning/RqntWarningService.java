package com.trs.police.control.service.warning;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.control.domain.entity.fkrxyj.RyypEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.mapper.FxPersonYpMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.trs.common.utils.TimeUtils.YYYYMMDD_HHMMSS;

/**
 * 入区n天
 *
 * <AUTHOR>
 */
@Component
public class RqntWarningService implements WarningService {

    @Autowired
    private FxPersonYpMapper fxPersonYpMapper;

    @Override
    public MonitorLevelEnum getWarningLevel(WarningFkrxyjEntity entity) {
        Integer time = BeanFactoryHolder.getEnv().getProperty("control.fkry.yp.valid.time", Integer.class, 30);
        String s = TimeUtils.dateBefOrAft(-time, YYYYMMDD_HHMMSS);
        Long count = fxPersonYpMapper.selectCount(Wrappers.lambdaQuery(RyypEntity.class)
                .ge(RyypEntity::getCreateTime, s)
                .eq(RyypEntity::getIdCard, entity.getIdCard())
        );
        if (count > 0) {
            return MonitorLevelEnum.BLUE;
        }
        return MonitorLevelEnum.YELLOW;
    }
}
