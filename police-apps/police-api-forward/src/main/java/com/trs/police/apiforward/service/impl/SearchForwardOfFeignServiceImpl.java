package com.trs.police.apiforward.service.impl;

import com.trs.police.apiforward.common.constant.SearchConstants;
import com.trs.police.apiforward.domain.dto.PersonArchivesDto;
import com.trs.police.apiforward.service.SearchForwardService;
import com.trs.police.common.openfeign.starter.service.SearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2024年02月04日 11:28
 */
@Service("SearchForwardOfFeignServiceImpl")
@RequiredArgsConstructor
@Slf4j
public class SearchForwardOfFeignServiceImpl implements SearchForwardService {

    @Resource
    private SearchService searchService;

    @Override
    public String personDetail(HttpServletRequest request, PersonArchivesDto dto) {
        String uuidStr = searchService.uuid();
        JSONObject uuidJson = JSONObject.fromObject(uuidStr);

        String uuid = uuidJson.getJSONArray("data").getString(0);

        String personDetail = searchService.detail(dto.getRecordId(), uuid, SearchConstants.Search_archives_Type, SearchConstants.Search_fieldName);
        log.info(personDetail);
        return personDetail;
    }
}
