package com.trs.police.ulportal.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 刑事案件
 * @date 2023/12/7 19:32
 */
@Data
@TableName(value = "ods_hik_jwzh_xsaj_aj_gx")
@Slf4j
public class Xsaj implements Serializable {

    private static final long serialVersionUID = 4358668288902865L;

    @TableField("asjfsdd_sfzlzz_pdbz")
    private String asjfsddSfzlzzPdbz;

    @TableField("gxsjdm")
    private String gxsjdm;

    @TableField("gxbmdm")
    private String gxbmdm;

    @TableField("sfcxaj_pdbz")
    private String sfcxajPdbz;

    private String zczjdwGajgmc;

    private String xtZxyy;

    private String zczjZxsj01;

    private String xtLrrbmid;

    private String asjfssjZasjDmbcms;

    private String asjswryRs;

    private String zczjZcxwyjms;

    private String ajywztmxdm;

    private String xtZhxgrxm;

    private String asjfsddSacsSacslbdm;

    private String asjfsddSfjzwnPdbz;

    private String ajxzdm;

    private String fxasjddDzmc;

    private String pafsdm;

    private String xtZxbz;

    private String gxfxjdm;

    /**
     *  案件状态
     *  0100：接受案事件、0200:立案调查、0300：不予立案、0301：移送其他执法机关、0302：移送管辖、0400：立案、0500：撤销案件
     *  0600：侦查终结、0700：移送审查起诉、0800：补充侦查
     */
    private String ajywztdm;

    private String fxasjsj;

    private String zj;

    /**
     *  案件唯一编号
     */
    @TableField("asjbh")
    private String asjbh;

    private String pasj;

    private String ladwGajgjgdm;

    private String slsj;

    private String larq;

    private String asjfsddDqwd;

    private String asjfsddKjbwKjbwlbdm;

    private String ajmc;

    private String asjfssjRsddm;

    private String ajxzmc;

    private String asjfsddKjbwDmbcms;

    private String asjfsddSacsDmbcms;

    private String asjsscwJyqk;

    private String sfkyscPdbs;

    private String ysqsGajgjgdm;

    private String zatzJyqk;

    private String depActionFlag;

    private String asjfsddDzid;

    private String xtZhxgrbmid;

    private String ysqsGajgmc;

    private String asjfsddSfbz;

    private String gajgdwfldm;

    private String ajxlbdm;

    private String xtZhxgip;

    private String xtLrsj;

    private String jyaq;

    private String zbrLxdh;

    private String pacjPdbz;

    private String sfza;

    private String sldwGajgjgdm;

    private String cxajyydm;

    private String asjfssjZasjZasjlbdm;

    private String asjssryRs;

    private String rowkey;

    private String cxajrq;

    private String ajlbdm;

    private String depFirstenterTime;

    private String asjfsddJzwcs;

    private String fxasjddDzid;

    private String bjsj;

    /**
     *  暂时定的统计时间
     *  事件发生时间？
     */
    @TableField("asjfssj_asjfsjssj")
    private String asjfssjAsjfsjssj;

    private String ladwGajgmc;

    private String jjbh;

    private String badwGajgmc;

    private String sldwGajgmc;

    private String asjfsddDylbdm;

    private String xtLrrxm;

    private String asjfsddXzqhdm;

    private String badwGajgjgdm;

    private String oldSystemid;

    private String zbrXm;

    private String asjfsddAsjfslc;

    private String xtZhxgrbm;

    private String ajxlbDmbcms;

    private String fxasjddXzqhdm;

    private String asjlydm;

    private String zaryRs;

    private String sjcwjzrmby;

    private String zaxsdm;

    private String asjfssjAsjfskssj;

    private String xtZxyybz;

    private String xtLrrbm;

    private String zczjdwGajgjgdm;

    private String gxpcsdm;

    private String xtLrip;

    private String asjfsddDqjd;

    private String cxajdwGajgjgdm;

    private String depActionTime;

    private String cxajdwGajgmc;

    private String ssjzrmby;

    private String xtLrrid;

    private String zbrGmsfhm;

    private String xckybh;

    private String xtZhxgrid;

    private String xtZhxgsj;

    private String asjfsddDzmc;

    private String lsjrkbz;

    private String ysqsrq;


}