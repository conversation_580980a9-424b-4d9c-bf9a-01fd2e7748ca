package com.trs.police.ulportal.domain.vo.ys;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 人员线索总览
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PersonClueOverviewVO implements Serializable {

    /**
     * 总数
     */
    private Long totalCount;

    /**
     * 今日增加数
     */
    private Long todayAddCount;

    /**
     * 使用总数
     */
    private Long useCount;

    /**
     * 使用数今日新增
     */
    private Long todayUseAddCount;
}
