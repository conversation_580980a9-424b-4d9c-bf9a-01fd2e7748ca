package com.trs.police.ulportal.service.impl.policesituationtopic;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.grt.condify.parser.MybatisSearchParser;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.ulportal.common.config.JqHasChildConfig;
import com.trs.police.ulportal.common.config.JqfxDeptConfig;
import com.trs.police.ulportal.common.config.WebSecurityConfig;
import com.trs.police.ulportal.common.constants.JqConstants;
import com.trs.police.ulportal.common.util.HierarchicalUtil;
import com.trs.police.ulportal.common.util.ResultHelper;
import com.trs.police.ulportal.common.util.TimeUtil;
import com.trs.police.ulportal.converter.PoliceSituationCategoryConverter;
import com.trs.police.ulportal.domain.dto.policesituationtopic.*;
import com.trs.police.ulportal.domain.entity.policesituationtopic.CommunityEntity;
import com.trs.police.ulportal.domain.entity.policesituationtopic.PoliceRegionInfoEntity;
import com.trs.police.ulportal.domain.entity.policesituationtopic.PoliceSituationCategoryEntity;
import com.trs.police.ulportal.domain.entity.policesituationtopic.PoliceSituationNotRegisteredEntity;
import com.trs.police.ulportal.domain.vo.policesituationtopic.*;
import com.trs.police.ulportal.mapper.ExtDeptMapper;
import com.trs.police.ulportal.mapper.policesituationtopic.*;
import com.trs.police.ulportal.mapper.ys.YsUserMapper;
import com.trs.police.ulportal.service.policesituationtopic.PoliceSituationCategoryService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-05-06 19:03
 */
@Service
@RequiredArgsConstructor
public class PoliceSituationCategoryServiceImpl extends ServiceImpl<PoliceSituationCategoryMapper, PoliceSituationCategoryEntity> implements PoliceSituationCategoryService {

    private final PoliceSituationCategoryMapper policeSituationCategoryMapper;

    private final PoliceSituationCategoryConverter policeSituationCategoryConverter;

    private final PoliceSituationMapper policeSituationMapper;

    private final CommunityMapper communityMapper;

    private final PoliceRepeatAlarmMapper policeRepeatAlarmMapper;

    private final PoliceSituationNotRegisteredMapper policeSituationNotRegisteredMapper;

    private final PoliceRegionInfoMapper policeRegionInfoMapper;

    private final JqfxDeptConfig jqfxDeptConfig;

    private final ExtDeptMapper extDeptMapper;

    private final JqHasChildConfig jqHasChildConfig;

    private final WebSecurityConfig webSecurityConfig;

    private final YsUserMapper ysUserMapper;

    @Override
    public RestfulResultsV2<PoliceSituationCategoryVO> queryForPage(IPage<PoliceSituationCategoryEntity> page, QueryWrapper<PoliceSituationCategoryEntity> queryWrapper) {
        IPage<PoliceSituationCategoryEntity> iPage = policeSituationCategoryMapper.selectPage(page, queryWrapper);
        return ResultHelper.getIPageConverter().convert(policeSituationCategoryConverter.toPageVo(iPage));
    }


    @Override
    public RestfulResultsV2<TotalPoliceCountVO> getTotalPoliceCount(TotalPoliceCountDTO dto) {
        int currCount = policeSituationCategoryMapper.countJqzsByCodeAndTime(dto);
        TotalPoliceCountVO vo = new TotalPoliceCountVO();
        vo.setCurrCount(currCount);
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();

        Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime, endTime);
        dto.setStartTime(lastMonthTimeRange._1);
        dto.setEndTime(lastMonthTimeRange._2);
        int monthCount = policeSituationCategoryMapper.countJqzsByCodeAndTime(dto);
        vo.setLastMonthCount(monthCount);

        Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime, endTime);
        dto.setStartTime(lastYearTimeRange._1);
        dto.setEndTime(lastYearTimeRange._2);
        int yearCount = policeSituationCategoryMapper.countJqzsByCodeAndTime(dto);
        vo.setLastYearCount(yearCount);

        return RestfulResultsV2.ok(vo);
    }

    @Override
    public RestfulResultsV2<CommonResultVo> getTotalPoliceTrend(TotalPoliceCountDTO dto) {
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        List<PoliceTrendCountVO> currList = policeSituationCategoryMapper.selectListByCodeAndTime(dto);
        CommonResultVo vo = new CommonResultVo();
        vo.setCurData(getFinalCountVO(currList, startTime, endTime));

        Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime, endTime);
        dto.setStartTime(lastMonthTimeRange._1);
        dto.setEndTime(lastMonthTimeRange._2);

        List<PoliceTrendCountVO> lastMonthList = policeSituationCategoryMapper.selectListByCodeAndTime(dto);
        vo.setLastMonthData(getFinalCountVO(lastMonthList, lastMonthTimeRange._1, lastMonthTimeRange._2));

        Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime, endTime);
        dto.setStartTime(lastYearTimeRange._1);
        dto.setEndTime(lastYearTimeRange._2);
        List<PoliceTrendCountVO> lastYearList = policeSituationCategoryMapper.selectListByCodeAndTime(dto);
        vo.setLastYearData(getFinalCountVO(lastYearList, lastYearTimeRange._1, lastYearTimeRange._2));
        return RestfulResultsV2.ok(vo);
    }

    /**
     *  补全所有的日期统计
     *
     * @param currList 检索的集合
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 最终补全时间的集合
     */
    private List<PoliceTrendCountVO> getFinalCountVO(List<PoliceTrendCountVO> currList, String startTime, String endTime){
        Map<String, PoliceTrendCountVO> map = CollectionUtils.isEmpty(currList) ? new HashMap<>() :
                currList.stream().collect(Collectors.toMap(e -> e.getRq(), e -> e));
        List<String> dateList = TimeUtils.getDateList(startTime, endTime, TimeUtils.YYYYMMDD);
        List<PoliceTrendCountVO> finalList = new ArrayList<>();
        for (String date : dateList) {
            PoliceTrendCountVO vo = map.get(date);
            if (vo == null){
                vo = new PoliceTrendCountVO();
                vo.setRq(date);
                vo.setCount(0);
            }
            finalList.add(vo);
        }
        return finalList;
    }

    @Override
    public RestfulResultsV2<EffectivePoliceCountVO> getEffectivePoliceCount(JqFxBaseDTO dto) {
        EffectivePoliceCountVO vo = policeSituationCategoryMapper.getEffectivePoliceCount(dto);
        return RestfulResultsV2.ok(vo);
    }


    @Override
    public RestfulResultsV2<CommonResultVo> getDeptPoliceCount(DeptPoliceCountDTO dto) {
        CommonResultVo vo = new CommonResultVo();
        vo.setDeptList(jqfxDeptConfig.getDeptList(dto.getRegionCode()));
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        List<String> codes = jqfxDeptConfig.getCodeList();
        List<SimpleDeptPoliceCountVO> currList = policeSituationCategoryMapper.getDeptPoliceCount(dto, codes);
        vo.setCurData(currList);

        Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime, endTime);
        dto.setStartTime(lastMonthTimeRange._1);
        dto.setEndTime(lastMonthTimeRange._2);
        List<SimpleDeptPoliceCountVO> lastMonthList = policeSituationCategoryMapper.getDeptPoliceCount(dto, codes);
        vo.setLastMonthData(lastMonthList);

        Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime, endTime);
        dto.setStartTime(lastYearTimeRange._1);
        dto.setEndTime(lastYearTimeRange._2);
        List<SimpleDeptPoliceCountVO> lastYearList = policeSituationCategoryMapper.getDeptPoliceCount(dto, codes);
        vo.setLastYearData(lastYearList);
        return RestfulResultsV2.ok(vo);
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getSocialSituationCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.SHWD));
    }

    /**
     * 获取警情类型VO
     *
     * @param dto    dto
     * @param column 统计的字段
     * @return 统计结果
     */
    private PoliceTypeVO getPoliceTypeVO(PoliceTypeDTO dto, String column) {
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        PoliceTypeVO vo = new PoliceTypeVO();
        int currCount = policeSituationCategoryMapper.getPoliceTypeCount(dto, column);
        vo.setCurrCount(currCount);
        int cityCount = policeSituationCategoryMapper.getCityPoliceTypeCount(dto, column);
        vo.setAverCityCount(cityCount / 14);

        Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime, endTime);
        dto.setStartTime(lastMonthTimeRange._1);
        dto.setEndTime(lastMonthTimeRange._2);
        int lastMonthCount = policeSituationCategoryMapper.getPoliceTypeCount(dto, column);
        vo.setLastMonthCount(lastMonthCount);
        Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime, endTime);
        dto.setStartTime(lastYearTimeRange._1);
        dto.setEndTime(lastYearTimeRange._2);
        int lastYearCount = policeSituationCategoryMapper.getPoliceTypeCount(dto, column);
        vo.setLastYearCount(lastYearCount);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        if(!CollectionUtils.isEmpty(jqHasChildConfig.getJqNotHasChildList()) && jqHasChildConfig.getJqNotHasChildList().contains(column)){
            vo.setHasChild(0);
        }else{
            vo.setHasChild(1);
        }
        return vo;
    }

    /**
     * 获取各派出所警情数
     *
     * @param dto    dto
     * @param column 统计的字段
     * @return 警情数
     */
    private List<PoliceCountVO> getPoliceCountVO(PoliceTypeDTO dto, String column) {
        List<JqfxDeptDTO> jqfxDeptList = new ArrayList<>(jqfxDeptConfig.getJqfxDeptList());
        jqfxDeptList.remove(0);
        List<String> codes = jqfxDeptList.stream().map(JqfxDeptDTO::getCode).collect(Collectors.toList());
        List<PoliceCountVO> voList = policeSituationCategoryMapper.getPoliceCountVO(dto, column, codes);
        return voList;
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getDisputeSituationCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.JF));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getAggrSituationCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.JJ));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getPersonSituationCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.GRJD));
    }

    @Override
    public RestfulResultsV2<PoliceCountVO> getSocialPoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceCountVO(dto, JqConstants.SHWD));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getSocialSecurityCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.SHZA));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getCriminalCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.XS));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getAdministrationCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.XZ));
    }

    @Override
    public RestfulResultsV2<PoliceCountVO> getSocialSecurityPoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceCountVO(dto, JqConstants.SHZA));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getPublicSafetyCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.GGAQ));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getTransportCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.JT));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getDisasterCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.ZHSG));
    }

    @Override
    public RestfulResultsV2<PoliceCountVO> getPublicSafetyPoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceCountVO(dto, JqConstants.GGAQ));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getPeopleHelpCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.QZQZ));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getLostPoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.ZS));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getResuePoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.JH));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getNoisePoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.ZYRM));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getConsultPoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.ZX));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getElectricPoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.SDQ));
    }

    @Override
    public RestfulResultsV2<PoliceTypeVO> getOtherPoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceTypeVO(dto, JqConstants.QTQZ));
    }

    @Override
    public RestfulResultsV2<PoliceCountVO> getPeopleHelpPoliceCount(PoliceTypeDTO dto) {
        return RestfulResultsV2.ok(getPoliceCountVO(dto, JqConstants.QZQZ));
    }

    @Override
    public RestfulResultsV2<CommonResultVo> getPoliceStationCount(PoliceSituationListDTO dto) {
        //处理特殊参数
        if (dto.getLbCodes() != null) {
            List<String> yxLbCodes = dto.getLbCodes().stream()
                    .map(HierarchicalUtil::getEffectiveCode)
                    .collect(Collectors.toList());
            dto.setLbCodes(yxLbCodes);
        }
        String startTime = dto.getBjsjStart();
        String endTime = dto.getBjsjEnd();
        CommonResultVo vo = new CommonResultVo();
        if(dto.getSearchType() == null || dto.getSearchType() == 0){
            //本期
            List<Integer> jqzs = policeSituationMapper.getJjPsNum(dto);
            vo.setCurData(jqzs);
            //上期
            Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime,endTime);
            dto.setBjsjStart(lastMonthTimeRange._1);
            dto.setBjsjEnd(lastMonthTimeRange._2);
            List<Integer> lastMonthJqzs = policeSituationMapper.getJjPsNum(dto);
            vo.setLastMonthData(lastMonthJqzs);
            //去年同期
            Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime,endTime);
            dto.setBjsjStart(lastYearTimeRange._1);
            dto.setBjsjEnd(lastYearTimeRange._2);
            List<Integer> lastYearJqzs = policeSituationMapper.getJjPsNum(dto);
            vo.setLastYearData(lastYearJqzs);
            return RestfulResultsV2.ok(vo);
        }else {
            //本期
            List<Integer> jqzs = policeSituationMapper.getCjPsNum(dto);
            vo.setCurData(jqzs);
            Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime,endTime);
            dto.setBjsjStart(lastMonthTimeRange._1);
            dto.setBjsjEnd(lastMonthTimeRange._2);
            List<Integer> lastMonthJqzs = policeSituationMapper.getCjPsNum(dto);
            vo.setLastMonthData(lastMonthJqzs);
            Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime,endTime);
            dto.setBjsjStart(lastYearTimeRange._1);
            dto.setBjsjEnd(lastYearTimeRange._2);
            List<Integer> lastYearJqzs = policeSituationMapper.getCjPsNum(dto);
            vo.setLastYearData(lastYearJqzs);
            return RestfulResultsV2.ok(vo);
        }
    }

    @Override
    public RestfulResultsV2<CommonResultVo> getPsCommunityCount(PoliceSituationListDTO dto) {
        //处理特殊参数
        if (dto.getLbCodes() != null) {
            List<String> yxLbCodes = dto.getLbCodes().stream()
                    .map(HierarchicalUtil::getEffectiveCode)
                    .collect(Collectors.toList());
            dto.setLbCodes(yxLbCodes);
        }
        QueryWrapper<CommunityEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("gxdmdm",dto.getRegionCode());
        queryWrapper.select("sqdm","sqmc");
        //获取派出所下的社区
        List<CommunityEntity> communityList = communityMapper.selectList(queryWrapper);
        List<String> communityNameList = new ArrayList<>();
        CommonResultVo resultVo = new CommonResultVo();
        List<PoliceCommunityRangeVO> voCurList = new ArrayList<>();
        List<PoliceCommunityRangeVO> voLastMonthList = new ArrayList<>();
        List<PoliceCommunityRangeVO> voLastYearList = new ArrayList<>();
        String startTime = dto.getBjsjStart();
        String endTime = dto.getBjsjEnd();
        for(CommunityEntity community : communityList){
            //本期
            dto.setBjsjStart(startTime);
            dto.setBjsjEnd(endTime);
            dto.setCommunity(community.getSqmc());
            PoliceCommunityRangeVO curVo = new PoliceCommunityRangeVO();
            curVo.setSqbh(community.getSqdm());
            curVo.setSqmc(community.getSqmc());
            curVo.setJqzs(policeSituationMapper.getCjPsNum(dto).get(0));
            voCurList.add(curVo);
            //上期
            Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime,endTime);
            dto.setBjsjStart(lastMonthTimeRange._1);
            dto.setBjsjEnd(lastMonthTimeRange._2);
            PoliceCommunityRangeVO lastMonthVo = new PoliceCommunityRangeVO();
            lastMonthVo.setSqbh(community.getSqdm());
            lastMonthVo.setSqmc(community.getSqmc());
            lastMonthVo.setJqzs(policeSituationMapper.getCjPsNum(dto).get(0));
            voLastMonthList.add(lastMonthVo);
            //去年同期
            Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime,endTime);
            dto.setBjsjStart(lastYearTimeRange._1);
            dto.setBjsjEnd(lastYearTimeRange._2);
            PoliceCommunityRangeVO lastYearVo = new PoliceCommunityRangeVO();
            lastYearVo.setSqbh(community.getSqdm());
            lastYearVo.setSqmc(community.getSqmc());
            lastYearVo.setJqzs(policeSituationMapper.getCjPsNum(dto).get(0));
            voLastYearList.add(lastYearVo);
            //社区名称集合
            communityNameList.add(community.getSqmc());
        }
        resultVo.setCurData(voCurList);
        resultVo.setLastMonthData(voLastMonthList);
        resultVo.setLastYearData(voLastYearList);
        resultVo.setDeptList(communityNameList);
        return RestfulResultsV2.ok(resultVo);
    }

    @Override
    public RestfulResultsV2<PoliceRepeatAlarmVO> getRepeatAlarmPeople(PoliceSituationListDTO dto) {
        //处理特殊参数
        if (dto.getLbCodes() != null) {
            List<String> yxLbCodes = dto.getLbCodes().stream()
                    .map(HierarchicalUtil::getEffectiveCode)
                    .collect(Collectors.toList());
            dto.setLbCodes(yxLbCodes);
        }
        //获取该条件下所有的报警人员电话
        List<String> phoneList = policeSituationMapper.queryJqAlarmPeopleList(dto);
        PoliceRepeatAlarmDTO repeatAlarmDto = new PoliceRepeatAlarmDTO();
        repeatAlarmDto.setStartTime(dto.getStartTime());
        repeatAlarmDto.setEndTime(dto.getEndTime());
        if(!CollectionUtils.isEmpty(phoneList)){
            repeatAlarmDto.setDhhmList(phoneList);
        }
        IPage<PoliceRepeatAlarmVO> page = Page.of(dto.getPageNum(), dto.getPageSize());
        page = policeRepeatAlarmMapper.getRepeatAlarmPeopleList(repeatAlarmDto, page);
        return ResultHelper.getIPageConverter().convert(page);
    }

    @Override
    public RestfulResultsV2<JqDetailVo> getPsNotRegistered(PoliceSituationListDTO dto) {
        //处理特殊参数
        if (dto.getLbCodes() != null) {
            List<String> yxLbCodes = dto.getLbCodes().stream()
                    .map(HierarchicalUtil::getEffectiveCode)
                    .collect(Collectors.toList());
            dto.setLbCodes(yxLbCodes);
        }
        //获取该范围内有哪些应立未立的警情接警单编号
        QueryWrapper<PoliceSituationNotRegisteredEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("jjdbh");
        queryWrapper.like("cjdw_gajgjgdm",dto.getRegionCode());
        queryWrapper.between("bjsj",dto.getBjsjStart(),dto.getBjsjEnd());
        if(StringUtils.isNotEmpty(dto.getCjr())){
            queryWrapper.like("cjr_xm",dto.getCjr());
        }
        if(StringUtils.isNotEmpty(dto.getCjrdw())){
            queryWrapper.like("cjdw",dto.getCjrdw());
        }
        List<Map<String, Object>> mapList  = policeSituationNotRegisteredMapper.selectMaps(queryWrapper);
        List<String> jjdbhList = mapList.stream()
                .map(map -> (String) map.get("jjdbh"))
                .collect(Collectors.toList());
        //根据这些接警单编号筛选查询
        if(CollectionUtils.isEmpty(jjdbhList)){
            Page<JqDetailVo> jjPsList = new Page<>();
            return ResultHelper.getIPageConverter().convert(jjPsList);
        }
        dto.setJjdbhList(jjdbhList);
        IPage<JqDetailVo> page = MybatisSearchParser.buildPage(dto);
        Page<JqDetailVo> jjPsList = policeSituationMapper.queryJjPsList(page, dto);
        for(JqDetailVo vo :jjPsList.getRecords()){
            QueryWrapper<PoliceSituationNotRegisteredEntity> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.select("cjr_xm","cjdw");
            queryWrapper1.eq("jjdbh",vo.getJjdbh());
            PoliceSituationNotRegisteredEntity entity = policeSituationNotRegisteredMapper.selectOne(queryWrapper1);
            if(entity != null){
                vo.setCjr(entity.getCjrXm());
                vo.setCjdwmc(entity.getCjdw());
            }
        }
        return ResultHelper.getIPageConverter().convert(jjPsList);
    }

    @Override
    public RestfulResultsV2<PoliceStationInfoVO> getPoliceStationInfo(PoliceSituationListDTO dto) {
        //查询派出所地址信息等
        QueryWrapper<PoliceRegionInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bmbh", dto.getRegionCode());
        PoliceRegionInfoEntity policeRegionInfo = policeRegionInfoMapper.selectOne(queryWrapper);
        List<String> codeList = jqfxDeptConfig.getCodeList();
        Optional<String> longestSubstring = codeList.stream()
                .filter(str -> dto.getRegionCode().contains(str) && !str.equals(dto.getRegionCode())) // 过滤包含在 searchString 中的字符串
                .max(Comparator.comparingInt(String::length));
        PoliceStationInfoVO vo = new PoliceStationInfoVO();
        if(policeRegionInfo != null){
            vo.setPcsmc(policeRegionInfo.getBmmc());
            vo.setDz(policeRegionInfo.getDz());
            vo.setXqmj(policeRegionInfo.getZdmj());
            vo.setXqrks(policeRegionInfo.getRksl());
            longestSubstring.ifPresent(s -> vo.setGxdw(jqfxDeptConfig.getDeptName(s)));
        }
        //社区数量单独查
        QueryWrapper<CommunityEntity> queryWrapperCommunity = new QueryWrapper<>();
        queryWrapperCommunity.eq("gxdmdm",dto.getRegionCode());
        vo.setSqsl(Math.toIntExact(communityMapper.selectCount(queryWrapperCommunity)));
        //民警数量单独查
        List<JqfxDeptDTO> jqfxDeptDtos = new ArrayList<>();
        JqfxDeptDTO jqfxDeptDTO = new JqfxDeptDTO();
        jqfxDeptDTO.setCode(dto.getRegionCode());
        jqfxDeptDtos.add(jqfxDeptDTO);
        List<PerCjAnalysisResultVO> perCjAnalysisResultVO;
        //判断是查ys的用户表还是门户的用户表
        if("false".equalsIgnoreCase(webSecurityConfig.securitySkip)){
            perCjAnalysisResultVO = ysUserMapper.getDeptMjNumber(jqfxDeptDtos);
        }else{
            perCjAnalysisResultVO = extDeptMapper.getDeptMjNumber(jqfxDeptDtos);
        }
        if(!CollectionUtils.isEmpty(perCjAnalysisResultVO)) {
            vo.setDwrs(perCjAnalysisResultVO.get(0).getMjNumber());
        }else{
            vo.setDwrs(0);
        }
        return RestfulResultsV2.ok(vo);
    }
}
