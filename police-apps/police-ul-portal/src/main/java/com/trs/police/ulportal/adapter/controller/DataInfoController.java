package com.trs.police.ulportal.adapter.controller;

import com.grt.condify.exception.CondifyException;
import com.trs.police.ulportal.domain.dto.DataInfoListDto;
import com.trs.police.ulportal.domain.vo.DataCategoryVO;
import com.trs.police.ulportal.domain.vo.DataInfoVO;
import com.trs.police.ulportal.domain.vo.DataTypeVO;
import com.trs.police.ulportal.service.DataInfoService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据情况接口
 */
@RestController
@Api(value = "数据情况", tags = "数据情况")
@RequiredArgsConstructor
@RequestMapping("/dataInfo")
@Slf4j
public class DataInfoController {

    private final DataInfoService dataInfoService;

    /**
     * 获取数据类型
     *
     * @return 返回值
     */
    @GetMapping("/getDataType")
    @ApiOperation(value = "获取数据类型", notes = "获取数据类型")
    public RestfulResultsV2<DataTypeVO> getDataType() {
        return dataInfoService.getDataType();
    }

    /**
     * 获取数据类型
     *
     * @param parentId parentId
     * @return 返回值
     */
    @GetMapping("/getDataCategory")
    @ApiOperation(value = "获取数据分类", notes = "获取数据分类")
    public RestfulResultsV2<DataCategoryVO> getDataCategory(Long parentId) {
        return dataInfoService.getDataCategory(parentId);
    }

    /**
     * 获取数据列表
     *
     * @param dto dto
     * @return 返回值
     */
    @GetMapping("/querySearchList")
    @ApiOperation(value = "获取数据列表", notes = "获取数据列表")
    public RestfulResultsV2<DataInfoVO> querySearchList(DataInfoListDto dto) throws CondifyException {
        return dataInfoService.querySearchList(dto);
    }
}
