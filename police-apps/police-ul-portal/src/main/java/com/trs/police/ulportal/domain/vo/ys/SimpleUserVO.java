package com.trs.police.ulportal.domain.vo.ys;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户信息
 *
 * <AUTHOR> yanghy
 * @date : 2022/7/29 15:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleUserVO implements Serializable {

    private static final long serialVersionUID = -5101258436340843366L;
    /**
     * 用户Id
     */
    @NotNull(message = "用户id不能为空！")
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 部门Id
     */
    @NotNull(message = "部门id不能为空！")
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 部门简称
     */
    private String deptShortName;

    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 用户手机号
     */
    private String tel;
    /**
     * 职务
     */
    private String duty;

    /**
     * 拼接字符串
     *
     * @return 结果
     */
    public String toUserString() {
        return userName + "（" + deptShortName + "）";
    }


}
