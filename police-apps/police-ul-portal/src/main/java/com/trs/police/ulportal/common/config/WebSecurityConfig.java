package com.trs.police.ulportal.common.config;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 高新门户不走Security
 *
 * <AUTHOR>
 * @since 2024/9/3 11:25
 **/
@Component
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${com.trs.security.skip:true}")
    public String securitySkip;

    @Value("${notLoginURL:/openApi/*}")
    private String notLoginUrl;

    @Override
    public void configure(WebSecurity web) {
        List<String> urls = new ArrayList<>();
        urls.add("/user/login");
        urls.add("/openApi/**");
        urls.add("/error");
        urls.add("/public/**");
        if (notLoginUrl != null) {
            urls.addAll(Lists.newArrayList(notLoginUrl.split(",")));
        }
        web.ignoring().antMatchers(urls.toArray(String[]::new));
    }
}
