package com.trs.police.ulportal.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 用户额外信息实体：比如
 * @date 2023/12/4 11:32
 */

@Data
@TableName(value = "t_portal_data_category")
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataCategoryVO implements Serializable {

    private static final long serialVersionUID = 930407643212942105L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 父节点id,根节点parent_id值为0
     */
    @TableId(value = "parent_id")
    private Long parentId;

    /**
     * 分类名
     */
    @TableField(value = "category_name")
    private String categoryName;


}
