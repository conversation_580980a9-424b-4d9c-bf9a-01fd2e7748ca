package com.trs.police.ulportal.domain.vo.policesituationtopic;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-06-03 16:03
 */
@Data
@AllArgsConstructor
public class CommonPoliceTypeCountVO {

    /**
     *  自定义类型id
     */
    private Long id;

    /**
     *  自定义类型名，或者警情类别名
     */
    private String jqlbmc;

    /**
     *  警情类别代码
     */
    private String jqlbdm;

    /**
     *  警情数
     */
    private Long count;

    /**
     *  是否有子节点，默认没有
     */
    private Integer hasChild = 0;

    public CommonPoliceTypeCountVO(String name, String code, Long count){
        this.jqlbmc = name;
        this.jqlbdm = code;
        this.count = count;
    }

}
