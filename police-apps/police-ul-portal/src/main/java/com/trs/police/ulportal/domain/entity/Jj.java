package com.trs.police.ulportal.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @description 接警
 * @date 2023/12/7 19:32
 */

@Data
@TableName(value = "ods_ds_gx_jjdb")
@Slf4j
public class Jj implements Serializable {

    private static final long serialVersionUID = 4358200288943105L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 接警单编号
     */
    @TableField(value = "jjdbh")
    private String jjdbh;

    /**
     * 报警时间
     */
    @TableField(value = "bjsj")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalTime bjsj;

    /**
     * 接警完成时间
     */
    @TableField(value = "jjwcsj")
    private LocalTime jjwcsj;

    /**
     * 接警单位编号
     */
    @TableField(value = "jjdwdm")
    private String jjdwdm;

    /**
     * 接警单位名称
     */
    @TableField(value = "jjdwmc")
    private String jjdwmc;

    /**
     * 接警员编号
     */
    @TableField(value = "jjybh")
    private String jjybh;

    /**
     * 接警员姓名
     */
    @TableField(value = "jjyxm")
    private String jjyxm;

    /**
     * 来话类型
     */
    @TableField(value = "lhlx")
    private String lhlx;

    /**
     * 接警类型
     */
    @TableField(value = "jjlx")
    private String jjlx;

    /**
     * 报警人姓名
     */
    @TableField(value = "bjrmc")
    private String bjrmc;

    /**
     * 报警人姓名
     */
    @TableField(value = "bjrxbdm")
    private String bjrxbdm;
    /**
     * 报警电话
     */
    @TableField(value = "bjdh")
    private String bjdh;
    /**
     * 报警地址
     */
    @TableField(value = "bjdz")
    private String bjdz;
    /**
     * 报警内容
     */
    @TableField(value = "bjnr")
    private String bjnr;
    /**
     * 管辖单位代码
     */
    @TableField(value = "gxdwdm")
    private String gxdwdm;

    /**
     * 警情级别
     */
    @TableField(value = "jqjb")
    private String jqjb;
    /**
     * 处警类别编号
     */
    @TableField(value = "cjlbbh")
    private String cjlbbh;
    /**
     * 警情类别代码
     */
    @TableField(value = "jqlbdm")
    private String jqlbdm;
    /**
     * 警情类型代码
     */
    @TableField(value = "jqlxdm")
    private String jqlxdm;
    /**
     * 警情细类代码
     */
    @TableField(value = "jqxldm")
    private String jqxldm;
    /**
     * 警情地址
     */
    @TableField(value = "jqdz")
    private String jqdz;
    /**
     * 报警人X坐标
     */
    @TableField(value = "bjrxzb")
    private String bjrxzb;
    /**
     * 报警人Y坐标
     */
    @TableField(value = "bjryzb")
    private String bjryzb;
    /**
     * 新增时间
     */
    @TableField(value = "xt_xzsj")
    private LocalTime xtxzsj;
    /**
     * 修改时间
     */
    @TableField(value = "xt_xgsj")
    private LocalTime xtxgsj;

    /**
     * 修改时间
     */
    @TableField(value = "dsjpt_ysjczsj")
    private String dsjptysjczsj;

    /**
     * 操作类型
     */
    @TableField(value = "dsjpt_ysjczlx")
    private String dsjptysjczlx;

    /**
     * 没备注-我也不晓得啥含义
     */
    @TableField(value = "dsjpt_jgsj")
    private LocalTime dsjptjgsj;

}
