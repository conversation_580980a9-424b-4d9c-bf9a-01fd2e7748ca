package com.trs.police.ulportal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.ulportal.domain.entity.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-07 16:24
 */
@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    /**
     *  设置消息是否已读
     *
     * @param userId 用户id
     * @param readStatus 已读状态
     * @param oldReadStatus 筛选消息的状态
     */
    void allRead(@Param("userId") String userId,@Param("readStatus") int readStatus, @Param("oldReadStatus") int oldReadStatus);
}
