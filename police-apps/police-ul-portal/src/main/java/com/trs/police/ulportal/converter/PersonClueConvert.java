package com.trs.police.ulportal.converter;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.PersonClueEntity;
import com.trs.police.ulportal.domain.dto.ys.PersonClueJudgeDTO;
import com.trs.police.ulportal.domain.entity.ys.PersonClueJudgeEntity;
import com.trs.police.ulportal.domain.vo.ys.PersonClueJudgeVO;
import com.trs.police.ulportal.domain.vo.ys.PersonClueNavigationVO;
import com.trs.police.ulportal.domain.vo.ys.PersonClueVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;


/**
 * 人员线索转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface PersonClueConvert {

    PersonClueConvert CONVERTER = Mappers.getMapper(PersonClueConvert.class);

    /**
     * xx
     *
     * @param entity xx
     * @return xx
     */
    @Mappings({
            @Mapping(target = "clueWarnTime", source = "clueWarnTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "acceptTime", source = "acceptTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "captureTime", source = "captureTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "lastDrugTime", source = "lastDrugTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "pushTime", source = "pushTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "personLabels", expression = "java(com.trs.police.ulportal.common.util.PersonClueUtil.dealPersonLabels(entity))"),
            @Mapping(target = "ajLabels", expression = "java(com.trs.police.ulportal.common.util.PersonClueUtil.dealAjLabels(entity))"),
            @Mapping(target = "zjhmList", expression = "java(com.trs.common.utils.StringUtils.isEmpty(entity.getZjhmList()) ? new ArrayList() : java.util.List.of(entity.getZjhmList().split(\",\")))")
    })
    PersonClueVO doToVo(PersonClueEntity entity);

    /**
     * 码表转导航
     *
     * @param dto 码表数据
     * @return 导航vo
     */
    @Mappings({
            @Mapping(target = "id", source = "id"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "code", source = "code"),
            @Mapping(target = "dictDesc", source = "dictDesc")
    })
    PersonClueNavigationVO dictToNavigation(DictDto dto);

    /**
     * 部门转导航
     *
     * @param dto 部门dto
     * @return 导航vo
     */
    @Mappings({
            @Mapping(target = "name", source = "shortName")
    })
    PersonClueNavigationVO deptToNavigation(DeptDto dto);

    /**
     * 研判dto转研判实体类
     *
     * @param judgeEntity 提供一个研判实体
     * @param dto         dto参数
     * @return 实体类
     */
    @Mappings({
            @Mapping(target = "id", source = "judgeEntity.id"),
            @Mapping(target = "clueId", ignore = true),
            @Mapping(target = "judgeContent", source = "dto.judgeContent"),
            @Mapping(target = "attachments", source = "dto.attachments")
    })
    PersonClueJudgeEntity judgeDtoToJudgeEntity(PersonClueJudgeEntity judgeEntity, PersonClueJudgeDTO dto);

    /**
     * 研判dto转研判实体类
     *
     * @param judgeEntity 研判实体
     * @return vo
     */
    @Mappings({
            @Mapping(target = "judgeId", source = "id"),
            @Mapping(target = "updateTime", source = "updateTime", dateFormat = TimeUtils.YYYYMMDD_HHMMSS)
    })
    PersonClueJudgeVO judgeEntityToJudgeVo(PersonClueJudgeEntity judgeEntity);
}
