package com.trs.police.ulportal.adapter.controller;

import com.trs.police.ulportal.common.annotation.ExcludeInfo;
import com.trs.police.ulportal.domain.dto.*;
import com.trs.police.ulportal.domain.vo.*;
import com.trs.police.ulportal.service.WarningService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @description 门户预警相关接口
 * @date 2023/11/13 15:20
 */
@RestController
@Api(value = "门户预警", tags = "门户预警")
@RequiredArgsConstructor
@RequestMapping("/portalWarning")
@Slf4j
public class PortalWarningController {

    private final WarningService warningService;

    /**
     * 查询预警列表
     *
     * @param listDto 入参
     * @return 结果
     * @throws Exception 异常
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "查询预警列表", notes = "查询预警列表")
    @ExcludeInfo
    public RestfulResultsV2<WarningVO> queryList(@Validated @RequestBody PortalWarningListDto listDto) throws Exception {
        return warningService.queryList(listDto);
    }

    /**
     * 获取最近的警情列表
     *
     * @param dto 入参
     * @return 结果
     */
    @PostMapping("/getRecentWarningCount")
    @ApiOperation(value = "获取最近的警情列表", notes = "获取最近的警情列表")
    public RestfulResultsV2<RecentWarningCountVO> getRecentWarningCount(@RequestBody RecentWarningCountDTO dto) {
        return warningService.getRecentWarningCount(dto);
    }

    /**
     * 查询预警人员统计列表
     *
     * @param listDto 入参
     * @return 结果
     */
    @PostMapping("/queryWarningPersonList")
    @ApiOperation(value = "查询预警列表", notes = "查询预警列表")
    @ExcludeInfo
    public RestfulResultsV2<WarningPersonListVO> queryWarningPersonList(@RequestBody WarningPersonListDTO listDto) {
        return warningService.queryWarningPersonList(listDto);
    }

    /**
     * 获取总预警统计
     *
     * @param dto 入参
     * @return 结果
     */
    @PostMapping("/getTotalWarningCount")
    @ApiOperation(value = "获取总预警统计", notes = "获取总预警统计")
    @ExcludeInfo
    public RestfulResultsV2<TotalWarningCountVO> getTotalWarningCount(@RequestBody TotalWarningCountDTO dto){
        return warningService.getTotalWarningCount(dto);
    }

    /**
     * 获取每种类型的预警统计
     *
     * @param dto 入参
     * @return 结果
     */
    @PostMapping("/getWarningCountByType")
    @ApiOperation(value = "获取每种类型的预警统计", notes = "获取每种类型的预警统计")
    public RestfulResultsV2<WarningCountByTypeVO> getWarningCountByType(@RequestBody WarningCountByTypeDTO dto) {
        return warningService.getWarningCountByType(dto);
    }

    /**
     *  加密历史数据
     *
     * @param dto dto
     */
    @PostMapping("/encryptHistory")
    @ApiOperation(value = "加密历史数据", notes = "加密历史数据")
    public void encryptHistory(EncryptDTO dto){
        warningService.encryptHistory(dto);
    }

    /**
     * 完整性加密历史数据
     *
     * @param dto dto
     */
    @PostMapping("/encryptHistoryMac")
    @ApiOperation(value = "完整性加密历史数据", notes = "完整性加密历史数据")
    public void encryptHistoryMac(EncryptDTO dto){
        warningService.encryptHistoryMac(dto);
    }
}
