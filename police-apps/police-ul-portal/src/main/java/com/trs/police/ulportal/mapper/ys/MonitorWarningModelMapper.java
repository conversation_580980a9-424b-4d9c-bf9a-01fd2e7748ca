package com.trs.police.ulportal.mapper.ys;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.ulportal.domain.entity.ys.MonitorWarningModelEntity;
import com.trs.police.ulportal.domain.vo.ys.MonitorWarningModelStatisticVO;
import com.trs.police.ulportal.domain.vo.ys.MonitorWarningModelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模型接口
 *
 * <AUTHOR>
 * @date 2024/03/25 17:59
 */
@Mapper
@DS("ys-mysqldb")
public interface MonitorWarningModelMapper extends BaseMapper<MonitorWarningModelEntity> {

    /**
     * 热门模型统计
     *
     * @return 统计结果
     */
    MonitorWarningModelStatisticVO statistics();

    /**
     * 根据每个警种下的模型数量获取警种排名
     *
     * @param count 排行榜数量
     * @return 排名结果
     */
    List<String> getRankByPoliceCategoryCount(@Param("count") Integer count);


    /**
     * 根据传入的警种信息返回这些警种下的模型
     *
     * @param policeCategorys 需要获取的警种信息
     * @return 模型信息
     */
    List<MonitorWarningModelVO> getModelByPoliceCategory(@Param("policeCategorys") List<String> policeCategorys);
}