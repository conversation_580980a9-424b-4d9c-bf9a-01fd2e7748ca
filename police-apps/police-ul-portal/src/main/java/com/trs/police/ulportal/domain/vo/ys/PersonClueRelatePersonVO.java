package com.trs.police.ulportal.domain.vo.ys;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PersonClueRelatePersonVO implements Serializable {

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 现住地址
     */
    private String xzdz;

    /**
     * 手机号码
     */
    private List<String> sjhm;

    /**
     * 性别
     */
    private String xb;
}
