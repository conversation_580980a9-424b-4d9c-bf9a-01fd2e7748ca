package com.trs.police.ulportal.mapper.policesituationtopic;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceTimeRangeDTO;
import com.trs.police.ulportal.domain.entity.policesituationtopic.PoliceSituationTimeSpanEntity;
import com.trs.police.ulportal.domain.vo.policesituationtopic.PoliceCountVO;
import com.trs.police.ulportal.domain.vo.policesituationtopic.PoliceTimeSpanVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-05-09 10:46
 */
@Mapper
@DS("jqfx-mysqldb")
public interface PoliceSituationTimeSpanMapper extends BaseMapper<PoliceSituationTimeSpanEntity> {

    /**
     *  各时间段的对比警情
     *
     * @param dto dto
     * @return 统计信息
     */
    @Select("SELECT SUM(t0) as t0,SUM(t1) as t1,SUM(t2) as t2,SUM(t3) as t3,SUM(t4) as t4,SUM(t5) as t5,SUM(t6) as t6,SUM(t7) as t7,SUM(t8) as t8,SUM(t9) as t9,SUM(t10) as t10,SUM(t11) as t11,SUM(t12) as t12,SUM(t13) as t13,SUM(t14) as t14,SUM(t15) as t15,SUM(t16) as t16,SUM(t17) as t17,SUM(t18) as t18,SUM(t19) as t19,SUM(t20) as t20,SUM(t21) as t21,SUM(t22) as t22,SUM(t23) as t23 " +
            "FROM `dwd_jqsdtjb` where bmbh like concat(#{dto.regionCode,jdbcType=VARCHAR}, '%') and rq >=#{dto.startTime} and rq <= #{dto.endTime}")
    PoliceTimeSpanVO getPoliceTimeRange(@Param("dto") PoliceTimeRangeDTO dto);

    /**
     *  获取按所属派出所分析
     *
     * @param dto dto
     * @param codes codes
     * @return 统计信息
     */
    List<PoliceCountVO> getDeptPoliceCount(@Param("dto") PoliceTimeRangeDTO dto, @Param("codes") List<String> codes);
}
