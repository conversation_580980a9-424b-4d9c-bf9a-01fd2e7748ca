package com.trs.police.ulportal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-07-17 09:47
 */
@Data
public class DeptEvaluateVO {

    @ApiModelProperty(value = "部门code")
    private String organCode;

    @ApiModelProperty(value = "部门名称")
    private String organName;

    @ApiModelProperty(value = "部门简称")
    private String organJc;

    @ApiModelProperty(value = "使用次数")
    private Long count;

    @ApiModelProperty(value = "部门人数")
    private Long personCount;
}
