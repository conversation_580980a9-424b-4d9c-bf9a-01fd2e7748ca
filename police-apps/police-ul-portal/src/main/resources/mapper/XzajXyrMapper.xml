<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.ulportal.mapper.XzajXyrMapper">


    <select id="countCaseArrestsNum" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            `ods_hik_jwzh_xzaj_xyr_gx`
        <where>

            <if test="ajbhList != null and ajbhList.size > 0">
                asjbh in
                <foreach collection="ajbhList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>
</mapper>