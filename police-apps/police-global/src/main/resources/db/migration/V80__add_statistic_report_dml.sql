CREATE TABLE IF NOT EXISTS t_statistic_analysis_report  (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `title` varchar(255) NOT NULL COMMENT '名称',
    `type` varchar(255) NULL COMMENT '类型',
    `template` varchar(255) NULL COMMENT '模版',
    `begin_time` timestamp NULL COMMENT '开始时间',
    `end_time` timestamp NULL COMMENT '结束时间',
    `duration` varchar(255) NULL COMMENT '时间段',
    `file` json NOT NULL COMMENT '文件信息',
    `district` varchar(255) NULL,
    PRIMARY KEY (`id`)
)comment '统计报表';
