DROP TABLE IF EXISTS `t_fight_result_case_relation`;
CREATE TABLE `t_fight_result_case_relation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `fight_result_id` bigint(20) NULL DEFAULT NULL COMMENT '战果id',
  `case_event_id` bigint(20) NULL DEFAULT NULL COMMENT '案事件id',
  `case_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '案件名称',
  `case_type` tinyint(4) NULL DEFAULT NULL COMMENT '案件类型：0-刑事，1-行政',
  `create_dept_id` bigint(20) NULL DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户主键',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint(20) NULL DEFAULT NULL COMMENT '更新单位主键',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '战果-案件关联表' ROW_FORMAT = Dynamic;