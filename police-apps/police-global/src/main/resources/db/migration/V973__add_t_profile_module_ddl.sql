DELETE FROM t_profile_module WHERE `type` = 'personV2' AND en_name = 'archive';
INSERT INTO t_profile_module (id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content) VALUES(1575, '档案管理', 'archive', 'personV2', NULL, 0, 1, '{}', '{}', '{"name": "人员信息", "type": "LIST_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "photo", "mapping": "json_to_image_array", "jdbcType": "json_object_array"}, "name": "photo", "listSchema": {"style": {"align": "center", "fixed": "left", "ellipsis": true}, "schema": {"type": "photo", "title": "照片"}, "properties": {"isPhoto": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "姓名"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "listSchema": {"style": {"align": "center", "ellipsis": false}, "schema": {"type": "string", "title": "证件号码"}, "properties": {"href": "/ys-app/archives/person/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true, "validateOption": {"pattern": "/[0-9]/"}}}}, {"db": {"table": "t_profile_person", "column": "person_label", "mapping": "label_id_array_to_name", "jdbcType": "label_id_array"}, "name": "personLabel", "listSchema": {"style": {"align": "center", "width": 400, "ellipsis": true}, "filter": {"key": "personLabel", "type": "multiple-tree", "value": ["&&person_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, "schema": {"type": "array", "title": "人员标签"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 2, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "risk_level", "jdbcType": "string"}, "name": "riskLevel", "listSchema": {"style": {"align": "center", "width": 128}, "schema": {"type": "string", "title": "风险等级"}, "properties": {"colorMap": {"关注": "#333333 ", "中风险": "#FFCE60", "低风险": "#6088D6", "高风险": "#FA8C34", "重中之重": "#EC3939"}, "copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_person", "mapping": "user_id_array_to_user_name_array", "jdbcType": "user_id_array", "databaseRelation": {"type": "FOREIGN_KEY", "table": "t_profile_person_control", "column": "person_id"}}, "name": "dutyPolice", "listSchema": {"style": {"align": "center"}, "schema": {"type": "array", "title": "责任民警"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "dept_code", "databaseRelation": {"type": "FOREIGN_KEY", "table": "t_profile_person_control", "column": "person_id"}}, "name": "dutyPoliceStation", "listSchema": {"style": {"align": "center"}, "filter": {"key": "dutyPoliceStation", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "displayName": "责任派出所"}, "schema": {"type": "string", "title": "责任派出所"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "district"}, "name": "registeredResidence", "listSchema": {"style": {"align": "center"}, "filter": {"key": "registeredResidence", "type": "multiple-tree", "value": ["&&district&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "户籍地"}, "schema": {"type": "string", "title": "户籍地"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "录入时间"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_person", "column": "update_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "updateTime", "listSchema": {"style": {"align": "center"}, "filter": {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}, "schema": {"type": "string", "title": "更新时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true, "sortDefault": "descending"}}}, {"db": {"table": "t_profile_person", "column": "profile_status", "mapping": "archive_code_to_name", "jdbcType": "string"}, "name": "profileStatus", "listSchema": {"style": {"align": "center", "ellipsis": true}, "schema": {"type": "string", "title": "档案状态"}, "properties": {"isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}], "selectable": true, "searchFields": [{"key": "idNumber", "name": "身份证号"}, {"key": "name", "name": "姓名"}], "profileDataPermission": [{"db": {"table": "t_profile_person", "column": "person_label", "jdbcType": "string"}, "type": "personLabel"}]}', 0, '{}', NULL, NULL, 1, 1, 1, 0);
