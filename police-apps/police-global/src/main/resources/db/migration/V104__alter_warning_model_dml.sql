DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_control_monitor_warning_model' AND column_name='recommended_filter')
    THEN
alter table t_control_monitor_warning_model drop recommended_filter;
END IF;
END $$
DELIMITER ;
CALL drop_column;


DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_control_monitor_warning_model' AND column_name='recommend_label')
    THEN
ALTER TABLE t_control_monitor_warning_model  ADD COLUMN `recommend_label` json  DEFAULT NULL COMMENT '推荐规则-人员标签' ;
END IF;
END $$
DELIMITER ;
CALL add_column;

DE<PERSON>IMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_control_monitor_warning_model' AND column_name='recommend_district')
    THEN
ALTER TABLE t_control_monitor_warning_model  ADD COLUMN `recommend_district` json  DEFAULT NULL COMMENT '推荐规则-户籍地' ;
END IF;
END $$
DELIMITER ;
CALL add_column;