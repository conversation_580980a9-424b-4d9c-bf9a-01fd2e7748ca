DELIMITER $$
DROP PROCEDURE IF EXISTS `update_request_type_records` $$
CREATE PROCEDURE update_request_type_records()
BEGIN
    -- 查询每个 id 对应的 p_id
    SELECT DISTINCT id, p_id FROM t_dict WHERE type = 'request_type';

    -- 更新 t_dict 表中 type=request_type 记录的 code、p_code 和 p_id 字段
    UPDATE t_dict t1
    JOIN (
        SELECT DISTINCT id, p_id FROM t_dict WHERE type = 'request_type'
    ) t2 ON t1.id = t2.id
    SET t1.p_code = t2.p_id
    WHERE t1.type = 'request_type';
END $$
DELIMITER ;
CALL update_request_type_records;
DROP PROCEDURE IF EXISTS `update_request_type_records`;