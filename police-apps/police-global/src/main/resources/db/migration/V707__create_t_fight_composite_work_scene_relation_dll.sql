-- 合成-工作对象关联表
CREATE TABLE IF NOT exists `t_fight_composite_work_scene_relation` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `create_user_id` bigint(20) DEFAULT NULL,
    `create_dept_id` bigint(20) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT NULL,
    `update_user_id` bigint(20) DEFAULT NULL,
    `update_dept_id` bigint(20) DEFAULT NULL,
    `update_time` timestamp NULL DEFAULT NULL,
    `composite_id` bigint(20) DEFAULT NULL COMMENT '合成id',
    `relate_type` bigint(20) DEFAULT NULL COMMENT '关联类型 码表值hit_subject_scene',
    `relate_id` bigint(20) DEFAULT NULL COMMENT '关联id',
    `work_object` json DEFAULT NULL COMMENT '关联对象内容-根据模板生成',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB;
