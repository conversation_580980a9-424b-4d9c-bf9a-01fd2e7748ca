DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_collaboration' AND column_name='security_type')
    THEN
alter table t_collaboration add column `security_type` bigint(20) NULL COMMENT '密级';
END IF;
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_collaboration' AND column_name='user_purpose_type')
    THEN
alter table t_collaboration add column `user_purpose_type` bigint(20) NULL COMMENT '使用目的';
END IF;
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_collaboration' AND column_name='case_event_name')
    THEN
alter table t_collaboration add column `case_event_name` varChar(255) NULL COMMENT '案事件名称';
END IF;
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_collaboration' AND column_name='case_event_type')
    THEN
alter table t_collaboration add column `case_event_type` varChar(255) NULL COMMENT '案事件类型';
END IF;
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_collaboration' AND column_name='case_event_base_info')
    THEN
alter table t_collaboration add column `case_event_base_info` text NULL COMMENT '案事件基本情况';
END IF;
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_collaboration' AND column_name='start_person_name')
    THEN
alter table t_collaboration add column `start_person_name` varChar(255) NULL COMMENT '承办人';
END IF;

END $$
DELIMITER ;
CALL add_column;




