DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    -- tb_intelligence_zdasj_swsfjj_attributes 同步字段
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_intelligence_zdasj_swsfjj_attributes' AND column_name='zysq')
    THEN
        ALTER TABLE tb_intelligence_zdasj_swsfjj_attributes ADD zysq LONGTEXT NULL COMMENT '主要诉求';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;

-- 更新指令表配置
UPDATE
    tb_intelligence_attribute_templates
SET
	fields = '[{"key":"happenedTime","dbKey":"happened_time","properties":{"required":true,"options":[]},"schema":{"title":"聚集时间","type":"datetime"}},{"key":"leaveTime","dbKey":"leave_time","properties":{"required":false,"options":[]},"schema":{"title":"离散时间","type":"datetime"}},{"key":"groupType","dbKey":"group_type","properties":{"required":true,"options":[{"label":"退役涉访","value":"退役涉访"},{"label":"投资受损","value":"投资受损"},{"label":"业主","value":"业主"},{"label":"非访","value":"非访"},{"label":"民师","value":"民师"},{"label":"其它","value":"其它"}]},"schema":{"title":"群体类型","type":"select"}},{"key":"personNum","dbKey":"person_num","properties":{"required":true,"options":[{"label":"50人以下","value":"50人以下"},{"label":"50人以上","value":"50人以上"}]},"schema":{"title":"聚集人数","type":"select"}},{"key":"localOrder","dbKey":"local_order","properties":{"required":true,"options":[{"label":"平稳，无过激行为","value":"平稳，无过激行为"},{"label":"激烈，有过激行为","value":"激烈，有过激行为"}]},"schema":{"title":"现场秩序","type":"select"}},{"key":"area","dbKey":"area","properties":{"required":false,"options":[]},"schema":{"title":"事发地点","type":"area"}},{"key":"zysq","dbKey":"zysq","properties":{"required":false,"options":[]},"schema":{"title":"主要诉求","type":"string"}},{"key":"ssxq","dbKey":"ssxq","properties":{"required":false,"options":[]},"schema":{"title":"所属辖区","type":"xiaqu"}},{"key":"labels","dbKey":"labels","properties":{"required":false,"options":[]},"schema":{"title":"标签","type":"intelligencePersonLabel"}},{"key":"drafter","dbKey":"drafter","properties":{"required":true,"options":[]},"schema":{"title":"拟稿","type":"string"}},{"key":"signer","dbKey":"signer","properties":{"required":true,"options":[]},"schema":{"title":"签发","type":"string"}},{"key":"phone","dbKey":"phone","properties":{"required":true,"options":[]},"schema":{"title":"电话","type":"string"}},{"key":"attachment","dbKey":"attachment","properties":{"required":false,"options":[]},"schema":{"title":"附件","type":"file"}}]'
WHERE
	`type` = 'yaoqing'
	AND data_type = 'zdasj'
	AND data_class = 'swsfjj';