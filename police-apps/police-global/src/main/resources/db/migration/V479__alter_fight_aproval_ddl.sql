UPDATE t_approval_process_config set enable = 1 where template_name = 'COMPOSITE_INITIATE';

DELIMITER //
CREATE PROCEDURE AddStatusColumnIfNotExists()
BEGIN
    DECLARE existing INT DEFAULT 0;

    SELECT COUNT(*)
    INTO existing
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_fight_composite_user_relation' AND COLUMN_NAME = 'status';

    IF existing = 0 THEN
        ALTER TABLE t_fight_composite_user_relation ADD status INT DEFAULT 3 NULL COMMENT '状态 1 待审核 2 审核中 3 已通过';
    END IF;
END //
DELIMITER ;
CALL AddStatusColumnIfNotExists();
DROP PROCEDURE IF EXISTS AddStatusColumnIfNotExists;

-- 插入记录，但如果已存在则不插入
INSERT INTO t_dict (`type`, code, name, p_code, dict_desc, show_number, standard, flag, color)
SELECT 'approval_type', NULL, '邀请人员', 0, NULL, 1, NULL, NULL, NULL
FROM DUAL
WHERE NOT EXISTS (
    SELECT 1 FROM t_dict WHERE type = 'approval_type' AND name = '邀请人员'
);
-- 更新p_id，但如果已有值则不更新
UPDATE t_dict AS old_record
JOIN t_dict AS new_parent
ON new_parent.type = 'approval_type' AND new_parent.name = '合成作战'
SET old_record.p_id = new_parent.id
WHERE old_record.type = 'approval_type' AND old_record.name = '邀请人员' AND old_record.p_id IS NULL;
-- 更新code为当前approval_type的最大code加1，但如果已有值则不更新
SET @max_code = (
    SELECT COALESCE(MAX(code), 0) FROM t_dict WHERE type = 'approval_type'
);
UPDATE t_dict
SET code = @max_code + 1
WHERE type = 'approval_type' AND name = '邀请人员' AND (code IS NULL OR code = 0);

DELIMITER //
CREATE PROCEDURE UpsertApprovalProcessConfig(templateName VARCHAR(100), processNumberPrefix VARCHAR(100), remark VARCHAR(255), isStopped INT, startFormTemplate VARCHAR(255), scope INT, allowUsers TEXT, allowRoles TEXT, allowUnits TEXT, isDeleted INT, enable INT)
BEGIN
    DECLARE typeValue INT;
    SELECT td.code INTO typeValue FROM t_dict td WHERE td.type = 'approval_type' AND td.name = '邀请人员';

    IF NOT EXISTS (
        SELECT 1 FROM t_approval_process_config WHERE template_name = templateName AND process_number_prefix = processNumberPrefix
    ) THEN
        INSERT INTO t_approval_process_config (template_name, `type`, remark, process_number_prefix, is_stopped, start_form_template, `scope`, allow_users, allow_roles, allow_units, is_deleted, enable)
        VALUES (templateName, typeValue, remark, processNumberPrefix, isStopped, startFormTemplate, scope, allowUsers, allowRoles, allowUnits, isDeleted, enable);
    END IF;
END //
DELIMITER ;
CALL UpsertApprovalProcessConfig('COMPOSITE_ADD_PERSON', '邀请人员', '区县-普通', 0, NULL, 1, NULL, NULL, NULL, 0, 1);
DROP PROCEDURE IF EXISTS UpsertApprovalProcessConfig;

DELIMITER //
CREATE PROCEDURE UpsertApprovalNodeConfig(orderNumber INT, nodeName VARCHAR(255), method INT, nodeFormTemplate VARCHAR(255), approvalUsers TEXT, approvalRoles TEXT, rejectType INT, rejectTo VARCHAR(255), isLastNode INT)
BEGIN
    DECLARE processConfigId INT;
    SELECT id INTO processConfigId FROM t_approval_process_config WHERE template_name = 'COMPOSITE_ADD_PERSON' AND process_number_prefix = '邀请人员';

    IF NOT EXISTS (
        SELECT 1 FROM t_approval_node_config WHERE process_config_id = processConfigId AND name = nodeName
    ) THEN
        INSERT INTO t_approval_node_config (process_config_id, order_number, name, `method`, node_form_template, approval_users, approval_roles, reject_type, reject_to, is_last_node)
        VALUES (processConfigId, orderNumber, nodeName, method, nodeFormTemplate, approvalUsers, approvalRoles, rejectType, rejectTo, isLastNode);
    END IF;
END //
DELIMITER ;
CALL UpsertApprovalNodeConfig(1, '''部门领导审批''', 1, NULL, '[]', '[{"level": 4, "roleId": 3}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig(2, '''分局领导审批''', 1, NULL, '[]', '[{"level": 2, "roleId": 3}]', 1, NULL, 1);
DROP PROCEDURE IF EXISTS UpsertApprovalNodeConfig;