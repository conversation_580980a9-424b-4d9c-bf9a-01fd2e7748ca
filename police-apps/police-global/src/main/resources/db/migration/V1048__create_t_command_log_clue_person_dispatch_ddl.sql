CREATE TABLE IF NOT EXISTS `t_command_log_clue_person_dispatch`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `create_time` date NULL DEFAULT NULL COMMENT '创建时间',
  `gabxfxshc` json NULL COMMENT '公安部下发线索核查',
  `qyzhbxfxshc` json NULL COMMENT '区域指挥部下发线索核查',
  `snsjxfxshc` json NULL COMMENT '省内搜集下发线索核查',
  `ykxtrylrjqsfkhc` json NULL COMMENT '云控系统人员录入及签收反馈核查',
  `fkbzlxfxs` json NULL COMMENT '风控办整理下发的线索',
  `qtxgzsx` json NULL COMMENT '其他需关注事项',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '指挥日志线索人员核查稳控调度表' ROW_FORMAT = Dynamic;