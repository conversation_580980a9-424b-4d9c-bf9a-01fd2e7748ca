DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_profile_certificate_type` $$
CREATE PROCEDURE insert_profile_certificate_type()
BEGIN
    DECLARE pid INT;
		DELETE FROM t_dict WHERE type like 'profile_certificate_type%';
    INSERT INTO t_dict (`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        ('profile_certificate_type_group',0,'证件类型',0,'certificateType',0,NULL,NULL,NULL,1);
		SET pid = LAST_INSERT_ID();
		UPDATE t_dict SET p_id = pid WHERE type = 'profile_certificate_type_group' and code = 0;

		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',1,'普通护照',0,'ptzh',1,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',2,'外交护照',0,'wjzh',2,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',3,'公务护照',0,'gwzh',3,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',4,'因公普通护照',0,'ygptzh',4,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',5,'外国人永久居留身份证',0,'wgrjyjlzsfz',5,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',6,'台湾居民来往大陆通行证',0,'tajmlwdltxz',6,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',7,'港澳居民来往内地通行证',0,'gajmlwnidxz',7,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',8,'其他',0,'qt',8,NULL,null,NULL,1);
		INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_certificate_type',9,'无证件',0,'wzj',9,NULL,null,NULL,1);
END $$
DELIMITER ;
CALL insert_profile_certificate_type;
DROP PROCEDURE IF EXISTS insert_profile_certificate_type;