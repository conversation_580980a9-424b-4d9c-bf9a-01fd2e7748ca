truncate table t_request_params;
INSERT INTO t_request_params (id, module, params) VALUES (1, 'list', '{"filterParams": [{"key": "level", "type": "option", "value": ["%%regular_monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控级别"}, {"key": "status", "type": "select", "value": ["%%monitor_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控状态"}, {"key": "person_label", "type": "multiple-tree", "value": ["&&person_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起时间"}, {"key": "latestWarningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "最近预警时间"}], "searchFields": [{"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "content", "name": "预警信息"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (2, 'warning-list', '{"filterParams": [{"key": "warningStatus", "type": "select", "value": [{"id": "1", "name": "待签收", "default": 0}, {"id": "2", "name": "已签收", "default": 0}, {"id": "3", "name": "已研判", "default": 0}, {"id": "4", "name": "已反馈", "default": 0}, {"id": "5", "name": "处置完结", "default": 0}, {"id": "6", "name": "无需签收", "default": 0}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "预警状态"}, {"key": "warningLevel", "type": "option", "value": ["%%monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "预警级别"}, {"key": "warningType", "type": "option", "value": ["%%control_monitor_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "预警对象"}, {"key": "controlType", "type": "option", "value": [{"id": "1", "name": "布控", "default": 0}, {"id": "2", "name": "常控", "default": 0}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "管控类型"}, {"key": "warningModel", "type": "multiple-tree", "value": ["&&control_warning_model&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "预警模型"}, {"key": "source", "type": "tree", "value": ["%%control_warning_source_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "感知源"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "content", "name": "预警信息"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (3, 'regular-list-my', '{"filterParams": [{"key": "level", "type": "option", "value": ["%%control_regular_monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控级别"}, {"key": "status", "type": "multiple-tree", "value": ["%%regular_monitor_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控状态"}, {"key": "person_label", "type": "multiple-tree", "value": ["&&person_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起时间"}, {"key": "latestWarningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "最近预警时间"}], "searchFields": [{"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "content", "name": "预警信息"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (4, 'regular-list-all', '{"filterParams": [{"key": "level", "type": "option", "value": ["%%control_regular_monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控级别"}, {"key": "status", "type": "multiple-tree", "value": ["%%regular_monitor_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "常控状态"}, {"key": "person_label", "type": "multiple-tree", "value": ["&&person_label&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "人员标签"}, {"key": "createDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "发起单位"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起时间"}, {"key": "latestWarningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "最近预警时间"}], "searchFields": [{"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "content", "name": "预警信息"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (5, 'monitor-list-my', '{"filterParams": [{"key": "monitorLevel", "type": "option", "value": ["%%monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "布控级别"}, {"key": "monitorType", "type": "select", "value": ["%%control_monitor_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "布控对象"}, {"key": "monitorStatus", "type": "select", "value": ["%%monitor_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "布控状态"}, {"key": "warningModel", "type": "multiple-tree", "value": ["&&control_warning_model&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "预警模型"}, {"key": "whetherHasWarning", "type": "option", "value": [{"id": "1", "name": "有预警"}, {"id": "0", "name": "无预警"}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "是否有预警"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "创建时间"}], "searchFields": [{"key": "monitorTitle", "name": "布控标题"}, {"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "carNumber", "name": "车牌号"}, {"key": "virtualIdentity", "name": "虚拟身份"}, {"key": "groupName", "name": "群体名称"}, {"key": "areaName", "name": "区域名称"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (6, 'monitor-list-all', '{"filterParams": [{"key": "monitorLevel", "type": "option", "value": ["%%monitor_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "布控级别"}, {"key": "monitorType", "type": "select", "value": ["%%control_monitor_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "布控对象"}, {"key": "monitorStatus", "type": "select", "value": ["%%monitor_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "布控状态"}, {"key": "monitorDept", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "name", "value": "deptCode", "children": "children"}, "displayName": "布控单位"}, {"key": "warningModel", "type": "multiple-tree", "value": ["&&control_warning_model&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "预警模型"}, {"key": "whetherHasWarning", "type": "option", "value": [{"id": "1", "name": "有预警"}, {"id": "0", "name": "无预警"}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "是否有预警"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "创建时间"}], "searchFields": [{"key": "monitorTitle", "name": "布控标题"}, {"key": "targetName", "name": "姓名"}, {"key": "idNumber", "name": "证件号码"}, {"key": "carNumber", "name": "车牌号"}, {"key": "virtualIdentity", "name": "虚拟身份"}, {"key": "groupName", "name": "群体名称"}, {"key": "areaName", "name": "区域名称"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (7, 'important-area', '{"filterParams": [{"key": "dataRange", "type": "option", "value": [{"id": "1", "name": "只看我的"}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "数据范围"}, {"key": "category", "type": "select", "value": ["%%control_warning_source_category%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "区域性质"}, {"key": "districtCode", "type": "select", "value": ["&&district&&"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "行政区划"}, {"key": "createDeptCode", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "name", "value": "deptCode", "children": "children"}, "displayName": "创建单位"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "创建时间"}], "searchFields": [{"key": "createUserName", "name": "创建人"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (8, 'source', '{"filterParams": [{"key": "dataUpdateStatus", "type": "option", "value": [{"id": "0", "name": "有数据"}, {"id": "1", "name": "无数据"}], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "今日数据更新状况"}, {"key": "type", "type": "select", "value": ["%%control_warning_source_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "类型"}, {"key": "category", "type": "select", "value": ["%%control_warning_source_category%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "性质"}, {"key": "districtCode", "type": "multiple-tree", "value": ["&&district&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "辖区"}, {"key": "deptCode", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "行政区划"}], "searchFields": []}');
INSERT INTO t_request_params (id, module, params) VALUES (9, 'composite-list-all', '{"filterParams": [{"key": "status", "type": "option", "value": [{"code": 3, "name": "侦办中"}, {"code": 4, "name": "已归档"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "合成状态"}, {"key": "organizedDept", "type": "multiple-tree", "value": ["&&dept_all&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "创办单位"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "创建时间"}], "searchFields": [{"key": "title", "name": "标题"}, {"key": "organizer", "name": "主办方"}, {"key": "message", "name": "反馈内容"}, {"key": "attachmentName", "name": "附件名称"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (10, 'approval-waiting', '{"filterParams": [{"key": "type", "type": "multiple-tree", "value": ["%%approval_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "类型"}, {"key": "mornitorDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "发起单位"}, {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起时间"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}], "searchFields": [{"key": "title", "name": "标题"}, {"key": "crUser", "name": "发起人"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (11, 'approval-done', '{"filterParams": [{"key": "status", "type": "option", "value": ["%%approval_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "审批状态"}, {"key": "type", "type": "multiple-tree", "value": ["%%approval_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "类型"}, {"key": "mornitorDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "发起单位"}, {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起时间"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}], "searchFields": [{"key": "title", "name": "标题"}, {"key": "crUser", "name": "发起人"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (12, 'approval-me', '{"filterParams": [{"key": "status", "type": "option", "value": ["%%approval_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "审批状态"}, {"key": "type", "type": "multiple-tree", "value": ["%%approval_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "类型"}, {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起时间"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}], "searchFields": [{"key": "title", "name": "标题"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (13, 'case-list', '{"filterParams": [{"key": "caseStatus", "type": "tree", "value": ["##jwzh_BD_D_XSAJYWZTMXDM##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "案件状态"}, {"key": "caseTopType", "type": "tree", "value": ["##jwzh_GA_D_XSAJLBDM##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "案件大类"}, {"key": "caseFineType", "type": "tree", "value": ["##jwzh_GA_D_XSAJXALBDM##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "案件细类"}, {"key": "intelligentClassify", "type": "select", "value": ["%%profile_case_intelligent_classification%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "智能分类"}, {"key": "importantLevel", "type": "select", "value": ["%%profile_case_importent_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "案件级别"}, {"key": "occurTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "录入时间"}, {"key": "updateTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "更新时间"}], "searchFields": [{"key": "caseName", "name": "案件名称"}, {"key": "asjbh", "name": "案事件编号"}, {"key": "sponsor", "name": "主办人"}, {"key": "relatedPerson", "name": "涉案人"}, {"key": "suspect", "name": "嫌疑人"}, {"key": "occurAddress", "name": "发生地点"}, {"key": "seriesCase", "name": "串案"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (14, 'task-tracing-list-publisher', '{"filterParams": [{"key": "taskType", "type": "option", "value": ["%%dy_task_tracing_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "挂账类型"}, {"key": "taskStatus", "type": "select", "value": [{"code": "1", "name": "草稿"}, {"code": "2", "name": "待审核"}, {"code": "3", "name": "已驳回"}, {"code": "4", "name": "在办中"}, {"code": "5", "name": "已办结"}, {"code": "6", "name": "已销账"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "挂账状态"}, {"key": "isOverdue", "type": "option", "value": [{"code": 1, "name": "是"}, {"code": 0, "name": "否"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "是否逾期"}, {"key": "taskTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "挂账时间"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "盯办时限"}, {"key": "createDept", "type": "select", "value": [{"id": 28, "name": "胡市派出所"}, {"id": 2, "name": "泸州市公安局"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起单位"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "责任单位"}], "searchFields": [{"key": "title", "name": "挂账标题"}, {"key": "taskCode", "name": "编号"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (15, 'task-tracing-list-commander', '{"filterParams": [{"key": "taskType", "type": "option", "value": ["%%dy_task_tracing_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "挂账类型"}, {"key": "taskStatus", "type": "select", "value": [{"code": "2", "name": "待审核"}, {"code": "3", "name": "已驳回"}, {"code": "4", "name": "在办中"}, {"code": "5", "name": "已办结"}, {"code": "6", "name": "已销账"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "挂账状态"}, {"key": "isOverdue", "type": "option", "value": [{"code": 1, "name": "是"}, {"code": 0, "name": "否"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "是否逾期"}, {"key": "taskTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "挂账时间"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "盯办时限"}, {"key": "createDept", "type": "select", "value": [{"id": 28, "name": "胡市派出所"}, {"id": 2, "name": "泸州市公安局"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起单位"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "责任单位"}], "searchFields": [{"key": "title", "name": "挂账标题"}, {"key": "taskCode", "name": "编号"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (16, 'task-tracing-list-handler', '{"filterParams": [{"key": "taskType", "type": "option", "value": ["%%dy_task_tracing_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "挂账类型"}, {"key": "taskStatus", "type": "select", "value": [{"code": "7", "name": "待签收"}, {"code": "8", "name": "待回复"}, {"code": "9", "name": "待办结"}, {"code": "5", "name": "已办结"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "挂账状态"}, {"key": "isOverdue", "type": "option", "value": [{"code": 1, "name": "是"}, {"code": 0, "name": "否"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "是否逾期"}, {"key": "taskTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "挂账时间"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "盯办时限"}, {"key": "createDept", "type": "select", "value": [{"id": 28, "name": "胡市派出所"}, {"id": 2, "name": "泸州市公安局"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起单位"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "责任单位"}], "searchFields": [{"key": "title", "name": "挂账标题"}, {"key": "taskCode", "name": "编号"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (17, 'composite-list-my', '{"filterParams": [{"key": "compositeStatus", "type": "select", "value": ["%%fight_composite_status%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "合成状态"}, {"key": "principalDept", "type": "multiple-tree", "value": ["&&dept_all&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主理单位"}, {"key": "createTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "创建时间"}], "searchFields": []}');
INSERT INTO t_request_params (id, module, params) VALUES (18, 'task-tracing-list-leader', '{"filterParams": [{"key": "taskType", "type": "option", "value": ["%%dy_task_tracing_type%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "挂账类型"}, {"key": "taskStatus", "type": "select", "value": [{"code": "4", "name": "在办中"}, {"code": "5", "name": "已办结"}, {"code": "6", "name": "已销账"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "挂账状态"}, {"key": "isOverdue", "type": "option", "value": [{"code": 1, "name": "是"}, {"code": 0, "name": "否"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "是否逾期"}, {"key": "taskTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "挂账时间"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "盯办时限"}, {"key": "createDept", "type": "select", "value": [{"id": 28, "name": "胡市派出所"}, {"id": 2, "name": "泸州市公安局"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "发起单位"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "责任单位"}], "searchFields": [{"key": "title", "name": "挂账标题"}, {"key": "taskCode", "name": "编号"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (19, 'monitor-export-params', '{"filterParams": [], "searchFields": [{"key": "moniterTitle", "name": "布控标题"}, {"key": "monitorLevel", "name": "布控级别"}, {"key": "monitorStatus", "name": "布控状态"}, {"key": "initiator", "name": "布控人"}, {"key": "monitorDept", "name": "布控单位"}, {"key": "createTime", "name": "创建时间"}, {"key": "monitorPerson", "name": "布控人员"}, {"key": "monitorGroup", "name": "布控群体"}, {"key": "monitorArea", "name": "布控区域"}, {"key": "monitorReason", "name": "布控事由"}, {"key": "enforcementBasicType", "name": "执法依据类型"}, {"key": "relatedEventNumber", "name": "关联案件（警情）编号"}, {"key": "disposalMeasure", "name": "处置措施"}, {"key": "disposalExplanation", "name": "处置要求说明"}, {"key": "notifyPerson", "name": "通知人员"}, {"key": "isMessageNotify", "name": "短信通知"}, {"key": "warningModel", "name": "预警模型"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (20, 'task-tracing-export-params', '{"filterParams": [], "searchFields": [{"key": "index", "name": "序号"}, {"key": "title", "name": "挂账标题"}, {"key": "taskStatus", "name": "挂账状态"}, {"key": "taskCode", "name": "编号"}, {"key": "taskType", "name": "类型"}, {"key": "createDept", "name": "布控单位"}, {"key": "responsibleDept", "name": "发起单位"}, {"key": "taskTime", "name": "挂账时间"}, {"key": "timeLimit", "name": "盯办时限"}, {"key": "content", "name": "内容"}, {"key": "attachments", "name": "附件"}, {"key": "reportLeader", "name": "报送领导"}, {"key": "replyCount", "name": "回复数量"}, {"key": "replyDetails", "name": "回复详情"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (21, 'risk-list-standing-book-responsiblePerson', '{"filterParams": [{"key": "riskLevel", "type": "option", "value": ["%%risk_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "风险等级"}, {"key": "overdueStatus", "type": "option", "value": [{"code": "1", "name": "已逾期"}, {"code": "0", "name": "未逾期"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期状态"}, {"key": "resolveStatus", "type": "option", "value": [{"code": "1", "name": "已化解"}, {"code": "0", "name": "未化解"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "化解状态"}, {"key": "handOverStatus", "type": "option", "value": [{"code": "1", "name": "已移交"}, {"code": "0", "name": "未移交"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "移交党政"}, {"key": "riskStatus", "type": "select", "value": [{"code": "3", "name": "待签收"}, {"code": "6", "name": "待研判"}, {"code": "8", "name": "在办中"}, {"code": "7", "name": "待办结"}, {"code": "9", "name": "已办结"}, {"code": "10", "name": "不处置"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险状态"}, {"key": "source", "type": "select", "value": ["%%risk_source%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险来源"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主责单位"}, {"key": "handleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "处置单位"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "反馈时限"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "title", "name": "风险标题"}, {"key": "riskCode", "name": "风险编号"}, {"key": "content", "name": "风险概述"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (22, 'risk-list-standing-book-dispatcher', '{"filterParams": [{"key": "riskLevel", "type": "option", "value": ["%%risk_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "风险等级"}, {"key": "overdueStatus", "type": "option", "value": [{"code": "1", "name": "已逾期"}, {"code": "0", "name": "未逾期"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期状态"}, {"key": "resolveStatus", "type": "option", "value": [{"code": "1", "name": "已化解"}, {"code": "0", "name": "未化解"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "化解状态"}, {"key": "handOverStatus", "type": "option", "value": [{"code": "1", "name": "已移交"}, {"code": "0", "name": "未移交"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "移交党政"}, {"key": "riskStatus", "type": "select", "value": [{"code": "1", "name": "待分派"}, {"code": "2", "name": "已退回"}, {"code": "3", "name": "待签收"}, {"code": "6", "name": "待研判"}, {"code": "8", "name": "在办中"}, {"code": "7", "name": "待办结"}, {"code": "9", "name": "已办结"}, {"code": "10", "name": "不处置"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险状态"}, {"key": "source", "type": "select", "value": ["%%risk_source%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险来源"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主责单位"}, {"key": "handleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "处置单位"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "反馈时限"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "title", "name": "风险标题"}, {"key": "riskCode", "name": "风险编号"}, {"key": "content", "name": "风险概述"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (23, 'risk-list-handle', '{"filterParams": [{"key": "riskLevel", "type": "option", "value": ["%%risk_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "风险等级"}, {"key": "overdueStatus", "type": "option", "value": [{"code": "1", "name": "已逾期"}, {"code": "0", "name": "未逾期"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期状态"}, {"key": "resolveStatus", "type": "option", "value": [{"code": "1", "name": "已化解"}, {"code": "0", "name": "未化解"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "化解状态"}, {"key": "handOverStatus", "type": "option", "value": [{"code": "1", "name": "已移交"}, {"code": "0", "name": "未移交"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "移交党政"}, {"key": "riskStatus", "type": "select", "value": [{"code": "3", "name": "待签收"}, {"code": "4", "name": "已签收"}, {"code": "5", "name": "已反馈"}, {"code": "7", "name": "待办结"}, {"code": "9", "name": "已办结"}, {"code": "10", "name": "不处置"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险状态"}, {"key": "source", "type": "select", "value": ["%%risk_source%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险来源"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主责单位"}, {"key": "handleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "处置单位"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "反馈时限"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "title", "name": "风险标题"}, {"key": "riskCode", "name": "风险编号"}, {"key": "content", "name": "风险概述"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (24, 'risk-list-copy', '{"filterParams": [{"key": "riskLevel", "type": "option", "value": ["%%risk_level%%"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "风险等级"}, {"key": "overdueStatus", "type": "option", "value": [{"code": "1", "name": "已逾期"}, {"code": "0", "name": "未逾期"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "逾期状态"}, {"key": "resolveStatus", "type": "option", "value": [{"code": "1", "name": "已化解"}, {"code": "0", "name": "未化解"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "化解状态"}, {"key": "handOverStatus", "type": "option", "value": [{"code": "1", "name": "已移交"}, {"code": "0", "name": "未移交"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "移交党政"}, {"key": "riskStatus", "type": "select", "value": [{"code": "1", "name": "待分派"}, {"code": "3", "name": "待签收"}, {"code": "6", "name": "待研判"}, {"code": "8", "name": "在办中"}, {"code": "7", "name": "待办结"}, {"code": "9", "name": "已办结"}, {"code": "2", "name": "已退回"}, {"code": "10", "name": "不处置"}], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险状态"}, {"key": "source", "type": "select", "value": ["%%risk_source%%"], "fieldNames": {"label": "name", "value": "code"}, "displayName": "风险来源"}, {"key": "responsibleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "主责单位"}, {"key": "handleDept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "处置单位"}, {"key": "timeLimit", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "反馈时限"}, {"key": "warningTime", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "预警时间"}], "searchFields": [{"key": "title", "name": "风险标题"}, {"key": "riskCode", "name": "风险编号"}, {"key": "content", "name": "风险概述"}]}');
INSERT INTO t_request_params (id, module, params) VALUES (25, 'jq-list', '{"filterParams": [{"key": "level", "type": "select", "value": ["##sthy_jqdj##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "警情等级"}, {"key": "jjlx", "type": "select", "value": ["##sthy_jjlx##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "接警类型"}, {"key": "jqclztdm", "type": "select", "value": ["##sthy_jqzt##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "警情状态"}, {"key": "jqlyfs", "type": "select", "value": ["##sthy_jqly##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "警情来源"}, {"key": "lhlx", "type": "select", "value": ["##sthy_lhlx##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "来话类型"}, {"key": "category", "type": "tree", "value": ["##sthy_jq_label##"], "fieldNames": {"label": "name", "value": "code", "children": "children"}, "displayName": "警情类别"}, {"key": "district", "type": "multiple-tree", "value": ["&&district&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "管辖单位"}, {"key": "dept", "type": "multiple-tree", "value": ["&&dept&&"], "fieldNames": {"label": "name", "value": "id", "children": "children"}, "displayName": "行政区划"}, {"key": "bjsj", "type": "timeParams", "value": [{"id": "1", "name": "今天"}, {"id": "11", "name": "昨天"}, {"id": "2", "name": "本周"}, {"id": "12", "name": "上周"}, {"id": "3", "name": "本月"}, {"id": "13", "name": "上月"}, {"id": "4", "name": "本季"}, {"id": "14", "name": "上季"}, {"id": "99", "name": "自定义"}], "fieldNames": {"label": "name", "value": "id"}, "displayName": "报警时间"}], "searchFields": [{"key": "jjdbh", "name": "警情编号"}, {"key": "bjnr", "name": "报警内容"}]}');