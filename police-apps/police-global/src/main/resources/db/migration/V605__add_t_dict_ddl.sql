DELIMITER $$
DROP PROCEDURE IF EXISTS `add_dict` $$
CREATE PROCEDURE add_dict()
BEGIN
IF NOT EXISTS( SELECT * FROM t_dict WHERE `type` = 'related_person_role_group' )
    THEN
INSERT INTO t_dict (`type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES('related_person_role_group', 0, '涉案人员类型', 0, NULL, 0, NULL, NULL, NULL);
SET @pid = (select id from t_dict where `type` = 'related_person_role_group');
update t_dict set p_id = @pid where `type` = 'related_person_role_group';
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 1, '被害人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 3, '被解救人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 4, '违法犯罪可疑人员', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 5, '违法行为人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 6, '犯罪嫌疑人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 7, '被通缉人员', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 8, '上网追逃犯罪嫌疑人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 9, '被布控人员', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 10, '收赃人员', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 11, '被摸底排查人员', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 12, '发现人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 13, '报案人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 14, '举报、控告人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 15, '检举揭发人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 16, '扭送人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 17, '提供线索人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 18, '证人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 19, '知情人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 20, '见证人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 21, '被查封物品所有人/持有人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 22, '被扣押物品所有人/持有人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 23, '被查询服务标识号所有人/持有人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 24, '被冻结银行账号所有人/持有人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 25, '被执行搜查人员', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 26, '保证人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 27, '辨认人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 28, '提供证据人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 29, '监护人', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 30, '失踪人员', 0, NULL, 0, NULL, NULL, NULL);
INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color) VALUES(@pid, 'related_person_role', 99, '其他', 0, NULL, 0, NULL, NULL, NULL);
END IF;
END $$
DELIMITER ;
CALL add_dict;
DROP PROCEDURE IF EXISTS `add_dict`;