DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_group' AND column_name='business_category')
THEN
    ALTER TABLE t_profile_group ADD business_category varchar(50) NULL COMMENT '经营类别';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_group' AND column_name='leader')
THEN
    ALTER TABLE t_profile_group ADD leader bigint NULL COMMENT '领头人';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_group' AND column_name='related_gt')
THEN
    ALTER TABLE t_profile_group ADD related_gt json NULL COMMENT '关联杆体';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.statistics WHERE table_schema=(select database()) AND table_name='t_profile_group' AND index_name = 'leader_index')
    THEN
ALTER TABLE t_profile_group ADD INDEX `leader_index`(`leader`) USING BTREE;
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.statistics WHERE table_schema=(select database()) AND table_name='t_warning_fkrxyj' AND index_name = 'device_code_index')
    THEN
ALTER TABLE t_warning_fkrxyj ADD INDEX `device_code_index`(`device_code`) USING BTREE;
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.statistics WHERE table_schema=(select database()) AND table_name='t_warning_fkrxyj' AND index_name = 'id_card_index')
    THEN
ALTER TABLE t_warning_fkrxyj ADD INDEX `id_card_index`(`id_card`) USING BTREE;
END IF;

END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;
