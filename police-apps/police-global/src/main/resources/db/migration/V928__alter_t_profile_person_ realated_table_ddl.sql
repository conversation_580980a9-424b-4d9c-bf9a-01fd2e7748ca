DROP PROCEDURE IF EXISTS AddColumnIfNotExists;
DELIMITER //
CREATE PROCEDURE AddColumnIfNotExists(IN tableName VARCHAR(64), IN columnName VARCHAR(64), IN columnDetails VARCHAR(64))
BEGIN
    DECLARE existing INT DEFAULT 0;

SELECT COUNT(*)
INTO existing
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = tableName AND COLUMN_NAME = columnName;
IF existing = 0 THEN
        SET @s = CONCAT('ALTER TABLE ', tableName, ' ADD ', columnName, ' ', columnDetails);
PREPARE stmt FROM @s;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
END IF;
END //
DELIMITER ;
CALL AddColumnIfNotExists('t_profile_family_relation', 'political_status', 'int DEFAULT NULL COMMENT \'政治面貌\'');
CALL AddColumnIfNotExists('t_profile_family_relation', 'work_unit', 'varchar(100) DEFAULT NULL COMMENT \'工作单位\'');
CALL AddColumnIfNotExists('t_profile_family_relation', 'work_duty', 'varchar(100) DEFAULT NULL COMMENT \'职务\'');
CALL AddColumnIfNotExists('t_profile_person_group_relation', 'group_location', 'varchar(255) DEFAULT NULL COMMENT \'群体位置\'');
CALL AddColumnIfNotExists('t_profile_person_group_relation', 'group_work', 'INT DEFAULT NULL COMMENT \'群体分工\'');
CALL AddColumnIfNotExists('t_profile_person_event_relation', 'event_type', 'INT DEFAULT NULL COMMENT \'事件类别\'');
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;