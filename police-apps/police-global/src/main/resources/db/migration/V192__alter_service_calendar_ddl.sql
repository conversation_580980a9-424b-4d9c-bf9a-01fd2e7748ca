DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_service_calendar' AND column_name='no_need_done_regular_id')
    THEN
        ALTER TABLE t_service_calendar ADD no_need_done_regular_id json default null COMMENT '无需完成的常控id';
    END IF;
END $$
DELIMITER ;
CALL add_column;


ALTER TABLE t_service_work_record MODIFY work_detail VARCHAR(300);
ALTER TABLE t_service_work_record MODIFY destination VARCHAR(300);
