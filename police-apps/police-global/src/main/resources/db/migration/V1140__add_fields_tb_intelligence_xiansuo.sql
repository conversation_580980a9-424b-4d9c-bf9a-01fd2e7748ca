DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS(
        SELECT * FROM  information_schema.columns
        WHERE table_schema=(select database())
        AND table_name='tb_intelligence_xiansuo_base_info' AND column_name='st_report_dept_names'
        )
    THEN
        ALTER TABLE tb_intelligence_xiansuo_base_info ADD st_report_dept_names LONGTEXT NULL COMMENT '省厅上报单位名称';
    END IF;
    IF NOT EXISTS(
        SELECT * FROM  information_schema.columns
        WHERE table_schema=(select database())
        AND table_name='tb_intelligence_xiansuo_base_info' AND column_name='pub_dept_id'
        )
    THEN
        ALTER TABLE tb_intelligence_xiansuo_base_info ADD pub_dept_id BIGINT(20) NULL COMMENT '发布单位ID';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;