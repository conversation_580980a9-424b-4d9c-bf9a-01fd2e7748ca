DELIMITER $$
DROP PROCEDURE IF EXISTS `modify_column` $$
CREATE PROCEDURE modify_column()
BEGIN
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='t_command_log_related_info' AND column_name='clue_person_dispatch'
    )
    THEN
ALTER TABLE t_command_log_related_info ADD clue_person_dispatch text NULL COMMENT '线索人员核查稳控调度';
END IF;

END $$
DELIMITER ;
CALL modify_column;
DROP PROCEDURE IF EXISTS `modify_column`;