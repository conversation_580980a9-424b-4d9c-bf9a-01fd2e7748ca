DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    -- t_profile_event 添加分数字段
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_event' AND column_name='risk_score')
    THEN
        ALTER TABLE t_profile_event ADD COLUMN risk_score DOUBLE DEFAULT 0.0 NULL COMMENT '风险分';
    END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_event' AND column_name='raw_score')
    THEN
        ALTER TABLE t_profile_event ADD COLUMN raw_score DOUBLE DEFAULT 0.0 NULL COMMENT '累积分';
    END IF;
    -- t_profile_person 添加分数字段
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='risk_score')
    THEN
        ALTER TABLE t_profile_person ADD COLUMN risk_score DOUBLE DEFAULT 0.0 NULL COMMENT '风险分';
    END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='raw_score')
    THEN
        ALTER TABLE t_profile_person ADD COLUMN raw_score DOUBLE DEFAULT 0.0 NULL COMMENT '累积分';
    END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person' AND column_name='risk_level')
    THEN
        ALTER TABLE t_profile_person ADD COLUMN risk_level varchar(255) NULL COMMENT '风险等级';
    END IF;
    -- t_profile_group 添加分数字段
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_group' AND column_name='risk_score')
    THEN
        ALTER TABLE t_profile_group ADD COLUMN risk_score DOUBLE DEFAULT 0.0 NULL COMMENT '风险分';
    END IF;
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_group' AND column_name='raw_score')
    THEN
        ALTER TABLE t_profile_group ADD COLUMN raw_score DOUBLE DEFAULT 0.0 NULL COMMENT '累积分';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;

