DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_profile_visa_type_data` $$
CREATE PROCEDURE insert_profile_visa_type_data()
BEGIN
    DECLARE pid INT;

    INSERT INTO t_dict (`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        ('profile_visa_type_group',0,'签证类型',0,'visaContentType',0,NULL,NULL,NULL,1);
    SET pid = LAST_INSERT_ID();
    UPDATE t_dict SET p_id = pid WHERE type = 'profile_visa_type_group' and code = 0;

    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',1,'乘务签证',0,'crew_visa',1,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',2,'永久居留签证',0,'permanent_residence_visa',2,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',3,'访问签证',0,'visit_visa',3,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',4,'过境签证',0,'transit_visa',4,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',5,'旅游签证',0,'tourism_visa',5,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',6,'商业贸易签证',0,'business_trade_visa',6,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',7,'家庭团聚签证',0,'family_reunion_visa',7,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',8,'人才签证',0,'talent_visa',8,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',9,'私人事务签证',0,'private_affairs_visa',9,NULL,null,NULL,1);

    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (LAST_INSERT_ID(),'profile_visa_type',10,'S1长期',9,'private_affairs_visa_s1_long',10,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (LAST_INSERT_ID(),'profile_visa_type',11,'S2短期',9,'private_affairs_visa_s2_short',11,NULL,null,NULL,1);

    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',12,'X字签证（学习签证）',0,'study_visa_x',12,NULL,null,NULL,1);

    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (LAST_INSERT_ID(),'profile_visa_type',13,'X1长期学习',12,'study_visa_x1_long',13,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (LAST_INSERT_ID(),'profile_visa_type',14,'X2短期学习',12,'study_visa_x2_short',14,NULL,null,NULL,1);

    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',15,'工作签证',0,'work_visa',15,NULL,null,NULL,1);

    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (pid,'profile_visa_type',16,'其他特殊类别',0,'special_category',16,NULL,null,NULL,1);

    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (LAST_INSERT_ID(),'profile_visa_type',17,'记者签证',16,'journalist_visa',17,NULL,null,NULL,1);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
        (LAST_INSERT_ID(),'profile_visa_type',18,'国际旅行卫生保健',16,'international_travel_health_care',18,NULL,null,NULL,1);

END $$
DELIMITER ;
CALL insert_profile_visa_type_data;
DROP PROCEDURE IF EXISTS insert_profile_visa_type_data;