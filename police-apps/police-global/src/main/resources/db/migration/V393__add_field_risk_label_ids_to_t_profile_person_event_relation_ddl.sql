DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_person_event_relation' AND column_name='risk_label_ids')
    THEN
        ALTER TABLE t_profile_person_event_relation ADD COLUMN risk_label_ids varchar(1000) NULL COMMENT '风险标签ID串';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;
