DROP TABLE IF EXISTS tb_risk_label;
CREATE TABLE `tb_risk_label` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cr_user` varchar(100) NOT NULL COMMENT '创建人',
  `cr_user_true_name` varchar(100) NOT NULL COMMENT '创建人真实姓名',
  `cr_time` datetime NOT NULL COMMENT '创建时间',
  `update_user_true_name` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `label_type` varchar(100) NOT NULL COMMENT '标签类型',
  `label_name` varchar(255) NOT NULL COMMENT '标签名称',
  `label_desc` varchar(2000) DEFAULT NULL COMMENT '标签描述',
  `label_score` double NOT NULL COMMENT '标签分数',
  `label_scene` varchar(100) DEFAULT NULL COMMENT '标签所属场景',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tb_risk_label_unique` (`label_type`,`label_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;