DELIMITER $$
DROP PROCEDURE IF EXISTS insert_dict  $$
CREATE PROCEDURE insert_dict()
BEGIN
	DECLARE pid INT;
    DECLARE pid03 INT;
    DECLARE pid0304 INT;
    DECLARE pid0305 INT;
    DECLARE pid0306 INT;
    DECLARE pid0308 INT;
    DECLARE pid05 INT;
    DECLARE pid06 INT;

	SET pid = (select id from t_dict where type = 'jz_ajlbdm_group');
	SET pid03 = (select id from t_dict where type = 'jz_ajlbdm' and dict_desc = '03000000');
	SET pid0304 = (select id from t_dict where type = 'jz_ajlbdm' and dict_desc = '03040000');
	SET pid0305 = (select id from t_dict where type = 'jz_ajlbdm' and dict_desc = '03050000');
	SET pid0306 = (select id from t_dict where type = 'jz_ajlbdm' and dict_desc = '03060000');
	SET pid0308 = (select id from t_dict where type = 'jz_ajlbdm' and dict_desc = '03080000');
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid, 'jz_ajlbdm', 05000000, "侵犯财产案", 0, 1, "05000000" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "05000000");
SET pid05 =  (select id from t_dict where type = 'jz_ajlbdm' and dict_desc = '05000000');
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid, 'jz_ajlbdm', 06000000, "妨害社会管理秩序案", 0, 1, "06000000" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "06000000");
SET pid06 =  (select id from t_dict where type = 'jz_ajlbdm' and dict_desc = '06000000');

INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid03, 'jz_ajlbdm', 03030202, "抽逃出资案", 03000000, 1, "03030202" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03030202");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid03, 'jz_ajlbdm', 03030800, "非国家工作人员受贿案", 03000000, 1, "03030800" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03030800");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid03, 'jz_ajlbdm', 03030900, "对非国家工作人员行贿案", 03000000, 1, "03030900" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03030900");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid03, 'jz_ajlbdm', 03031100, "非法经营同类营业案", 03000000, 1, "03031100" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03031100");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03040100, "伪造货币案", 03040000, 1, "03040100" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03040100");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03040201, "出售假币案", 03040000, 1, "03040201" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03040201");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03040203, "运输假币案", 03040000, 1, "03040203" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03040203");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03040402, "使用假币案", 03040000, 1, "03040402" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03040402");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03040800, "高利转贷案", 03040000, 1, "03040800" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03040800");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03040901, "骗取贷款案", 03040000, 1, "03040901" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03040901");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03041000, "非法吸收公众存款案", 03040000, 1, "03041000" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03041000");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03041101, "伪造金融票证案", 03040000, 1, "03041101" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03041101");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03041200, "妨害信用卡管理案", 03040000, 1, "03041200" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03041200");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03042900, "洗钱案", 03040000, 1, "03042900" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03042900");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0305, 'jz_ajlbdm', 03050200, "贷款诈骗案", 03050000, 1, "03050200" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03050200");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0305, 'jz_ajlbdm', 03050600, "信用卡诈骗案", 03050000, 1, "03050600" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03050600");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0305, 'jz_ajlbdm', 03050800, "保险诈骗案", 03050000, 1, "03050800" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03050800");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0306, 'jz_ajlbdm', 03060100, "逃税案", 03060000, 1, "03060100" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03060100");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0306, 'jz_ajlbdm', 03060300, "逃避追缴欠税案", 03060000, 1, "03060300" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03060300");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0306, 'jz_ajlbdm', 03060500, "虚开增值税专用发票、用于骗取出口退税、抵扣税款发票案", 03060000, 1, "03060500" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03060500");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0306, 'jz_ajlbdm', 03060600, "虚开发票案", 03060000, 1, "03060600" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03060600");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0306, 'jz_ajlbdm', 03060800, "非法出售增值税专用发票案", 03060000, 1, "03060800" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03060800");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0306, 'jz_ajlbdm', 03060901, "非法购买增值税专用发票案", 03060000, 1, "03060901" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03060901");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0306, 'jz_ajlbdm', 03061300, "非法出售发票案", 03060000, 1, "03061300" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03061300");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080300, "串通投标案", 03080000, 1, "03080300" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080300");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080400, "合同诈骗案", 03080000, 1, "03080400" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080400");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080500, "组织、领导传销活动案", 03080000, 1, "03080500" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080500");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080601, "非法经营烟草制品案", 03080000, 1, "03080601" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080601");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080602, "非法经营出版物案", 03080000, 1, "03080602" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080602");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080603, "非法经营药品案", 03080000, 1, "03080603" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080603");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080613, "非法买卖外汇案", 03080000, 1, "03080613" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080613");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080616, "非法经营电讯业务案", 03080000, 1, "03080616" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080616");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080618, "非法从事支付结算业务案", 03080000, 1, "03080618" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080618");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080622, "未经许可经营专营、专卖或限制买卖的其他业务、物品案", 03080000, 1, "03080622" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080622");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080627, "擅自设立互联网上网服务场所或从事上网服务经营活动案", 03080000, 1, "03080627" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080627");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid05, 'jz_ajlbdm', 05000700, "职务侵占案", 05000000, 1, "05000700" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "05000700");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid05, 'jz_ajlbdm', 05000800, "挪用资金案", 05000000, 1, "05000800" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "05000800");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid06, 'jz_ajlbdm', 06021900, "虚假诉讼案", 06000000, 1, "06021900" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "06021900");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid03, 'jz_ajlbdm', 03030601, "隐匿会计凭证、会计帐簿、财务会计报告案", 03000000, 1, "03030601" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03030601");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03041601, "擅自发行股票案", 03040000, 1, "03041601" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03041601");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080619, "利用POS机刷卡套现案", 03080000, 1, "03080619" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080619");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080607, "非法经营证券投资咨询业务案", 03080000, 1, "03080607" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080607");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03040401, "持有假币案", 03040000, 1, "03040401" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03040401");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid03, 'jz_ajlbdm', 03031800, "欺诈发行证券案", 03000000, 1, "03031800" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03031800");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0305, 'jz_ajlbdm', 03050500, "信用证诈骗案", 03050000, 1, "03050500" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03050500");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0306, 'jz_ajlbdm', 03060400, "骗取出口退税案", 03060000, 1, "03060400" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03060400");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0308, 'jz_ajlbdm', 03080610, "非法经营外汇期货案", 03080000, 1, "03080610" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03080610");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03040202, "购买假币案", 03040000, 1, "03040202" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03040202");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0305, 'jz_ajlbdm', 03050100, "集资诈骗案", 03050000, 1, "03050100" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03050100");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid0304, 'jz_ajlbdm', 03042400, "违法发放贷款案", 03040000, 1, "03042400" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "03042400");
INSERT  INTO t_dict ( `p_id`, `type`, `code`, `name`, `p_code`, `show_number`, `dict_desc`) SELECT pid06, 'jz_ajlbdm', 06030600, "偷越国（边）境案", 06030000, 1, "06030600" FROM dual WHERE NOT EXISTS (
        SELECT * FROM t_dict WHERE type = 'jz_ajlbdm' AND code = "06030600");

END $$
DELIMITER ;
CALL insert_dict();
Drop PROCEDURE if EXISTS insert_dict;