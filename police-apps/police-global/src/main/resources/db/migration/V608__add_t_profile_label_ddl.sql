DELIMITER $$
DROP PROCEDURE IF EXISTS `add_label` $$
CREATE PROCEDURE add_label()
BEGIN

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_label' AND column_name='code')
    THEN
ALTER TABLE t_profile_label ADD code varchar(100) NULL;
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '00')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '嫌疑人', NULL, '0', 'person', NULL, '1', 1, '-', '00');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '01')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被害人', NULL, '0', 'person', NULL, '1', 1, '-', '01');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '03')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被解救人', NULL, '0', 'person', NULL, '1', 1, '-', '03');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '04')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '违法犯罪可疑人员', NULL, '0', 'person', NULL, '1', 1, '-', '04');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '05')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '违法行为人', NULL, '0', 'person', NULL, '1', 1, '-', '05');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '06')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '犯罪嫌疑人', NULL, '0', 'person', NULL, '1', 1, '-', '06');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '07')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被通缉人员', NULL, '0', 'person', NULL, '1', 1, '-', '07');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '08')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '上网追逃犯罪嫌疑人', NULL, '0', 'person', NULL, '1', 1, '-', '08');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '09')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被布控人员', NULL, '0', 'person', NULL, '1', 1, '-', '09');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '10')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '收赃人员', NULL, '0', 'person', NULL, '1', 1, '-', '10');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '11')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被摸底排查人员', NULL, '0', 'person', NULL, '1', 1, '-', '11');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '12')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '发现人', NULL, '0', 'person', NULL, '1', 1, '-', '12');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '13')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '报案人', NULL, '0', 'person', NULL, '1', 1, '-', '13');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '14')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '举报、控告人', NULL, '0', 'person', NULL, '1', 1, '-', '14');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '15')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '检举揭发人', NULL, '0', 'person', NULL, '1', 1, '-', '15');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '16')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '扭送人', NULL, '0', 'person', NULL, '1', 1, '-', '16');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '17')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '提供线索人', NULL, '0', 'person', NULL, '1', 1, '-', '17');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '18')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '证人', NULL, '0', 'person', NULL, '1', 1, '-', '18');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '19')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '知情人', NULL, '0', 'person', NULL, '1', 1, '-', '19');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '20')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '见证人', NULL, '0', 'person', NULL, '1', 1, '-', '20');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '21')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被查封物品所有人/持有人', NULL, '0', 'person', NULL, '1', 1, '-', '21');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '22')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被扣押物品所有人/持有人', NULL, '0', 'person', NULL, '1', 1, '-', '22');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '23')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被查询服务标识号所有人/持有人', NULL, '0', 'person', NULL, '1', 1, '-', '23');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '24')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被冻结银行账号所有人/持有人', NULL, '0', 'person', NULL, '1', 1, '-', '24');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '25')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '被执行搜查人员', NULL, '0', 'person', NULL, '1', 1, '-', '25');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '26')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '保证人', NULL, '0', 'person', NULL, '1', 1, '-', '26');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '27')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '辨认人', NULL, '0', 'person', NULL, '1', 1, '-', '27');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '28')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '提供证据人', NULL, '0', 'person', NULL, '1', 1, '-', '28');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '29')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '监护人', NULL, '0', 'person', NULL, '1', 1, '-', '29');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '30')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '失踪人员', NULL, '0', 'person', NULL, '1', 1, '-', '30');
END IF;

IF NOT EXISTS( SELECT * FROM t_profile_label where module= 'person' and code = '99')
    THEN
INSERT INTO t_profile_label ( create_dept_id, create_user_id, create_time, update_user_id, update_dept_id, update_time, name, remark, create_type, module, pid, status, show_order, `path`, code) VALUES(NULL, NULL, '2024-05-31 18:30:09', NULL, NULL, '2024-05-31 18:30:09', '其他', NULL, '0', 'person', NULL, '1', 1, '-', '99');
END IF;

END $$
DELIMITER ;
CALL add_label;
DROP PROCEDURE IF EXISTS `add_label`;
