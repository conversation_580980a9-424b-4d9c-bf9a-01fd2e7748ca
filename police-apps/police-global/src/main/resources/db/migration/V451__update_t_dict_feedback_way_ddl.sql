DELIMITER $$
DROP PROCEDURE IF EXISTS `update_feedback_way_records` $$
CREATE PROCEDURE update_feedback_way_records()
BEGIN
    -- 查询每个 id 对应的 p_id
    SELECT DISTINCT id, p_id FROM t_dict WHERE type = 'feedback_way';

    -- 更新 t_dict 表中 type=feedback_way 记录的 code、p_code 和 p_id 字段
    UPDATE t_dict t1
    JOIN (
        SELECT DISTINCT id, p_id FROM t_dict WHERE type = 'feedback_way'
    ) t2 ON t1.id = t2.id
    SET t1.p_code = t2.p_id
    WHERE t1.type = 'feedback_way';
END $$
DELIMITER ;
CALL update_feedback_way_records;
DROP PROCEDURE IF EXISTS `update_feedback_way_records`;