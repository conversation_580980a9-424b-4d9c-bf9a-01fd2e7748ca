DELIMITER //
CREATE PROCEDURE AddSameColumnIfNotExists()
BEGIN
    DECLARE existing INT DEFAULT 0;

    SELECT COUNT(*)
    INTO existing
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_approval_node' AND COLUMN_NAME = 'is_same_org';

    IF existing = 0 THEN
        ALTER TABLE t_approval_node ADD is_same_org INT NULL COMMENT '是否是本单位或者上级单位';
    END IF;
END //
DELIMITER ;
CALL AddSameColumnIfNotExists();
DROP PROCEDURE IF EXISTS AddSameColumnIfNotExists;

DELIMITER //
CREATE PROCEDURE AddMsgColumnIfNotExists()
BEGIN
    DECLARE existing INT DEFAULT 0;

    SELECT COUNT(*)
    INTO existing
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_approval_process_config' AND COLUMN_NAME = 'is_send_operate_msg';

    IF existing = 0 THEN
        ALTER TABLE t_approval_process_config ADD is_send_operate_msg INT NULL COMMENT '是否每个流程都发送消息';
    END IF;
END //
DELIMITER ;
CALL AddMsgColumnIfNotExists();
DROP PROCEDURE IF EXISTS AddMsgColumnIfNotExists;


DELIMITER //
CREATE PROCEDURE UpsertApprovalProcessConfig(templateName VARCHAR(255), typeValue BIGINT, remark VARCHAR(1000), processNumberPrefix VARCHAR(10), isStopped BIT, startFormTemplate JSON, scope TINYINT, allowUsers JSON, allowRoles JSON, allowUnits JSON, isDeleted TINYINT, enable TINYINT, isSendOperateMsg INT)
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM t_approval_process_config WHERE template_name = templateName
    ) THEN
        INSERT INTO t_approval_process_config (template_name, `type`, remark, process_number_prefix, is_stopped, start_form_template, `scope`, allow_users, allow_roles, allow_units, is_deleted, enable, is_send_operate_msg)
        VALUES (templateName, typeValue, remark, processNumberPrefix, isStopped, startFormTemplate, scope, allowUsers, allowRoles, allowUnits, isDeleted, enable, isSendOperateMsg);
    END IF;
END //
DELIMITER ;
CALL UpsertApprovalProcessConfig('COMPOSITE_INITIATE_3_6_552566', 18, '区县-实战服务中心技侦', '发起协作', 0, NULL, 1, NULL, NULL, NULL, 0, 1, 1);
CALL UpsertApprovalProcessConfig('COMPOSITE_INITIATE_3_6_', 18, '区县-实战服务中心普通', '发起协作', 0, NULL, 1, NULL, NULL, NULL, 0, 1, 1);
CALL UpsertApprovalProcessConfig('COMPOSITE_INITIATE_3__552566', 18, '区县-普通技侦', '发起协作', 0, NULL, 1, NULL, NULL, NULL, 0, 1, 1);
CALL UpsertApprovalProcessConfig('COMPOSITE_INITIATE_3__', 18, '区县-普通', '发起协作', 0, NULL, 1, NULL, NULL, NULL, 0, 1, 1);
CALL UpsertApprovalProcessConfig('COMPOSITE_INITIATE_2_6_552566', 18, '市局-实战服务中心技侦', '发起协作', 0, NULL, 1, NULL, NULL, NULL, 0, 1, 1);
CALL UpsertApprovalProcessConfig('COMPOSITE_INITIATE_2_6_', 18, '市局-实战服务中心普通', '发起协作', 0, NULL, 1, NULL, NULL, NULL, 0, 1, 1);
CALL UpsertApprovalProcessConfig('COMPOSITE_INITIATE_2__552566', 18, '市局-普通技侦', '发起协作', 0, NULL, 1, NULL, NULL, NULL, 0, 1, 1);
CALL UpsertApprovalProcessConfig('COMPOSITE_INITIATE_2__', 18, '市局-普通', '发起协作', 0, NULL, 1, NULL, NULL, NULL, 0, 1, 1);
DROP PROCEDURE IF EXISTS UpsertApprovalProcessConfig;


DELIMITER //
CREATE PROCEDURE UpsertApprovalNodeConfig(configName VARCHAR(255), orderNumber INT, name VARCHAR(255), method TINYINT, nodeFormTemplate JSON, approvalUsers JSON, approvalRoles JSON, rejectType TINYINT, rejectTo INT, isLastNode TINYINT)
BEGIN
    DECLARE processConfigId BIGINT;
    SELECT id INTO processConfigId FROM t_approval_process_config WHERE template_name = configName;

    IF NOT EXISTS (
        SELECT 1 FROM t_approval_node_config WHERE process_config_id = processConfigId AND order_number = orderNumber
    ) THEN
        INSERT INTO t_approval_node_config (process_config_id, order_number, name, `method`, node_form_template, approval_users, approval_roles, reject_type, reject_to, is_last_node)
        VALUES (processConfigId, orderNumber, name, method, nodeFormTemplate, approvalUsers, approvalRoles, rejectType, rejectTo, isLastNode);
    END IF;
END //
DELIMITER ;
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3_6_552566', 1, '部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": null}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3_6_552566', 2, '分局领导人审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": 2}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3_6_552566', 3, '目标部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": false, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": null}]', 1, NULL, 1);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3_6_', 1, '实战服务中心审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSimpleOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": null}]', 1, NULL, 1);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3__552566', 1, '部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": null}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3__552566', 2, '分局领导人审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": 2}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3__552566', 3, '目标部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": false, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": null}]', 1, NULL, 1);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3__', 1, '部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": null}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_3__', 2, '实战服务中心审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": 6, \"promoterAreaType\": 3, \"targetParentDeptType\": 2}]', 1, NULL, 1);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2_6_552566', 1, '部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 2, \"targetParentDeptType\": null}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2_6_552566', 2, '目标部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": false, \"targetDeptType\": null, \"promoterAreaType\": 2, \"targetParentDeptType\": null}]', 1, NULL, 1);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2_6_552566', 2, '分局领导人审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 3, \"targetParentDeptType\": 2}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2_6_', 1, '目标部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 2, \"targetParentDeptType\": null}]', 1, NULL, 1);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2__552566', 1, '大队审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 2, \"targetParentDeptType\": null}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2__552566', 2, '支队审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 2, \"targetParentDeptType\": 4}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2__552566', 3, '目标部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": false, \"targetDeptType\": null, \"promoterAreaType\": 2, \"targetParentDeptType\": null}]', 1, NULL, 1);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2__', 1, '部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": null, \"promoterAreaType\": 2, \"targetParentDeptType\": null}]', 1, NULL, 0);
CALL UpsertApprovalNodeConfig('COMPOSITE_INITIATE_2__', 2, '目标部门领导审批', 1, NULL, '[]', '[{\"level\": null, \"roleId\": 3, \"isSameOrg\": true, \"targetDeptType\": 6, \"promoterAreaType\": 2, \"targetParentDeptType\": 1}]', 1, NULL, 1);
DROP PROCEDURE IF EXISTS UpsertApprovalNodeConfig;

