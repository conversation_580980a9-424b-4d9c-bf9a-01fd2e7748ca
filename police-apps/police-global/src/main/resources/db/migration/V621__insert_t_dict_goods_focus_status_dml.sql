DROP PROCEDURE IF EXISTS insert_goods_focus_status;
CREATE PROCEDURE insert_goods_focus_status()
BEGIN
    DECLARE pid INT;
    DECLARE pCode INT;
    INSERT INTO t_dict(`type`, `code`, `name`, `p_code`)
    SELECT 'goods_goods_focus_status_group', 0, '物品档案-关注状态',0
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'goods_goods_focus_status_group' AND code = 0
    );
    select id into pid from t_dict where type = 'goods_goods_focus_status_group' and code = 0;
    select code into pCode from t_dict where type = 'goods_goods_focus_status_group' and code = 0;

    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`, `p_code`)
    SELECT pid, 'goods_focus_status', 1, '短期关注',pCode
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'goods_focus_status' AND code = 1
    );
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`, `p_code`)
    SELECT pid, 'goods_focus_status', 2, '长期关注',pCode
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'goods_focus_status' AND code = 2
    );
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`, `p_code`)
    SELECT pid, 'goods_focus_status', 3, '重点关注',pCode
    FROM dual
    WHERE NOT EXISTS (
        SELECT * FROM t_dict
        WHERE type = 'goods_focus_status' AND code = 3
    );

END;
CALL insert_goods_focus_status();
DROP PROCEDURE IF EXISTS insert_goods_focus_status;