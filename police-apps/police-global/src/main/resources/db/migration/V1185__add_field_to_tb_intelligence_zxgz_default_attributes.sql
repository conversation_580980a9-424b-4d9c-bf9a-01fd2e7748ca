-- 增加字段
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_intelligence_zxgz_default_attributes' AND column_name='leibie')
THEN
    ALTER TABLE tb_intelligence_zxgz_default_attributes ADD leibie varchar(1000) NULL COMMENT '类别';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;

-- 调整Scheme
UPDATE tb_intelligence_attribute_templates SET fields='[{"key":"leibie","dbKey":"leibie","properties":{"required":false,"options":[]},"schema":{"title":"类别","type":"string"}},{"key":"phone","dbKey":"phone","properties":{"required":true,"options":[]},"schema":{"title":"电话","type":"string"}},{"key":"attachment","dbKey":"attachment","properties":{"required":false,"options":[]},"schema":{"title":"附件","type":"file"}}]' WHERE `type`='yaoqing' AND data_type='zxgz' AND data_class='default';


