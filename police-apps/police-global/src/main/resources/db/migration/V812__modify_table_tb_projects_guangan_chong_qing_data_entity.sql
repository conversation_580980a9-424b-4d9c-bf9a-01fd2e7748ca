DELIMITER $$
DROP PROCEDURE IF EXISTS `modify_column` $$
CREATE PROCEDURE modify_column()
BEGIN
    IF EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_projects_guangan_chong_qing_data_entity' AND column_name='jdccl_lxdm'
    )
    THEN
    ALTER TABLE tb_projects_guangan_chong_qing_data_entity CHANGE jdccl_lxdm jdccllxdm varchar(255) NULL;
    END IF;
    IF EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_projects_guangan_chong_qing_data_entity' AND column_name='sbe_pp_xhxid'
    )
    THEN
    ALTER TABLE tb_projects_guangan_chong_qing_data_entity CHANGE sbe_pp_xhxid sbe_pp_xh_xxid varchar(255) NULL;
    END IF;
END $$
DELIMITER ;
CALL modify_column;
DROP PROCEDURE IF EXISTS `modify_column`;


