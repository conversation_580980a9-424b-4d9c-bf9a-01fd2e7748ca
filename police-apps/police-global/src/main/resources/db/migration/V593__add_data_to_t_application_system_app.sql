DELIMITER $$
DROP PROCEDURE IF EXISTS `add_data` $$
CREATE PROCEDURE add_data()
BEGIN
    IF NOT EXISTS(SELECT * FROM t_application_system_app WHERE module_id = 123)
    THEN
        INSERT INTO t_application_system_app (name, module, url, icon, show_order, module_id, description, operation_name) VALUES('情指_指令', 'intelligence.zhiling', NULL, '["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", "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", "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"]', 12, 123, '指令', '[]');
    END IF;
    IF NOT EXISTS(SELECT * FROM t_application_system_app WHERE module_id = 124)
    THEN
        INSERT INTO t_application_system_app (name, module, url, icon, show_order, module_id, description, operation_name) VALUES('情指_线索', 'intelligence.xiansuo', NULL, '["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", "iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAAEi6oPRAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAASKADAAQAAAABAAAASAAAAACQMUbvAAARA0lEQVR4AdVcCXhURbY+tzud7uwkhBAgQdkReA8cFyYBMSwDqF+GAQEFZAQdHb7RAZKRRZ4MAWQRMAmMo7zRN4bgiPAAhwkIbkMATUBGcXniEKIsCRASsnc66XS676tTN3WXvre7b6c7iPV9nVt16tRf557aTp2qGwAdgWM8KRlNPIvLn8U5ERxlkjO8v9oFFosZ7l/mEHkNYqw9smHz11D/2hIFWWRaP+UMpEWcgULrCPjlxfWUKaXPFfoUmUal3guZ3f5XgbD2yV40rZJJwdWeEJFY5ui+V2Bt+pcsSZ9yFVwllB6KXIADRAW/cqMBEHVccye2y2SdDMAdds80mGzRn27q1mgQFCkwHHvJRPmObjDSp8sR3oD5ouBvL6yECcubaebY552AaRY4eZMw4sDQMihpTWZJEJFECoksT3hbnpSY5PBPlC/zXN2YfuVw/PskiLHYob7FLKLJlanqT9iXkFNkEou1R1IzbW/xPD/HnU564Oqi7IgsFR0JWlpwZ/SRbtbU0vpHrkPBfwm6dwdAusnocieHqYC2zyuFFbu7w7T1Jijq94yiAKbT14XB/QOuw5QR5Yq89r6kPSgVnD4SKon2LW0URUcJ+pgqKMSbSRtFCXdnNkB4aJsC2qNE+Irvf2mGd7+Uut3mmaUQFQ6wIK+/AgQTFCh1sfUdnuMeUeXqJPAcTPHYjxBDo1u0kQ4ojDedlYhsqRnWiaMymu8TCR4imhLNmMEby5NsSm22A7Ah4Y6nACKv0kQYiDr1BP5EcU7kGMZJgTR0wfJ1PVFKVT9iJbfNrwU2jyEN4+OG1LNs1VMTCMfT4c9dihUFV5f5E1xw8IUWFQgSVEA4e+F4Ovx1V3in9xqx0LSY4zB3WywcOXkDNs9SjjNNoIaGBloYh8er1VNEoKfjCmD/bSvhlY+SIMqslkolUfZ7cbRwqb0XHG8aLgJNvrAZTOCk6SuV2LjK0Hmt9tpTdbQq97kIiYwmb00ml+rVkhKEWb+VV05sCIJLBwamRwaCzxB5AuM3quvg2Qk1kPZRriIr9fs/0zS26uNbuyjyMKGSaP72HjAsqRlwPpKHbpEC7dApG9RYQ+VZNK6SCKls4lr54Few9j2h5R4adonQB6sAGCEYrWYnY82iGP2jMpruJgvNaVaLt2dDTLj52yyulfEogBgRnz9fbF3KcdxLchoP3CMnc8L3yGks7hGIMXh6pmTYegHHHwEehrnzkAqP2oxh6V9v4dRDwJ3ZLa1LIK23dMPRk2wycK70T7OjjnpjVgg08ve2JEMIeWuAod4KBTOP42FdUW7ECwxTFCjQ2Z8BBvLEFURznHkCnTvqBsydJMyeyFNVbYVF/2OBG7IBvDS9EsbfEy/ak06nCx5Yrb8aXRpKH1EFj08QzIbpW9TTkKcXQPqjI6/D9PuEedJXWdSQT4EW/+IiDO9rhF0nQuGDs91p3WMjz8C67m/QOK4zvy5fQeP4h83oOEmn/SDNr2/+tozyzP9vyWQTC7VHdAm0dda30NzKwfJ9Q2gxXHZ7m67D8oqnFQueOzgTjE3ymI9YLWRDuWyv9pjRJZB7RZ2ZRoFUq1BnVqgH26dAH6xxAf5YQOsIm+NInyWMpHommyopD2s2ZBiU2ERxVj1cpeKXE3x26sGJjfDiY06wO5wwZ2tXsSxaWokhNVDdFgPplwQfAWYyIdDK+Le9N+WPj2qF7b+10bi3keZXH2KjpKTcCRsO3U7Bff2JCXNA7q+FHVXhNwA7ijyPMMTySyBW+TNpJTCop7ChIPtlqG7kYM1BYQQiD44ksrpTdsxffaAf1NgsrLjXZ4cE8ooYYKZCIIaV+lxjAu80FJD0vYzWWU+iwKyTuRGr5fhip5YTteIpmbYZwPNvkTy15alVQEm76OSMkz7LtpQoyeqUboHURZHCE2eS7VUSWSDlcxuLc8Kfl9L+xQIUCAC3zWVJzXkcx7cWZ4f/hjgOVB4lf0TqsEApGVYyM3KbNCojFiw/rzg7Ml8jzyfJL4FSFlkngYHbT1DDfSILDBecRsP4z7aEXdDJL9pRHvmDZtbykF+cGz7PV5NqakhwpzQTlfOzPUoaSAYPTxbnRvxVC0IhUGqmdRnPcxu1GDuJdtnlNE44tc1ynuFTgYiBjytfGCP+GE+cpbFeZn78qMLIFaB/O9BeKnVAA6x7IhYMBuFd5Icc76/hwWIWJvL/O18Fz7zh34ZAriG5kB7jh1a2QtbcSHA4HNDSYocjn15V8M7e1AZlV6vBbrfDgN7R1CBbMN67QaYAIAnWh7zOrkYy+f59heDysja1wqzcGHccVXrckFrImCqYHReuWGFhXjcVj5zg3ofkear47j/U07c+8Em9LmEQ4J9nY6mDEDXZM94Ev7lfdYajqgcJrFNrZiJx+UNl0NraCp//uxHeOJYo8qFt/Y/bpf0YZuCxF26TTJxgwCFtxstdaPlxw42k3yHFe/DJMigJqHY2HJTMz8K+i+G5+N0Qb1Q6Z5+IPUz3bMf6LqLCsapzDhgpxsp03yuIV4HSBlZQoEOnGTRQN2oo54AGVzjIN4HIgTtY3EBiWJ/4hqip05e6UpwesYLnlDJ4+ONVoJF9ainQ3i8k7eBOAwO6ZrUCum+ZUDuT14ssV244aNOJBA8RrwJdqzNAVT3Z+8oCbnvmXF4po6ijKNRlR3fYXp0uZr75STJU17eKaU8RXcPeU+Fg0v0a9sGs2BeW1ybzVbgz8r0K9OFa4exFXjFulf+apPDyyrPFOPLJ5yk8JdA6BRALtEe8CnTsiyo6yob0VHp3B5svAzoUPAXmiMirnSyy9EuOplgiwUPEq0CbC+LoUF01S5o/2FHF7t6rVZrCk1HUTLTBBuhB218/hlZrNLgojpWaXR4kaSd7FcjeZoTm5hYwci6Ij7TTIujRYEKhpuRhW89tNFnRFqdw5+U/W0usgxZ4YafXNZyW9Tns4yJa4ZUn6yjz7G0J8vp1xZ8dXw6pQ0OpO2f+a+63KSQI3cO+pikUjpy20/ZHl0y0WTlRSpDq2PMPXYS7+vO0rDdh5CV1WYz5xcngdF6C+4Y4YNOsS9BALPBN7yUR/7Rg78gBMT7tznKYOFywn+z2NvF8yZ1PK+2zyeSF4sJbYNWU7ymJ+YAWvi35hlaln4W4SKGfYH7J1RB4pXCgHMJjnDWZLg0xFHQ8Ldo1lF4VmTriAvRLELTA8j/+NgbSBlfBiZJ4OFZK7JYOBL801AF83UWYhoRhzwEuy7g3+1ECaeR9rGKqIZbAJ7lrs5D4BrfKaZ0ULzWCcdInOZYf5PgqgeSZKYutOcSDuVhOCzDuIJ19VlF2uKgRdzyvAjHmtCzeYq+3vUPS0rUAlqnjSZrkxZM5Ed6tunYcXQLJ67wn05YcwvMHCe0/5XSN+CFzTPj0wixOORQ1GOUkvwWSFx75h+bRBpfr74RGXfwErMxpcE0+9XLUWTmfP/GABPKnIi3e0ZnW4W28YS4H9MKatOmTmLHD7+B5Q/7J3LCLEvnmxW6KgtJ+x0faLU3TiBN5Lnm1ceTn1erx8fpW4mnYy3OGvOIcy3FfHkIfWD6zg64gcq9tBKl1Lrnbht5JrV7hU6gOMJB5hT9O7sTlWVoi9ha+ylk7gKFZpMMKGrW0KsrpCJvGgYH0Cn4sQQ+kV2gKFwTiJeL+yueMxh1Fmy2C0eQnqC4FpWQ23UlOgYgiaK8QDu79rOgWYreRc4/9HOfKK46OOApZZPfiJSgURHtFW9jDHJk4Sa9II+VuxV7h5XUCyvqUnCXm8c6WPaf+1FW4YkjgFNY03gAXNCaY6AFV99MrPIrsSEZxIZbXiehix1EoqLPfqVcXO0wa3kSu2Rigf3IUmM0h8Nb71ZB3XLqMJJdh3pgaeGxSV+IQcEJpWQN8c8EJh7+KhPIa6Va8nL8z4p2qoKS4Fnh+WjPc0bcrmcLIyOfCyFPY5eIGEmmT7g4lCtJ+tYl3hZKDS46cm4TAsP5x5Acw6xcS7/lLNbD53VAordR7sCmV1RsTuxIWCMZ9OHTSL3moEtJ+FquQAZVRXWeDDz53wMEzXaDW5t+F8y5hbfDAiDqY+DMjJMZHKrAx8a/vamHNvnhwOAOfNtleFXGDqqCMydfg/hFRiCuGMyWN8PKhbmC1B7ezhoY4IWNyFaQMUyrrq/NWWLUvMPMr6ApKiG6B7MfJ5xchUut9+IWdHBn1FBXVmZGZ91TAw6OlHuly8fDHXWFwvjKiQ9UGVUFDezTAsqmS4Wqzu2DpW4nQoNFj0DG6PvF1ej2MSe5+TYzR8Vlw2wroGiIde6EDdUXFU+L1MTkv9qgts69Cl0jhmyzMe/3DUDhxPl7OpiseNAWFmdrg5TmXIbS95zTYXPDcrj7gIja/PKBitvfKBjxakwf0Kq+9/jhccGgPCXR1r+y+A9w9zuj2XnAlU1NRL077HhLjBCXhvLf23W5wuUY5DOUyaMWDpqD5qT/A3f2kl95Q0AvK65TC4GksHoCygGePT5UvgTKHf15uPLx4PWkzPRdgWFtuPCKeDzBaJHFUb5zxA1kxBUrpNQPkfDSAZet6yhUkTRq6iiqZBiVaqRccbwp8c4lXKQe558UeEQthj8GzUH+VgwBYBsuesyeLeHJsRrTaTVB8TjhlRrm6RTWzrA49FWMhGMt8h6S4xQoFrQfdYu/VKeIENMQ6RaJbDDQgBW16rBaObgiB91aRL/0GS8ux/B3xegwu1/lJ68VPU+X5/sZxZUMsxERsrZB2RwMcznLBx+sMsDS9WotFNy0gBXWLMZL9FPm4nuyVls4M17wLtKBrAbVl+puvwN96r4UxEV/pFs6dEcsiBmKhfYTYWuG56WFgDkXLnYf/6BOqxaKbFpCC9hfhPbgW+jOQ6X7hRPU9grllKwANPBY2Jv6Fftzgj6KQF+8hYFkWEBOx3cOiSZVkgyucMeMqtv8T4STfnU9vOuBVbMWUCrj3Dsn22VNog7dPqm0c/HJoVcIOlbHo/hWRXHAcSthb5AGNxCxiXBYSJ6d7ePTnlfBomrSzP/1dI6w74PlWgHt5lpavYgErCEGXPHgF7h4kHdafL2+BP+7tqbKokRev6qVHF8HMmEIIN5A93I2Z9D9XYJ57wDkG/2mFzWWBPfVpUNCQCg5evenFHrNm+lUYkCTJ8EVJC7x0qJc7pK500BWEtY4j5/LzxklWNdKKzjrhL8eTweXV64ucHQvoWnl6TBmkDpH2X4iUfzQEPvpO3Yv11tIpCsLKUeBnxpIP8/ooRWkmG9h//CscPj6XGLCysLdMuOM6pN9lgzCzcgr9+gIPrxwle8EAG6TTFCRXy7hB1yD9znoINSlGschSR/7jRWlFKBz4sqfHT5bwisuvRlyF/j0cECNNLSIGRlodPBSciYZ/nguea+WmKEj+FgMS6iBt4HUYmGgHo7LR6ZeSK97VPuffMPUbMJuUBwjk21goqTBD4bkEOF8VK68maPGbrqCgSX6TgOQKcmvPmyTBT6gazQlCdtlgDnmX8eSnXCZ+Qi+oQ9QiPJ42u1p3F+bGClcyZYU0FSTLV0RTMlr687xzDllJZpNDHH0X/hQIP2qiluw89hAzO598ClikVxK/FKQJmsUbRjY2jTe4OOxtU8kvWpPv5hJPEWXsdDqc73z25+iAdquBK8jDi49eXhfrsptm8IAf6XFjCFtn1FVPcPdyLkN+0VbLCVKFcsnzIJs/5M4Q2mv9ozIah7p4wxyiLqI4uM0rsyyTCPo5GdY7OaNzV9GWKPWuWMYbzOhNV5CW8Hc9zZtMEbbJxD0xmwNuLOkHhwFcO4u6RBb6up6ihRdM2v8D7WYadsnt2PEAAAAASUVORK5CYII=", "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"]', 13, 124, '线索', '[]');
    END IF;
END $$
DELIMITER ;
CALL add_data;
DROP PROCEDURE IF EXISTS `add_data`;
