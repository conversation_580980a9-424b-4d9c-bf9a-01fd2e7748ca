CREATE TABLE if not exists`t_auth_center_backend_manage` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '标题',
  `content` text COLLATE utf8mb4_bin COMMENT '内容',
  `type` int DEFAULT NULL COMMENT '类型 1：通知通报，2：技战法，3：典型案例',
  `status` tinyint DEFAULT NULL COMMENT '状态',
  `attachments` json DEFAULT NULL COMMENT '附件',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='权限中心后台管理表';