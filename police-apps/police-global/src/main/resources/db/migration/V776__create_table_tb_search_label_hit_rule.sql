-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `tb_search_label_hit_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cr_user` varchar(255) NOT NULL,
  `cr_time` datetime NOT NULL,
  `cr_dept_id` bigint NOT NULL,
  `is_del` tinyint DEFAULT '0',
  `update_user` varchar(255) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_dept_id` bigint DEFAULT NULL,
  `label_name` varchar(255) DEFAULT NULL,
  `archives_type` varchar(255) DEFAULT NULL,
  `hit_rule_desc` longtext,
  `hit_rule_detail` longtext,
  PRIMARY KEY (`id`),
  KEY `tb_search_label_hit_rule_label_name_IDX` (`label_name`,`archives_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;