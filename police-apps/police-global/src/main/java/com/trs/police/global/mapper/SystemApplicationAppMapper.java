package com.trs.police.global.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.global.entity.ApplicationSystemApp;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/11/22
 */
public interface SystemApplicationAppMapper extends BaseMapper<ApplicationSystemApp> {
    /**
     * 获取app全部应用
     *
     * @param notAppIds 排除的AppId
     * @param perNames  权限位
     * @return 全部应用
     */
    List<ApplicationSystemApp> selectAll(
            @Param("notAppIds") Collection<Long> notAppIds,
            @Param("perNames") Collection<String> perNames
    );

    /**
     * 根据角色id和部门id获取权限模块名字
     *
     * @param roleIds 角色id
     * @param deptId  部门id
     * @return 权限模块名字
     */
    List<String> selectPerName(@Param("roleIds") Set<Long> roleIds, @Param("deptId") Long deptId);
}
