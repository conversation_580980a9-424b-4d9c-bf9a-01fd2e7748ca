package com.trs.police.global.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * poi点
 *
 * <AUTHOR>
 * @date 2022/09/13
 */
@Data
@TableName("t_location")
public class Location implements Serializable {

    private static final long serialVersionUID = 6279436579651606783L;

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("name")
    private String name;

    @TableField("category")
    private String category;

    @TableField("type")
    private String type;

    @TableField("lng")
    private Double lng;

    @TableField("lat")
    private Double lat;

    @TableField("province")
    private String province;

    @TableField("city")
    private String city;

    @TableField("district")
    private String district;
}
