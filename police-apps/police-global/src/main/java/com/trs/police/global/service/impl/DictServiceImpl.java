package com.trs.police.global.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.ProfileLabelPoliceKindRelationEntity;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.ProfileLabelPoliceKindRelationMapper;
import com.trs.police.common.core.vo.*;
import com.trs.police.common.openfeign.starter.service.FightService;
import com.trs.police.common.redis.starter.service.RedisService;
import com.trs.police.global.constant.DictConstant;
import com.trs.police.global.converter.DictConverter;
import com.trs.police.global.entity.Dict;
import com.trs.police.global.entity.JwzhDictEntity;
import com.trs.police.global.mapper.DictMapper;
import com.trs.police.global.mapper.JwzhDictMapper;
import com.trs.police.global.service.DictService;
import com.trs.police.global.vo.DictVO;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.global.constant.CaseTypeConstant.IMPORTENT_LEVEL;
import static com.trs.police.global.constant.CaseTypeConstant.INTELLIGENT_CLASSIFICATION;


/**
 * 字典服务
 *
 * <AUTHOR>
 * @since 2022/4/11 9:43
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class DictServiceImpl implements DictService {

    public static final String CODE_SUFFIX = "_group";
    public static final String KEY_PREFIX = "dictList::";

    private final DictMapper dictMapper;

    @Autowired
    private DictConverter dictConverter;

    @Autowired
    private FightService fightService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedisService redisService;
    @Resource
    private JwzhDictMapper jwzhDictMapper;
    @Resource
    private ProfileLabelPoliceKindRelationMapper profileLabelPoliceKindRelationMapper;

    @PostConstruct
    private void deleteRedisCache() {
        log.info("删除字典相关缓存");
        redisService.delByPrefix(KEY_PREFIX, "dict");
    }

    @Override
    @Cacheable(value = {"dict"}, key = "#type+'-'+#code", unless = "#result == null")
    public DictDto getDictByCodeAndType(Long code, String type) {
        Dict dict = dictMapper.getByCodeAndType(code, type);
        return Objects.isNull(dict) ? null : dict.toDto();
    }

    @Override
    @Cacheable(value = {"dict"}, key = "#type+'-'+#name", unless = "#result == null")
    public DictDto getDictByTypeAndName(String type, String name) {
        Dict dict = dictMapper.selectByTypeAndName(type, name);
        return Objects.isNull(dict) ? null : dict.toDto();
    }

    @Override
    @Cacheable(value = {"dict-desc"}, key = "#type+'-'+#desc", unless = "#result == null")
    public DictDto getDictByTypeAndDesc(String type, String desc) {
        Dict dict = dictMapper.selectByTypeAndDesc(type, desc);
        return Objects.isNull(dict) ? null : dict.toDto();
    }

    @Override
    @Cacheable(value = {"dictList"}, key = "#type")
    public List<DictDto> getDictListByType(String type) {
        if (!type.endsWith(CODE_SUFFIX)) {
            type += CODE_SUFFIX;
        }
        Dict dict = dictMapper.getByCodeAndType(0L, type);
        return Objects.isNull(dict) ? Collections.emptyList() : dict.toDto().getChildren();
    }

    @Override
    public List<DictDto> getDictByType(String type) {
        String child = type.replace(CODE_SUFFIX, "");
        List<Dict> dictList = dictMapper.findDictList(child, null, null, null, null);
        List<Long> codeList = dictList.stream().map(Dict::getCode).collect(Collectors.toList());
        Map<Long, List<LabelPoliceKindRelationVO>> listMap = profileLabelPoliceKindRelationMapper.selectByCode(codeList)
                .stream()
                .collect(Collectors.groupingBy(
                        LabelPoliceKindRelationVO::getCode,
                        Collectors.mapping(
                                vo -> new LabelPoliceKindRelationVO(vo.getId(), vo.getName()),
                                Collectors.toList()
                        )
                ));
            // 将listMap中的数据整合到dictList中
        dictList.forEach(dict -> {
            // 根据code从listMap中获取labels
            List<LabelPoliceKindRelationVO> labels = listMap.get(dict.getCode());
            if (labels != null) {
                dict.setLabels(labels);
            }
        });
        List<DictDto> dtoList = dictList.stream().map(Dict::toDto).collect(Collectors.toList());
        Map<Long, List<DictDto>> map = dtoList.stream()
                .filter(dto -> dto.getPid() != null)
                .collect(Collectors.groupingBy(DictDto::getPid));
        dtoList.forEach(dictDto -> dictDto.setChildren(map.get(dictDto.getId())));
        return dtoList.stream()
                .filter(dto -> Objects.equals(0L,dto.getPCode()))
                .sorted(Comparator.comparing(DictDto::getShowNumber, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    @Override
    public DictDto getDictById(Long id) {
        return dictMapper.getById(id).toDto();
    }

    @Override
    public List<Long> getPath(String type, Long code) {
        Dict dict = dictMapper.getByCodeAndType(code, type);
        List<Long> path = new LinkedList<>();
        while (!dict.getPId().equals(dict.getId())) {
            path.add(0, dict.getCode());
            dict = dictMapper.selectById(dict.getPId());
        }
        return path;
    }

    @Override
    public List<Long> getTree(String type, Long code) {
        Dict dict = dictMapper.getByCodeAndType(code, type);
        List<Long> path = new LinkedList<>();
        path.add(dict.getCode());
        List<Dict> children = dictMapper.getByParentId(dict.getId());
        if (!children.isEmpty()) {
            path.addAll(children.stream().map(Dict::getCode).collect(Collectors.toList()));
        }
        children.forEach(c -> {
            path.add(c.getCode());
            path.addAll(getTree(type, c.getCode()));
        });

        return path.stream().distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addDictByType(String type, DictVO dictVO) {
        String child = type.replace(CODE_SUFFIX, "");
        if(dictMapper.selectList(new QueryWrapper<Dict>()
                .eq("type", child)
                .eq("code", dictVO.getCode())).size() > 0)
        {
            throw new TRSException("当前类型下编码已存在");
        }
        List<Dict> dictList = dictMapper.findDict(child, null, null, null);
        Dict dict = new Dict();
        dict.setType(child);
        dict.setName(dictVO.getName());
        dict.setCode((long) (dictList.size() + 1));
        if (Objects.nonNull(dictVO.getPid())) {
            Dict dict1 = dictMapper.selectById(dictVO.getPid());
            List<Dict> children = dictMapper.selectList(
                    Wrappers.lambdaQuery(Dict.class).eq(Dict::getPId, dictVO.getPid()).notIn(Dict::getId, dictVO.getPid()));
            dict.setPId(dictVO.getPid());
            dict.setPCode(dict1.getCode());
            dict.setShowNumber(children.size() + 1);
        } else {
            Dict groupDict = dictMapper.getByCodeAndType(0L, type);
            dict.setPCode(0L);
            dict.setPId(groupDict.getId());
            dict.setShowNumber(groupDict.getChildren().size() + 1);
        }
        dictMapper.insert(dict);
        //关联警种和标签的关联关系
        if (StringUtils.isNotEmpty(dictVO.getLabelId())){
            List<Long> labelIdList = Arrays.stream(dictVO.getLabelId().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            for (Long labelId : labelIdList) {
                ProfileLabelPoliceKindRelationEntity relationEntity = new ProfileLabelPoliceKindRelationEntity();
                relationEntity.setLabelId(labelId);
                relationEntity.setPoliceKindId(dict.getCode());
                profileLabelPoliceKindRelationMapper.insert(relationEntity);
            }
        }
        redisService.delByPrefix(KEY_PREFIX + child, "profileSchema");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void editDictByType(String type, DictVO dictVO) {
        //关联警种和标签的关联关系
        if (StringUtils.isNotEmpty(dictVO.getLabelId())){
            List<Long> labelIdList = Arrays.stream(dictVO.getLabelId().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            //先根据policeKindId删除所有关联关系
            profileLabelPoliceKindRelationMapper.delete(
                    Wrappers.lambdaQuery(ProfileLabelPoliceKindRelationEntity.class)
                            .eq(ProfileLabelPoliceKindRelationEntity::getPoliceKindId, dictVO.getCode()));
            //再重新建立关联关系
            for (Long labelId : labelIdList) {
                ProfileLabelPoliceKindRelationEntity relationEntity = new ProfileLabelPoliceKindRelationEntity();
                relationEntity.setLabelId(labelId);
                relationEntity.setPoliceKindId(dictVO.getCode());
                profileLabelPoliceKindRelationMapper.insert(relationEntity);
            }
        }
        Dict dict = dictMapper.selectById(dictVO.getId());
        dict.setName(dictVO.getName());
        dictMapper.updateById(dict);
        redisService.delByPrefix(KEY_PREFIX + type.replace(CODE_SUFFIX, ""), "profileSchema");
    }

    @Override
    public Long searchTypeCode(String type) {
        String child = type.replace(CODE_SUFFIX, "");
        List<Dict> dict = dictMapper
                .selectList(Wrappers.lambdaQuery(Dict.class).eq(Dict::getType, child).orderByDesc(Dict::getCode));
        return dict.stream().findFirst().orElse(new Dict()).getCode() + 1;
    }

    @Override
    public Boolean checkNameRepeat(String type, String name, Long id) {
        String child = type.replace(CODE_SUFFIX, "");
        List<Dict> repeat;
        List<Dict> byType = dictMapper.getByType(child);
        if (Objects.nonNull(id)) {
            repeat = byType.stream().filter(c -> c.getName().equals(name) && !c.getId().equals(id))
                    .collect(Collectors.toList());
        } else {
            repeat = byType.stream().filter(c -> c.getName().equals(name)).collect(Collectors.toList());
        }
        return !repeat.isEmpty();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public List<DictDto> editDictShowOrder(String type, List<DictDto> dictVO) {
        List<Long> ids = new ArrayList<>();
        getAllIds(ids, dictVO);
        Dict root = dictMapper.getByCodeAndType(0L, type);
        extracted(type, dictVO, root.getId());
        redisTemplate.delete(KEY_PREFIX + type);
        redisTemplate.delete(KEY_PREFIX + type.replace(CODE_SUFFIX, ""));
        return getDictListByType(type);
    }

    @Override
    @Cacheable(value = {"jwzhDictVOList"}, key = "#type")
    public List<JwzhDictVO> getJwzhDictVO(String type) {
        return jwzhDictMapper.selectTree(type);
    }

    @Override
    @Cacheable(value = {"jwzhDict"}, key = "#type+'-'+#code", unless = "#result == null")
    public JwzhDictVO getJwzhDict(String type, String code) {
        List<JwzhDictVO> jwzhDicts= jwzhDictMapper.selectByTypeAndCode(type, code);
        if (CollectionUtils.isEmpty(jwzhDicts)) {
            return null;
        }
        return jwzhDicts.get(0);
    }

    @Override
    public List<JwzhDictVO> getJwzhDict(String type, List<String> codes) {
        List<JwzhDictEntity> jwzhDictEntities = jwzhDictMapper.selectList(Wrappers.lambdaQuery(JwzhDictEntity.class)
                .eq(JwzhDictEntity::getZdbh, type)
                .in(JwzhDictEntity::getDm, codes));
        if (CollectionUtils.isEmpty(jwzhDictEntities)) {
            return new ArrayList<>(0);
        }
        return jwzhDictEntities.stream().map(jwzhDictEntity -> {
            JwzhDictVO vo = new JwzhDictVO();
            BeanUtils.copyProperties(jwzhDictEntity, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = {"jwzhDictList"}, key = "#type")
    public List<CodeNameVO> getJwzhDictByType(String type) {
        return jwzhDictMapper.selectTree(type).stream().map(item -> new CodeNameVO(item.getDm(), item.getCt()))
                .collect(Collectors.toList());
    }

    /**
     * 递归
     *
     * @param type   类型
     * @param dictVO 参数
     * @param pid    id
     */
    private void extracted(String type, List<DictDto> dictVO, Long pid) {
        for (int i = 0; i < dictVO.size(); i++) {
            DictDto dictDto = dictVO.get(i);
            Dict dict = new Dict();
            dict.setId(dictDto.getId());
            dict.setType(type.replace(CODE_SUFFIX, ""));
            dict.setCode(dictDto.getCode());
            dict.setName(dictDto.getName());
            dict.setShowNumber(i + 1);
            dict.setPId(pid);
            dictMapper.updateById(dict);
            if (!dictVO.get(i).getChildren().isEmpty()) {
                extracted(type, dictDto.getChildren(), dict.getId());
            }
        }
    }

    /**
     * 遍历id
     *
     * @param ids    id
     * @param dictVO 参数
     */
    private void getAllIds(List<Long> ids, List<DictDto> dictVO) {
        dictVO.forEach(c -> {
            if (Objects.nonNull(c.getId())) {
                ids.add(c.getId());
            }
            if (!c.getChildren().isEmpty()) {
                getAllIds(ids, c.getChildren());
            }
        });
    }

    @Override
    @Cacheable(value = {"dictListByTypeAndCode"}, key = "#type+'-'+#code", unless = "#result == null")
    public List<DictDto> getChildByTypeAndCode(String type, Long code) {
        return Objects.isNull(code) ? Collections.emptyList()
                : dictMapper.getByParentId(this.getDictByCodeAndType(code, type).getId()).stream().map(Dict::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据类型（type）和父节点ID（parentId）查询码表信息。
     *
     * @param type     码表类型
     * @param pCode 父节点code
     * @param keyValue 关键字
     * @param names 名字列表
     * @return 码表信息列表
     */

    @Override
    public List<Dict2VO> commonSearch(String type, Long pCode,Long pId,String keyValue, String names) {
        // 一级列表
        List<String> namesList = StringUtils.isEmpty(names) ? null : Stream.of(names.split(",|;")).collect(Collectors.toList());
        List<Dict> dictList = dictMapper.findDictList(type, pCode, pId, keyValue, namesList);
        if(dictList.isEmpty()){
            //如果为空，返回一个空集合
            return new ArrayList<>();
        }
        if (IMPORTENT_LEVEL.equals(type) || INTELLIGENT_CLASSIFICATION.equals(type)){
            dictList.forEach(dict -> dict.setDictDesc(String.valueOf(dict.getCode())));
        }
        List<Long> ids = dictList.stream().map(Dict::getId).collect(Collectors.toList());
        // 二级列表（用来设置是否含有子节点参数）
        List<Dict> dictLists = dictMapper.selectList(new QueryWrapper<Dict>().in("p_id", ids));
        Map<Long, List<Dict>> map = dictLists.stream().collect(Collectors.groupingBy(Dict::getPId));
        List<Dict2VO> collect = dictList.stream()
                .map(dictConverter::convertDictToDict2VO)
                .peek(dict2VO -> {
                    if (map.containsKey(Long.valueOf(dict2VO.getId()))) {
                        dict2VO.setHisChildren(true);
                    }
                })
                .collect(Collectors.toList());
        Comparator<Dict2VO> comparator = Comparator.comparing(Dict2VO::getShowNumber,Comparator.nullsLast(Integer::compareTo));
        Collections.sort(collect, comparator.reversed());
        return collect;
    }

    /**
     * 根据codeList获取码表集合（不递归）
     *
     * @param typeList 类型集合
     * @return dto集合
     */
    @Override
    @Cacheable(value = "dictListByTypeList", key = "#typeList", unless = "#result == null")
    public List<DictDto> getDictListByTypeList(List<String> typeList) {
        if (CollectionUtils.isEmpty(typeList)) {
            return new ArrayList<>();
        }
        QueryWrapper<Dict> queryWrapper = new QueryWrapper<Dict>()
                .in("type", typeList)
                .ne("code", 0)
                .eq("status", 1)
                .orderByAsc("show_number");
        List<Dict> dicts = dictMapper.selectList(queryWrapper);
        return dicts.stream()
                .map(Dict::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "dictListTreeByTypeAndFlag", key = "#type + #flag", unless = "#result == null")
    public List<DictDto> getDictListByTypeAndFlag(String type, String flag) {
        String groupType = type;
        if (!groupType.endsWith(CODE_SUFFIX)) {
            groupType = type + CODE_SUFFIX;
        }
        QueryWrapper<Dict> queryWrapper = new QueryWrapper<Dict>()
                .eq("type", groupType)
                .eq("status", 1)
                .eq("code", 0);
        List<Dict> groupDicts = dictMapper.selectList(queryWrapper);
        Dict groupDict = groupDicts.get(0);
        List<Dict> dicts = dictMapper.getDictListByPidAndFlag(groupDict.getId(), flag);
        List<DictDto> childrens = dicts.stream()
                .map(Dict::toDto)
                .collect(Collectors.toList());
        return childrens;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteDictByType(String type, Long id) {
        Dict dict = dictMapper.selectById(id);
        if (dict == null){
            throw new TRSException("要删除的字典已经不存在");
        }

        List<Dict> dictList;
        dictList = dictMapper.getByParentId(id);
        if (!dictList.isEmpty()) {
            throw new TRSException("要删除的字典存在子级，不允许删除！");
        }

        if (dict.getStatus().equals(DictConstant.STATUS_ENABLE)) {
            dict.setStatus(DictConstant.STATUS_DISABLE);
        } else {
            throw new TRSException("当前字典已经停用，无法停用");
        }

        dictMapper.updateById(dict);
        redisService.delByPrefix(KEY_PREFIX + type.replace(CODE_SUFFIX, ""), "profileSchema");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void remove(String type, Long id) {
        Dict dict = dictMapper.selectById(id);
        if (dict == null){
            throw new TRSException("要删除的字典已经不存在");
        }
        Long num = fightService.getPlanNum(dict.getCode());
        if (num > 0){
            throw new TRSException("当前字典存在数据，无法停用");
        }
        if (dict.getStatus().equals(DictConstant.STATUS_ENABLE)) {
            dict.setStatus(DictConstant.STATUS_DISABLE);
        } else {
            throw new TRSException("当前字典已经停用，无法停用");
        }

        dictMapper.updateById(dict);
        redisService.delByPrefix(KEY_PREFIX + type.replace(CODE_SUFFIX, ""), "profileSchema");
    }

    @Override
    public RestfulResultsV2 getDictTree(String type) throws ServiceException {
        List<DictListVO> dictList = dictMapper.findDictList(type, null,null,null, null).stream()
                .filter(vo -> vo.getStatus() == 1)
                .map(Dict::toDto2)
                .collect(Collectors.toList());
       return convertTree(type, dictList);

    }

    /**
     * convertTree<BR>
     *
     * @param type 类型
     * @param data 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    private RestfulResultsV2 convertTree(String type, List<DictListVO> data) throws ServiceException {
        List<DictListVO>tmp = new ArrayList<>(data.size());
        tmp.addAll(data);
        // 获取到所有分类ID与自身的对象Map
        Map<Long, DictListVO> map = tmp
                .stream()
                .collect(Collectors.toMap(
                        DictListVO::getCode,
                        r -> r,
                        (a, b) -> a
                ));
        Map<Long, Long> countMap = new HashMap<>(0);
        for (Long code : map.keySet()) {
            Long count = fightService.getPlanNum(code);
            countMap.put(code, count);
        }
        // 获取到所有分类父级ID与子集的关系Map
        final Map<Long, List<DictListVO>> parentIdAndChildrenMap = tmp.stream()
                .collect(Collectors.groupingBy(DictListVO::getPCode));
        List<DictListVO> returnData = new ArrayList<>(map.size());
        for (DictListVO vo : map.values()) {
            Long pCode = Optional.ofNullable(vo.getPCode()).orElse(0L);
            if (pCode > 0L) {
                DictListVO pVO = map.get(pCode);
                if (pVO != null) {
                    pVO.addChildren(vo);
                    vo.setPName(pVO.getName());
                }
            } else {
                vo.setPName("全部");
                returnData.add(vo);
            }
            Long num = countMap.get(vo.getCode());
            if (vo.getCode() > 0) {
                List<DictListVO> list = parentIdAndChildrenMap.get(vo.getCode());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
                    List<DictListVO> allChild = findAllChild(list, parentIdAndChildrenMap);
                    List<Long> categoryCodes = allChild.stream().map(DictListVO::getCode)
                            .collect(Collectors.toList());

                    for (Long code : countMap.keySet()) {
                        if (categoryCodes.contains(code)) {
                            num += countMap.get(code);
                        }
                    }
                }
            }
            vo.setNum(num);
        }
        DictListVO all = new DictListVO();
        //获取在分类映射表中根据实体id排重后的总量，页面全部的数量需要显示这个
        Long reduceNum = returnData.stream().map(DictListVO::getNum).reduce(0L, Long::sum);
        //获取最顶级的dict
        Dict dict = dictMapper.findDictList(type + "_group", 0L, null, null, null).get(0);
        all.setId(dict.getId());
        all.setName("全部");
        all.setType(type);
        all.setCode(dict.getCode());
        all.setPCode(dict.getPCode());
        all.setNum(reduceNum);
        all.addChildren(returnData);
        return RestfulResultsV2.ok(all);
    }

    private List<DictListVO> findAllChild(List<DictListVO> list, Map<Long, List<DictListVO>> parentIdAndChildrenMap) {
        List<DictListVO> allChild = new ArrayList<>(0);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
            allChild.addAll(list);
            list.forEach(vo -> {
                List<DictListVO> children = parentIdAndChildrenMap.get(vo.getCode());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(children)) {
                    allChild.addAll(findAllChild(children, parentIdAndChildrenMap));
                }
            });
        }
        return allChild;
    }


}
