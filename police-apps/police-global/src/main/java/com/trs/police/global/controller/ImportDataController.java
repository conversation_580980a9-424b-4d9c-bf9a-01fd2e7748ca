package com.trs.police.global.controller;

import com.trs.police.global.service.ImportService;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/2 15:35
 */
@RestController
@RequestMapping("/data")
public class ImportDataController {

    @Resource
    ImportService importService;

    /**
     * 导入poi数据
     *
     * @param file poi-excel
     */
    @PostMapping("/import/poi")
    public void importPoi(MultipartFile file){
        importService.importPoi(file);
    }
}
