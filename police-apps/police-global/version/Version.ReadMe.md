# global
## 17.1
- XMKFB-8202 风险日历，批量导入操作完成后应给出对应的结果提示

## 17.2
- XMKFB-8115 广安-布控预警的时候可以根据需求选择不同的感知源
- XMKFB-8330[XMKFB-8185] 后-【省厅情指】- 后端相关接口提供

## 16.4
- XMKFB-8009 自贡-后台配置-模型配置中增加删除按钮

## 15.3
- XMKFB-7268 合 -【广安】- 紧急警情表单及功能优化
- XMKFB-7279 角色权限，角色列表中的数据排序方式应作优化

## 15.2
- XMKFB-7277[XMKFB-7276] 后-允许移动到根目录

## 15.1
- XMKFB-7002 自贡GA-后台权限管理新增左侧目录树+展示描述

## 14.3
- flyway1069脚本修改后, flyway无法正常启动，需要备份原有数据，删除flyway历史表的1069的记录, 再启动

## 12.4
- XMKFB-5955 合-【高新】- 人员档案户籍地筛选改用懒加载方式

## RC20240716

- XMKFB-2621 协作码表调整

> 分割线：发版文件最新的在最前面

## v1.2 发版日志

- XMKFB-573、【南充】风险警情详情-相关风险无数据
- XMKFB-591[XMKFB-454] 增加通用码表查询接口

# nacos配置更新

```

```

## 1.3

### 发版日志

- XMKFB-714 【优化】人员、群体、事件档案档案列表中风险分值字段下的数据应靠左对齐
- XMKFB-724[XMKFB-723] 【后】—— 风险分数不要小数
- # 高新： 2024-03-15 发版
- XMKFB-788 后 - 组织类型列表接口

## 1.4

### 发版日志

- XMKFB-824[XMKFB-819] 后-新增词典扩充
- # 泸州： 2024-03-27 发版
-

## 1.5

### 发版日志

- XMKFB-1223 后-管控相关问题处理
- XMKFB-925 后-【广安】线索新增之后列表数据都是--
- XMKFB-981 【广安】风险管理-处置人签收报错等bug
- XMKFB-1019[XMKFB-1012] 后- 优化派出所映射表的数据
- feat(t_business_filter): 增加SQL
- XMKFB-1032 【广安】风险详情页的溯源数据列表中无法显示手动关联的警情数据
- XMKFB-1184 【泸州】警情档案筛选行政区划筛选异常；12345导出异常
- XMKFB-1042 编辑选择相关事件后事件的录入时间显示为空
- filterChildren 过滤掉部门类型为空的数据，因为在配置页面部门类型不是必填项
- XMKFB-1250[XMKFB-1237] 后-交通事故模板部分字段调整
- XMKFB-1275 【高新】常控中心
- XMKFB-1239 后-1、预警消息未消费，系统中无预警信息 2、预警中心相关的统计不正常，地图左右的数据都为空
- XMKFB-1317 要情，将涉稳上访聚集模板里的“离散时间”调整为“散离时间”
- XMKFB-1343[XMKFB-1309] 要情，情报专报模板相关接口支持
- XMKFB-1351[XMKFB-1305] 要情、 情报专报和治安综述模板对应的字段的schema种type调整

## 5.3

- XMKFB-1710 后 - 工作台增加线索跟指令的待办模块跟列表
- XMKFB-1722 后 - 工作台增加线索跟指令的快捷入口
- XMKFB-1675、【后】标签，字典的删除接口提供
-

# 5.4

- XMKFB-1747 自贡公安，app底部tab接口调整
- XMKFB-1810[XMKFB-1799] 后-相关接口修改
- XMKFB-1729、人档新增电话号码
-

# 6.1

- XMKFB-1766 【泸州】出入境jw人员住宿登记比对

# 6.2

- XMKFB-2035 【泸州】涉艾人员核查工具
- # 20240618 RC发版

# 6.3

- XMKFB-2121[XMKFB-1961] 后 - 要情通报功能开发
- XMKFB-2036[XMKFB-1725] 后-突发处置-预案管理相关接口提供
- XMKFB-2213 [XMKFB-1848] 新增人员、群体档案失败

# 6.4

- XMKFB-2204[XMKFB-2198] 后 - 补充相关字段
- XMKFB-2273[XMKFB-2198] 后 - 完善导出行为
-

# 7.1

- XMKFB-2488[XMKFB-1848] 生产环境上的任务地图未显示出人员的撒点信息
-

# 7.2

- XMKFB-2620[XMKFB-2579] 后 - 单位补充归属地域名称的返回
-

## 8.2

- APP-160 低权限用户进入APP仍然可以看到所有功能模块及部分列表数据
- APP-194 工作台快捷入口缺少填报线索

### sql

```text
## 自贡环境单独执行
UPDATE t_application_system set name = '接收指令' where name = '上级推送' and module = 'intelligence.zhiling';
UPDATE t_application_system set name = '推送指令' where name = '推送下级' and module = 'intelligence.zhiling';
UPDATE t_application_system set name = '推送线索' where name = '我发起的' and module = 'intelligence.xiansuo';
UPDATE t_application_system set name = '接收线索' where name = '我接收的' and module = 'intelligence.xiansuo';
```

## 8.3

- APP-231 底部我的菜单图标丢失
- XMKFB-3170[XMKFB-2919] 后-二维码接口开发
-

# 9.1

- XMKFB-3631[XMKFB-2556] 要情、指令导出优化
-

## 9.2

- XMKFB-3876 后-【高新】-群体档案列表筛选项：新增-主责警种和主责单位

## 10.2

- XMKFB-4340[XMKFB-4312] 后 - 事件档案详情页调整

## 10.3

- XMKFB-4469[XMKFB-4378] 后 - 【云哨】人员档案详情增加schema
- XMKFB-4618 事件关联人员失败，接口报错
- 

## 10.4

- XMKFB-4685 【泸州】融合检索，星座标签名称存在错别字

## 10.5

-XMKFB-4827[XMKFB-4826] 后-增加ST相关组织跟人员信息
-XMKFB-4577 警情档案详情页中显示的反馈信息数据有误  