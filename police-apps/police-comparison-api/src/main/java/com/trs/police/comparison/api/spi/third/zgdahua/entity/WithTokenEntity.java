package com.trs.police.comparison.api.spi.third.zgdahua.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class WithTokenEntity {
        private String token;

        private Integer duration;

        private String userName;

        private String userId;

        private String userCode;

        private String serviceAbility;

        private String lastLoginTime;
    }