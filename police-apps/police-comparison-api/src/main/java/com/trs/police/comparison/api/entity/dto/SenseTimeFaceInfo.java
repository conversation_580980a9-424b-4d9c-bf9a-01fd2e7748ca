package com.trs.police.comparison.api.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 商汤布控接口请求信息
 *
 * <AUTHOR>
 **/
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SenseTimeFaceInfo {

  /**
   * 姓名
   */
  private String targetName;
  /**
   * 证件号码
   */
  private String identityId;
  /**
   * 库类型 固定为1
   */
  private int senseType = 1;
  /**
   * 库id 商汤下发
   */
  private String tarLibSerial;
  /**
   * 图片集合
   */
  private List<ImageInfo> images = new ArrayList<>();

  /**
   * 图片信息
   */
  @Data
  @Builder
  public static class ImageInfo {

    private String url;

  }

}
