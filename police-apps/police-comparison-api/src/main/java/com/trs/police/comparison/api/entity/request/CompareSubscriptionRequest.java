package com.trs.police.comparison.api.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 比对订阅服务请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 15:29
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompareSubscriptionRequest implements DataServiceAPIBaseRequest {

    private static final long serialVersionUID = 8003119188641092382L;

    /**
     * 传入的header下的终端用户信息
     */

    /**
     * 申请人公民身份号码
     */
    private String applicantIdCard;

    /**
     * 行政区划代码
     */
    private String regionalismCode;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 审批人姓名
     */
    private String approvalName;

    /**
     * 审批人身份证号
     */
    private String approvalIdCard;

    @NotNull(message = "订阅编号不可为空")
    private String subscribeCode;

    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private LocalDateTime subscribeStartTime = LocalDateTime.now();

    @JsonFormat(pattern = "yyyyMMddHHmmss")
    @Future
    private LocalDateTime subscribeEndTime = LocalDateTime.MAX;

    @NotEmpty(message = "特征集合不可为空")
    @Valid
    private Set<Eigenvalues> identifiers = new HashSet<>();

    /**
     * 1 合众本地感知引擎
     * 2 云控平台
     */
    private String subscribePlatform;

    /**
     * 订阅特征值信息
     */
    @Data
    public static class Eigenvalues {

        @NotNull(message = "标识符不可为空")
        private String identifier;

        @NotNull(message = "标识符类型不可为空")
        @Max(value = 9, message = "标识符类型不合法")
        @Min(value = 1, message = "标识符类型不合法")
        private Integer identifierType;

        private String picture;

        private Object customInfo;
    }

}
