package com.trs.police.comparison.api.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.trs.police.comparison.api.constant.Constants;
import com.trs.police.comparison.api.exception.UnauthorizedException;
import com.trs.police.comparison.api.entity.request.RuleAddRequest;
import com.trs.police.comparison.api.entity.response.RuleAddResponse;
import com.trs.police.comparison.api.service.DataServiceEntranceAPIBaseService;
import com.trs.police.comparison.api.service.base.BaseRuleService;
import com.trs.police.comparison.api.utils.RedisUtil;
import com.trs.police.comparison.api.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 新增标签规则
 * redis key命名规则 high_risk_warn:tag_rule_front:{id}
 * <AUTHOR>
 * @since 2023/4/25 17:55
 */

@Slf4j
@Service
public class TagRuleAddServiceImpl extends BaseRuleService implements DataServiceEntranceAPIBaseService<RuleAddRequest, RuleAddResponse> {

    /**
     * 前端使用的 标签规则 key
     */
    private static final String TAG_RULE_FRONT = "tag_rule_front";

    /**
     * 预警服务使用的标签规则 key
     */
    public static final String TAG_RULE_BACKEND = "tag_rule_backend";

    @Resource
    private RedisUtil redisUtil;

    @Override
    public String getServiceId() {
        return "addTagRule";
    }

    @Override
    public Class<RuleAddRequest> getRequestClass() {
        return RuleAddRequest.class;
    }

    @Override
    public Class<RuleAddResponse> getResponseClass() {
        return RuleAddResponse.class;
    }

    @Override
    public RuleAddResponse execute(RuleAddRequest request)
        throws BizException, UnauthorizedException, InterruptedException, JsonProcessingException {
        RuleAddResponse tagRuleAddResponse = new RuleAddResponse();
        try {
            String rules = request.getRules();
            JSONArray array = JSONArray.parseArray(rules);
            for (Object o : array) {
                JSONObject jsonObject = (JSONObject)o;
                //获取id
                Long id = jsonObject.getLong("id");
                //操作前置先校验json格式
                Set<String> fieldSet = getFieldSet(jsonObject.getString("filter"));
                String filter = processFilterJson(jsonObject.getString("filter"));
                if (id == null) {
                    synchronized (TagRuleAddServiceImpl.class) {
                        //获取自增id
                        id = redisUtil.incr(Constants.HIGH_RISK_WARN_REDIS_KEY + ":" + TAG_RULE_BACKEND + ":autoIncrId", 1);
                    }
                }
                redisUtil.hset(Constants.HIGH_RISK_WARN_REDIS_KEY + ":" + TAG_RULE_FRONT, String.valueOf(id), jsonObject.toJSONString());
                jsonObject.put("fieldSet", fieldSet);
                jsonObject.put("filter", filter);
                redisUtil.hset(Constants.HIGH_RISK_WARN_REDIS_KEY + ":" + TAG_RULE_BACKEND, String.valueOf(id), jsonObject.toJSONString());
                tagRuleAddResponse.setCode(HttpStatus.OK.value());
                tagRuleAddResponse.setMessage(HttpStatus.OK.getReasonPhrase());
            }
            return tagRuleAddResponse;
        } catch (Exception e) {
            log.error("新增标签规则出错", e);
            tagRuleAddResponse.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
            tagRuleAddResponse.setMessage("新增标签规则出错, 原因为:" + e.getMessage());
        }
        return tagRuleAddResponse;
    }
}
