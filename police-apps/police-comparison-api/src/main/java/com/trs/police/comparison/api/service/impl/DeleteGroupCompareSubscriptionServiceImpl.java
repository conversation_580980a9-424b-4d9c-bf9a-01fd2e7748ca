package com.trs.police.comparison.api.service.impl;

import com.trs.police.comparison.api.service.component.CompareSubscriptionCache;
import com.trs.police.comparison.api.utils.DataServiceHelper;
import com.trs.police.comparison.api.dao.SubscribeGroupInfoMapper;
import com.trs.police.comparison.api.dao.SubscribeGroupMemberMapper;
import com.trs.police.comparison.api.entity.SubscribeGroupMember;
import com.trs.police.comparison.api.exception.UnauthorizedException;
import com.trs.police.comparison.api.entity.request.DeleteGroupCompareSubscriptionRequest;
import com.trs.police.comparison.api.entity.response.DeleteGroupCompareSubscriptionResponse;
import com.trs.police.comparison.api.service.DeleteGroupCompareSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 群体布控撤销数据服务实现
 *
 * <AUTHOR>
 * @version 1.0
 **/

@Service
@Slf4j
@Validated
public class DeleteGroupCompareSubscriptionServiceImpl implements DeleteGroupCompareSubscriptionService {

  @Resource
  private CompareSubscriptionCache compareSubscriptionCache;

  @Resource
  private SubscribeGroupInfoMapper subscribeGroupInfoMapper;

  @Resource
  private SubscribeGroupMemberMapper subscribeGroupMemberMapper;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public DeleteGroupCompareSubscriptionResponse execute(DeleteGroupCompareSubscriptionRequest request) throws UnauthorizedException, InterruptedException {
    final String userAccount = DataServiceHelper.UserSafeBox.getUserAccount();

    //删除群体信息之前先查询群体下关联的人员
    final String requestMonitorId = request.getMonitorId();
    List<SubscribeGroupMember> membersInGroup = subscribeGroupMemberMapper.selectByMonitorId(requestMonitorId, userAccount);

    //将关联的人员依次取消关联
    membersInGroup.forEach(member -> {
      member.getMonitorIds().remove(requestMonitorId);
      member.setUpdateTime(LocalDateTime.now());
      subscribeGroupMemberMapper.updateByMemberIdAndUserName(member);
    });

    //删除群体信息
    boolean deleteResult = subscribeGroupInfoMapper.deleteByMonitorIdAndUserName(requestMonitorId, userAccount) == 1;

    //统一更新删除的群体关联的人员信息，若monitorIds为空则删除之
    Integer memberDeleteCount = 0;
    if (Objects.nonNull(membersInGroup) && !membersInGroup.isEmpty()) {
      memberDeleteCount = subscribeGroupMemberMapper.deleteMembersIfMonitorIdsEmpty(membersInGroup.stream().map(SubscribeGroupMember::getMemberId).collect(Collectors.toList()), userAccount);
    }

    //删除对应缓存
    compareSubscriptionCache.deleteGroupCacheFor(requestMonitorId, userAccount);

    return deleteResult ?
            DeleteGroupCompareSubscriptionResponse.success(MessageFormat.format("删除成功，共更新{0}条人员信息，其中删除{1}条人员信息", membersInGroup.size(), memberDeleteCount)) :
            DeleteGroupCompareSubscriptionResponse.error("删除失败");
  }
}
