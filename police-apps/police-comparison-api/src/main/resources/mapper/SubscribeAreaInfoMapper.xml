<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.comparison.api.dao.SubscribeAreaInfoMapper">
    <resultMap id="BaseResultMap" type="com.trs.police.comparison.api.entity.SubscribeAreaInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="area_id" jdbcType="VARCHAR" property="areaId"/>
        <result column="tags" jdbcType="VARCHAR" property="tags"
            typeHandler="com.trs.police.comparison.api.dao.handler.Set2JsonArrayTypeHandler"/>
        <result column="geometries" jdbcType="VARCHAR" property="geometries"
            typeHandler="com.trs.police.comparison.api.dao.handler.Set2JsonArrayTypeHandler"/>
    </resultMap>

    <select id="selectAreaIdsByUserNameAndAreaId" resultMap="BaseResultMap">
        select * from t_subscribe_area_info where user_name = #{userName} and area_id = #{areaId}
    </select>

    <select id="selectAreaIdsByAreaId" resultMap="BaseResultMap">
        select * from t_subscribe_area_info where area_id = #{areaId}
    </select>

</mapper>