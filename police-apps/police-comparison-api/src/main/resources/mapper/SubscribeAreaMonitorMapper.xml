<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.comparison.api.dao.SubscribeAreaMonitorMapper">
    <resultMap id="BaseResultMap" type="com.trs.police.comparison.api.entity.SubscribeAreaMonitor">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="monitor_id" jdbcType="VARCHAR" property="monitorId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="area_ids" jdbcType="VARCHAR" property="areaIds"
                typeHandler="com.trs.police.comparison.api.dao.handler.JsonArrayTypeHandler"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , monitor_id, area_ids, user_name, create_time, update_time
    </sql>
    <sql id="PageCond">
        <if test="@java.util.Objects@nonNull(pageInfo)">
            limit #{pageInfo.offset,jdbcType=INTEGER}, #{pageInfo.pageSize, jdbcType = INTEGER}
        </if>
    </sql>
    <select id="checkAreaMonitor" resultType="java.lang.Integer">
        select count(1)
        from t_subscribe_area_monitor
        where monitor_id = #{monitorId}
          and user_name = #{userName}
    </select>

    <update id="updateByMonitorIdAndUserName">
        update t_subscribe_area_monitor
        set name                    = #{areaMonitor.name},
            area_ids                = #{areaMonitor.areaIds,jdbcType=VARCHAR,typeHandler=com.trs.police.comparison.api.dao.handler.JsonArrayTypeHandler},
            update_time             = #{areaMonitor.updateTime}
        where monitor_id = #{areaMonitor.monitorId}
          and user_name = #{areaMonitor.userName}
    </update>

    <delete id="deleteByMonitorIdAndUserName">
        delete
        from t_subscribe_area_monitor
        where monitor_id = #{monitorId}
          and user_name = #{userName}
    </delete>

</mapper>