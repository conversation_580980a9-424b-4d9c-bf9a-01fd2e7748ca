#procude lz
com.trs.risk.exclude.types=jjsj;cjsj;rx12345

com.trs.risk.topic.12345rx=hotline_12345
com.trs.risk.topic.12345rx_group=hotline_12345_group

com.trs.risk.topic.cjsj=data_read_6088_DD00000073
com.trs.risk.topic.cjsj_group=cjsj_group

com.trs.risk.topic.jjsj=data_read_6089_DD00000071
com.trs.risk.topic.jjsj_group=jjsj_group

com.trs.hive.metastore=thrift://80.79.140.5:21088,thrift://80.79.140.4:21088
com.trs.hive.warehouse=hdfs:///user/hive/warehouse
com.trs.hive.catalog=iceberg
com.trs.hive.database.name=trs
com.trs.hive.database.table.jj=jjsj
com.trs.hive.database.table.cj=cjsj
com.trs.hive.database.table.xs=rx12345
com.trs.hive.database.table.history=history_police_situation

com.trs.kafka.producer.brokers=80.75.96.30:9092
com.trs.kafka.producer.topic=high_risk_warn

com.trs.jdbc.trino.url=*****************************
com.trs.jdbc.trino.user=admin
com.trs.jdbc.trino.jiejing.table=iceberg.trs.jjsj
com.trs.jdbc.trino.chujing.table=iceberg.trs.cjsj
com.trs.jdbc.trino.rx12345.table=iceberg.trs.rx12345
com.trs.risk.table.union.rx12345=true

com.trs.jdbc.trino.driver.name=io.trino.jdbc.TrinoDriver

#??????, ????????????????
com.trs.redis.high_risk.expire_second=81820800

com.trs.redis.url=redis://80.79.140.28:22400,80.79.140.29:22400,80.79.140.30:22400

com.trs.risk.sql.high_risk_warn=select \
	format_datetime(date_add(''hour'', \
	8, \
	jj.bjsj), \
	''yyyy-MM-dd HH:mm:ss'') as sj, \
	jj.jjdbh as bh, \
	jj.jqdz as dd, \
	(select value from mpp_hnd.public.icc_dict where dict_key = jj.jqxldm) as lb, \
	jj.lxdh as dh, \
    jj.bjrzjdm as zjdm, \
    jj.bjrzjhm as id_card, \
	jj.jqxldm as xldm, \
	jj.gxdwdm as dddm, \
	cjxx.cjdwbms, \
	cjxx.fksjs, \
	case \
		when jj.jqlyfs = ''01'' then ''????'' \
		when jj.jqlyfs = ''02'' then ''????'' \
		when jj.jqlyfs = ''03'' then ''?????'' \
		when jj.jqlyfs = ''04'' then ''????'' \
		when jj.jqlyfs = ''05'' then ''????'' \
		when jj.jqlyfs = ''06'' then ''????'' \
		when jj.jqlyfs = ''07'' then ''????'' \
		when jj.jqlyfs = ''08'' then ''????'' \
		when jj.jqlyfs = ''09'' then ''??????'' \
		else ''??????'' \
	end as ly, \
	jj.bjnr, \
	cjxx.contents as fknrs, \
	''jq'' as sourceType \
from \
	mpp_hnd.public.jjdb jj \
left join ( \
	select \
		cj.jjdbh bh, \
		listagg (cj.jqcljgsm) within group ( \
	order by \
		cj.fksj desc ) contents, \
		listagg (cj.fkdwdm || '',,'') within group ( \
	order by \
		cj.fksj desc ) cjdwbms, \
		listagg (format_datetime(cj.fksj, \
		''yyyy-MM-dd HH:mm:ss'') || '',,'') within group ( \
	order by \
		cj.fksj desc ) fksjs \
	from \
		mpp_hnd.public.fkdb cj \
	group by \
		cj.jjdbh ) cjxx on \
	jj.jjdbh = cjxx.bh \
where \
	( jj.wxbs = 0 \
	and jj.lhlx in (''01'', ''02'', ''03'') \
		    and jj.jqlbdm  != ''03000000'' \
		    and jj.jqlxdm != ''07010000'' \
		  	and jj.jjlx != ''02'' and format_datetime(date_add(''hour'', \
	8,jj.bjsj), \
	''yyyyMMddHHmmss'') >= {0} \
		and format_datetime(date_add(''hour'', \
	8,jj.bjsj), \
		''yyyyMMddHHmmss'') < {1} ) \
	or exists ( \
	select \
		1 \
	from \
		mpp_hnd.public.fkdb cj \
	where \
        cj.wxbs != ''1'' \
		and jj.jjdbh = cj.jjdbh \
		and format_datetime(date_add(''hour'', \
	8,cj.fksj), \
		''yyyyMMddHHmmss'') >= {0} \
			and format_datetime(date_add(''hour'', \
	8,cj.fksj), \
			''yyyyMMddHHmmss'') < {1} ) \
union ( \
select \
	format_datetime(rx.dep_firstenter_time, \
	''yyyy-MM-dd HH:mm:ss'') as sj, \
	rx.code as bh, \
	rx.address as dd, \
	rx.contypename as lb, \
	rx.fromtel as dh, \
    ''01'' as zjdm, \
    idcard as id_card, \
	rx.purtypename as xldm, \
	rx.areaname as dddm, \
	'''', \
	'''', \
	''12345'', \
	rx.content as nr, \
	'''' as fknrs, \
	''12345'' as sourceType \
from \
	mpp_new.public.shzy_12345rx_gdjcxx rx \
where \
	rx.areaname like ''5%'' and format_datetime(rx.dep_firstenter_time , \
	''yyyyMMddHHmmss'') >= {0} \
		and format_datetime(rx.dep_firstenter_time , \
		''yyyyMMddHHmmss'') < {1} )  \
union ( \
select \
	format_datetime(jj.RECEIVETIME,  \
	''yyyy-MM-dd HH:mm:ss'') as sj, \
	jj.RECEIVENUM as bh, \
	jj.ADDRESS as dd, \
	case \
		when jj.CASEPART = ''08030000'' then ''????'' \
		when jj.CASEPART = ''08050000'' then ''????'' \
		when jj.CASEPART = ''01080400'' then ''????'' \
		when jj.CASEPART = ''09070000'' then ''????'' \
		when jj.CASEPART = ''04020000'' then ''????'' \
		when jj.CASEPART = ''04010000'' then ''????'' \
		when jj.CASEPART = ''01080600'' then ''??????'' \
		when jj.CASEPART = ''08040000'' then ''??????'' \
		when jj.CASEPART = ''10010300'' then ''????'' \
		when jj.CASEPART = ''08060100'' then ''????'' \
		when jj.CASEPART = ''08060200'' then ''????'' \
		when jj.CASEPART = ''08120000'' then ''??????'' \
		when jj.CASEPART = ''05130300'' then ''??????'' \
		when jj.CASEPART = ''08060400'' then ''??????'' \
		when jj.CASEPART = ''02010400'' then ''?????????????????????????'' \
		when jj.CASEPART = ''02140200'' then ''????????????'' \
		when jj.CASEPART = ''10010200'' then ''????'' \
		when jj.CASEPART = ''03050500'' then ''??????'' \
		when jj.CASEPART = ''10010100'' then ''??????'' \
		when jj.CASEPART = ''10010400'' then ''??????'' \
		else '''' \
	end as lb, \
	jj.CALLERTELE as dh, \
    '''' as zjdm, \
    '''' as id_card, \
	jj.CASEPART as xldm, \
	jj.DISTRICTCODE as dddm, \
	'''' as cjdwbms, \
	'''' as fksjs, \
    jj.RECEIVETYPE  as ly, \
	jj.CONTENTS, \
	cjxx.contents as fknrs, \
	''old-sthy'' as sourceType \
from \
	mpp_old_sthy_mapping.st_big_data.t_jjdb jj \
left join ( \
	select \
		cj.RECEIVENUM bh, \
		listagg (cj.CONTENTS) within group ( \
	order by \
		cj.handletime desc ) contents \
	from \
		mpp_old_sthy_mapping.st_big_data.t_cjdb  cj \
	group by \
		cj.RECEIVENUM ) cjxx on \
	jj.receivenum = cjxx.bh \
where \
    ( jj.isdelete = ''0'' and format_datetime(jj.RECEIVETIME, \
		''yyyyMMddHHmmss'') <= ''20230712000000''  \
     and format_datetime(jj.RECEIVETIME, \
	''yyyyMMddHHmmss'') >= {0} \
		and format_datetime(jj.RECEIVETIME, \
		''yyyyMMddHHmmss'') < {1} ) \
	or exists ( \
	select \
		1 \
	from \
		mpp_old_sthy_mapping.st_big_data.t_cjdb  cj \
	where \
        cj.isdelete = ''0'' and \
		jj.RECEIVENUM = cj.RECEIVENUM \
		and format_datetime(cj.HANDLETIME, \
		''yyyyMMddHHmmss'') <= ''20230712000000'' \
		and format_datetime(cj.HANDLETIME, \
		''yyyyMMddHHmmss'') >= {0} \
			and format_datetime(cj.HANDLETIME, \
			''yyyyMMddHHmmss'') < {1} ) \
)