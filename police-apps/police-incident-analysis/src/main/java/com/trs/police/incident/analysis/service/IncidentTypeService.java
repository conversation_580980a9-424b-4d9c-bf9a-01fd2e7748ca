package com.trs.police.incident.analysis.service;

import com.trs.police.incident.analysis.domain.request.IncidentTypeRequest;
import com.trs.police.incident.analysis.domain.response.IncidentTypeResponse;

/**
 * 警情类别分析服务
 */
public interface IncidentTypeService {

    /**
     * 按警情类别分析实现
     *
     * @param request 请求参数
     * @return {@link IncidentTypeResponse}
     */
    IncidentTypeResponse getIncidentType(IncidentTypeRequest request);

}
