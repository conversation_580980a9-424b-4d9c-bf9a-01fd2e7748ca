package com.trs.police.incident.analysis.constants;


/**
 * Description: 警情常量类
 *
 * <AUTHOR>
 * @create 2024-11-12 19:56
 */
public class PoliceSituationConstants {

    /**
     * 警情类别编码长度
     */
    public static final Integer PS_TYPE_LENGTH = 8;

    /**
     * 警情单位编码长度
     */
    public static final Integer PS_DEPT_CODE_LENGTH = 12;

    /**
     * 计算父级编码的偏移量
     */
    public static final int PS_TYPE_CALCULATE_OFFSET = 2;

    /**
     * 查询有效警情配置
     */
    public static final Integer PS_IS_EFFECTIVE = 1;

    /**
     * 查询无效警情配置
     */
    public static final Integer PS_IS_NOT_EFFECTIVE = 0;

    /**
     * 认定为重复报警的报警数量
     */
    public static final Integer REPEAT_ALARM_NUM_FLAG = 10;
}
