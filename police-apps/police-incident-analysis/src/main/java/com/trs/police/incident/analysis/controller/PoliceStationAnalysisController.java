package com.trs.police.incident.analysis.controller;


import com.trs.police.incident.analysis.domain.request.PoliceStationAnalysisRequest;
import com.trs.police.incident.analysis.domain.response.OverallStatisticsResponse;
import com.trs.police.incident.analysis.domain.response.PoliceStationInfoResponse;
import com.trs.police.incident.analysis.service.PoliceStationAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 一个派出所分析模块接口
 */

@Api(value = "一个派出所分析模块接口", tags = "一个派出所分析模块接口")
@RestController
@RequestMapping("/police-station")
public class PoliceStationAnalysisController {

    @Resource
    private PoliceStationAnalysisService policeStationAnalysisService;

    /**
     * 获取一个派出所的详细信息
     *
     * @param deptCode 派出所编号
     * @return 一个派出所的详细信息
     */
    @ApiOperation(value = "获取一个派出所的详细信息")
    @GetMapping("/getPoliceStationInfo/{deptCode}")
    public PoliceStationInfoResponse getPoliceStationInfo(@PathVariable("deptCode") String deptCode){
        return policeStationAnalysisService.getPoliceStationInfo(deptCode);
    }

    /**
     * 总警情对比
     *
     * @param request 公共请求体
     * @return 总警情对比
     */
    @ApiOperation(value = "总警情对比")
    @PostMapping("/getOverallStatistics")
    public OverallStatisticsResponse getOverallStatistics(@RequestBody PoliceStationAnalysisRequest request){
        return policeStationAnalysisService.getOverallStatistics(request);
    }
}
