package com.trs.police.incident.analysis.domain.request;

import com.trs.police.common.core.params.PageParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险公司列表请求参数
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("风险公司列表请求参数")
public class RiskCompanyGetRiskCompanyListRequest extends TopRequest {

    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private PageParams pageParams;

    /**
     * 只看风险公司标志
     * true：只看风险公司
     * false：不按照风险公司筛选
     */
    @ApiModelProperty("只看风险公司标志")
    private Boolean onlyRisk;

    /**
     * 公司名称模糊查询
     */
    @ApiModelProperty("公司名称模糊查询")
    private String companyName;

}
