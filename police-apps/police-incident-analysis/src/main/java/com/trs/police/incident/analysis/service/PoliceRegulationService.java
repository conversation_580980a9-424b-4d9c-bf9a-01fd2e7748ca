package com.trs.police.incident.analysis.service;

import com.trs.police.incident.analysis.domain.request.PoliceRegulationRequest;
import com.trs.police.incident.analysis.domain.response.GetDeptGroupStatisticsResponse;

/**
 * Description: 出警规范分析服务
 *
 * <AUTHOR>
 * @create 2024-11-19 15:02
 */
public interface PoliceRegulationService {

    /**
     * 违规点到
     *
     * @param request 请求参数
     * @return 各下属单位违规点到出警数
     */
    GetDeptGroupStatisticsResponse getViolationCount(PoliceRegulationRequest request);

    /**
     * 未签收
     *
     * @param request 请求参数
     * @return 各下属单位未签收出警数
     */
    GetDeptGroupStatisticsResponse getUnSignedCount(PoliceRegulationRequest request);
}
