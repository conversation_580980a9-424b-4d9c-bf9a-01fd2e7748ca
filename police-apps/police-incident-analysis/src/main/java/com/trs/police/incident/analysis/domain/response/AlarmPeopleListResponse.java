package com.trs.police.incident.analysis.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 报警人分析详情返回
 *
 * <AUTHOR>
 * @date 2024年08月13日 10:54
 */
@Data
@ApiModel("报警人分析列表返回")
public class AlarmPeopleListResponse {

    /**
     * 报警人姓名
     */
    @ApiModelProperty(value = "报警人姓名")
    private String bjrxm;

    /**
     * 报警人电话
     */
    @ApiModelProperty(value = "报警人电话")
    private String bjrdh;

    /**
     * 历史报警数量
     */
    @ApiModelProperty(value = "历史报警数量")
    private Integer lsbjsl;

    /**
     * 本期报警数量
     */
    @ApiModelProperty(value = "近30天报警数量")
    private Integer bjslj30;

    /**
     * 12345报警数量
     */
    @ApiModelProperty(value = "12345报警数量")
    private Integer n12345;

    /**
     * 人员标签
     */
    @ApiModelProperty(value = "人员标签")
    private String rybq;

    /**
     *疑似重复报警标志    true or false
     */
    @ApiModelProperty(value = "疑似重复报警标志    true or false")
    private Boolean cfbjbz = false;
    /**
     *涉及重点人员标志  true or false
     */
    @ApiModelProperty(value = "涉及重点人员标志  true or false")
    private Boolean zdrybz = false;
}
