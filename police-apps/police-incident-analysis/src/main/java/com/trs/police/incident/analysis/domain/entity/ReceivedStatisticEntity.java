package com.trs.police.incident.analysis.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 接警统计实体
 *
 * <AUTHOR>
 * @date  2024/11/11 11:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "jjdbtj", autoResultMap = true)
public class ReceivedStatisticEntity implements Serializable {
    private static final long serialVersionUID = -1763475832880049857L;

    /**
     * 接警单编号
     */
    @TableField(value = "jjdbh")
    private String jjdbh;

    /**
     * 所属分局代码
     */
    @TableField(value = "ssfjdm")
    private String ssfjdm;

    /**
     * 所属分局名称
     */
    @TableField(value = "ssfjmc")
    private String ssfjmc;

    /**
     * 管辖单位名称
     */
    @TableField(value = "gxdwmc")
    private String gxdwmc;

    /**
     * 警情类别名称
     */
    @TableField(value = "jqlbmc")
    private String jqlbmc;

    /**
     * 警情类型名称
     */
    @TableField(value = "jqlxmc")
    private String jqlxmc;

    /**
     * 警情细类名称
     */
    @TableField(value = "jqxlmc")
    private String jqxlmc;

    /**
     * 机器子类名称
     */
    @TableField(value = "jqzlmc")
    private String jqzlmc;

    /**
     * 人员标签
     */
    @TableField(value = "rybq")
    private String rybq;

    /**
     * 管控级别
     */
    @TableField(value = "gkjb")
    private String gkjb;

    /**
     * 12345报警数
     */
    @TableField(value = "n12345")
    private Integer n12345;

    /**
     * 出警数
     */
    @TableField(value = "cjs")
    private Integer cjs;

    /**
     * 是否未签收
     */
    @TableField(value = "wqs")
    private Integer wqs;

    /**
     * 是否出警时长小于60s
     */
    @TableField(value = "cjzs60")
    private Integer cjzs60;

    /**
     * 是否应立未立
     */
    @TableField(value = "ylwl")
    private Integer ylwl;

    /**
     * 是否公司警情
     */
    @TableField(value = "sfgsjq")
    private Integer sfgsjq;

    /**
     * 公司名称
     */
    @TableField(value = "gsmc")
    private String gsmc;

    /**
     * 小时
     */
    @TableField(value = "hour")
    private Integer hour;

    /**
     * 社区编号
     */
    @TableField(value = "sqbh")
    private String sqbh;

    /**
     * 社区名称
     */
    @TableField(value = "sqmc")
    private String sqmc;

    /**
     * 报警人姓名
     */
    @TableField(value = "bjrxm")
    private String bjrxm;

    /**
     * 报警人证件号
     */
    @TableField(value = "bjrzjhm")
    private String bjrzjhm;

    /**
     * 是否有效警情
     */
    @TableField(value = "sfyxjq")
    private Integer sfyxjq;
}
