package com.trs.police.incident.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.incident.analysis.domain.entity.BoundaryEntity;
import com.trs.police.incident.analysis.domain.response.GeographyBoundaryResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Description: 边界查询接口
 *
 * <AUTHOR>
 * @create 2024-11-11 18:59
 */
@Mapper
public interface BoundaryMapper extends BaseMapper<BoundaryEntity> {

    /**
     * 查询边界列表
     *
     * @return 边界列表
     */
    @Select("select * from t_boundary")
    List<GeographyBoundaryResponse> getBoundaryList();
}
