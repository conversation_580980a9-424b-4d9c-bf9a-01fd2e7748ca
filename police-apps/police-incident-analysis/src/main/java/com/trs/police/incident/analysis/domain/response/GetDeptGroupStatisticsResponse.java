package com.trs.police.incident.analysis.domain.response;

import com.trs.police.incident.analysis.domain.dto.DeptDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 各单位警情返回
 */
@Data
@ApiModel("各单位警情返回")
@EqualsAndHashCode(callSuper = true)
public class GetDeptGroupStatisticsResponse extends MultiPeriodDataResponseGeneric<List<PoliceSituationDeptCountResponse>>{
    /**
     * 部门横轴信息
     */
    @ApiModelProperty(value = "部门横轴信息")
    List<DeptDTO> deptList;
}
