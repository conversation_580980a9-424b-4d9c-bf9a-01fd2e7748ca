package com.trs.police.incident.analysis.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取学校库列表返回
 */

@Data
@ApiModel("获取学校库列表返回")
public class GetSchoolLibraryListResponse {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String schoolName;

    /**
     * 学校别名，用“,”分割
     */
    @ApiModelProperty("学校别名，用“,”分割")
    private String schoolAliases;

}
