<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.incident.analysis.mapper.WordDocBuildMapper">
    <select id="getDeptCount" resultType="com.trs.police.incident.analysis.domain.response.PoliceSituationDeptCountResponse">
        SELECT
        <choose>
            <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                b.sssjdm as bmbh,
                b.sssjmc as bmmc,
            </when>
            <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                b.ssfjdm as bmbh,
                b.ssfjmc as bmmc,
            </when>
            <otherwise>
                a.gxdwdm as bmbh,
                b.gxdwmc as bmmc,
            </otherwise>
        </choose>
        count(1) as count
        FROM t_jjdb as a
        LEFT JOIN t_jjdbtj as b on a.jjdbh = b.jjdbh
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        and a.bjsj between #{dto.startTime} and #{dto.endTime}
        <if test="dto.lbCodes != null and dto.lbCodes.size > 0">
            and
            <foreach collection="dto.lbCodes" item="item" index="index" open="(" close=")" separator="OR">
                <if test="item != null and item != ''">
                    <if test="item.length() == 2">
                        a.jqlbdm like CONCAT(#{item}, '%')
                    </if>
                    <if test="item.length() == 4">
                        a.jqlxdm like CONCAT(#{item}, '%')
                    </if>
                    <if test="item.length() == 6">
                        a.jqxldm like CONCAT(#{item}, '%')
                    </if>
                    <if test="item.length() == 8">
                        a.jqzldm like CONCAT(#{item}, '%')
                    </if>
                </if>
            </foreach>
        </if>
        group by
        <choose>
            <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                b.sssjdm,
                b.sssjmc
            </when>
            <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                b.ssfjdm,
                b.ssfjmc
            </when>
            <otherwise>
                a.gxdwdm,
                b.gxdwmc
            </otherwise>
        </choose>
    </select>
</mapper>