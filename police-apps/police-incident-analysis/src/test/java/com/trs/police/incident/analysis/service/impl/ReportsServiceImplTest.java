package com.trs.police.incident.analysis.service.impl;


import com.trs.police.incident.analysis.service.ReportsService;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;

@SpringBootTest
class ReportsServiceImplTest {

    @Resource
    private ReportsService reportsService;

    @Test
    void jqStatisticsTest() throws IOException {
        byte[] data = reportsService.jqStatistics(LocalDate.now(), LocalDate.now());
        Assertions.assertNotNull(data);
        FileUtils.writeByteArrayToFile(new File("test.xlsx"), data);
    }
}
