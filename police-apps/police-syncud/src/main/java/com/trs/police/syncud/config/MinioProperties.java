package com.trs.police.syncud.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *  Minio配置类
 */
@ConfigurationProperties(prefix = "com.trs.minio")
@Data
@Component
public class MinioProperties {

    private String host;
    private String bucket;
    private String url;
    private String accessKey;
    private String secretKey;
    private String photoBucket;
}
