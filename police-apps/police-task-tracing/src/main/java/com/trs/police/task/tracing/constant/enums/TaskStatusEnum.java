package com.trs.police.task.tracing.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.List;
import java.util.Objects;
import lombok.Getter;

/**
 * 挂帐盯办状态枚举类 盯办状态：1. 草稿 2. 待审核 3. 已驳回 4. 在办中 5.已办结 6.已销账  7. 待签收 8. 待回复 9.待办结
 *
 * <AUTHOR>
 */
@Getter
public enum TaskStatusEnum {
    /**
     * 草稿
     */
    DRAFT(1, "草稿"),
    /**
     * 审批中
     */
    APPROVING(2, "待审核"),
    /**
     * 已驳回
     */
    REJECTED(3, "已驳回"),
    /**
     * 在办中
     */
    DOING(4, "在办中"),
    /**
     * 已办结
     */
    DONE(5, "已办结"),
    /**
     * 已销账
     */
    CLOSED(6, "已销账"),
    /**
     * 待签收
     */
    WAITING_SIGN(7, "待签收"),
    /**
     * 待回复
     */
    WAITING_REPLY(8, "待回复"),
    /**
     * 待办结
     */
    WAITING_DONE(9, "待办结"),
    /**
     * 已逾期
     */
    OVERDUE(10, "已逾期"),
    /**
     * 高位化解
     */
    HIGH_RESOLVE(11, "高位化解"),
    IN_PROCESS(12, "审批中")
    ;

    TaskStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @EnumValue
    @JsonValue
    private final int code;

    private final String name;

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static TaskStatusEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (TaskStatusEnum typeEnum : TaskStatusEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * name转code
     *
     * @param name name
     * @return 枚举
     */
    @JsonCreator
    public static Integer nameOf(String name) {
        if (Objects.nonNull(name)) {
            for (TaskStatusEnum typeEnum : TaskStatusEnum.values()) {
                if (name.equals(typeEnum.getName())) {
                    return typeEnum.getCode();
                }
            }
        }
        return null;
    }

    /**
     * 判断挂账是否可以操作（回复）
     *
     * @return 结果
     */
    public Boolean isOpen() {
        List<TaskStatusEnum> open = List.of(DOING, WAITING_SIGN, WAITING_REPLY, WAITING_DONE, OVERDUE, HIGH_RESOLVE);
        return open.contains(this);
    }
}
