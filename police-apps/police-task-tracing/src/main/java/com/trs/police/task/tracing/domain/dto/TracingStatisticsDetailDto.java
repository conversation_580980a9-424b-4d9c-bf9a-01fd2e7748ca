package com.trs.police.task.tracing.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 盯办统计dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TracingStatisticsDetailDto {
    /**
     * 区域代码
     */
    private String areaCode;
    private String date;
    private String startTime;
    private String endTime;

    /**
     * 搜索类型
     */
    private String searchType;

    /**
     * 关键词
     */
    private String searchValue;
    /**
     * 挂账类型，警务督办：100；政务督办：101，全部不传
     */
    private Integer dbType;

    /**
     * 发布：publish；办结：finish
     */
    private String taskStatusType = "";
    /**
     * 部门id
     */
    private Integer deptId;

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 挂账状态（在办中，已办结......）
     */
    private String taskStatus;
    /**
     * 挂账类型（领导批示、敏感案事件......），传key
     */
    private String taskType;
}

