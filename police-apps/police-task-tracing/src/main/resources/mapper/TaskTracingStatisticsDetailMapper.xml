<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.task.tracing.mapper.TaskTracingStatisticsDetailMapper">

    <resultMap id="pageResultMap" type="com.trs.police.task.tracing.domain.entity.TaskTracing">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="title" property="title"/>
        <result column="task_code" property="taskCode"/>
        <result column="short_name" property="responsibleGov"/>
        <result column="task_type" jdbcType="BIGINT" property="taskType"/>
        <result column="task_time" property="taskTime"/>
        <result column="time_limit_type" property="timeLimitType"/>
        <result column="time_limit" jdbcType="TIMESTAMP" property="timeLimit"/>
        <result column="create_dept_id" property="createDeptId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="responsible_dept" property="responsibleDept"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="taskStatus" jdbcType="BIGINT" column="task_status"/>
    </resultMap>

    <select id="trendDetailList" resultMap="pageResultMap">
        select tr.id,tr.title,tr.db_type,tr.task_status,tr.update_time,tr.create_time
        from t_task_tracing tr
        <choose>
            <when test="dto.taskStatusType == 'finish'">
                left join t_dept d on tr.update_dept_id = d.id
            </when>
            <otherwise>
                left join t_dept d on tr.create_dept_id = d.id
            </otherwise>
        </choose>
        <include refid="where"/>
    </select>
    <select id="areaDetailList" resultMap="pageResultMap">
        select tr.id,tr.title,tr.db_type,tr.task_status,tr.update_time,tr.create_time
        <choose>
            <when test="dto.taskStatusType == 'publish'">
                ,d.short_name as short_name
            </when>
            <otherwise>
                ,tr.responsible_dept
            </otherwise>
        </choose>
        from t_task_tracing tr
        <choose>
            <when test="dto.taskStatusType == 'publish'">
                left join t_dept d on tr.create_dept_id = d.id
            </when>
        </choose>
        <include refid="where"/>
    </select>
    <select id="getStatusTypeDetail" resultMap="pageResultMap">
        select tr.id,tr.title,tr.task_type,tr.task_status,tr.create_time,tr.update_time
        from t_task_tracing tr left join t_dept d on tr.create_dept_id = d.id
        <include refid="where"/>
    </select>

    <sql id="where">
        <where>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and d.district_code like concat('',#{dto.areaCode},'%')
            </if>
            <if test="status != null and status.size > 0">
                and tr.task_status in
                <foreach collection="status" separator="," close=")" open="(" item="statu">
                    #{statu}
                </foreach>
            </if>
            <if test="dto.date != null and dto.date != ''">
                <choose>
                    <when test="dto.taskStatusType == 'finish'">
                        and tr.create_time like CONCAT('', #{dto.date}, '%')
                    </when>
                    <otherwise>
                        and tr.create_time like CONCAT('', #{dto.date}, '%')
                    </otherwise>
                </choose>
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                and tr.title like CONCAT('%',#{dto.searchValue},'%')
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                <choose>
                    <when test="dto.taskStatusType == 'finish'">
                        and tr.create_time >= #{dto.startTime}
                    </when>
                    <otherwise>
                        and tr.create_time >= #{dto.startTime}
                    </otherwise>
                </choose>
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                <choose>
                    <when test="dto.taskStatusType == 'finish'">
                        and tr.create_time &lt;= #{dto.endTime}
                    </when>
                    <otherwise>
                        and tr.create_time &lt;= #{dto.endTime}
                    </otherwise>
                </choose>
            </if>
            <if test="dto.deptId != null and dto.deptId != ''">
                <choose>
                    <when test="dto.taskStatusType == 'finish'">
                        and tr.update_dept_id = #{dto.deptId}
                    </when>
                    <otherwise>
                        and tr.create_dept_id = #{dto.deptId}
                    </otherwise>
                </choose>
            </if>
            <if test="dto.dbType != null and dto.dbType != ''">
                and tr.db_type = #{dto.dbType}
            </if>
            <if test="dto.taskType != null and dto.taskType != ''">
                and tr.task_type = #{dto.taskType}
            </if>
        </where>
    </sql>
</mapper>
