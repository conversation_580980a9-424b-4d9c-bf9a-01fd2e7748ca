package com.trs.police.api.constant;

import lombok.Getter;

/**
 * 人员类型枚举
 */
public enum ImportantPeopleTypeEnum {
    /**
     * A类人员
     */
    A(3,"A"),
    /**
     * B类人员
     */
    B(2,"B"),
    /**
     * C类人员
     */
    C(1,"C");

    @Getter
    private final Integer code;

    @Getter
    private final String type;

    ImportantPeopleTypeEnum(Integer code, String type){
        this.code = code;
        this.type = type;
    }

    /**
     * 根据level获取对应类型
     *
     * @param code level
     * @return 对应类型
     */
    public static String getTypeByCode(Integer code) {
        for (ImportantPeopleTypeEnum typeEnum : ImportantPeopleTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getType();
            }
        }
        // 或者可以抛出异常或返回一个默认值，具体根据你的需求来定
        return null;
    }

}
