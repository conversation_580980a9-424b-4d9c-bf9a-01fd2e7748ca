package com.trs.police.oss.service;

import com.trs.police.common.core.entity.FileInfo;
import com.trs.police.common.core.vo.oss.ExportZipRequestVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 对象存储
 *
 * <AUTHOR>
 */
public interface ObjectStorageService {

    /**
     * storageJjZt<BR>
     *
     * @param file 参数
     * @return 结果
     * @throws IOException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/17 22:14
     */
    FileInfoVO storageJjZt(MultipartFile file) throws IOException;

    /**
     * 存储文件
     *
     * @param file 文件
     * @param needPreHandle 是否需要预处理
     * @return 文件信息
     * @throws IOException md5 hash错误
     */
    FileInfoVO storage(MultipartFile file, Boolean needPreHandle) throws IOException;

    /**
     * storage<BR>
     *
     * @param bucketName 参数
     * @param file       参数
     @param needPreHandle 是否需要预处理
     @param virtualizeBackgrounds 如果是图片，是否需要虚化背景
     * @return 结果
     * @throws IOException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/17 22:07
     */
    FileInfoVO storage(String bucketName, MultipartFile file, Boolean needPreHandle, Boolean virtualizeBackgrounds) throws IOException;

    /**
     * 根据文件id读取文件流
     *
     * @param fileInfoId 文件id
     * @param idCard       用户id
     * @return 文件流
     */
    ResponseEntity<byte[]> download(Long fileInfoId,String idCard);

    /**
     * 批量上传文件
     *
     * @param files 文件
     * @return 上传结果
     */
    List<FileInfoVO> uploadAttachments(List<MultipartFile> files);

    /**
     * 根据id查询文件信息
     *
     * @param fileId 文件id
     * @return 用户列表
     */
    FileInfo getFileInfo(Long fileId);

    /**
     * 根据id查询文件信息
     *
     * @param fileIds 文件id
     * @return 用户列表
     */
    List<FileInfo> getFileInfos(List<Long> fileIds);

    /**
     * 根据身份证号码查询大头照
     *
     * @param idNumber 身份证号码
     * @return 字节流
     */
    byte[] getPhoto(@Param("idNumber") String idNumber);

    /**
     * 获取文件
     *
     * @param fileName 文件名
     * @param range    视频拖放
     * @return 文件内容
     */
    ResponseEntity<byte[]> getFile(String fileName, String range);

    /**
     * getJjZtFile<BR>
     *
     * @param fileName 参数
     * @param range    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/17 22:17
     */
    ResponseEntity<byte[]> getJjZtFile(String fileName, String range);


    /**
     * 通过模块信息获取文件信息
     *
     * @param module 模块
     * @return {@link ResponseEntity}
     */
    ResponseEntity<byte[]> getByModule(String module);

    /**
     * 下载默认浏览器
     *
     * @param browserName 文件名
     * @return 文件
     */
    ResponseEntity<byte[]> downloadChrome(String browserName);

    /**
     * 到处zip包
     *
     * @param vo {@link ExportZipRequestVO}
     * @return {@link ResponseEntity}
     */
    ResponseEntity<byte[]> exportZip(ExportZipRequestVO vo);
}
