package com.trs.police.oss;

import com.trs.police.common.security.starter.annotation.EnablePoliceCloudResourceServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.ComponentScan;

/**
 * 对象存储服务
 *
 * <AUTHOR>
 */
@SpringBootApplication
@RefreshScope
@EnableDiscoveryClient
@EnablePoliceCloudResourceServer
@ComponentScan({"com.trs.police.oss", "com.trs.police.common.core.ybss", "com.trs.web.builder.util"})
public class OssApplication {

    /**
     * 启动类
     *
     * @param args 命令行参数 
     */
    public static void main(String[] args) {
        SpringApplication.run(OssApplication.class, args);
    }
}
