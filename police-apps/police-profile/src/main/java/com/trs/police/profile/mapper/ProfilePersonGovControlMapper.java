package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.profile.domain.entity.ProfilePersonGovControlEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-03-05 10:36
 */
@Mapper
public interface ProfilePersonGovControlMapper extends BaseMapper<ProfilePersonGovControlEntity> {

    /**
     * 根据 人员id 和 管控警种 查询 政府管控信息
     *
     * @param personId   人员id
     * @param policeKind 管控警种
     * @return ProfilePersonGovControlEntity
     */
    @Select("select * from t_profile_person_government_control where person_id = #{personId} and police_kind = #{policeKind}")
    ProfilePersonGovControlEntity selectByPersonIdAndPoliceKind(@Param("personId") Long personId, @Param("policeKind") Long policeKind);
}
