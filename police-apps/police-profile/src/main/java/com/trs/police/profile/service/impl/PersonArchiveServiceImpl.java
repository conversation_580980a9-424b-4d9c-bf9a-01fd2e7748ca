package com.trs.police.profile.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.constant.enums.TimeRangeEnum;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.ParamValidationException;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.*;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO;
import com.trs.police.common.openfeign.starter.DTO.AiGjDTO;
import com.trs.police.common.openfeign.starter.service.*;
import com.trs.police.common.openfeign.starter.vo.ThemeGjxxVO;
import com.trs.police.profile.domain.dto.PersonAberrationDTO;
import com.trs.police.profile.domain.dto.TrajectoryQueryDTO;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.vo.*;
import com.trs.police.profile.factory.TrajectoryQueryStrategyFactory;
import com.trs.police.profile.factory.strategy.trajectory.TrajectoryQueryStrategy;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.properties.ProfileProperties;
import com.trs.police.profile.service.PersonArchiveService;
import com.trs.police.profile.service.PersonService;
import com.trs.police.profile.trino.TrackMapper;
import com.trs.police.profile.util.PersonTrackUtil;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员档案
 *
 * <AUTHOR>
 * @since 2022/11/15 17:01
 **/
@Service
@Slf4j
public class PersonArchiveServiceImpl implements PersonArchiveService {

    @Resource
    private PersonMapper personMapper;
    @Resource
    private PersonTrackUtil personTrackUtil;
    @Resource
    private ControlService controlService;
    @Resource
    private DictService dictService;
    @Resource
    private TrackMapper trackMapper;
    @Resource
    private ProfileService profileService;
    @Resource
    private SearchService searchService;
    @Resource
    private ProfileProperties properties;

    @Resource
    private PermissionService permissionService;

    @Resource
    private PersonService personService;

    private static final String ACTIVITY_TIME = "activity_time";

    private static final String WARNING_SOURCE_TYPE = "control_warning_source_type";

    public static final String FUNCTION_HOUR = "Hour";

    public static final String FUNCTION_MONTH = "Month";

    public static final String FUNCTION_DAY = "Day";

    @Override
    public PageResult<PersonArchiveWarningVO> getPersonWarningRelationList(Long personId, PageParams pageParams) {
        Page<PersonArchiveWarningDto> page = pageParams.toPage();
        Page<PersonArchiveWarningDto> warningPage = personMapper.selectWarningListByIdCard(personId, page);
        List<PersonArchiveWarningVO> collect = warningPage.getRecords().stream()
                .map(PersonArchiveWarningDto::toVo).collect(Collectors.toList());
        return PageResult.of(collect, pageParams.getPageNumber(), warningPage.getTotal(), pageParams.getPageSize());
    }

    @Override
    public PageResult<TrackPointVO> getPersonTrackList(Long personId, ListParamsRequest request) {
        try {
            Person person = personMapper.selectById(personId);

            switch (properties.getTrackListDataSource()) {
                case "mysql":
                    final PageParams pageParams = request.getPageParams();
                    Page<TrackPointWithWarningIdVO> result = personMapper.selectTrackPage(personId,
                            request.getFilterParams(), request.getSearchParams(), request.getSortParams(),
                            pageParams.toPage());
                    return PageResult.of(result.getRecords().stream().map(vo -> {
                        vo.setName(person.getName());
                        vo.setType(vo.getType());
                        vo.setIsStayPoint(false);
                        vo.setSourceId(controlService.getSourceIdByName(vo.getSourceName()));
                        vo.setTime(TimeUtil
                                .getSimpleTime(LocalDateTime.parse(vo.getTime(), TimeUtil.DEFAULT_TIME_PATTERN)));
                        TrackPointVO pointVO = new TrackPointVO();
                        BeanUtils.copyProperties(vo, pointVO);
                        return pointVO;
                    }).collect(Collectors.toList()), pageParams.getPageNumber(), result.getTotal(),
                            pageParams.getPageSize());
                case "es":
                    TrajectoryQueryStrategy queryStrategy = TrajectoryQueryStrategyFactory
                            .getStrategy(TrajectoryQueryStrategyFactory.ES);
                    return queryStrategy.getPersonTrackPage(personId, request);
                // RestfulResultsV2<ThemeGjxxVO> results =
                // searchService.aiAnalysisGjList(buildAiGjDTOFrom(request,
                // person.getIdNumber()));
                // return toResult(results);
                default:
                    throw new ParamValidationException("trackListDataSource非法");
            }
        } catch (RuntimeException e) {
            log.error("人员轨迹查询错误!", e);
        }
        return PageResult.empty(request.getPageParams());
    }

    private PageResult<TrackPointVO> toResult(RestfulResultsV2<ThemeGjxxVO> gjxxResults) {
        RestfulResultsV2.SummaryInfo summary = gjxxResults.getSummary();
        return PageResult.of(gjxxResults.getDatas().stream().map(data -> {
            TrackPointVO vo = new TrackPointVO();
            vo.setLng(Double.valueOf(data.getJdwgs84()));
            vo.setLat(Double.valueOf(data.getWdwgs84()));
            vo.setTime(TimeUtil.getSimpleTime(LocalDateTime.parse(data.getHdsj(), TimeUtil.DEFAULT_TIME_PATTERN)));
            vo.setLocation(data.getHddz());
            // vo.setSourceId(Long.valueOf(data.getGzybh()));
            vo.setSourceName(data.getGzymc());
            vo.setIsStayPoint(false);
            vo.setType(getParentDictForTrack(data.getGzylx()));
            vo.setContent(new String[] {});
            return vo;
        }).collect(Collectors.toList()), summary.getPageNum(), summary.getTotal(), summary.getPageSize());
    }

    private AiGjDTO buildAiGjDTOFrom(ListParamsRequest request, String idNumber) {
        AiGjDTO dto = new AiGjDTO();
        dto.setPageNum(request.getPageParams().getPageNumber());
        dto.setPageSize(request.getPageParams().getPageSize());
        dto.setArchivesType("person");
        Optional<KeyValueTypeVO> timeVO = request.getFilterParams()
                .stream()
                .filter(vo -> "timeParams".equalsIgnoreCase(vo.getType()))
                .findAny();
        if (timeVO.isPresent()) {
            TimeParams timeParams = (TimeParams) timeVO.get().getProcessedValue();
            dto.setStartTime(timeParams.getBeginTime().format(TimeUtil.DEFAULT_TIME_PATTERN));
            dto.setEndTime(timeParams.getEndTime().format(TimeUtil.DEFAULT_TIME_PATTERN));
        }
        dto.setRecordId(idNumber);
        return dto;
    }

    private Long getParentDictForTrack(String gzylx) {
        DictDto dict = dictService.getDictByTypeAndName(WARNING_SOURCE_TYPE, gzylx);
        if (dict == null) {
            return null;
        }
        if (dict.getPCode() != null && dict.getPCode() != 0) {
            return dictService.getDictById(dict.getPid()).getCode();
        }
        return dict.getCode();
    }

    private Long getParentDict(String gzylx) {
        DictDto dict = dictService.getDictByTypeAndDesc(WARNING_SOURCE_TYPE, gzylx);
        if (dict == null) {
            return null;
        }
        if (dict.getPCode() != null && dict.getPCode() != 0) {
            return dictService.getDictById(dict.getPid()).getCode();
        }
        return dict.getCode();
    }

    private PageResult<TrackPointVO> selectTrackPage(Long personId, ListParamsRequest request, PageParams pageParams) {
        final TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "activeTime",
                TimeParams.class);
        if (timeParams == null) {
            throw new TRSException("未传时间筛选参数！");
        }
        final Long sourceCode = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "source", Long.class);
        if (sourceCode == null) {
            throw new TRSException("未传轨迹类型参数 source！");
        }
        TrajectoryQueryStrategy queryStrategy = TrajectoryQueryStrategyFactory
                .getStrategy(properties.getTrackListDataSource());
        PageResult<TrackPointVO> personTrackPage = queryStrategy.getPersonTrackPage(personId, request);
        return PageResult.of(personTrackPage.getItems(), pageParams.getPageNumber(), personTrackPage.getTotal(),
                pageParams.getPageSize());
    }

    @Override
    public List<TrackPointVO> getPersonTrack(Long personId, ListParamsRequest listParamsRequest) {
        try {
            Person person = personMapper.selectById(personId);
            List<TrackPointVO> trackNoPage = personTrackUtil.getTrackList(personTrackUtil
                    .getRequest(person.getIdNumber(), listParamsRequest));
            trackNoPage.forEach(vo -> {
                vo.setName(person.getName());
                vo.setIsStayPoint(false);
            });
            return trackNoPage;
        } catch (RuntimeException e) {
            log.error("人员轨迹查询错误!");
        }
        return Collections.emptyList();
    }

    @Override
    public PageResult<MonitorListDto> getPersonMonitor(Long personId, ListParamsRequest listParamsRequest) {
        Person person = personMapper.selectById(personId);
        List<Long> monitorIds = personMapper.selectRelationMonitorId(person.getIdNumber());
        MonitorListByIdVO monitorListByIdVO = new MonitorListByIdVO();
        monitorListByIdVO.setMonitorIds(monitorIds);
        monitorListByIdVO.setPageParams(listParamsRequest.getPageParams());
        return controlService.getMonitorList(monitorListByIdVO);
    }

    @Override
    public List<TrackHeatMap> getPersonTrackHeatMap(Long personId, ListParamsRequest listParamsRequest) {
        try {
            Map<TrackHeatMap, List<TrackPointVO>> collect = selectTrackPage(personId, listParamsRequest,
                    PageParams.getAll()).getItems()
                    .stream().collect(Collectors.groupingBy(track -> new TrackHeatMap(track.getLng(), track.getLat())));
            collect.forEach((k, v) -> k.setFrequency(v.size()));
            return new ArrayList<>(collect.keySet());
        } catch (Exception e) {
            log.error("轨迹热力图查询错误! 错误信息为 {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TrackActivityTime> getPersonTrackActivityTime(Long personId, ListParamsRequest request) {
        if (personId == null) {
            throw new RuntimeException("未传入人员ID");
        }
        final TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "activeTime",
                TimeParams.class);
        if (timeParams == null) {
            throw new TRSException("未传时间筛选参数！");
        }
        final String beginStr = TimeUtil.localDateTimeToString(timeParams.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
        final String endStr = TimeUtil.localDateTimeToString(timeParams.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        List<String> identifiers = getIdentifiers(personId);
        TrajectoryQueryDTO trajectoryQueryDTO = new TrajectoryQueryDTO();
        trajectoryQueryDTO.setBeginStr(beginStr);
        trajectoryQueryDTO.setEndStr(endStr);
        trajectoryQueryDTO.setIdentifiers(identifiers);
        trajectoryQueryDTO.setGroupColumn(ACTIVITY_TIME);
        TrajectoryQueryStrategy queryStrategy = TrajectoryQueryStrategyFactory
                .getStrategy(properties.getTrackListDataSource());
        // 如果是统计今天
        List<String> times;
        if (TimeRangeEnum.TODAY.getCode().equals(timeParams.getRange())) {
            // 获取所有的小时
            times = generateCurrentDayHours();
            trajectoryQueryDTO.setFunctionName(FUNCTION_HOUR);
        } else {
            // 如果不是统计今天
            // 获取所有的日期
            times = getDatesBetween(timeParams.getBeginTime(), timeParams.getEndTime());
            trajectoryQueryDTO.setFunctionName(FUNCTION_DAY);
        }
        return setTrackActivityTime(queryStrategy.countTrackByColumn(trajectoryQueryDTO), times);
    }

    @Override
    public List<TrackActivityTime> getPersonTrendChart(Long personId, ListParamsRequest request) {
        if (personId == null) {
            throw new RuntimeException("未传入人员ID");
        }
        final TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "activeTime",
                TimeParams.class);
        final String beginStr = TimeUtil.localDateTimeToString(timeParams.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
        final String endStr = TimeUtil.localDateTimeToString(timeParams.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        List<String> identifiers = getIdentifiers(personId);
        TrajectoryQueryDTO trajectoryQueryDTO = new TrajectoryQueryDTO();
        trajectoryQueryDTO.setBeginStr(beginStr);
        trajectoryQueryDTO.setEndStr(endStr);
        trajectoryQueryDTO.setIdentifiers(identifiers);
        trajectoryQueryDTO.setFunctionName(TimeRangeEnum.TODAY.getCode().equals(timeParams.getRange()) ? FUNCTION_HOUR : FUNCTION_DAY);
        trajectoryQueryDTO.setGroupColumn(ACTIVITY_TIME);
        TrajectoryQueryStrategy queryStrategy = TrajectoryQueryStrategyFactory
                .getStrategy(properties.getTrackListDataSource());
        // 如果是统计今天
        List<String> times;
        if (TimeRangeEnum.TODAY.getCode().equals(timeParams.getRange())) {
            // 获取所有的小时
            times = generateCurrentDayHours();
        } else {
            // 如果不是统计今天
            // 获取所有的日期
            times = getDatesBetween(timeParams.getBeginTime(), timeParams.getEndTime());
        }
        return setTrackActivityTime(queryStrategy.countTrackByColumn(trajectoryQueryDTO), times);
    }

    @Override
    public PageResult<TrackActivityTime> getPersonTrendList(Long personId, ListParamsRequest request) {
        List<TrackActivityTime> personTrendChart = getPersonTrendChart(personId, request);
        // 根据参数排序
        SortParams sortParams = request.getSortParams();
        String sortField = sortParams.getSortField();
        String sortDirection = sortParams.getSortDirection();
        if (StringUtils.isNotEmpty(sortField) && StringUtils.isNotEmpty(sortDirection)) {
            String sortEnum = sortField + "_" + sortDirection;
            switch (sortEnum) {
                case "num_ascending":
                    personTrendChart.sort(Comparator.comparing(TrackActivityTime::getNum));
                    break;
                case "num_descending":
                    personTrendChart.sort(Comparator.comparing(TrackActivityTime::getNum).reversed());
                    break;
                case "node_ascending":
                    personTrendChart.sort(Comparator.comparing(TrackActivityTime::getNodeName));
                    break;
                case "node_descending":
                    personTrendChart.sort(Comparator.comparing(TrackActivityTime::getNodeName).reversed());
                    break;
                default:
            }

        }
        return PageResult.of(personTrendChart, request.getPageParams());
    }

    /**
     * 按照时间节点设置轨迹活动次数
     *
     * @param result 统计数据
     * @param times 所有时间节点
     *
     * @return 填充后的结果
     */
    private List<TrackActivityTime> setTrackActivityTime(List<TrackActivityTime> result, List<String> times) {
        for (String time : times) {
            if(result.stream().noneMatch(trackActivityTime -> trackActivityTime.getNodeName().equals(time))) {
                result.add(new TrackActivityTime(0, time, 0));
            }
        }
        return result.stream().sorted(Comparator.comparing(TrackActivityTime::getNodeName)).collect(Collectors.toList());
    }

    /**
     * 生成当天所有小时的时间字符串（格式：yyyy-MM-dd HH）
     *
     * @return 按时间顺序排列的小时列表
     */
    private List<String> generateCurrentDayHours() {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
        List<String> hourList = new ArrayList<>(24);
        for (int i = 0; i < 24; i++) {
            LocalDateTime currentHour = startOfDay.plusHours(i);
            hourList.add(currentHour.format(formatter));
        }
        return hourList;
    }

    /**
     * 获取两个时间点之间的所有日期（包含开始和结束日期）
     *
     * @param start 开始时间
     * @param end 结束时间
     *
     * @return 日期列表（格式：yyyy-MM-dd）
     */
    private List<String> getDatesBetween(LocalDateTime start, LocalDateTime end) {
        // 参数校验
        if (start.isAfter(end)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }

        // 提取日期部分
        LocalDate startDate = start.toLocalDate();
        LocalDate endDate = end.toLocalDate();

        // 计算总天数（包含结束日）
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;

        // 格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        List<String> dates = new ArrayList<>((int) days);
        for (int i = 0; i < days; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            dates.add(currentDate.format(formatter));
        }
        return dates;
    }

    @Override
    public PageResult<RegularMonitorListVO> getPersonRegularMonitor(Long personId, ListParamsRequest paramsRequest) {
        PageParams pageParams = paramsRequest.getPageParams();
        Page<RegularMonitorListVO> personRegularMonitorVO = personMapper.selectRegularMonitorList(personId,
                pageParams.toPage());
        List<RegularMonitorListVO> records = personRegularMonitorVO.getRecords();
        PersonVO person = profileService.findById(personId);
        if (Objects.isNull(person)) {
            throw new TRSException("人员不存在");
        }
        String personName = person.getName();
        records.parallelStream().forEach(vo -> vo.setTitle(vo.getCreateUser() + "对" + personName + "的常控"));
        return PageResult
                .of(records, pageParams.getPageNumber(), personRegularMonitorVO.getTotal(), pageParams.getPageSize());
    }

    @Override
    public List<IdNameCountVO> selectTrackWarningType(Long personId, String type, TimeParams timeParams) {
        switch (type) {
            case "warning":
                return personMapper.countTrackByWarningType(personId, timeParams.getBeginTime(),
                        timeParams.getEndTime());
            case "source":
                final String beginStr = TimeUtil.getSubscribeTime(timeParams.getBeginTime());
                final String endStr = TimeUtil.getSubscribeTime(timeParams.getEndTime());
                List<String> identifier = getIdentifiers(personId);
                // List<IdNameCountVO> result = trackMapper.countTrackBySourceType(identifier,
                // beginStr, endStr);
                List<IdNameCountVO> result = new ArrayList<>();
                for (IdNameCountVO r : result) {
                    String sourceType = r.getName();
                    DictDto source = dictService.getDictByTypeAndDesc(WARNING_SOURCE_TYPE, sourceType);
                    r.setId(source.getCode());
                    r.setName(source.getName());
                }
                return result;
            case "photo":
            default:
        }
        return Collections.emptyList();
    }

    @Override
    public PageResult<TrackPointVO> findAbnormalTrackPage(Long personId, ListParamsRequest request) {
        final PageParams pageParams = request.getPageParams();
        Page<TrackPointWithWarningIdVO> result = personMapper.selectTrackPage(personId,
                request.getFilterParams(), request.getSearchParams(), request.getSortParams(), pageParams.toPage());
        final List<Long> warningIdList = result.getRecords()
                .stream()
                .map(TrackPointWithWarningIdVO::getWarningId)
                .collect(Collectors.toList());
        List<ContentVO> contents = personMapper.selectContent(warningIdList);
        Map<Long, String[]> contentMap = warningIdList.isEmpty()
                ? new HashMap<>()
                : contents
                        .stream()
                        .filter(content -> Objects.nonNull(content.getContent()))
                        .collect(Collectors.toMap(ContentVO::getWarningId, ContentVO::getContent));
        List<TrackPointVO> list = result.getRecords().stream().map(record -> {
            record.setContent(contentMap.get(record.getWarningId()));
            TrackPointVO vo = new TrackPointVO();
            BeanUtils.copyProperties(record, vo);
            return vo;
        }).collect(Collectors.toList());
        return PageResult.of(list, pageParams.getPageNumber(), result.getTotal(),
                pageParams.getPageSize());
    }

    private List<String> getIdentifiers(Long personId) {
        PersonVO personVO = personService.getById(personId);
        List<String> identifiers = personVO.getCarNumber();
        identifiers.add(personVO.getCertificateNumber());
        identifiers.addAll(
                personVO.getVirtualIdentity().stream()
                        .map(PersonVirtualIdentityVO::getVirtualNumber)
                        .collect(Collectors.toList()));
        return identifiers;
    }

    @Override
    public List<WarningCardVO> findTrackWarningList(Long trackId) {
        return personMapper.selectWarningByTrack(trackId);
    }

    @Override
    public WarningPhotoVO getWarningPhoto(String trackId) {
        return null;
    }

    @Override
    public WarningSourceVO getWarningSource(String trackId) {
        return trackMapper.selectByZjlid(trackId);
    }

    @Override
    public List<TrackVO> getSinglePersonTrack(List<String> trackIds) {
        try {
            return trackMapper.selectTrackByZjlid(trackIds);
        } catch (RuntimeException e) {
            log.error("人员轨迹查询错误!");
        }
        return Collections.emptyList();
    }

    @Override
    public PageResult<TrackPointVO> getPersonTrackPage(Long personId, ListParamsRequest listParamsRequest) {
        Person person = personMapper.selectById(personId);
        Page<TrackPointVO> page = listParamsRequest.getPageParams().toPage();
        Page<TrackPointVO> result = personMapper.getTrack(listParamsRequest, page, person.getId());

        if (Objects.nonNull(result.getRecords()) && !result.getRecords().isEmpty()) {
            result.getRecords().forEach(e -> {
                e.setContentName(e.getName() + e.getTime() + "在" + e.getLocation() + "的轨迹");
            });
        }
        return PageResult.of(result.getRecords(), listParamsRequest.getPageParams().getPageNumber(), result.getTotal(),
                listParamsRequest.getPageParams().getPageSize());
        // return PageResult.empty(listParamsRequest.getPageParams());
    }

    @Override
    public PageResult<PersonArchiveWarningVO> getPersonAberration(PersonAberrationDTO personAberrationDTO) {
        Page<PersonArchiveWarningDto> page = Page.of(personAberrationDTO.getPageNum(),
                personAberrationDTO.getPageSize());
        Page<PersonArchiveWarningDto> warningPage = personMapper.getPersonAberration(personAberrationDTO, page);
        List<PersonArchiveWarningVO> collect = warningPage.getRecords().stream()
                .map(PersonArchiveWarningDto::toVo).collect(Collectors.toList());
        return PageResult.of(collect, personAberrationDTO.getPageNum(), warningPage.getTotal(),
                personAberrationDTO.getPageSize());
    }

    @Override
    public List<MonitorResultVO> addMonitorWarn(List<String> idCardList, Long userId, Long deptId) {
        Objects.requireNonNull(userId);
        Objects.requireNonNull(deptId);
        if (null == idCardList || idCardList.isEmpty()) {
            return new ArrayList<>();
        }
        CurrentUser user = permissionService.findCurrentUser(userId, deptId);
        List<CareMonitorVO> vos = idCardList.stream()
                .map(v -> buildMonitorVoByIdCard(v, user))
                .collect(Collectors.toList());
        MonitorResultVO success = new MonitorResultVO(true, new ArrayList<>());
        MonitorResultVO fail = new MonitorResultVO(false, new ArrayList<>());
        for (CareMonitorVO vo : vos) {
            try {
                Boolean b = controlService.initiateCareMonitorNoAuth(vo);
                if (Boolean.TRUE.equals(b)) {
                    success.getIdList().add(vo.getCertificateValue());
                } else {
                    fail.getIdList().add(vo.getCertificateValue());
                }
            } catch (Exception e) {
                log.error("感知订阅预警失败", e);
                fail.getIdList().add(vo.getCertificateValue());
            }
        }
        return Arrays.asList(success, fail);
    }

    private CareMonitorVO buildMonitorVoByIdCard(String idCard, CurrentUser user) {
        CareMonitorVO vo = new CareMonitorVO();
        vo.setCertificateType(IdentifierTypeEnum.ID_NUMBER.getCode());
        vo.setCertificateValue(idCard);
        Long id = BeanFactoryHolder.getEnv().getProperty("ys.control.sub.default.config", Long.class);
        vo.setMonitorConfigId(id);
        vo.setModuleCareMonitorHandlerClassName("com.trs.police.control.handler.care.impl.SubscriptionsMonitorHandler");
        vo.setCreateUserId(user.getId().toString());
        vo.setCreateDeptId(user.getDeptId().toString());
        return vo;
    }
}
