package com.trs.police.profile.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 统计结果基类
 *
 * <AUTHOR>
 * @date 2023/06/01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AbstractDataVO implements Serializable {

    private static final long serialVersionUID = 3620334326174349112L;

    /**
     * 数量
     */
    Integer count;

    /**
     * 环比
     */
    IndexVO monthOnMonth;

    /**
     * 同比
     */
    IndexVO yearOnYear;

    /**
     * 今年以来
     */
    Integer fromThisYear;

    /**
     * 名称
     */
    String name;

    /**
     * key，代码这些
     */
    String key;

    /**
     * 百分比
     */
    Double percent;

    public AbstractDataVO(AbstractDataVO other) {
        this.count = other.getCount();
        this.monthOnMonth = other.getMonthOnMonth();
        this.yearOnYear = other.getYearOnYear();
        this.fromThisYear = other.getFromThisYear();
    }

    public AbstractDataVO(Integer count,Integer fromThisYear) {
        this.count = count;
        this.fromThisYear = fromThisYear;
    }

    public AbstractDataVO(Integer count, IndexVO monthOnMonth, IndexVO yearOnYear, Integer fromThisYear) {
        this.count = count;
        this.monthOnMonth = monthOnMonth;
        this.yearOnYear = yearOnYear;
        this.fromThisYear = fromThisYear;
    }

    public AbstractDataVO(Integer count, IndexVO monthOnMonth, IndexVO yearOnYear, Integer fromThisYear, String name, String key) {
        this.count = count;
        this.monthOnMonth = monthOnMonth;
        this.yearOnYear = yearOnYear;
        this.fromThisYear = fromThisYear;
        this.name = name;
        this.key = key;
    }

    /**
     * dataVO相加
     *
     * @param data1 数据1
     * @param data2 数据2
     * @return 结果
     */
    public static AbstractDataVO plusDataVO(AbstractDataVO data1, AbstractDataVO data2) {
        AbstractDataVO total = new AbstractDataVO();
        total.setCount(data1.getCount() + data2.getCount());
        total.setMonthOnMonth(new IndexVO(data1.getMonthOnMonth().getCount() + data2.getMonthOnMonth().getCount(),
            data1.getMonthOnMonth().getLast() + data2.getMonthOnMonth().getLast()));
        total.setYearOnYear(new IndexVO(data1.getYearOnYear().getCount() + data2.getYearOnYear().getCount(),
            data1.getYearOnYear().getLast() + data2.getYearOnYear().getLast()));
        total.setFromThisYear(data1.getFromThisYear() + data2.getFromThisYear());
        return total;
    }
}
