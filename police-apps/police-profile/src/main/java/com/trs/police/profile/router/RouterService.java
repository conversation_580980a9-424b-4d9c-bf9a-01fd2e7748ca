package com.trs.police.profile.router;

import com.trs.police.common.core.constant.SystemDefaultRoleConstant;
import com.trs.police.common.core.excpetion.ParamValidationException;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.redis.starter.service.RedisService;
import com.trs.police.profile.mapper.ModuleMapper;
import com.trs.police.profile.mapper.RouterJsonMapper;
import com.trs.police.profile.schema.module.ModuleEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 路由表service
 *
 * <AUTHOR>
 * @date 2022/12/19 14:09
 */
@Service
@Slf4j
public class RouterService {

    @Resource
    private ModuleMapper moduleMapper;

    @Resource
    private RouterJsonMapper routerJsonMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private PermissionService permissionService;

    /**
     * 获取人员档案详情
     *
     * @return Operation集合
     */
    public List<Operation> getPersonProfileDetail() {
        List<Operation> list = getOperation(RouteConstant.PROFILE_PERSON_DETAIL);
        list.addAll(getModuleEntity(RouteConstant.PERSON).stream()
            .filter(module -> !RouteConstant.RELATED_WARNING.equals(module.getCnName()))
            .map(module -> {
                String cnName = module.getCnName();
                Operation operation = createOperation(RouteConstant.PROFILE_PERSON_DETAIL, cnName);
                if (RouteConstant.RELATED_MONITOR.equals(cnName)) {
                    operation.setKey(RouteConstant.PROFILE_PERSON_DETAIL + cnName + RouteConstant.MONITOR1);
                    operation.setName(cnName);
                }
                return operation;
            }).collect(Collectors.toList()));
        return list;
    }

    /**
     * 获取群体档案详情
     *
     * @return Operation集合
     */
    public List<Operation> getGroupProfileDetail() {
        List<Operation> list = getOperation(RouteConstant.PROFILE_GROUP_DETAIL);
        list.addAll(getModuleEntity(RouteConstant.GROUP).stream()
            .map(module -> {
                String cnName = module.getCnName();
                return createOperation(RouteConstant.PROFILE_GROUP_DETAIL, cnName);
            }).collect(Collectors.toList()));
        return list;
    }

    /**
     * 获取事件档案详情
     *
     * @return Operation集合
     */
    public List<Operation> getEventProfileDetail() {
        List<Operation> list = getOperation(RouteConstant.PROFILE_EVENT_DETAIL);
        list.addAll(getModuleEntity(RouteConstant.EVENT).stream()
            .map(moduleEntity -> {
                String cnName = moduleEntity.getCnName();
                return createOperation(RouteConstant.PROFILE_EVENT_DETAIL, cnName);
            }).collect(Collectors.toList()));
        return list;
    }

    /**
     * 获取线索档案
     *
     * @return Operation集合
     */
    public List<Operation> getClueProfileDetail() {
        List<Operation> list = getOperation(RouteConstant.PROFILE_CLUE_DETAIL);
        list.addAll(getModuleEntity(RouteConstant.CLUE).stream()
            .map(moduleEntity -> {
                String cnName = moduleEntity.getCnName();
                Operation operation = createOperation(RouteConstant.PROFILE_CLUE_DETAIL, cnName);
                if (RouteConstant.CLUE_MATERIAL.equals(cnName)) {
                    operation.setKey(RouteConstant.PROFILE_CLUE_DETAIL + cnName + RouteConstant.UPLOAD_MATERIAL1);
                    operation.setName(RouteConstant.UPLOAD_MATERIAL);
                }
                return operation;
            }).collect(Collectors.toList()));
        return list;
    }

    /**
     * 向前端返回路由json
     *
     * @param id 路由id
     * @return router
     */
    @Cacheable(value = {"routerCache"})
    public Router getRouter(Integer id) {
        String routerText = routerJsonMapper.selectJson(id);
        //        List<RouterList> fourProfileModule = router.getRoutes().stream().findFirst().orElse(new RouterList()).
//                getChildren().stream().filter(routerEntity -> "archives".equals(routerEntity.getPath()))
//                .collect(Collectors.toList());
//        fourProfileModule.forEach(routers -> routers.getChildren().forEach(children -> {
//            RouterList routerList = children.getChildren().stream().filter(detail -> "details".equals(detail.getPath()))
//                    .findAny().orElse(new RouterList());
//            routerList.getMeta().setOperations(insertJson(routerList.getMeta().getTitle()));
//        }));
        return Router.build(routerText);
    }

    /**
     * 重启时清除缓存
     */
    @PostConstruct
    private void deleteRedisCache() {
        log.info("删除路由相关缓存");
        redisService.delByPrefix("routerCache");
    }

    /**
     * 向路由表的Operation里插入数据
     *
     * @param id     路由id
     * @param router 路由表数据
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateRouter(Router router, Integer id) {
        permissionService.getUserListByRoleId(SystemDefaultRoleConstant.ADMIN)
            .stream()
            .filter(item -> item.getUserId().equals(AuthHelper.getNotNullUser().getId()))
            .findFirst()
            .ifPresentOrElse(item -> {
                final RouterJson routerJson = routerJsonMapper.selectById(id);
                routerJson.setRouter(router);
                routerJsonMapper.updateById(routerJson);
                redisService.delByPrefix("routerCache");
            }, () -> {
                throw new ParamValidationException("当前角色不是超级管理员！不能更新路由表");
            });
    }

    /**
     * 获取标题
     *
     * @param profileType 档案类型
     * @return 标题集合
     */
    public List<ModuleEntity> getModuleEntity(String profileType) {
        List<ModuleEntity> archiveModuleByType = moduleMapper
            .findArchiveModuleByType(profileType, RouteConstant.IS_ARCHIVE);
        return archiveModuleByType.stream()
            .filter(moduleEntity -> moduleEntity.getPid() != null || RouteConstant.SENSITIVE_TIME
                .equals(moduleEntity.getEnName()))
            .collect(Collectors.toList());
    }

    /**
     * 填充第一个operation
     *
     * @param profileType 档案类型
     * @return Operation集合
     */
    public List<Operation> getOperation(String profileType) {
        List<Operation> list = new ArrayList<>();
        list.add(new Operation(profileType + RouteConstant.LOOK, RouteConstant.DETAIL_LOOK));
        return list;
    }

    /**
     * 创建Operation
     *
     * @param profileType 档案类型
     * @param cnName      档案中文标题
     * @return Operation
     */
    public Operation createOperation(String profileType, String cnName) {
        return new Operation(profileType + cnName + RouteConstant.EDIT1, cnName + RouteConstant.EDIT);
    }

    /**
     * 向router表里插入operation
     *
     * @param type 档案类型
     * @return Operation集合
     */
    public List<Operation> insertJson(String type) {
        List<Operation> operations;
        switch (type) {
            case RouteConstant.PERSON_DETAIL:
                operations = getPersonProfileDetail();
                break;
            case RouteConstant.GROUP_DETAIL:
                operations = getGroupProfileDetail();
                break;
            case RouteConstant.EVENT_DETAIL:
                operations = getEventProfileDetail();
                break;
            case RouteConstant.CLUE_DETAIL:
                operations = getClueProfileDetail();
                break;
            default:
                operations = new ArrayList<>();
        }
        return operations;
    }


}
