package com.trs.police.profile.factory.strategy.trajectory;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.profile.domain.dto.TrajectoryQueryDTO;
import com.trs.police.profile.domain.vo.TrackActivityTime;
import com.trs.police.profile.domain.vo.TrackPointVO;

import java.util.List;

/**
 * 轨迹查询策略接口
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public interface TrajectoryQueryStrategy {
    /**
     *查询人员轨迹列表
     *
     * @param personId 人员id
     * @param request 查询条件
     * @return 分页列表
     */
    PageResult<TrackPointVO> getPersonTrackPage(Long personId, ListParamsRequest request);


    /**
     *按照字段统计
     *
     * @param trajectoryQueryDTO 查询条件
     * @return 统计结果
     */
    List<TrackActivityTime> countTrackByColumn(TrajectoryQueryDTO trajectoryQueryDTO);
}
