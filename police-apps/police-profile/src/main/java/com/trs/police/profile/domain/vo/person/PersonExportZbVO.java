package com.trs.police.profile.domain.vo.person;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员档案导出-政保
 *
 * <AUTHOR>
 * @date 2025/1/21
 */
@Data
@NoArgsConstructor
public class PersonExportZbVO implements Serializable {

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    @ColumnWidth(20)
    private String name;

    /**
     * 证件号码
     */
    @ExcelProperty("证件号码")
    @ColumnWidth(25)
    private String idNumber;

    /**
     * 常控标签
     */
    @ExcelProperty("常控标签")
    @ColumnWidth(20)
    private String regularLabelName;

    /**
     * 人员标签
     */
    @ExcelProperty("人员标签")
    @ColumnWidth(20)
    private String personLabelName;

    /**
     * 归属警种
     */
    @ExcelProperty("归属警种")
    @ColumnWidth(20)
    private String policeKindName;

    /**
     * 是否关注
     */
    @ExcelProperty("是否关注")
    @ColumnWidth(20)
    private String isFollowedName;

    /**
     * 关注时间
     */
    @ExcelProperty("关注时间")
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date followTime;

    /**
     * 档案状态
     */
    @ExcelProperty("档案状态")
    @ColumnWidth(20)
    private String profileStatusName;

    /**
     * 人员级别
     */
    @ExcelProperty("人员级别")
    @ColumnWidth(20)
    private String personLevelName;

    /**
     * 审批状态
     */
    @ExcelProperty("审批状态")
    @ColumnWidth(20)
    private String approvalStatueCodeName;

    /**
     * 责任民警
     */
    @ExcelProperty("责任民警")
    @ColumnWidth(20)
    private String dutyPoliceName;

    /**
     * 责任单位
     */
    @ExcelProperty("责任单位")
    @ColumnWidth(20)
    private String controlBureauName;

    /**
     * 户籍地所在区
     */
    @ExcelProperty("户籍地所在区")
    @ColumnWidth(25)
    private String registeredResidenceName;

    /**
     * 录入时间
     */
    @ExcelProperty("录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ColumnWidth(25)
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ColumnWidth(25)
    private Date updateTime;
}
