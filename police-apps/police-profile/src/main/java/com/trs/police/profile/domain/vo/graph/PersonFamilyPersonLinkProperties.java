package com.trs.police.profile.domain.vo.graph;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 人员-家庭人员关系属性
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PersonFamilyPersonLinkProperties extends AbstractLinkProperties {
    /**
     * 关系
     */
    private String realtion;
}
