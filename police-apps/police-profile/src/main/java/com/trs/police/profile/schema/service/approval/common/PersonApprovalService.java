package com.trs.police.profile.schema.service.approval.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.ApprovalStatusEnum;
import com.trs.police.common.core.constant.enums.DeptTypeEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.ProfileLabelPoliceKindRelationEntity;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.ProfileLabelPoliceKindRelationMapper;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.configure.UserContext;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.common.openfeign.starter.service.ControlService;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.profile.constant.enums.ApprovalDeptType;
import com.trs.police.profile.constant.enums.ProfileStatus;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.properties.ProfileProperties;
import com.trs.police.profile.schema.service.approval.ProfilePersonApprovalService;
import com.trs.police.profile.service.PersonArchiveService;
import com.trs.police.profile.service.impl.PersonMonitorServiceImpl;
import com.trs.police.profile.service.mp.PersonMpService;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.trs.police.common.core.constant.enums.ApprovalStatusEnum.IN_PROCESS;
import static com.trs.police.common.core.constant.enums.ApprovalStatusEnum.WAITING;

/**
 * 人员档案审批服务
 */
@Slf4j
@Component
public class PersonApprovalService extends ProfilePersonApprovalService {


    @Autowired
    private ApprovalService approvalService;

    @Resource
    private PersonMapper personMapper;

    @Autowired
    private ProfileProperties controlProperties;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    public ControlService controlService;

    @Resource
    private ProfileLabelPoliceKindRelationMapper policeKindRelationMapper;

    @Autowired
    private DictService dictService;

    @Autowired
    private PersonMpService personMpService;

    @Autowired
    private PersonMonitorServiceImpl personMonitorService;

    @Autowired
    private PersonArchiveService personArchiveService;


    @Override
    public void approval(Long relatedId, Consumer<ApprovalRequest> requestParamsSetter) {
        approvalPersonAdd(relatedId);
    }

    @Override
    public void finishApproval(ApprovalActionVO actionVO) {
        Long id = actionVO.getId();
        Person person = personMapper.selectById(id);
        if (Objects.isNull(person)) {
            try {
                TimeUnit.SECONDS.sleep(controlProperties.getMsgSleep());
            } catch (Exception e) {
                log.error("time sleep error", e);
            }
            person = personMapper.selectById(id);
        }
        if (Objects.isNull(person)) {
            log.warn("未能匹配到人员档案：{}", id);
            return;
        }
        // 修改person状态
        person.setApprovalStatueCode(actionVO.getApprovalStatus().getCode());
        UpdateWrapper<Person> wapper = new UpdateWrapper<Person>()
                .eq("id", person.getId())
                .set("approval_statue_code", actionVO.getApprovalStatus().getCode());
        personMapper.update(null, wapper);
        // 如果状态是通过，发起常控请求
        List<Long> policeKind = person.getPoliceKind();
        if (ApprovalStatusEnum.PASSED.equals(actionVO.getApprovalStatus())) {
            if (isFk(policeKind)) {
                personArchiveService.addMonitorWarn(Arrays.asList(person.getIdNumber()), person.getCreateUserId(), person.getCreateDeptId());
                // 同步到fk人员档案
                controlService.addFkryByPersonId(person.getId());
            } else {
                personMonitorService.regular(person);
            }
        }
    }

    @Override
    public void reApproval(Long id, Long policeKind) {

    }

    /**
     * 发起添加人员档案的审批请求
     *
     * @param relatedId 人员档案主键
     */
    public void approvalPersonAdd(Long relatedId) {
        Person person = personMapper.selectById(relatedId);
        // 发起审批请求
        createCompositeApproval(relatedId, person, Operation.ADD);
        // 修改person状态
        person.setApprovalStatueCode(IN_PROCESS.getCode());
        UpdateWrapper<Person> wapper = new UpdateWrapper<Person>()
                .eq("id", person.getId())
                .set("approval_statue_code", IN_PROCESS.getCode());
        personMapper.update(null, wapper);
    }

    /**
     * 发起添加人员档案的审批请求
     *
     * @param relatedId 人员档案主键
     * @param action ac
     */
    public void approvalPersonProfile(Long relatedId, Operation action) {
        Person person = personMapper.selectById(relatedId);
        // 发起审批请求
        createCompositeApproval(relatedId, person, action);
    }


    /**
     * 重启
     *
     * @param actionVO 消息
     * @return 人员
     */
    @Transactional(rollbackFor = Exception.class)
    public Person reopen(ApprovalActionVO actionVO) {
        Long id = actionVO.getId();
        Person person = personMapper.selectById(id);
        personMapper.getPersonByCertificateNumberAndTypeForUpdate(person.getIdNumber(), person.getIdType());
        // 除开审批中 其它状态都修改档案状态
        if (!IN_PROCESS.equals(actionVO.getApprovalStatus())) {
            person.setProfileStatus(ProfileStatus.NORMAL.getCode());
            personMapper.updateById(person);
        }
        return person;
    }

    /**
     * 归档
     *
     * @param actionVO 消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void finishArchive(ApprovalActionVO actionVO) {
        Long id = actionVO.getId();
        Person person = personMapper.selectById(id);
        personMapper.getPersonByCertificateNumberAndTypeForUpdate(person.getIdNumber(), person.getIdType());
        // 审批通过取消布控
        if (ApprovalStatusEnum.PASSED.equals(actionVO.getApprovalStatus())) {
            finishArchiveWhenPass(Arrays.asList(person), true);
        } else if (!Arrays.asList(WAITING, IN_PROCESS).contains(actionVO.getApprovalStatus())) {
            // 除开审批中 其它状态都修改档案状态
            person.setProfileStatus(ProfileStatus.NORMAL.getCode());
            personMapper.updateById(person);
        }
    }

    /**
     * 通过档案归档审批
     *
     * @param people 人员列表
     * @param needApproval 撤控是否需要审批
     */
    public void finishArchiveWhenPass(List<Person> people, Boolean needApproval) {
        if (CollectionUtils.isEmpty(people)) {
            return;
        }
        // 撤销常控
        if (Boolean.FALSE.equals(needApproval)) {
            String idStrList = people.stream()
                    .map(Person::getId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            Try.run(() -> controlService.batchRevoke(idStrList, "人员批量归档", Integer.valueOf(1)))
                    .onFailure(e -> log.error("取消常控失败", e));
        } else {
            for (Person person : people) {
                // 取消常控
                Try.run(() -> controlService.revoke(person.getId(), "人员归档", Integer.valueOf(1)))
                        .onFailure(e -> log.error("取消常控失败", e));
            }
        }
        // 跟新人员档案状态
        List<Long> ids = people.stream()
                .map(Person::getId)
                .collect(Collectors.toList());
        personMpService.lambdaUpdate()
                .in(Person::getId, ids)
                .set(Person::getProfileStatus, ProfileStatus.FINISH.getCode())
                .update();
        log.info("归档审批通过状态更新成功");
    }


    private void createCompositeApproval(Long relatedId, Person personInfo, Operation action) {
        ApprovalActionVO actionVO = new ApprovalActionVO();
        actionVO.setService(OperateModule.PROFILE_PERSON);
        actionVO.setId(relatedId);
        actionVO.setTitle(personInfo.getName());
        actionVO.setAction(action);
        ApprovalRequest request = new ApprovalRequest();
        request.setApprovalActionVO(actionVO);
        ApprovalDeptType tp = findMinApprovalType(personInfo.getPersonLabel());
        CurrentUser user = Objects.nonNull(AuthHelper.getCurrentUser())
                ? AuthHelper.getCurrentUser()
                : permissionService.findCurrentUser(UserContext.getCurrentUser().getId(), UserContext.getCurrentUser().getDeptId());
        log.info("当前用户:{}的人员标签：{}审批类型为：{}", user.getRealName(), personInfo.getPersonLabel(), tp);
        request.setApprovalConfigName(findConfigName(tp));
        request.setUser(UserDeptVO.of(user));
        // 第一级审批添加审批对象
        if (!tp.equals(ApprovalDeptType.OTHER)) {
            List<UserDeptVO> users = personInfo.getPersonLabel()
                    .stream()
                    .map(lb -> this.getApprovalUserInfo(lb, tp))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            // 根据部门id去重
            List<UserDeptVO> vos = users.stream()
                    .collect(Collectors.groupingBy(UserDeptVO::getDeptId))
                    .values()
                    .stream().map(list -> list.get(0))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(vos)) {
                // 没找到部门 不审批
                request.setApprovalConfigName(controlProperties.getPersonProfileApprovalDefault());
            }
            request.setTargetUserList(vos);
        }
        String s = approvalService.startApprovalWithResult(request);
        JSONObject result = JSON.parseObject(s);
        if (result.getInteger("code").equals(500)) {
            throw new TRSException(result.getString("message"));
        }
    }

    private String findConfigName(ApprovalDeptType tp) {
        // 返回需要审批的流程
        switch (tp) {
            case QZ_CENTER:
                return controlProperties.getPersonProfileApprovalQz();
            case DUTY_POLICE:
                return controlProperties.getPersonProfileApprovalDuty();
            case POLICE_STATION:
                return controlProperties.getPersonProfileApprovalPolice();
            case OTHER:
                return controlProperties.getPersonProfileApprovalDefault();
            default:
                throw new IllegalArgumentException("没有匹配到审批流程配置文件");
        }
    }

    /**
     * 寻找最小级别的审核类型
     *
     * @param label 标签列表
     * @return 审核类型
     */
    private ApprovalDeptType findMinApprovalType(Collection<Long> label) {
        if (CollectionUtils.isEmpty(label)) {
            return ApprovalDeptType.OTHER;
        }
        Optional<ApprovalDeptType> min = label.stream()
                .map(this::findApprovalType)
                .map(Optional::get)
                .min(Comparator.comparingDouble(ApprovalDeptType::getLevel));
        return min.get();
    }

    private Optional<ApprovalDeptType> findApprovalType(Long personLabel) {
        // 获取人员标签对应的责任警种
        Optional<List<Long>> dutyPoliceType = approvalPoliceDeptType(personLabel);
        // 如果没有责任警种（不需要审批）
        if (dutyPoliceType.isEmpty() || CollectionUtils.isEmpty(dutyPoliceType.get())) {
            log.warn("标签：{}没有匹配到责任警种，跳过审批", personLabel);
            return Optional.of(ApprovalDeptType.OTHER);
        }
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        // 根据用户类型选择配置
        Optional<DeptTypeEnum> dept = DeptTypeEnum.findDeptType(currentUser.getDept(), permissionService::getDeptById);
        if (dept.isEmpty()) {
            log.warn("未知的部门类型，当前部门id:{}, 跳过审批", currentUser.getDeptId());
            return Optional.of(ApprovalDeptType.OTHER);
        }
        switch (dept.get()) {
            case PROVINCIAL:
            case MUNICIPAL:
                log.warn("非区县用户添加人员档案，不审批，当前部门id:{}", currentUser.getDeptId());
                return Optional.of(ApprovalDeptType.OTHER);
            case COUNTY:
            case POLICE_STATION:
                DeptDto deptById = permissionService.getDeptById(currentUser.getDeptId());
                if (Objects.equals(deptById.getChildType(), controlProperties.getQzDeptType())) {
                    return Optional.of(ApprovalDeptType.QZ_CENTER);
                } else if (dutyPoliceType.get().contains(Optional.ofNullable(deptById.getChildType()).orElse(-1L))) {
                    return Optional.of(ApprovalDeptType.DUTY_POLICE);
                } else {
                    return Optional.of(ApprovalDeptType.POLICE_STATION);
                }
            default:
                log.warn("未知的部门类型，当前部门id:{}, 跳过审批", currentUser.getDeptId());
                return Optional.of(ApprovalDeptType.OTHER);
        }
    }

    /**
     * 根据标签确定责任警种
     *
     * @param personLabel 标签
     * @return 责任警种部门子类别
     */
    private Optional<List<Long>> approvalPoliceDeptType(Long personLabel) {
        LambdaQueryWrapper<ProfileLabelPoliceKindRelationEntity> wrapper = Wrappers.lambdaQuery(ProfileLabelPoliceKindRelationEntity.class)
                .eq(ProfileLabelPoliceKindRelationEntity::getLabelId, personLabel);
        List<Long> policeKind = policeKindRelationMapper.selectList(wrapper)
                .stream()
                .map(ProfileLabelPoliceKindRelationEntity::getPoliceKindId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(policeKind)) {
            return Optional.empty();
        }
        // 映射到警察码表中去 (名字映射)
        List<String> names = dictService.getDictListByType("police_kind")
                .stream()
                .filter(d -> policeKind.contains(d.getCode()))
                .map(DictDto::getName)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(names)) {
            return Optional.empty();
        }
        List<Long> result = dictService.getDictByType("dept_type")
                .stream()
                .filter(d -> StringUtils.isNotEmpty(d.getFlag()))
                .filter(d -> names.contains(d.getName()))
                .map(d -> Long.valueOf(d.getFlag()))
                .distinct()
                .collect(Collectors.toList());
        return result.isEmpty() ? Optional.empty() : Optional.of(policeKind);
    }

    private Optional<List<UserDeptVO>> getApprovalUserInfo(Long personLabel, ApprovalDeptType tp) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser.getDept().getChildType())) {
            DeptDto deptById = permissionService.getDeptById(currentUser.getDeptId());
            currentUser.getDept().setChildType(deptById.getChildType());
        }

        DeptDto topDept = permissionService.parenDeptInKeyPath(currentUser.getDeptId());
        switch (tp) {
            case QZ_CENTER:
                // 本部门的审批人审批
                UserDeptVO vo = new UserDeptVO();
                vo.setDeptId(currentUser.getDeptId());
                return Optional.of(Arrays.asList(vo));
            case DUTY_POLICE:
                // 找到情指中心审批
                List<DeptVO> list = permissionService.findByAreaAndPoliceKind(topDept.getDistrictCode(), controlProperties.getQzDeptType());
                return buildDeptVo(list);
            case POLICE_STATION:
                // 找到责任警种审批 如果实际就是责任人 不审批
                Optional<List<Long>> dutyPolice = approvalPoliceDeptType(personLabel);
                if (dutyPolice.isEmpty() || CollectionUtils.isEmpty(dutyPolice.get())) {
                    return Optional.empty();
                }
                if (dutyPolice.get().contains(Optional.ofNullable(currentUser.getDept().getChildType()).orElse(-1L))) {
                    return Optional.empty();
                }
                List<DeptVO> ds = dutyPolice
                        .get()
                        .stream()
                        .map(kind -> permissionService.findByAreaAndPoliceKind(topDept.getDistrictCode(), kind))
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
                return buildDeptVo(ds);
            default:
                return Optional.empty();
        }
    }


    private Optional<List<UserDeptVO>> buildDeptVo(List<DeptVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        List<UserDeptVO> vos = list.stream()
                .map(d -> {
                    UserDeptVO vo = new UserDeptVO();
                    vo.setDeptId(d.getDeptId());
                    return vo;
                })
                .collect(Collectors.toList());
        return Optional.ofNullable(vos);
    }

    private Boolean isFk(List<Long> policeKind) {
        if (CollectionUtils.isEmpty(policeKind)) {
            return false;
        }
        return policeKind.contains(BeanFactoryHolder.getEnv().getProperty("profile.fk.policeKind", Long.class, 12L));
    }
}
