package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName SearchFootholdEntity
 * @Description 落脚点对象实体
 * <AUTHOR>
 * @Date 2023/12/5 9:17
 **/
@Data
@TableName("tb_search_luo_jiao_dian")
public class SearchFootholdEntity implements Serializable {

    /**
     * MD5(要素标识符、要素值、最后出 现区域、采集地) 中间用“|”分隔
     */
    @TableId
    private String id;

    /**
     * 作业id，作为查询结果时使用
     */
    private String jobid;

    /**
     * 作业名
     */
    private String jobname;

    /**
     * 要素标识符：身份证、IMSI等
     */
    private String ysbsf;

    /**
     * 要素值
     */
    private String ysz;

    /**
     * 查询区域
     */
    private String cxqy;

    /**
     * 精度
     */
    private String jd;

    /**
     * 维度
     */
    private String wd;

    /**
     * geohash，需要比较高精度的，控制在100米范围内的，后续用来做排重
     */
    private String geohash;

    /**
     * 详细地址
     */
    private String xxdz;

    /**
     * 落脚点开始出现时间
     */
    private Date kscxsj;

    /**
     * 落脚点最后出现时间
     */
    private Date zhcxsj;

    /**
     * 计算类型，逗留：sojourn,轨迹：locus
     */
    private String jslx;

    /**
     * 计算周期类型：day: 按天，week: 按周，month: 按月
     */
    private String jszqlx;

    /**
     * 计算周期内出现次数
     */
    private Integer jszqcs;

    /**
     * 最后更新时间
     */
    private Date lastupdatetime;

}
