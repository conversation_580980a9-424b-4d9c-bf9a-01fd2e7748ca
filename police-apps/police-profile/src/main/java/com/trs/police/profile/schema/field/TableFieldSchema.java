package com.trs.police.profile.schema.field;

import com.trs.police.profile.domain.vo.TableFieldConfigVO;
import java.io.Serializable;
import lombok.Data;

/**
 * tableSchema格式
 *
 * <AUTHOR>
 */
@Data
public class TableFieldSchema implements Serializable {

    private static final long serialVersionUID = 6521255047278036271L;
    /**
     * 这一个item所占的列数
     */
    private Integer span;

    /**
     * 列表项标题
     */
    private String title;

    /**
     * 类型 text(文本)/label(标签数组)/user(用户信息)/mapLocation(地图点位)
     */
    private String type;

    /**
     * 字段是否可复制
     */
    private Boolean copyable;
    /**
     * 字段前图片url
     */
    private String titleSuffixIcon;


    private Boolean colorable;

    /**
     * 字段查询配置
     */
    private TableFieldConfigVO config;
}
