package com.trs.police.profile.service;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.ApprovalStatusEnum;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.openfeign.starter.vo.ProfileApprovalDetail;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 *  列表辅助
 *
 * <AUTHOR>
 */
public class ListResultHelper {

    /**
     * 构建审批状态
     *
     * @param paramsRequest 请求参数
     * @param list          列表
     * @param getApprovalDetail 获取审批详情
     * @param setApprovalDetail 设置审批详情
     * @param <T>               泛型
     */
    public static <T> void buildApprovalStatus(ListParamsRequest paramsRequest,
                                               List<T> list,
                                               Function<T, String> getApprovalDetail,
                                               BiConsumer<T, ProfileApprovalDetail> setApprovalDetail){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Optional<KeyValueTypeVO> first = paramsRequest.getFilterParams().stream().filter(f -> "policeKind".equals(f.getKey())).findFirst();
        if(!first.isPresent()){
            return;
        }
        String policeKind = first.get().getValue().toString();
        list.forEach(item -> {
            String approvalDetail = getApprovalDetail.apply(item);
            if(StringUtils.isNotEmpty(approvalDetail)){
                JSONObject jsonObject = JSONObject.parseObject(approvalDetail);
                ProfileApprovalDetail detail = jsonObject.getObject(policeKind, ProfileApprovalDetail.class);
                if(detail != null){
                    ApprovalStatusEnum approvalStatusEnum = ApprovalStatusEnum.codeOf(detail.getStatus());
                    if(approvalStatusEnum != null){
                        detail.setStatusName(approvalStatusEnum.getName());
                    }
                }
                setApprovalDetail.accept(item, detail);
            }
        });
    }
}
