package com.trs.police.profile.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 警种工具类
 *
 * <AUTHOR>
 * @date 2024/12/9
 */
public class PoliceKindConstants {

    // 其它
    public static final Long QT = 99L;

    /**
     * profile module中，警种enName与警种code的映射
     */
    public static final Map<String, Long> POLICE_KIND_ENNAME_CODE_MAP = new HashMap<>();

    /**
     * profile module中，警种警种code与中文的映射
     */
    public static final Map<Long, String> POLICE_KIND_CODE_CNNAME_MAP = new HashMap<>();

    /**
     * 导出档案时，警种cnName与警种导出缩写的映射
     */
    public static final Map<String, String> POLICE_KIND_CNNAME_EXPORTNAME_MAP = new HashMap<>();

    public static final Map<Long, String> KIND_CHILD_TYPE_MAP = new HashMap<>();

    static {
        POLICE_KIND_ENNAME_CODE_MAP.put("jz", 3L);
        POLICE_KIND_ENNAME_CODE_MAP.put("sd", 10L);
        POLICE_KIND_ENNAME_CODE_MAP.put("xz", 5L);
        POLICE_KIND_ENNAME_CODE_MAP.put("zb", 2L);
        POLICE_KIND_ENNAME_CODE_MAP.put("za", 4L);
        POLICE_KIND_ENNAME_CODE_MAP.put("qt", 99L);
        POLICE_KIND_ENNAME_CODE_MAP.put("crj", 6L);

        POLICE_KIND_CODE_CNNAME_MAP.put(3L, "经侦");
        POLICE_KIND_CODE_CNNAME_MAP.put(10L, "禁毒");
        POLICE_KIND_CODE_CNNAME_MAP.put(5L, "刑侦");
        POLICE_KIND_CODE_CNNAME_MAP.put(2L, "政保");
        POLICE_KIND_CODE_CNNAME_MAP.put(4L, "治安");
        POLICE_KIND_CODE_CNNAME_MAP.put(99L, "其他");
        POLICE_KIND_CODE_CNNAME_MAP.put(6L, "出入境");

        POLICE_KIND_CNNAME_EXPORTNAME_MAP.put("经侦", "JZ");
        POLICE_KIND_CNNAME_EXPORTNAME_MAP.put("禁毒", "JD");
        POLICE_KIND_CNNAME_EXPORTNAME_MAP.put("刑侦", "XZ");
        POLICE_KIND_CNNAME_EXPORTNAME_MAP.put("政保", "ZB");
        POLICE_KIND_CNNAME_EXPORTNAME_MAP.put("治安", "ZA");
        POLICE_KIND_CNNAME_EXPORTNAME_MAP.put("其他", "QT");
        POLICE_KIND_CNNAME_EXPORTNAME_MAP.put("出入境", "CRJ");

        KIND_CHILD_TYPE_MAP.put(3L, "3");
        KIND_CHILD_TYPE_MAP.put(10L, "10");
        KIND_CHILD_TYPE_MAP.put(5L, "5");
        KIND_CHILD_TYPE_MAP.put(2L, "2");
        KIND_CHILD_TYPE_MAP.put(4L, "4");
        KIND_CHILD_TYPE_MAP.put(6L, "6");
        KIND_CHILD_TYPE_MAP.put(98L, "6");
    }
}
