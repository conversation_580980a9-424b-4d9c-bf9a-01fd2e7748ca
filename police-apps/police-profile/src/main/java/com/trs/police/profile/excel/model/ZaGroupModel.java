package com.trs.police.profile.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用于对应治安警种群档的excel的实体类
 *
 * <AUTHOR>
 * @date 2024年04月02日 14:56
 */
@Data
public class ZaGroupModel {

    @ExcelProperty("群体名称")
    private String name;
    @ExcelProperty("群体类别")
    private String labelIds;
    @ExcelProperty("群体级别")
    private String controlLevel;
    @ExcelProperty("基本情况")
    private String basicInfo;
    @ExcelProperty("工作措施")
    private String workMeasures;
    @ExcelProperty("现实动向")
    private String realtimeTrend;
    @ExcelProperty("打处情况")
    private String punishInfo;
    @ExcelProperty("诉求情况")
    private String mainDemand;
    @ExcelProperty("工作难点")
    private String petitionInfo;
    @ExcelProperty("主管监管部门")
    private String controlGovernment;
    @ExcelProperty("主管监管部门责任人")
    private String controlGovernmentPerson;
    @ExcelProperty("主管监管部门联系方式")
    private String controlGovernmentContact;
    @ExcelProperty("化解责任单位")
    private String defuseGovernment;
    @ExcelProperty("化解责任人")
    private String defuseGovernmentPerson;
    @ExcelProperty("化解责任人联系方式")
    private String defuseGovernmentContact;
    @ExcelProperty("管控分县局")
    private String controlBureau;
    @ExcelProperty("负责人")
    private String controlBureauLeader;
    @ExcelProperty("负责人联系方式")
    private String controlBureauLeaderContact;
    @ExcelProperty("管控责任警种")
    private String controlPolice;
    @ExcelProperty("管控责任警种负责人")
    private String controlPoliceLeader;
    @ExcelProperty("管控责任警种负责人联系方式")
    private String controlPoliceLeaderContact;
    @ExcelProperty("相关人员")
    private String relatedPerson;

    @Override
    public String toString() {
        return name + "@" + super.toString();
    }


}
