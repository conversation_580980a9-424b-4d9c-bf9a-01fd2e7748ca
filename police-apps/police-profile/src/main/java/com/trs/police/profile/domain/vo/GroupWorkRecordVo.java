package com.trs.police.profile.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.configure.PoliceCloudCoreAutoConfigure;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024年05月24日 15:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupWorkRecordVo extends ProfileExportListVO implements Serializable {
    private static final long serialVersionUID = -253453753992036542L;

    private Long id;

    /**
     * 群档id
     */
    private Long groupId;

    /**
     * 群体名称
     */
    private String groupName;

    /**
     * 工作方式名称
     */
    private String workMethodName;

    /**
     * 群体状态名称
     */
    private String statusName;

    /**
     * 工作民警名称
     */
    private String workPoliceName;

    /**
     * 工作方式
     */
    private Long workMethod;
    /**
     * 群体状态：1-在控中,2-有异动,3-已失控,4-已稳控
     */
    private Integer status;
    /**
     * 失控时间
     */
    private LocalDateTime outOfControlTime;
    /**
     * 稳控时间
     */
    private Long inControlTime;

    /**
     * 工作详情
     */
    private String workDetail;
    /**
     * 当前去向
     */
    private String destination;
    /**
     * 附件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private FileInfoVO[] attachments;

    /**
     * 工作民警
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private SimpleUserVO[] workPolice;

    /**
     * 创建用户主键
     */
    private Long createUserId;

    /**
     * 创建单位主键
     */
    private Long createDeptId;

    /**
     * 更新时间
     */
    @JsonSerialize(using = PoliceCloudCoreAutoConfigure.LocalDateTimeSerializer.class)
    @JsonDeserialize(using = PoliceCloudCoreAutoConfigure.LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    /**
     * 更新用户主键
     */
    private Long updateUserId;

    /**
     * 更新单位主键
     */
    private Long updateDeptId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 新版工作方式
     */
    private RiskScoreConfigVO[] workMethodNew;

}
