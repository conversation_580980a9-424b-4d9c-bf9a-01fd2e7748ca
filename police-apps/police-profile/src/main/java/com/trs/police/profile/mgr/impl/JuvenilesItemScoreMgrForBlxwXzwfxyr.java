package com.trs.police.profile.mgr.impl;

import com.trs.police.profile.domain.entity.es.DwsWcnPoints;
import com.trs.police.profile.mgr.BaseJuvenilesItemScoreMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/24 20:59
 * @since 1.0
 */
@Component
public class JuvenilesItemScoreMgrForBlxwXzwfxyr extends BaseJuvenilesItemScoreMgr {
    /**
     * firstCategory<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 17:59
     */
    @Override
    public String firstCategory() {
        return "不良行为";
    }

    /**
     * secondCategory<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 17:59
     */
    @Override
    public String secondCategory() {
        return "行政违法嫌疑人";
    }

    /**
     * makeScore<BR>
     *
     * @param item 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:06
     */
    @Override
    public Long makeScore(Optional<DwsWcnPoints> item) {
        return makeOneScore(item) * Math.min(makeCount(item), 5L);
    }

    /**
     * makeOneScore<BR>
     *
     * @param item 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:05
     */
    @Override
    public Long makeOneScore(Optional<DwsWcnPoints> item) {
        return 5L;
    }

    /**
     * order<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/31 09:16
     */
    @Override
    public Integer order() {
        return 3;
    }
}
