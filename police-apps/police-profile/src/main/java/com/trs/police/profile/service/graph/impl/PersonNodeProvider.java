package com.trs.police.profile.service.graph.impl;

import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.enums.NodeTypeEnum;
import com.trs.police.profile.domain.vo.graph.GraphNodeVO;
import com.trs.police.profile.factory.NodeFactory;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.service.graph.NodeProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 人员节点提供者
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@RequiredArgsConstructor
public class PersonNodeProvider implements NodeProvider {

    private final PersonMapper personMapper;

    @Override
    public String getNodeType() {
        return NodeTypeEnum.PERSON.getCode();
    }

    @Override
    public GraphNodeVO<?> getNodeById(String id) {
        Person person = personMapper.selectById(Long.valueOf(id));
        if (person == null) {
            return null;
        }
        return NodeFactory.createPersonNode(person, NodeTypeEnum.PERSON);
    }
}