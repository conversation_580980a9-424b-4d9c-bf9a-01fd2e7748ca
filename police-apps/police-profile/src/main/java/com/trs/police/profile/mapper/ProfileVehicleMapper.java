package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.profile.domain.entity.ProfileVehicleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 人员车辆查询
 *
 * <AUTHOR>
 * @date 2022/12/27 11:27
 */
@Mapper
public interface ProfileVehicleMapper extends BaseMapper<ProfileVehicleEntity> {


    /**
     * 根据车牌号和所属人查询车辆
     *
     * @param carNumber 车牌号
     * @param owner     所有人
     * @return 车辆信息
     */
    @Select("select * from t_profile_vehicle where car_number=#{carNumber} and owner=#{owner} limit 1")
    ProfileVehicleEntity getByCarNumber(@Param("carNumber") String carNumber, @Param("owner") String owner);
}
