package com.trs.police.profile.factory;

import com.trs.police.profile.domain.entity.Group;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.enums.NodeTypeEnum;
import com.trs.police.profile.domain.vo.RelatedPersonVO;
import com.trs.police.profile.domain.vo.graph.GraphNodeVO;
import com.trs.police.profile.domain.vo.graph.GroupNodeProperties;
import com.trs.police.profile.domain.vo.graph.PersonNodeProperties;

/**
 * 节点工厂类
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
public class NodeFactory {

    /**
     * 创建人员节点
     *
     * @param person 人员实体
     * @param nodeTypeEnum 节点类型
     * @return 人员节点
     */
    public static GraphNodeVO<PersonNodeProperties> createPersonNode(Person person, NodeTypeEnum nodeTypeEnum) {
        if (person == null) {
            return null;
        }

        GraphNodeVO<PersonNodeProperties> node = new GraphNodeVO<>();
        node.setId(person.getId().toString());
        node.setName(person.getName());
        node.setType(nodeTypeEnum.getCode());
        node.setTypeName(nodeTypeEnum.getName());

        // 设置节点属性
        PersonNodeProperties properties = new PersonNodeProperties();
        properties.setIdNumber(person.getIdNumber());
        properties.setGender(person.getGender());
        properties.setControlLevel(person.getControlLevel());
        node.setProperties(properties);

        return node;
    }

    /**
     * 创建群体节点
     *
     * @param group 群体实体
     * @param nodeTypeEnum 节点类型
     * @return 群体节点
     */
    public static GraphNodeVO<GroupNodeProperties> createGroupNode(Group group, NodeTypeEnum nodeTypeEnum) {
        if (group == null) {
            return null;
        }

        GraphNodeVO<GroupNodeProperties> node = new GraphNodeVO<>();
        node.setId(group.getId().toString());
        node.setName(group.getName());
        node.setType(nodeTypeEnum.getCode());
        node.setTypeName(nodeTypeEnum.getName());

        // 设置节点属性
        GroupNodeProperties properties = new GroupNodeProperties();
        properties.setGroupLabel(group.getGroupLabel());
        node.setProperties(properties);

        return node;
    }

    /**
     * 创建人员节点
     *
     * @param person 人员实体
     * @return 人员节点
     */
    public static GraphNodeVO<PersonNodeProperties> createFamilyPersonNode(RelatedPersonVO person) {
        if (person == null) {
            return null;
        }

        GraphNodeVO<PersonNodeProperties> node = new GraphNodeVO<>();
        node.setId(person.getIdNumber());
        node.setName(person.getName());
        node.setType(NodeTypeEnum.FAMILY_PERSON.getCode());
        node.setTypeName(NodeTypeEnum.FAMILY_PERSON.getName());

        // 设置节点属性
        PersonNodeProperties properties = new PersonNodeProperties();
        properties.setIdNumber(person.getIdNumber());
        properties.setGender(person.getGender());
        properties.setControlLevel(person.getControlLevel());
        properties.setHavingWarning(person.getHavingWarning());
        node.setProperties(properties);

        return node;
    }
}
