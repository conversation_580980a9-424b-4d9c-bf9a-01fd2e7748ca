package com.trs.police.profile.factory;

import com.trs.police.profile.domain.entity.PersonGroupRelation;
import com.trs.police.profile.domain.enums.RelationTypeEnum;
import com.trs.police.profile.domain.vo.RelatedPersonVO;
import com.trs.police.profile.domain.vo.graph.GraphLinkVO;
import com.trs.police.profile.domain.vo.graph.PersonFamilyPersonLinkProperties;
import com.trs.police.profile.domain.vo.graph.PersonGroupLinkProperties;

/**
 * 关系工厂类
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
public class RelationFactory {

    /**
     * 创建人员-群体关系
     *
     * @param personId 人员ID
     * @param groupId  群体ID
     * @param relation 关系实体
     * @return 关系
     */
    public static GraphLinkVO<PersonGroupLinkProperties> createPersonToGroupRelation(String personId, String groupId, PersonGroupRelation relation) {
        if (relation == null) {
            return null;
        }

        GraphLinkVO<PersonGroupLinkProperties> link = new GraphLinkVO<>();
        link.setId(personId + "_" + groupId);
        link.setType(RelationTypeEnum.PERSON_GROUP.getCode());
        link.setTypeName(RelationTypeEnum.PERSON_GROUP.getName());
        link.setSource(personId);
        link.setTarget(groupId);

        // 设置关系属性
        PersonGroupLinkProperties properties = new PersonGroupLinkProperties();
        properties.setActivityLevel(relation.getActivityLevel());
        properties.setGroupWork(relation.getGroupWork());
        properties.setPoliceKind(relation.getPoliceKind());
        link.setProperties(properties);

        return link;
    }

    /**
     * 创建群体-人员关系
     *
     * @param groupId  群体ID
     * @param personId 人员ID
     * @param relation 关系实体
     * @return 关系
     */
    public static GraphLinkVO<PersonGroupLinkProperties> createGroupToPersonRelation(String groupId, String personId, PersonGroupRelation relation) {
        if (relation == null) {
            return null;
        }

        GraphLinkVO<PersonGroupLinkProperties> link = new GraphLinkVO<>();
        link.setId(groupId + "_" + personId);
        link.setType(RelationTypeEnum.GROUP_PERSON.getCode());
        link.setTypeName(RelationTypeEnum.GROUP_PERSON.getName());
        link.setSource(groupId);
        link.setTarget(personId);

        // 设置关系属性
        PersonGroupLinkProperties properties = new PersonGroupLinkProperties();
        properties.setActivityLevel(relation.getActivityLevel());
        properties.setGroupWork(relation.getGroupWork());
        properties.setPoliceKind(relation.getPoliceKind());
        link.setProperties(properties);

        return link;
    }

    /**
     * 创建群体-人员关系
     *
     * @param personId 人员ID
     * @param relation 关系实体
     * @return 关系
     */
    public static GraphLinkVO<PersonFamilyPersonLinkProperties> createPersonToFamilyPersonRelation(String personId, RelatedPersonVO relation) {
        if (relation == null) {
            return null;
        }

        GraphLinkVO<PersonFamilyPersonLinkProperties> link = new GraphLinkVO<>();
        link.setId(personId + "_" + relation.getIdNumber());
        link.setType(RelationTypeEnum.PERSON_FAMILY_PERSON.getCode());
        link.setTypeName(RelationTypeEnum.PERSON_FAMILY_PERSON.getName());
        link.setSource(personId);
        link.setTarget(relation.getIdNumber());

        // 设置关系属性
        PersonFamilyPersonLinkProperties properties = new PersonFamilyPersonLinkProperties();
        properties.setRealtion(relation.getRealtion());
        link.setProperties(properties);

        return link;
    }
}
