package com.trs.police.profile.util;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.profile.domain.vo.PersonInfoVo;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 基础信息缓存-用户和部门，减少查库
 * @date 2023/11/17 10:03
 */
public class BasicInfoCache {

    /**
     * 缓存用户信息
     */
    public static Map<Long, UserDto> UserInfoCache = new HashMap<>();

    /**
     * 缓存部门信息
     */
    public static Map<Long, DeptDto> DeptInfoCache = new HashMap<>();

    /**
     * 缓存部门信息-通过code缓存
     */
    public static Map<String, DeptDto> DeptInfoCacheOfCode = new HashMap<>();

    /**
     * 缓存用户信息：PersonInfoVo
     */
    public static Map<Long, PersonInfoVo> PersonInfoVoCache = new HashMap<>();

    /**
     * 缓存标签信息：key为type_labelName,value为标签id
     */
    public static Map<String, Long> LabelInfoCache = new HashMap<>();

}
