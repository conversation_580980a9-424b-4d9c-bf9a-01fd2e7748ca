package com.trs.police.profile.schema.field;

import com.trs.police.common.core.constant.enums.DataPermissionEnum;
import com.trs.police.profile.schema.db.DatabaseInfo;
import java.io.Serializable;
import lombok.Data;

/**
 * 动态列表 数据权限配置
 *
 * <AUTHOR>
 */
@Data
public class DataPermission implements Serializable {

    private static final long serialVersionUID = -825961627379901645L;

    /**
     * 字段配置
     */
    private DatabaseInfo db;

    /**
     * 配置对应权限级别(全大写字符串)
     * {@link DataPermissionEnum}
     */
    private String level;

    /**
     * 对应权限信息字段
     */
    private String infoField;

}
