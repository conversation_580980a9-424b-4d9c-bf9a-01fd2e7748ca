package com.trs.police.profile.util;


import org.apache.commons.lang3.StringUtils;

/**
 * 工具
 *
 * <AUTHOR>
 * @since 2022/11/21 17:20
 **/
public class PersonUtil {

    /**
     * 根据身份证获取性别
     *
     * @param idNumber 身份证
     * @return gender
     * @throws IllegalArgumentException 异常
     */
    public static Integer judgeGender(String idNumber) throws IllegalArgumentException {
        if (StringUtils.isEmpty(idNumber)){
            return null;
        }
        int gender = 0;
        if (idNumber.length() == 18) {
            //如果身份证号18位，取身份证号倒数第二位
            char c = idNumber.charAt(idNumber.length() - 2);
            gender = Integer.parseInt(String.valueOf(c));
        } else if (idNumber.length() == 15){
            //如果身份证号15位，取身份证号最后一位
            char c = idNumber.charAt(idNumber.length() - 1);
            gender = Integer.parseInt(String.valueOf(c));
        } else {
            return null;
        }
        if (gender % 2 == 1) {
            return 1;
        } else {
            return 2;
        }
    }
}
