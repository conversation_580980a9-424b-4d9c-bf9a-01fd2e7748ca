package com.trs.police.profile.service.graph.impl;

import com.trs.police.profile.domain.dto.graph.GraphQueryDTO;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.entity.PersonGroupRelation;
import com.trs.police.profile.domain.enums.NodeTypeEnum;
import com.trs.police.profile.domain.enums.RelationTypeEnum;
import com.trs.police.profile.domain.vo.graph.GraphLinkVO;
import com.trs.police.profile.domain.vo.graph.GraphNodeVO;
import com.trs.police.profile.factory.NodeFactory;
import com.trs.police.profile.factory.RelationFactory;
import com.trs.police.profile.mapper.GroupMapper;
import com.trs.police.profile.mapper.PersonGroupRelationMapper;
import com.trs.police.profile.service.graph.RelationProvider;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * fk群体到人员关系提供者
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@RequiredArgsConstructor
public class FkGroupToPersonRelationProvider implements RelationProvider {

    private final PersonGroupRelationMapper personGroupRelationMapper;

    private final GroupMapper groupMapper;

    @Override
    public String getSourceNodeType() {
        return NodeTypeEnum.FKGROUP.getCode();
    }

    @Override
    public RelationResult queryRelations(String sourceNodeId, GraphQueryDTO queryDTO) {
        // 检查是否需要过滤节点类型
        if (CollectionUtils.isNotEmpty(queryDTO.getNodeTypes())
                && !queryDTO.getNodeTypes().contains(NodeTypeEnum.FKPERSON.getName())
                && !queryDTO.getNodeTypes().contains(NodeTypeEnum.FKPERSON.getCode())) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        // 检查是否需要过滤关系类型
        if (CollectionUtils.isNotEmpty(queryDTO.getRelationTypes())
                && !queryDTO.getRelationTypes().contains(RelationTypeEnum.GROUP_PERSON.getName())
                && !queryDTO.getRelationTypes().contains(RelationTypeEnum.GROUP_PERSON.getCode())) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        // 查询群体-人员关系
        List<Person> personList = personGroupRelationMapper.getPersonGroupListByGroupId(
                Long.valueOf(sourceNodeId), queryDTO.getPoliceKind())
                .stream().map(pg->{
                    Person person = new Person();
                    person.setId(pg.getId());
                    person.setName(pg.getName());
                    person.setIdNumber(pg.getIdNumber());
                    return person;
                }).collect(Collectors.toList());
        List<Person> relatedGtWarningPersonList = groupMapper.getRelatedGtWarningPerson(Long.valueOf(sourceNodeId))
                .stream().map(gt->{
                    Person person = new Person();
                    person.setId(gt.getId());
                    person.setName(gt.getName());
                    person.setIdNumber(gt.getIdNumber());
                    return person;
                }).collect(Collectors.toList());
        personList.addAll(relatedGtWarningPersonList);

        if (CollectionUtils.isEmpty(personList)) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        List<GraphLinkVO<?>> links = new ArrayList<>();
        List<GraphNodeVO<?>> targetNodes = new ArrayList<>();

        // 构建关系和节点
        for (Person person : personList) {
            // 创建人员节点
            GraphNodeVO<?> personNode = NodeFactory.createPersonNode(person, NodeTypeEnum.FKPERSON);
            targetNodes.add(personNode);

            // 创建群体-人员关系
            PersonGroupRelation relation = new PersonGroupRelation();
            relation.setPersonId(person.getId());
            relation.setGroupId(Long.valueOf(sourceNodeId));

            GraphLinkVO<?> link = RelationFactory.createGroupToPersonRelation(
                    sourceNodeId, person.getId().toString(), relation);
            links.add(link);
        }

        return new RelationResult(links, targetNodes);
    }
}