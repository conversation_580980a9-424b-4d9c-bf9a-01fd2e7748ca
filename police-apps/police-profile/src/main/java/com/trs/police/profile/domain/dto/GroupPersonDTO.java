package com.trs.police.profile.domain.dto;


import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024年04月10日 14:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GroupPersonDTO extends BaseDTO {

    /**
     * 群档id
     */
    private Long groupId;

    /**
     * 人员等级
     */
    private String personLevel;

    /**
     * 活跃程度
     */
    private Integer activityLevel;

    /**
     * 筛选分类code
     */
    private Integer groupType;

    /**
     * 所属警种
     */
    private Integer policeKind;
}
