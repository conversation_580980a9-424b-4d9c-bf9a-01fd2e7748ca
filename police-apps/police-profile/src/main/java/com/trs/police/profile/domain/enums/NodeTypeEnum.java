package com.trs.police.profile.domain.enums;

import lombok.Getter;

/**
 * 节点类型枚举
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Getter
public enum NodeTypeEnum {
    /**
     * 人员节点
     */
    PERSON("PERSON", "人员"),

    /**
     * 群体节点
     */
    GROUP("GROUP", "群体"),

    /**
     * 事件节点，预留扩展
     */
    EVENT("EVENT", "事件"),
    /**
     * 人员节点
     */
    FKPERSON("FKPERSON", "fk人员"),

    /**
     * 群体节点
     */
    FKGROUP("FKGROUP", "fk群体"),

    /**
     * 家庭人员节点
     */
    FAMILY_PERSON("FAMILY_PERSON", "家庭人员");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    NodeTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static NodeTypeEnum getByCode(String code) {
        for (NodeTypeEnum type : NodeTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
