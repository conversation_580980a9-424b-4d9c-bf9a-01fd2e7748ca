package com.trs.police.profile.domain.entity.yq;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * yq人员与标签关系实体
 *
 * <AUTHOR>
 * @date 2021/7/30 15:47
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@TableName("T_PS_PERSON_LABEL_RELATION")
public class YqPersonLabelRelationEntity implements Serializable {

    private static final long serialVersionUID = 1457829635436482503L;


    /**
     * 数据主键
     */
    @Id
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 数据创建用户id
     */
    @CreatedBy
    private String crBy;
    /**
     * 数据创建用户姓名
     */
    private String crByName;

    /**
     * 数据创建时间
     */
    @CreatedDate
    private LocalDateTime crTime;

    /**
     * 数据最后更新人
     */
    @LastModifiedBy
    private String upBy;

    /**
     * 数据最后更新人
     */
    private String upByName;

    /**
     * 数据最后更新时间
     */
    @LastModifiedDate
    private LocalDateTime upTime;

    /**
     * 创建部门名称
     */
    private String crDept;

    /**
     * 创建部门单位代码
     */
    private String crDeptCode;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 证件号码
     */
    @TableField(exist = false)
    private String idNumber;

    /**
     * 标签id
     */
    private String labelId;

    /**
     * 构造方法
     *
     * @param personId 人员id
     * @param labelId  标签id
     */
    public void personLabelRelationEntity(String personId, String labelId) {
        this.personId = personId;
        this.labelId = labelId;
    }
}
