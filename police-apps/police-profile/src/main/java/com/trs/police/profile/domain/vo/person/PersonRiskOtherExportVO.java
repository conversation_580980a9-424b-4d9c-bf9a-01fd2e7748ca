package com.trs.police.profile.domain.vo.person;

import lombok.Data;

/**
 * 人员-风险点信息-刑侦、政保、其他-导出
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
public class PersonRiskOtherExportVO{
    /**
     * 人员级别
     * 经侦、其他、5、99：profile_person_control_level
     * 政保、2：profile_person_person_level_zb
     */
    private String personLevel;

    /**
     * 风险背景
     */
    private String mainDemand;

    /**
     * 化解难点
     */
    private String petitionInfo;

    /**
     * 前科背景
     */
    private String qkbj;
}
