package com.trs.police.profile.domain.statement;

import com.trs.police.common.core.excpetion.TRSException;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/11/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeleteStatement extends BaseStatement {

    String keyName;

    String keyValue;

    @Override
    public String toSql() {
        this.syntaxValidation();
        return String.format("delete from %s where %s", table, getWhere());
    }

    private String getWhere() {
        return String.format("%s=%s", keyName, keyValue);
    }

    @Override
    protected void syntaxValidation() {
        super.syntaxValidation();
        if (StringUtils.isBlank(keyName) || Objects.isNull(keyValue)) {
            throw new TRSException("sql语句创建失败，主键为空！");
        }
    }
}
