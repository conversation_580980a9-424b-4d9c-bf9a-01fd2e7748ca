package com.trs.police.profile.domain.vo;

import com.trs.police.profile.constant.enums.ModuleDisplayTypeEnum;
import com.trs.police.profile.schema.module.ModuleEntity;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 档案目录VO
 *
 * <AUTHOR>
 * @date 2021/08/02
 */
@Data
@AllArgsConstructor
public class ModuleVO implements Serializable {

    private static final long serialVersionUID = -4687311076910196633L;
    /**
     * 名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 类型
     */
    private String type;

    /**
     * 计数 临时处理成1
     */
    private Integer count = 0;

    /**
     * 模块编号
     */
    private Long moduleId;

    /**
     * 上级id
     */
    private Long pid;

    /**
     * 子节点
     */
    private List<ModuleVO> children;

    /**
     * 类型
     */
    private String moduleType;

    /**
     * 构造方法
     *
     * @param moduleId 编号
     * @param name     模块名
     * @param enName   模块英文名
     *
     */
    public ModuleVO(Long moduleId, String name, String enName) {
        this.name = name;
        this.enName = enName;
        this.moduleId = moduleId;
    }

    /**
     * 构造方法
     *
     * @param module 模块
     * @param type   展示类型
     */
    public ModuleVO(ModuleEntity module, ModuleDisplayTypeEnum type) {
        this.name = module.getCnName();
        this.enName = module.getEnName();
        this.moduleId = module.getId();
        if (type.equals(ModuleDisplayTypeEnum.ADD)) {
            this.type = module.getAddSchemaType() == null ? null : module.getAddSchemaType().getName();
        } else if (type.equals(ModuleDisplayTypeEnum.SHOW)) {
            this.type = module.getShowSchemaType() == null ? null : module.getShowSchemaType().getName();
        }
        this.pid = module.getPid();
        this.moduleType = module.getType();
    }

    /**
     * 构造方法
     *
     * @param count    数量
     * @param name     模块名
     * @param moduleId 编号
     */
    public ModuleVO(Long moduleId, String name, Integer count) {
        this.name = name;
        this.count = count;
        this.moduleId = moduleId;
    }
}
