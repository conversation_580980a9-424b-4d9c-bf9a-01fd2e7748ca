package com.trs.police.profile.domain.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/24 13:51
 * @since 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class BaseJuvenilesSearchDTO extends BaseListDTO {

    /**
     * 支持搜索的方式
     */
    protected static final List<String> SEARCH_TYPES = Arrays.asList("all", "name", "zjhm", "address");

    private String sex;

    private String zjhm;

    private Integer minAge;

    private Integer maxAge;

    private String searchType;

    private String keyword;

    private String startTime;

    private String endTime;

    private String searchKeys;

    /**
     * makeMinAndMaxYear<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 13:53
     */
    public Tuple2<Integer, Integer> makeMinAndMaxYear() {
        Integer now = TimeUtils.getFieldOfDate(new Date(), Calendar.YEAR);
        return Tuple.of(
                Optional.ofNullable(getMaxAge()).map(i -> Integer.max(now - i, 0)).orElse(null),
                Optional.ofNullable(getMinAge()).map(i -> Integer.max(now - i, 0)).orElse(null)
        );
    }

    /**
     * makeSex<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 13:56
     */
    public List<String> makeSex() {
        return StringUtils.getList(getSex(), true);
    }

    /**
     * makeZjhm<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 13:56
     */
    public List<String> makeZjhm() {
        return StringUtils.getList(getZjhm(), true);
    }

    /**
     * makeSearchKeys<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/31 11:01
     */
    public List<String> makeSearchKeys() {
        return StringUtils.getList(getSearchKeys(), true);
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        if (StringUtils.isNotEmpty(getKeyword())) {
            PreConditionCheck.checkNotEmpty(getSearchType(), new ParamInvalidException("搜索类型不能为空"));
            if (!SEARCH_TYPES.contains(getSearchType())) {
                throw new ParamInvalidException("搜索类型不合法");
            }
        }
        return super.checkParams();
    }
}
