package com.trs.police.profile.mgr;

import com.alibaba.nacos.common.utils.Objects;
import com.trs.police.common.core.constant.enums.ListSourceTypeEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.JwzhDictVO;
import com.trs.police.common.core.vo.control.MonitorInfoVO;
import com.trs.police.common.core.vo.profile.ListSourceVO;
import com.trs.police.common.openfeign.starter.service.ControlService;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.RiskService;
import com.trs.police.profile.domain.entity.RiskLabelEntity;
import com.trs.police.profile.domain.vo.MapLocationVO;
import com.trs.police.profile.domain.vo.ProfileExportListVO;
import com.trs.police.profile.domain.vo.ProfileInspectorExportVO;
import com.trs.police.profile.domain.vo.ProfileInspectorVO;
import com.trs.police.profile.mapper.LabelMapper;
import com.trs.police.profile.mapper.RiskLabelMapper;
import com.trs.police.profile.service.ProfileInspectorService;
import com.trs.police.profile.util.DictUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-03-04 11:07
 */
@Component
public class ProfileExportMgr {

    @Resource
    private DictService dictService;

    @Resource
    private LabelMapper labelMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private ProfileInspectorService profileInspectorService;

    @Resource
    private RiskLabelMapper riskLabelMapper;

    /**
     * 根据code获取码表中的name
     *
     * @param value code
     * @param type  类型
     * @return name
     */
    public String dictCodeToName(Integer value, String type) {
        if (value == null) {
            return null;
        }
        Long code = Long.valueOf(value);
        return dictCodeToName(code, type);
    }

    /**
     * 根据code获取码表中的name
     *
     * @param code code
     * @param type 类型
     * @return name
     */
    public String dictCodeToName(Long code, String type) {
        if (code == null) {
            return null;
        }
        DictDto dictDto = dictService.getDictByTypeAndCode(type, code);
        if (Objects.isNull(dictDto)) {
            return null;
        }
        return dictDto.getName();
    }

    /**
     * 根据jwzh code获取码表中的name
     *
     * @param code code
     * @param type 类型
     * @return name
     */
    public String jwzhDictToName(String code, String type) {
        if (code == null) {
            return null;
        }
        JwzhDictVO jwzhDictEntity = dictService.getJwzhDictVO(type, code);
        return java.util.Objects.nonNull(jwzhDictEntity) ? jwzhDictEntity.getCt() : null;
    }

    /**
     * list转为逗号分隔的字符串
     *
     * @param list list
     * @return 字符串
     */
    public String listToString(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return list.stream().collect(Collectors.joining(","));
    }

    /**
     * 标签id集合转成标签名集合
     *
     * @param labelIds 标签id集合
     * @return 标签名集合
     */
    public List<String> labelIdArrayToName(List<Long> labelIds) {
        if (CollectionUtils.isEmpty(labelIds)) {
            return Collections.emptyList();
        }
        List<Label> labels = labelMapper.selectBatchIds(labelIds);
        return labels.stream().map(Label::getName).collect(Collectors.toList());
    }

    /**
     * 标签id集合转成标签名集合
     *
     * @param labelIdStr json字符串类型的labeiid
     * @return labelNames
     */
    public String labelIdArrayToName(String labelIdStr) {
        if (StringUtils.isEmpty(labelIdStr)) {
            return "";
        }
        List<Long> labelIds = JsonUtil.objectToArray(labelIdStr, Long.class);
        return listToString(labelIdArrayToName(labelIds));
    }

    /**
     * 根据地址编码获取地址
     *
     * @param code 编码
     * @return 地址
     */
    public String districtCodeToName(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        DistrictListDto districtDto = dictService.getDistrictByCode(code);
        return districtDto == null ? null : districtDto.getName();
    }

    /**
     * 根据dept编码获取dept名
     *
     * @param code 编码
     * @return 名字
     */
    public String deptCodeToDeptName(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        DeptDto dept = permissionService.getDeptByCode(code);
        return java.util.Objects.nonNull(dept) ? dept.getShortName() : "";
    }

    /**
     * 根据deptId获取dept名
     *
     * @param id 部门id
     * @return 部门名
     */
    public String deptIdToDeptName(Long id) {
        if (id == null || id <= 0) {
            return "";
        }
        DeptDto dept = permissionService.getDeptById(id);
        return java.util.Objects.nonNull(dept) ? dept.getShortName() : "";
    }

    /**
     * 根据deptId获取dept名
     *
     * @param ids 部门id
     * @return 部门名
     */
    public String deptIdsToDeptName(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return "";
        }
        List<DeptDto> depts = permissionService.getDeptByIds(ids);
        return CollectionUtils.isEmpty(depts) ? "--" : depts.stream().map(DeptDto::getName)
                .collect(Collectors.joining(","));
    }

    /**
     * 根据userIds集合获取username字符串
     *
     * @param userIds userIds集合
     * @return username字符串
     */
    public String userIdArrayToUserName(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return "";
        }
        List<UserDto> users = permissionService.getUserListById(userIds);
        if (CollectionUtils.isEmpty(users)) {
            return "";
        }
        return users.stream().map(UserDto::getRealName).collect(Collectors.joining(","));
    }

    /**
     * 根据userId集合获取username
     *
     * @param userId userId
     * @return username字符串
     */
    public String userIdToUserName(Long userId) {
        if (userId == null || userId <= 0) {
            return "";
        }
        UserDto user = permissionService.getUserById(userId);
        return user == null ? "" : user.getRealName();
    }

    /**
     * 来源
     *
     * @param sourceVO source
     * @return 名称
     */
    public String sourceToName(ListSourceVO sourceVO) {
        if (sourceVO == null) {
            return ListSourceTypeEnum.MANUAL.getDescription();
        } else {
            if (sourceVO.getType().equals(ListSourceTypeEnum.MONITOR)) {
                MonitorInfoVO monitorInfoVO = BeanUtil.getBean(ControlService.class)
                        .getMonitorInfo(sourceVO.getRelatedId());
                return sourceVO.getType().getDescription() + monitorInfoVO.getMonitorTitle();
            } else if (sourceVO.getType() == ListSourceTypeEnum.RISK) {
                RiskService riskService = BeanUtil.getBean(RiskService.class);
                String riskTitle = riskService.getRiskTitle(sourceVO.getRelatedId());
                return sourceVO.getType().getDescription() + riskTitle;
            } else {
                return sourceVO.getType().getDescription();
            }
        }
    }

    /**
     * 获取空白导出集合
     *
     * @param tClass   tClass
     * @param headName 列表头
     * @param <T>      泛型
     * @return 集合
     * @throws Exception Exception
     */
    public <T extends ProfileExportListVO> List<T> buildExportEmptyList(Class<T> tClass, String headName) throws Exception {
        List<T> list = new ArrayList<>();
        for (int i = 0; i < 1; i++) {
            T t = tClass.getConstructor().newInstance();
            t.setNum(i + 1);
            t.setCellHead(headName);
            list.add(t);
        }
        return list;
    }

    /**
     *  获取导出集合
     *
     * @param list 数据
     * @param tClass tClass
     * @param headName 列表头
     * @param <T> 泛型
     * @return 集合
     * @throws Exception Exception
     */
    public <T extends ProfileExportListVO> List<T> buildExportEmptyList(List<T> list, Class<T> tClass, String headName) throws Exception {
        if(CollectionUtils.isEmpty(list)){
            T t = tClass.getConstructor().newInstance();
            t.setNum(1);
            t.setCellHead(headName);
            return Arrays.asList(t);
        }
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setNum(i+1);
            list.get(i).setCellHead(headName);
        }
        return list;
    }

    /**
     * 获取督查清单
     *
     * @param type 类型
     * @param id   主键id
     * @return 督查清单
     * @throws Exception Exception
     */
    public List<ProfileInspectorExportVO> getInspectorList(String type, String id) throws Exception {
        List<ProfileInspectorVO> items = profileInspectorService.selectList(type, id);
        if (CollectionUtils.isEmpty(items)) {
            return buildExportEmptyList(ProfileInspectorExportVO.class, "督查清单");
        }
        List<ProfileInspectorExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            ProfileInspectorVO item = items.get(i);
            ProfileInspectorExportVO vo = new ProfileInspectorExportVO();
            vo.setCellHead("督查清单");
            vo.setNum(i + 1);
            vo.setInspectorDetail(item.getInspectorDetail());
            vo.setInspectorTypeName(item.getInspectorTypeName());
            LocalDateTime createTime = item.getCreateTime();
            vo.setCreateTimeStr(createTime == null ? "--" : createTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 映射地址类型
     *
     * @param location location
     * @return location
     */
    public String mapLocation(String location) {
        if (StringUtils.isEmpty(location)) {
            return null;
        }
        MapLocationVO mapLocationVO = JsonUtil.parseSpecificObject(location, MapLocationVO.class);
        if (mapLocationVO == null) {
            return null;
        }
        MapLocationVO.PositionInfo positionInfo = mapLocationVO.getPositionInfo();
        if (positionInfo == null) {
            return null;
        }
        String point = StringUtils.isEmpty(positionInfo.getPoint()) ? "" : positionInfo.getPoint().replace("POINT", "");
        String name = positionInfo.getName();
        if (mapLocationVO.getRegion() != null) {
            name = mapLocationVO.getRegion().getName();
        }
        return name + point;
    }

    /**
     * 风险标签映射为风险名
     *
     * @param riskLabels riskLabels
     * @return 风险名
     */
    public String riskLabelIdArrayToName(String riskLabels) {
        if (StringUtils.isEmpty(riskLabels)) {
            return "- -";
        }
        List<Long> labelIds = Arrays.stream(riskLabels.split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(labelIds)) {
            return "- -";
        }
        List<RiskLabelEntity> labels = riskLabelMapper.selectBatchIds(labelIds);
        if (CollectionUtils.isEmpty(labels)) {
            return "- -";
        }
        return labels.stream().map(it -> String
                .format("%s(%s)", it.getLabelName(), it.getLabelScore())
        ).collect(Collectors.joining(","));
    }

    /**
     * 根据code获取码表中的name
     *
     * @param code code
     * @param type 类型
     * @return name
     */
    public String dictCodeListToName(List<Long> code, String type) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        List<DictDto> dictTree = dictService.getDictListByTypeList(Collections.singletonList(type));
        if (Objects.isNull(dictTree) || dictTree.isEmpty()) {
            return null;
        }
        return dictTree.stream().filter(dict -> code.contains(dict.getCode()))
                .map(dto -> DictUtils.findParentNames(dictTree, dto.getCode(), "-"))
                .filter(java.util.Objects::nonNull)
                .collect(Collectors.joining(";"));
    }

    /**
     * 根据code获取name
     *
     * @param code code
     * @return name
     */
    public String groupInfoTypeToName(Integer code) {
        if (code == null) {
            return null;
        }
        switch (code) {
            case 1:
                return "微信";
            case 2:
                return "QQ";
            case 3:
                return "其他";
            default:
                return null;
        }
    }

    /**
     * 根据code获取name
     *
     * @param code code
     * @return name
     */
    public String workRecordToName(Integer code) {
        if (code == null) {
            return null;
        }
        switch (code) {
            case 1:
                return "在控中";
            case 2:
                return "有异动";
            case 3:
                return "已失控";
            case 4:
                return "已稳控";
            default:
                return null;
        }
    }
}
