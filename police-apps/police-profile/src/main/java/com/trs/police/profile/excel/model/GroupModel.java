package com.trs.police.profile.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trs.police.profile.excel.converter.LocalDateTimeConverter;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用于对应群档的excel的实体类
 *
 * <AUTHOR>
 * @date 2024年04月02日 14:56
 */
@Data
public class GroupModel {

    @ExcelProperty("群体名称")
    private String name;
    @ExcelProperty("群体类别")
    private String labelIds;
    @ExcelProperty("基本情况")
    private String basicInfo;
    @ExcelProperty("主要诉求")
    private String mainDemand;
    @ExcelProperty("责任分局")
    private String controlBureau;
    @ExcelProperty("责任分局领导")
    private String controlBureauLeader;
    @ExcelProperty("责任警钟")
    private String controlPolice;
    @ExcelProperty("责任警钟领导")
    private String controlPoliceLeader;
    @ExcelProperty("责任派出所")
    private String controlStation;
    @ExcelProperty("责任派出所领导")
    private String controlStationLeader;
    @ExcelProperty("责任民警")
    private String controlPerson;
    @ExcelProperty("党政责任部门")
    private String controlGovernment;
    @ExcelProperty("党政责任人")
    private String controlGovernmentPerson;
    @ExcelProperty("党政责任人联系方式")
    private String controlGovernmentContact;
    @ExcelProperty("责任街道社区")
    private String controlCommunity;
    @ExcelProperty("社区责任人")
    private String controlCommunityPerson;
    @ExcelProperty("社区责任人联系方式")
    private String controlCommunityContact;
    @ExcelProperty("敏感时间节点名称")
    private String sensitiveTimeName;
    @ExcelProperty(value = "起始时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime sensitiveStartTime;
    @ExcelProperty(value = "结束时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime sensitiveEndTime;
    @ExcelProperty("备注")
    private String sensitiveRemark;
    @ExcelProperty("相关人员")
    private String relatedPerson;
    //dy专属
    @ExcelProperty("化解难度")
    private String resolveDifficultyName;
    @ExcelProperty("化解难度评判依据")
    private String  petitionInfo;

    @Override
    public String toString() {
        return name + "@" + super.toString();
    }


}
