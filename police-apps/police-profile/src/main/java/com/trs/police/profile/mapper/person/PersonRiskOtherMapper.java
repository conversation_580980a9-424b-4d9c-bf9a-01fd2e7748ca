package com.trs.police.profile.mapper.person;

import com.trs.police.profile.domain.entity.person.PersonRiskOther;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 人员-风险点信息-刑侦、政保、其他mapper
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Mapper
public interface PersonRiskOtherMapper extends PersonRelatedBaseMapper<PersonRiskOther> {

    /**
     * 根据人员id查询风险点信息
     *
     * @param personId   人员id
     * @param policeKind  管控警种
     * @return 结果
     */
    @Override
    @Select("select * from t_profile_person_risk_other where person_id = #{personId} and police_kind = #{policeKind}")
    PersonRiskOther selectByPersonIdAndPoliceKind(@Param("personId") Long personId, @Param("policeKind") Long policeKind);
}
