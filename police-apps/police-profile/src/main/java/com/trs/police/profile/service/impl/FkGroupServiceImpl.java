package com.trs.police.profile.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.openfeign.starter.vo.GroupListVO;
import com.trs.police.profile.domain.vo.FkGroupExportVO;
import com.trs.police.profile.domain.vo.RelatedGtWarningPersonVO;
import com.trs.police.profile.domain.vo.RelatedWarningPersonGtVO;
import com.trs.police.profile.mapper.GroupMapper;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.service.FkGroupService;
import com.trs.police.profile.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 群体service实现类
 *
 * <AUTHOR>
 * @date 2022/08/31
 */
@Service
@Slf4j
public class FkGroupServiceImpl implements FkGroupService {

    @Resource
    private GroupMapper groupMapper;
    @Resource
    private PersonMapper personMapper;

    @Override
    public List<RelatedGtWarningPersonVO> getRelatedGtWarningPerson(Long groupId) {
        PreConditionCheck.checkNotNull(groupId, "群体id不能为空");
        List<RelatedGtWarningPersonVO> relatedGtWarningPersons = groupMapper.getRelatedGtWarningPerson(groupId);
        if (CollectionUtils.isEmpty(relatedGtWarningPersons)) {
            return relatedGtWarningPersons;
        }
        List<Long> personIds = relatedGtWarningPersons.stream().map(RelatedGtWarningPersonVO::getId).collect(Collectors.toList());
        List<RelatedWarningPersonGtVO> relatedWarningPersonGts = personMapper.getRelatedWarningPersonGt(personIds);
        Map<Long, List<RelatedWarningPersonGtVO>> relatedWarningPersonGtMap = relatedWarningPersonGts.stream().collect(Collectors.groupingBy(RelatedWarningPersonGtVO::getId));
        relatedGtWarningPersons.forEach(p->{
            List<RelatedWarningPersonGtVO> myRelatedWarningPersonGts = relatedWarningPersonGtMap.get(p.getId());
            if (!CollectionUtils.isEmpty(myRelatedWarningPersonGts)) {
                p.setWarningGts(myRelatedWarningPersonGts.stream()
                        .filter(gt -> StringUtils.isNotBlank(gt.getDeviceCode()))
                        .distinct()
                        .map(RelatedWarningPersonGtVO::getDeviceCode).collect(Collectors.toList()));
                p.setWarningGtName(myRelatedWarningPersonGts.stream()
                        .filter(gt -> StringUtils.isNotBlank(gt.getName()))
                        .distinct()
                        .map(RelatedWarningPersonGtVO::getName).collect(Collectors.joining(",")));
                p.setLastWarningTime(myRelatedWarningPersonGts.stream().map(RelatedWarningPersonGtVO::getWarningTime).max(Comparator.naturalOrder()).get());
                p.setWarningCount(myRelatedWarningPersonGts.size());
            }
        });
        return relatedGtWarningPersons;
    }

    @Override
    public void fkGroupExport(HttpServletResponse response, ExportParams params) throws IOException {
        ListParamsRequest paramsRequest = params.getListParamsRequest();
        paramsRequest.nullCheck();
        if(Boolean.TRUE.equals(params.getIsAll()) || !com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(params.getIds())){
            paramsRequest.getPageParams().setPageNumber(1);
            paramsRequest.getPageParams().setPageSize(Integer.MAX_VALUE);
            paramsRequest.getFilterParams().add(new KeyValueTypeVO("ids", params.getIds()));
        }
        List<GroupListVO> list = BeanUtil.getBean(GroupServiceImpl.class).groupList(params.getListParamsRequest()).getItems();
        List<FkGroupExportVO> exportVoList = new ArrayList<>();
        for (GroupListVO groupListVO : list) {
            FkGroupExportVO exportVO = new FkGroupExportVO();
            BeanUtils.copyProperties(groupListVO, exportVO);
            exportVO.setControlBureauName(groupListVO.getControlBureauName().stream().collect(Collectors.joining(",")));
            exportVoList.add(exportVO);
        }

        ExcelUtil.exportList(response, params.getFieldNames(), exportVoList, FkGroupExportVO.class, "群体列表");
    }
}