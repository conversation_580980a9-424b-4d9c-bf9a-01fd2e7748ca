package com.trs.police.profile.domain.vo.person;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.profile.domain.vo.ProfileExportListVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 矛盾纠纷记录
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PersonMdjfVO extends ProfileExportListVO {

    /**
     * 主键ID
     */
    private String zjid;

    /**
     * 业务编号
     */
    private String ywbh;

    /**
     * 矛盾纠纷类型
     */
    private String mdjflx;

    /**
     * 纠纷详情
     */
    private String jfxq;

    /**
     * 登记时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date djsj;

    /**
     * 当事人姓名
     */
    private String dsrxm;

    /**
     * 当事人证件号码
     */
    private String dsrzjhm;

    /**
     * 当事人联系方式
     */
    private String dsrlxfs;

    /**
     * 当事人数量
     */
    private Integer dsrsl;

    /**
     * 涉事单位
     */
    private String ssdw;

    /**
     * 事发地地址
     */
    private String sfddz;

    /**
     * 承办单位
     */
    private String cbdw;

    /**
     * 承办人员
     */
    private String cbry;

    /**
     * 处置状态
     */
    private String czzt;

    /**
     * 处置情况
     */
    private String czqk;

    /**
     * 调解组织联系方式
     */
    private String tjzzlxfs;

    /**
     * 纠纷来源
     */
    private String jfly;

    /**
     * 数据来源
     */
    private String sjly;

    /**
     * 数据标识
     */
    private String depActionFlag;

    /**
     * 数据抽取时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date depActionTime;

    /**
     * 首次入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date depFirstenterTime;

}
