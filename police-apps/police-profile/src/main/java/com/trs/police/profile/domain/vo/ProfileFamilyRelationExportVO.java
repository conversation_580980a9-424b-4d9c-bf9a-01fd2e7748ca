package com.trs.police.profile.domain.vo;

import lombok.Data;

/**
 * Description:人员档案导出-家庭关系
 *
 * @author: lv.bo
 * @create: 2024-03-05 16:32
 */
@Data
public class ProfileFamilyRelationExportVO extends ProfileExportListVO{

    /**
     * 关系
     */
    private String relation;

    /**
     * 姓名
     */
    private String name;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     *  现住址
     */
    private String currentLocation;

    /**
     * 联系方式
     */
    private String phoneNumber;

    /**
     * 户籍地址
     */
    private String registeredResidenceDetail;

    /**
     * 政治面貌，profile_political_status
     */
    private String politicalStatus;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 职务
     */
    private String workDuty;

    /**
     * 类型，profile_person_duty_type
     */
    private String workDutyType;

    /**
     * 现住地址
     */
    private String currentResidenceDetail;

    /**
     * 联系方式
     */
    private String phone;
}
