package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.profile.domain.entity.EventPersonRelation;
import com.trs.police.profile.domain.vo.EventPersonExportVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 事件人员查询
 *
 * <AUTHOR>
 * @date 2024/04/09
 */
@Mapper
public interface EventPersonRelationMapper extends BaseMapper<EventPersonRelation> {
    /**
     * 根据事件id修改事件人员关联信息
     *
     * @param eventPersonRelation 事件人员关联
     * <AUTHOR>
     */
    @Update("update t_profile_person_event_relation set person_id = #{eventPersonRelation.personId} where event_id = #{eventPersonRelation.eventId}")
    void updateByEventId(@Param("eventPersonRelation") EventPersonRelation eventPersonRelation);
    /**
     * 根据事件id删除事件人员关联信息
     *
     * @param eventId 事件id
     * <AUTHOR>
     */
    @Delete("delete from t_profile_person_event_relation where event_id = #{eventId}")
    void deleteByEventId(Long eventId);

    /**
     *  获取事件涉及的人员
     *
     * @param eventId eventId
     * @return 人员集合
     */
    @Select("SELECT pe.risk_label_ids,pe.record_status,pe.is_persuaded,pe.persuasion_situation,p.* FROM t_profile_person_event_relation pe,t_profile_person p where pe.event_id=#{eventId} AND pe.person_id = p.id and p.deleted=0 order by p.update_time desc,p.id desc")
    List<EventPersonExportVO> selectEventPersonList(@Param("eventId") Long eventId);

    /**
     * 根据事件编号修改事件id
     *
     * @param eventCode 事件编号
     * @param eventId   事件id
     */
    @Update("update t_profile_person_event_relation set event_id = #{eventId} where event_code = #{eventCode}")
    void updateEventIdByEventCode(@Param("eventCode") String eventCode, @Param("eventId") Long eventId);
}
