package com.trs.police.profile.domain.vo;


import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.profile.domain.entity.ProfileFamilyRelation;
import com.trs.police.profile.domain.entity.ProfileSocialRelation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 人员详细信息VO
 *
 * @author: duzhaoyang
 * @create: 2024-04-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RelatedPersonVO implements Serializable {
    private static final long serialVersionUID = 1340688614398019122L;

    /**
     * 人员id
     */
    private Integer id;
    /**
     * 人员身份证号
     */
    private String idNumber;
    /**
     * 证件类型
     */
    private Integer idType;
    /**
     * 人员姓名
     */
    private String name;
    /**
     * 人员性别
     */
    private Integer gender;
    /**
     * 人员曾用名
     */
    private String formerName;
    /**
     * 人员民族
     */
    private Integer nation;
    /**
     * 人员绰号
     */
    private String nickName;
    /**
     * 人员政治面貌
     */
    private Integer politicalStatus;
    /**
     * 人员宗教信仰
     */
    private String religiousBelief;
    /**
     * 人员婚姻状况
     */
    private Integer martialStatus;
    /**
     * 人员现职业
     */
    private String currentJob;
    /**
     * 人员目前所在地代码
     */
    private Integer currentPosition;
    /**
     * 人员标签
     */
    private List<Long> personLabel;
    /**
     * 人员户籍地区域代码
     */
    private String registeredResidence;
    /**
     * 人员户籍地详细地址
     */
    private String registeredResidenceDetail;
    /**
     * 人员现住址区域代码
     */
    private String currentResidence;
    /**
     * 人员现住址详细地址
     */
    private String currentResidenceDetail;
    /**
     * 人员工作目标
     */
    private String workTarget;
    /**
     * 人员风险背景
     */
    private String mainDemand;
    /**
     * 人员工作措施
     */
    private String workMeasures;
    /**
     * 人员化解难点
     */
    private String petitionInfo;
    /**
     * 人员维权及打处情况
     */
    private String punishInfo;
    /**
     * 人员照片信息地址集合
     */
    private List<FileInfoVO> photo;
    /**
     * 人员关联车辆集合信息
     */
    private List<Long> vehicleIds;
    /**
     * 人员电话号码集合
     */
    private List<String> tel;
    /**
     * 人员关联虚拟身份集合
     */
    private List<Long> virtualIdentityIds;
    /**
     * 家庭关系信息
     */
    private List<ProfileFamilyRelation> profileFamilyRelationList;

    /**
     * 家庭关系id
     */
    private List<Long> familyRelationIds;

    /**
     * 社会关系id
     */
    private List<Long> socialRelationIds;

    /**
     * 社会关系信息
     */
    private List<ProfileSocialRelation> profileSocialRelationList;
    /**
     * 人员布控状态
     */
    private Integer monitorStatus;
    /**
     * 人员管控状态
     */
    private Integer controlStatus;
    /**
     * 人员管控级别
     */
    private Integer controlLevel;
    /**
     * 人员档案完整度统计
     */
    private Double completeRate;
    /**
     * 人员档案类型
     */
    private Integer personType;
    /**
     * 人员风险分
     */
    private Double riskScore;
    /**
     * 人员累计分
     */
    private Double rawScore;

    /**
     * 人员风险等级
     */
    private String riskLevel;

    /**
     * 人员活跃度
     */
    private String activityLevel;

    /**
     * 人员现住址详细地址经纬度
     */
    private String currentResidenceDetailBlh;

    /**
     * 归属警种的level
     */
    private Long level;

    /**
     * 分组明文
     */
    private String groupType;

    /**
     * 关系
     */
    private String realtion;

    /**
     * 是否有预警
     */
    private Integer havingWarning;
}
