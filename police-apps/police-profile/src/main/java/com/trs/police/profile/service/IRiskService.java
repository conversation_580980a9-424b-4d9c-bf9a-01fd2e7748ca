package com.trs.police.profile.service;

import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.vo.KeyNameVO;
import com.trs.police.profile.domain.dto.*;
import com.trs.police.profile.domain.vo.*;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * IRiskService
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/7 17:34
 * @since 1.0
 */
public interface IRiskService {

    /**
     * addOrUpdateLabel<BR>
     *
     * @param save 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/7 16:57
     */
    Report<String> addOrUpdateLabel(RiskLabelSaveDTO save) throws ServiceException;

    /**
     * deleteLabel<BR>
     *
     * @param delete 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/7 16:58
     */
    Report<String> deleteLabel(RiskLabelDeleteDTO delete) throws ServiceException;

    /**
     * labelList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/7 17:23
     */
    RestfulResultsV2<RiskLabelVo> labelList(RiskLabelQueryDTO dto) throws ServiceException;

    /**
     * labelSceneList<BR>
     *
     * @param type 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/7 17:34
     */
    List<LabelSceneVo> labelSceneList(String type) throws ServiceException;

    /**
     * personScoreAndCount<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    PersonScoreAndCountVO personScoreAndCount(RiskScoreAndCountDTO dto) throws ServiceException;

    /**
     * findRiskScoreBatch<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/26 13:52
     */
    List<RiskScoreBatchVo> findRiskScoreBatch(RiskScoreBatchDTO dto) throws ServiceException;

    /**
     * findRiskScoreBatchGroupByDay<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/3 17:09
     */
    List<RiskScoreBatchGroupVo> findRiskScoreBatchGroupByDay(RiskScoreBatchDTO dto) throws ServiceException;

    /**
     * 修改时间是否发生状态
     *
     * @param dto 请求参数
     * @return {@link List }<{@link Report }>
     * @throws ServiceException 相关异常
     * <AUTHOR>
     * @since 2024-03-08 11:35:30
     */
    List<Report> changeEventHappened(ChangeEventHappenedDTO dto) throws ServiceException;

    /**
     * 风险分数列表<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/11 09:33
     */
    RestfulResultsV2<RiskScoreItemVo> scoreList(RiskScoreItemSearchDTO dto);

    /**
     * 分数计算任务<BR>
     *
     * @param message 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/27 18:51
     */
    void countScoreTask(String message) throws ServiceException;

    /**
     * 获取扣分配置信息
     *
     * @param type 参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    DeductConfigVO getDeductConfig(String type) throws ServiceException;

    /**
     * 保存扣分配置信息
     *
     * @param dto 参数
     * @throws ServiceException 相关异常
     */
    void saveOrUpdateConfig(DeductConfigDTO dto) throws ServiceException;

    /**
     * juvenilesLabelList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 13:43
     */
    RestfulResultsV2<JuvenilesLabelVo> juvenilesLabelList(JuvenilesLabelSearchDTO dto);

    /**
     * juvenilesLabel<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 19:27
     */
    List<KeyNameVO> juvenilesLabel();

    /**
     * juvenilesScoreClass<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/28 14:55
     */
    List<KeyNameVO> juvenilesScoreClass() throws ServiceException;

    /**
     * juvenilesScoreList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 13:43
     */
    RestfulResultsV2<JuvenilesScoreVo> juvenilesScoreList(JuvenilesScoreSearchDTO dto);

}
