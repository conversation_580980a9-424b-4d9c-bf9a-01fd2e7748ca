package com.trs.police.profile.excel.download;

import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.entity.ProfilePersonPoliceControlEntity;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.MonitorTimeUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.ExportExcelVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.MonitorListDto;
import com.trs.police.common.core.vo.control.RegularMonitorListVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.profile.constant.CommonConstants;
import com.trs.police.profile.constant.PoliceKindConstants;
import com.trs.police.profile.domain.converter.PersonConverter;
import com.trs.police.profile.domain.dto.GroupWorkRecordSearchDTO;
import com.trs.police.profile.domain.entity.*;
import com.trs.police.profile.domain.entity.person.*;
import com.trs.police.profile.domain.vo.*;
import com.trs.police.profile.domain.vo.person.*;
import com.trs.police.profile.mapper.*;
import com.trs.police.profile.mapper.person.*;
import com.trs.police.profile.mgr.ProfileExportMgr;
import com.trs.police.profile.schema.field.MapperFactory;
import com.trs.police.profile.service.GroupWorkRecordService;
import com.trs.police.profile.service.PersonArchiveService;
import com.trs.police.profile.util.ConverterHelper;
import com.trs.police.profile.util.ExcelUtil;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Slf4j
@Service
public class PersonDownloadSerivce {

    @Resource
    private PersonMapper personMapper;
    @Resource
    private ProfilePersonPoliceControlMapper profilePersonPoliceControlMapper;
    @Resource
    private ProfileVirtualIdentityMapper profileVirtualIdentityMapper;
    @Resource
    private ProfileVehicleMapper profileVehicleMapper;

    @Resource
    private ProfileExportMgr exportMgr;

    @Resource
    private ProfilePersonGovControlMapper profilePersonGovControlMapper;

    @Resource
    private PersonArchiveService personArchiveService;

    @Resource
    private PersonGroupRelationMapper personGroupRelationMapper;

    @Resource
    private PersonEventRelationMapper personEventRelationMapper;

    @Resource
    private PersonClueRelationMapper personClueRelationMapper;

    @Resource
    private ProfileFamilyRelationMapper profileFamilyRelationMapper;

    @Resource
    private ProfileSocialRelationMapper profileSocialRelationMapper;

    @Resource
    private OssService ossService;

    @Resource
    private ProfilePlatformAccountMapper profilePlatformAccountMapper;

    @Resource
    private ProfileCaseRelationPersonMapper profileCaseRelationPersonMapper;

    @Resource
    private PersonDcqkMapper personDcqkMapper;
    @Resource
    private PersonGzcsMapper personGzcsMapper;
    @Resource
    private PersonGkcsMapper personGkcsMapper;
    @Resource
    private PersonXsbxMapper personXsbxMapper;
    @Resource
    private PersonXswhMapper personXswhMapper;
    @Resource
    private PersonDzControlMapper personDzControlMapper;
    @Resource
    private PersonRiskJzMapper personRiskJzMapper;
    @Resource
    private PersonRiskSdMapper personRiskSdMapper;
    @Resource
    private PersonRiskZaMapper personRiskZaMapper;
    @Resource
    private PersonRiskOtherMapper personRiskOtherMapper;
    @Resource
    private PersonMdjfMapper personMdjfMapper;
    @Resource
    private PersonWorkRecordMapper personWorkRecordMapper;

    /**
     * 下载人员档案
     *
     * @param response 响应
     * @param personId 人员id
     *
     */
    public void personRecord(HttpServletResponse response, Long personId) throws Exception {
        Person person = personMapper.selectById(personId);
        if (person == null) {
            throw new TRSException("人员不存在！");
        }
        List<Long> policeKindList = person.getPoliceKind();
        if(CollectionUtils.isEmpty(policeKindList)){
            throw new TRSException("人员未关联管控警种！");
        }
        ExportExcelVO vo = new ExportExcelVO();
        vo.setTemplateFilePath("/template/person/downloadPersonTemplate.xlsx");
        vo.setResponse(response);
        vo.setMergeRowIndex(6);
        Map<String, Object> baseInfo = buildBaseInfo(person);
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Map<String, Map<String, Object>> sheetData = new HashMap<>();
        policeKindList.stream().parallel().forEach(policeKind -> {
            //塞入认证信息，使子线程能获取到用户
            SecurityContextHolder.setContext(securityContext);
            Tuple2<String, Map<String, Object>> sd = personRecordByPoliceKind(baseInfo, person, policeKind);
            sheetData.put(sd._1, sd._2);
        });
        vo.setSheetData(sheetData);
        List<String> exportNameList = Arrays.asList("治安", "经侦", "禁毒", "刑侦", "政保", "其他", "出入境");
        String policeKindExportName = exportNameList.stream()
                .filter(name ->  vo.getSheetData().keySet().contains(name))
                .map(key -> PoliceKindConstants.POLICE_KIND_CNNAME_EXPORTNAME_MAP.get(key)).collect(Collectors.joining("-"));
        vo.setExcelName("重点人员档案-" + policeKindExportName + "-" + person.getName() + ".xlsx");

        ExcelUtil.downloadRecordExcelWithMultiSheet(vo);
    }

    /**
     * 单个警种人员档案数据
     *
     * @param baseInfo 人员基本信息
     * @param person 人员
     * @param policeKind 管控警种
     *
     * @return sheet名称，sheet数据
     */
    public Tuple2<String, Map<String, Object>> personRecordByPoliceKind(Map<String, Object> baseInfo, Person person, Long policeKind) {
        AuthHelper.getCurrentUser();
        PreConditionCheck.checkNotNull(policeKind, "管控警种不能为空");
        String policeKindStr = PoliceKindConstants.POLICE_KIND_CODE_CNNAME_MAP.get(policeKind);
        PreConditionCheck.checkNotNull(policeKindStr, "未知的管控警种");
        try {
            // 构建警种信息
            Map<String, Object> data = buildPoliceKindInfo(person, policeKind, baseInfo);
            return Tuple.of(policeKindStr, data);
        } catch (Exception e) {
            log.error("下载人员档案失败:[{}]！", e.getMessage(), e);
            throw new TRSException("下载人员档案失败，请重试！");
        }
    }

    /**
     * 人员档案通用数据
     *
     * @param person 人员
     *
     * @return 结果
     */
    public Map<String, Object> buildBaseInfo(Person person) {
        AuthHelper.getCurrentUser();
        try {
            ProfilePersonVO profilePersonVO = buildProfilePersonVO(person);
            List<ProfileVehicleVO> profileVehicleVOList = buildProfileVehicleVOList(person);
            List<ProfileVirtualIdentityVO> profileVirtualIdentityVOList = buildProfileVirtualIdentityVOList(person);
            List<ProfileFamilyRelationExportVO> familyRelationExportList = getFamilyRelationList(person);
            List<ProfileSocialRelationExportVO> socialRelationExportList = getSocialRelationList(person);
            List<ProfilePlatformAccountExportVO> platformAccountList = getPlatformAccountList(person);

            List<MonitorListExportVO> personMonitor = getPersonMonitor(person);
            List<RegularMonitorListExportVO> personRegularMonitor = getPersonRegularMonitor(person);
            List<PersonArchiveWarningExportVO> personWarningRelationList = getPersonWarningRelationList(person);
            List<TrackPointExportVO> personTrackList = getPersonTrackList(person);
            List<ProfileInspectorExportVO> inspectorExportList = exportMgr.getInspectorList("person", String.valueOf(person.getId()));
            Map<String, Object> data = new HashMap<>();
            data.put("profilePersonVO", profilePersonVO);
            data.put("vehicles", profileVehicleVOList);
            data.put("vIdentities", profileVirtualIdentityVOList);
            data.put("family", familyRelationExportList);
            data.put("social", socialRelationExportList);
            data.put("platformAccount", platformAccountList);

            data.put("warnRels", personWarningRelationList);
            data.put("pMonitor", personMonitor);
            data.put("rMonitor", personRegularMonitor);
            data.put("track", personTrackList);
            data.put("inspector", inspectorExportList);

            // 构建风险研判
            buildFxyp(person, data);
            // 构建矛盾纠纷相关
            buildMdjf(person, data);

            return data;
        } catch (Exception e) {
            log.error("下载人员档案失败:[{}]！", e.getMessage(), e);
            throw new TRSException("下载人员档案失败，请重试！");
        }
    }

    private Map<String, Object> buildPoliceKindInfo(Person person, Long policeKind, Map<String, Object> baseInfo) throws Exception {
        Map<String, Object> data = new HashMap<>();
        data.putAll(baseInfo);
        ProfilePersonVO vo = (ProfilePersonVO) data.get("profilePersonVO");
        ProfilePersonVO newVo = new ProfilePersonVO();
        BeanUtil.copyPropertiesIgnoreNull(vo, newVo);
        //公安管控信息
        dealPersonControlInfo(person, newVo, policeKind);
        //政府管控信息
        dealGovControlInfo(person, newVo, policeKind);
        data.put("profilePersonVO", newVo);
        //其他警种信息
        switch (policeKind.intValue()) {
            // 经侦
            case 3:
                buildPersonRiskJzList(person, data);
                buildPersonGroupList(person, policeKind, data);
                buildPersonEventList(person, policeKind, data);
                break;
                // 禁毒
            case 10:
                buildPersonRiskSdList(person, data);
                buildPersonEventList(person, policeKind, data);
                buildPersonCaseList(person, policeKind, data);
                break;
                // 刑侦
            case 5:
                buildPersonGkcsList(person, policeKind, data);
                buildPersonGzcsList(person, policeKind, data);
                buildPersonDcqkList(person, policeKind, data);
                buildPersonClueList(person, policeKind, data);
                buildPersonCaseList(person, policeKind, data);
                break;
                // 政保
            case 2:
                buildPersonRiskZbList(person, policeKind, data);
                buildPersonGzcsList(person, policeKind, data);
                buildPersonXswhList(person, policeKind, data);
                buildPersonGroupList(person, policeKind, data);
                buildPersonEventList(person, policeKind, data);
                buildPersonClueList(person, policeKind, data);
                break;
                // 治安
            case 4:
                buildPersonRiskZaList(person, data);
                buildPersonGzcsList(person, policeKind, data);
                buildPersonDcqkList(person, policeKind, data);
                buildPersonXsbxList(person, policeKind, data);
                buildPersonDzControlList(person, data);
                buildPersonGroupList(person, policeKind, data);
                buildPersonEventList(person, policeKind, data);
                buildPersonClueList(person, policeKind, data);
                break;
                // 其他
            case 99:
                buildPersonRiskOtherList(person, policeKind, data);
                buildPersonGzcsList(person, policeKind, data);
                buildPersonDcqkList(person, policeKind, data);
                buildPersonGroupList(person, policeKind, data);
                buildPersonEventList(person, policeKind, data);
                buildPersonClueList(person, policeKind, data);
                break;
            case 6:
                buildPersonWorkRecordList(person, data);
            default:
                break;
        }

        return data;
    }

    /**
     * 构建ProfilePersonVO
     *
     * @param person person
     * @return ProfilePersonVO
     */
    private ProfilePersonVO buildProfilePersonVO(Person person) {
        if (person == null) {
            return null;
        }
        ProfilePersonVO vo = new ProfilePersonVO();
        vo.setId(person.getId());
        vo.setIdNumber(person.getIdNumber());
        vo.setName(person.getName());
        vo.setIdType(getIdType(person, person.getIdType()));
        vo.setGender(exportMgr.dictCodeToName(person.getGender(), "gender"));
        vo.setFormerName(person.getFormerName());
        vo.setNickName(person.getNickName());
        vo.setNation(exportMgr.dictCodeToName(person.getNation(), "nation"));
        vo.setTel(exportMgr.listToString(person.getTel()));
        vo.setPoliticalStatus(exportMgr.dictCodeToName(person.getPoliticalStatus(), "profile_political_status"));
        vo.setMartialStatus(exportMgr.dictCodeToName(person.getMartialStatus(), "profile_martial_status"));
        vo.setCurrentJob(person.getCurrentJob());
        String currentPositionDict = BeanFactoryHolder.getEnv().getProperty("profile.person.dict.currentLocation", "profile_current_position_zg");
        vo.setCurrentPosition(exportMgr.dictCodeToName(person.getCurrentPosition(), currentPositionDict));
        vo.setPersonLabel(exportMgr.listToString(exportMgr.labelIdArrayToName(person.getPersonLabel())));
        vo.setRegisteredResidence(exportMgr.districtCodeToName(person.getRegisteredResidence()));
        vo.setRegisteredResidenceDetail(person.getRegisteredResidenceDetail());
        vo.setCurrentResidence(exportMgr.districtCodeToName(person.getCurrentResidence()));
        vo.setCurrentResidenceDetail(person.getCurrentResidenceDetail());
        vo.setWorkTarget(exportMgr.dictCodeToName(person.getWorkTarget(), "profile_work_target"));
        vo.setControlLevel(exportMgr.dictCodeToName(person.getControlLevel(), "profile_person_control_level"));
        vo.setMainDemand(person.getMainDemand());
        vo.setWorkMeasures(person.getWorkMeasures());
        vo.setPetitionInfo(person.getPetitionInfo());
        vo.setPunishInfo(person.getPunishInfo());
        LocalDateTime createTime = person.getCreateTime();
        vo.setCreateTime(createTime == null ? "" : TimeUtil.getSimpleTime(createTime));
        LocalDateTime updateTime = person.getUpdateTime();
        vo.setUpdateTime(updateTime == null ? "" : TimeUtil.getSimpleTime(updateTime));


        List<FileInfoVO> fileInfoVOList = person.getPhoto();
        if (!CollectionUtils.isEmpty(fileInfoVOList)) {
            List<String> urls = fileInfoVOList.stream().filter(v -> !StringUtils.isEmpty(v.getUrl())).map(FileInfoVO::getUrl).collect(Collectors.toList());
            Boolean isCrj = person.getPoliceKind()!=null && person.getPoliceKind().contains(6L);
            WriteCellData<Void> photo = buildExportPhoto(urls, isCrj);
            vo.setPhoto(photo);
        }

        vo.setPersonLabelIds(person.getPersonLabel());
        vo.setRiskScore(person.getRiskScore());
        vo.setRiskLevel(person.getRiskLevel());
        vo.setCreateDeptId(person.getCreateDeptId());
        vo.setCreateDept(exportMgr.deptIdToDeptName(person.getCreateDeptId()));

        vo.setVisaType(exportMgr.dictCodeToName(person.getVisaType(), "profile_visa_type"));
        vo.setVisitReason(exportMgr.dictCodeToName(person.getVisitReason(), "profile_reason_for_visit"));
        vo.setCountryRegion(exportMgr.districtCodeToName(person.getCountryRegion()));
        vo.setEnglishName(person.getEnglishName());
        if(person.getBirthday()!=null){
            vo.setBirthday(TimeUtils.dateToString(person.getBirthday(), TimeUtils.YYYYMMDD));
        }
        if (person.getVisaStartDate()!=null){
            vo.setVisaStartDate(TimeUtils.dateToString(person.getVisaStartDate(), TimeUtils.YYYYMMDD));
        }
        if (person.getVisaEndDate()!=null){
            vo.setVisaEndDate(TimeUtils.dateToString(person.getVisaEndDate(), TimeUtils.YYYYMMDD));
        }
        vo.setWorkUnit(person.getWorkUnit());

        return vo;
    }

    private String getIdType(Person person, Integer idType){
        if(person.getPoliceKind()!=null && person.getPoliceKind().contains(6L)){
            return exportMgr.dictCodeToName(idType, "profile_certificate_type");
        }else {
            return exportMgr.dictCodeToName(idType, "id_type");
        }
    }

    /**
     * 处理公安管控个信息
     *
     * @param person 人档
     * @param vo     vo
     * @param policeKind 管控警种
     */
    private void dealPersonControlInfo(Person person, ProfilePersonVO vo, Long policeKind) {
        QueryWrapper<ProfilePersonPoliceControlEntity> policeQueryWrapper = new QueryWrapper<>();
        policeQueryWrapper.eq("person_id", person.getId()).eq("police_kind", policeKind);
        List<ProfilePersonPoliceControlEntity> profilePersonPoliceControlEntityList
                = profilePersonPoliceControlMapper.selectList(policeQueryWrapper);
        StringBuilder dutyPoliceStation = new StringBuilder();
        StringBuilder dutyPolice = new StringBuilder();
        StringBuilder controlBureau = new StringBuilder();
        StringBuilder controlBureauLeader = new StringBuilder();
        StringBuilder controlPolice = new StringBuilder();
        StringBuilder controlPoliceLeader = new StringBuilder();
        StringBuilder controlStation = new StringBuilder();
        StringBuilder controlStationLeader = new StringBuilder();
        StringBuilder technologyControl = new StringBuilder();
        for (ProfilePersonPoliceControlEntity policeControlEntity : profilePersonPoliceControlEntityList) {
            dutyPoliceStation.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlStation())).append(",");
            dutyPolice.append(exportMgr.userIdArrayToUserName(policeControlEntity.getControlPerson())).append(",");
            controlBureau.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlBureau())).append(",");
            controlBureauLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlBureauLeader()));
            controlPolice.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlPolice())).append(",");
            controlPoliceLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlPoliceLeader())).append(",");
            controlStation.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlStation())).append(",");
            controlStationLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlStationLeader())).append(",");
            if(!CollectionUtils.isEmpty(policeControlEntity.getTechnologyControl())){
                technologyControl.append(exportMgr.dictCodeListToName(policeControlEntity.getTechnologyControl(), "profile_person_technology_control").replaceAll(";",",")).append(";");
            }
        }
        vo.setDutyPoliceStation(dutyPoliceStation.toString());
        vo.setDutyPolice(dutyPolice.toString());
        vo.setControlBureau(controlBureau.toString());
        vo.setControlBureauLeader(controlBureauLeader.toString());
        vo.setControlPolice(controlPolice.toString());
        vo.setControlPoliceLeader(controlPoliceLeader.toString());
        vo.setControlStation(controlStation.toString());
        vo.setControlStationLeader(controlStationLeader.toString());
        vo.setTechnologyControl(technologyControl.toString());
    }

    /**
     * 处理政府管控个信息
     *
     * @param person 人档
     * @param vo     vo
     * @param policeKind 管控警种
     */
    private void dealGovControlInfo(Person person, ProfilePersonVO vo, Long policeKind) {
        QueryWrapper<ProfilePersonGovControlEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("person_id", person.getId())
                .eq("police_kind", policeKind);
        ProfilePersonGovControlEntity govControlEntity = profilePersonGovControlMapper.selectOne(queryWrapper);
        if (!Objects.isNull(govControlEntity)) {
            vo.setControlGovernment(govControlEntity.getControlGovernment());
            vo.setControlGovernmentPerson(govControlEntity.getControlGovernmentPerson());
            vo.setControlGovernmentContact(govControlEntity.getControlGovernmentContact());
            vo.setControlCommunity(govControlEntity.getControlCommunity());
            vo.setControlCommunityPerson(govControlEntity.getControlCommunityPerson());
            vo.setControlCommunityContact(govControlEntity.getControlCommunityContact());
        }else {
            vo.setControlGovernment(null);
            vo.setControlGovernmentPerson(null);
            vo.setControlGovernmentContact(null);
            vo.setControlCommunity(null);
            vo.setControlCommunityPerson(null);
            vo.setControlCommunityContact(null);
        }
    }

    /**
     * 构建excel图片实体
     *
     * @param urls 图片url集合
     * @param isCrj 是否是crj
     * @return WriteCellData
     */
    private WriteCellData<Void> buildExportPhoto(List<String> urls, Boolean isCrj) {
        if (CollectionUtils.isEmpty(urls)) {
            return null;
        }
        try {
            WriteCellData<Void> writeCellData = new WriteCellData<>();
            List<ImageData> imageDataList = new ArrayList<>();
            for (int i = 0; i < urls.size(); i++) {
                String url = urls.get(i);
                byte[] body;
                String fileName = url.substring(url.lastIndexOf("/") + 1);
                if (url.contains(CommonConstants.PERSON_PHOTO_CONTAIN_PATH)) {
                    body = ossService.getPhoto(fileName);
                } else {
                    ResponseEntity<byte[]> download = ossService.getFile(fileName);
                    body = download.getBody();
                }
                if (body == null || body.length == 0) {
                    continue;
                }
                ImageData imageData = isCrj ? imageCellsCrj(body) : imageCells(body);
                imageDataList.add(imageData);
            }
            writeCellData.setImageDataList(imageDataList);
            return writeCellData;
        } catch (Exception e) {
            log.error("人档导出头像失败:[{}]！", e.getMessage(), e);
        }
        return null;
    }

    /**
     * Excel图片设置
     *
     * @param bytes bytes
     * @return ImageData
     */
    public static ImageData imageCells(byte[] bytes) {

        ImageData imageData = new ImageData();
        imageData.setImage(bytes);
        // 上 右 下 左 需要留空，这个类似于 css 的 margin；这里实测 不能设置太大
        imageData.setTop(0);
        imageData.setRight(5);
        imageData.setBottom(0);
        imageData.setLeft(5);

        // 设置图片的位置。Relative表示相对于当前的单元格index。first是左上点，last是对角线的右下点，这样确定一个图片的位置和大小。
        imageData.setRelativeFirstRowIndex(1);
        imageData.setRelativeFirstColumnIndex(0);
        imageData.setRelativeLastRowIndex(3);
        imageData.setRelativeLastColumnIndex(1);
        return imageData;
    }

    /**
     * Excel图片设置，出入境
     *
     * @param bytes bytes
     * @return ImageData
     */
    public static ImageData imageCellsCrj(byte[] bytes) {

        ImageData imageData = new ImageData();
        imageData.setImage(bytes);
        // 上 右 下 左 需要留空，这个类似于 css 的 margin；这里实测 不能设置太大
        imageData.setTop(0);
        imageData.setRight(3);
        imageData.setBottom(0);
        imageData.setLeft(3);

        // 设置图片的位置。Relative表示相对于当前的单元格index。first是左上点，last是对角线的右下点，这样确定一个图片的位置和大小。
        imageData.setRelativeFirstRowIndex(0);
        imageData.setRelativeFirstColumnIndex(0);
        imageData.setRelativeLastRowIndex(3);
        imageData.setRelativeLastColumnIndex(1);
        return imageData;
    }

    /**
     * 构建ProfileVehicleVOList
     *
     * @param person person
     * @return ProfileVehicleVOList
     */
    private List<ProfileVehicleVO> buildProfileVehicleVOList(Person person) throws Exception {
        List<Long> vehicleIds = person.getVehicleIds();
        String cellHead = "车辆信息";
        if (person == null || CollectionUtils.isEmpty(vehicleIds)) {
            return exportMgr.buildExportEmptyList(ProfileVehicleVO.class, cellHead);
        }
        List<ProfileVehicleEntity> vehicleEntities = profileVehicleMapper.selectBatchIds(vehicleIds);
        if (CollectionUtils.isEmpty(vehicleEntities)) {
            return exportMgr.buildExportEmptyList(ProfileVehicleVO.class, cellHead);
        }
        List<ProfileVehicleVO> profileVehicleVOList = new ArrayList<>();
        for (int i = 0; i < vehicleEntities.size(); i++) {
            ProfileVehicleEntity vehicle = vehicleEntities.get(i);
            ProfileVehicleVO vehicleVO = new ProfileVehicleVO();
            vehicleVO.setNum(i + 1);
            vehicleVO.setCellHead(cellHead);
            vehicleVO.setCarNumber(vehicle.getCarNumber());
            vehicleVO.setOwner(vehicle.getOwner());
            vehicleVO.setType(ConverterHelper.getDictname(vehicle.getType(), "profile_vehicle_type"));
            vehicleVO.setSource(exportMgr.sourceToName(vehicle.getSource()));
            vehicleVO.setFeature(vehicle.getFeature());
            profileVehicleVOList.add(vehicleVO);
        }
        return profileVehicleVOList;
    }

    /**
     * 构建ProfileVirtualIdentityVOLIST
     *
     * @param person person
     * @return ProfileVirtualIdentityVOLIST
     */
    private List<ProfileVirtualIdentityVO> buildProfileVirtualIdentityVOList(Person person) throws Exception {
        List<Long> virtualIdentityIds = person.getVirtualIdentityIds();
        String cellHead = "手机三码";
        if (person == null || CollectionUtils.isEmpty(virtualIdentityIds)) {
            return exportMgr.buildExportEmptyList(ProfileVirtualIdentityVO.class, cellHead);
        }
        List<ProfileVirtualIdentityEntity> virtualIdentityEntities = profileVirtualIdentityMapper.selectBatchIds(virtualIdentityIds);
        if (CollectionUtils.isEmpty(virtualIdentityEntities)) {
            return exportMgr.buildExportEmptyList(ProfileVirtualIdentityVO.class, cellHead);
        }
        List<ProfileVirtualIdentityVO> voList = new ArrayList<>();
        for (int i = 0; i < virtualIdentityEntities.size(); i++) {
            ProfileVirtualIdentityEntity entity = virtualIdentityEntities.get(i);
            ProfileVirtualIdentityVO vo = new ProfileVirtualIdentityVO();
            vo.setNum(i + 1);
            vo.setCellHead(cellHead);
            vo.setVirtualNumber(entity.getVirtualNumber());
            vo.setType(ConverterHelper.getDictname(entity.getType(), "virtual_identity_type"));
            vo.setSource(exportMgr.sourceToName(entity.getSource()));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取相关布控
     *
     * @param person person
     * @return 相关布控
     * @throws Exception Exception
     */
    private List<MonitorListExportVO> getPersonMonitor(Person person) throws Exception {
        ListParamsRequest request = buildNoPageRequest();
        PageResult<MonitorListDto> pageResult = personArchiveService.getPersonMonitor(person.getId(), request);
        List<MonitorListDto> items = pageResult.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return exportMgr.buildExportEmptyList(MonitorListExportVO.class, "相关布控");
        }
        List<MonitorListExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            MonitorListDto dto = items.get(i);
            MonitorListExportVO vo = new MonitorListExportVO();
            vo.setCellHead("相关布控");
            vo.setNum(i + 1);
            BeanUtils.copyProperties(dto, vo);
            vo.setWarningModelStr(exportMgr.listToString(dto.getWarningModel()));
            vo.setExpirationDateStr(MonitorTimeUtil.calculateDaysAndHours(vo.getDeadLine() == null ? 0 : vo.getDeadLine().intValue()));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 构建不分页的检索参数
     *
     * @return request
     */
    private ListParamsRequest buildNoPageRequest() {
        ListParamsRequest request = new ListParamsRequest();
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(0);
        pageParams.setPageSize(-1);
        request.setPageParams(pageParams);
        return request;
    }

    /**
     * 构建分页的检索参数
     *
     * @param pageNumber 当前页
     * @param pageSize   分页参数
     * @return request
     */
    private ListParamsRequest buildPageRequest(int pageNumber, int pageSize) {
        ListParamsRequest request = new ListParamsRequest();
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(pageNumber);
        pageParams.setPageSize(pageSize);
        request.setPageParams(pageParams);
        return request;
    }

    /**
     * 获取常控列表
     *
     * @param person person
     * @return 常控列表
     * @throws Exception Exception
     */
    private List<RegularMonitorListExportVO> getPersonRegularMonitor(Person person) throws Exception {
        ListParamsRequest request = buildNoPageRequest();
        PageResult<RegularMonitorListVO> pageResult = personArchiveService.getPersonRegularMonitor(person.getId(), request);
        List<RegularMonitorListVO> items = pageResult.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return exportMgr.buildExportEmptyList(RegularMonitorListExportVO.class, "相关常控");
        }
        List<RegularMonitorListExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            RegularMonitorListVO dto = items.get(i);
            RegularMonitorListExportVO vo = new RegularMonitorListExportVO();
            vo.setCellHead("相关常控");
            vo.setNum(i + 1);
            vo.setCreateTimeStr(TimeUtil.getSimpleTime(dto.getCreateTime()));
            BeanUtils.copyProperties(dto, vo);
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取相关预警
     *
     * @param person person
     * @return 获取相关预警
     * @throws Exception Exception
     */
    private List<PersonArchiveWarningExportVO> getPersonWarningRelationList(Person person) throws Exception {
        ListParamsRequest request = buildPageRequest(1, 10);
        PageResult<PersonArchiveWarningVO> pageResult = personArchiveService.getPersonWarningRelationList(person.getId(), request.getPageParams());
        List<PersonArchiveWarningVO> items = pageResult.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return exportMgr.buildExportEmptyList(PersonArchiveWarningExportVO.class, "相关预警");
        }
        List<PersonArchiveWarningExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            PersonArchiveWarningVO dto = items.get(i);
            PersonArchiveWarningExportVO vo = new PersonArchiveWarningExportVO();
            vo.setCellHead("相关预警");
            vo.setNum(i + 1);
            BeanUtils.copyProperties(dto, vo);
            voList.add(vo);
        }
        return voList;
    }

    private void buildPersonGroupList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonGroupExportVO> list = new ArrayList<>();
        List<PersonGroupVO> voList = personGroupRelationMapper.getPersonGroupList(person.getId(), policeKind);
        if (CollectionUtils.isEmpty(voList)) {
            list =  exportMgr.buildExportEmptyList(PersonGroupExportVO.class, "相关群体");
        }

        for (int i = 0; i < voList.size(); i++) {
            PersonGroupVO groupVO = voList.get(i);
            PersonGroupExportVO vo = new PersonGroupExportVO();
            vo.setCellHead("相关群体");
            vo.setNum(i + 1);
            vo.setName(groupVO.getName());
            vo.setGroupLocation(groupVO.getGroupLocation());
            vo.setGroupLabels(exportMgr.labelIdArrayToName(groupVO.getGroupLabel()));
            vo.setActivityLevel(exportMgr.dictCodeToName(groupVO.getActivityLevel(), "profile_activity_level"));
            vo.setCreateDept(exportMgr.deptIdToDeptName(groupVO.getCreateDeptId()));
            vo.setGroupLocation(groupVO.getGroupLocation());
            vo.setGroupWork(exportMgr.dictCodeToName(groupVO.getGroupWork(), "profile_person_group_work"));
            list.add(vo);
        }

        data.put("pGroup", list);
    }

    private void buildPersonEventList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonEventExportVO> list = new ArrayList<>();
        List<PersonEventVO> voList = personEventRelationMapper.getPersonEventList(person.getId(), policeKind);
        if (CollectionUtils.isEmpty(voList)) {
            list = exportMgr.buildExportEmptyList(PersonEventExportVO.class, "相关事件");
        }
        for (int i = 0; i < voList.size(); i++) {
            PersonEventVO eventVO = voList.get(i);
            PersonEventExportVO vo = new PersonEventExportVO();
            vo.setCellHead("相关事件");
            vo.setNum(i + 1);
            vo.setName(eventVO.getName());
            LocalDateTime createTime = eventVO.getCreateTime();
            vo.setCreateTime(createTime == null ? "--" : createTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            vo.setCreateDept(exportMgr.deptIdToDeptName(eventVO.getCreateDeptId()));
            vo.setEventType(exportMgr.dictCodeToName(eventVO.getEventType(), "profile_event_type"));
            vo.setEventStartTime(eventVO.getEventStartTime() == null ? "--" : eventVO.getEventStartTime().format(TimeUtil.DEFAULT_TIME_PATTERN));
            list.add(vo);
        }
        data.put("pEvent", list);
    }

    private void buildPersonClueList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonClueExportVO> list = new ArrayList<>();
        List<Clue> clueList = personClueRelationMapper.getPersonClueList(person.getId(), policeKind);
        if (CollectionUtils.isEmpty(clueList)) {
            list =  exportMgr.buildExportEmptyList(PersonClueExportVO.class, "相关线索");
        }
        for (int i = 0; i < clueList.size(); i++) {
            Clue clue = clueList.get(i);
            PersonClueExportVO vo = new PersonClueExportVO();
            vo.setCellHead("相关线索");
            vo.setNum(i + 1);
            vo.setName(clue.getName());
            vo.setDetail(clue.getDetail());
            vo.setClueLabel(exportMgr.listToString(exportMgr.labelIdArrayToName(clue.getClueLabel())));
            vo.setEmergencyLevel(exportMgr.dictCodeToName(clue.getEmergencyLevel(), "profile_clue_emergency_level"));
            vo.setSource(exportMgr.dictCodeToName(clue.getSource(), "profile_clue_source"));
            vo.setCreateTime(TimeUtil.getSimpleTime(clue.getCreateTime()));
            vo.setDisposalStatus(exportMgr.dictCodeToName(clue.getDisposalStatus(), "profile_clue_disposal_status"));
            vo.setTargetTime((String) MapperFactory.getMapper("date_time_to_general_string").mappingDisplayValue(clue.getTargetTime(), null));
            vo.setTargetLocation(exportMgr.dictCodeToName(clue.getTargetLocation(), "profile_clue_location"));
            vo.setYjStatus(exportMgr.dictCodeToName(clue.getYjStatus(), "profile_person_clue_yj_status"));
            vo.setYjhStatus(exportMgr.dictCodeToName(clue.getYjhStatus(), "profile_person_clue_yjh_status"));
            list.add(vo);
        }

        data.put("pClue", list);
    }

    private void buildPersonCaseList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonCaseExportVO> list = new ArrayList<>();
        List<ProfileCaseEntity> voList = profileCaseRelationPersonMapper.selectByPersonIdAndPoliceKind(person.getId(), policeKind);
        if (CollectionUtils.isEmpty(voList)) {
            list = exportMgr.buildExportEmptyList(PersonCaseExportVO.class, "相关事件");
        }
        for (int i = 0; i < voList.size(); i++) {
            ProfileCaseEntity eventVO = voList.get(i);
            PersonCaseExportVO vo = new PersonCaseExportVO();
            vo.setCellHead("相关案件");
            vo.setNum(i + 1);
            vo.setName(eventVO.getAjmc());
            LocalDateTime createTime = eventVO.getUpdateTime();
            vo.setUpdateTime(createTime == null ? "--" : createTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            vo.setUpdateDept(exportMgr.deptIdToDeptName(eventVO.getUpdateDeptId()));
            list.add(vo);
        }
        data.put("pCase", list);
    }


    /**
     * 获取家庭关系
     *
     * @param person person
     * @return 家庭关系
     */
    private List<ProfileFamilyRelationExportVO> getFamilyRelationList(Person person) throws Exception {
        List<Long> ids = person.getFamilyRelationIds();
        if (CollectionUtils.isEmpty(ids)) {
            return exportMgr.buildExportEmptyList(ProfileFamilyRelationExportVO.class, "家庭关系");
        }
        QueryWrapper<ProfileFamilyRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids).orderByDesc("update_time").orderByDesc("id");
        List<ProfileFamilyRelation> familyRelations = profileFamilyRelationMapper.selectList(queryWrapper);
        List<ProfileFamilyRelationExportVO> collect = familyRelations.stream().map(r->{
            ProfileFamilyRelationExportVO vo = PersonConverter.CONVERTER.familyRelationToExportVO(r);
            vo.setIdType(getIdType(person, r.getIdType()));
            return vo;
        }).collect(Collectors.toList());
        return exportMgr.buildExportEmptyList(collect, ProfileFamilyRelationExportVO.class, "家庭关系");
    }

    /**
     * 获取社会关系
     *
     * @param person person
     * @return 社会关系
     */
    private List<ProfileSocialRelationExportVO> getSocialRelationList(Person person) throws Exception {
        List<Long> ids = person.getSocialRelationIds();
        if (CollectionUtils.isEmpty(ids)) {
            return exportMgr.buildExportEmptyList(ProfileSocialRelationExportVO.class, "社会关系");
        }
        QueryWrapper<ProfileSocialRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids).orderByDesc("update_time").orderByDesc("id");
        List<ProfileSocialRelation> relations = profileSocialRelationMapper.selectList(queryWrapper);
        List<ProfileSocialRelationExportVO> collect = relations.stream().map(r->{
            ProfileSocialRelationExportVO vo = PersonConverter.CONVERTER.socialRelationToExportVO(r);
            vo.setIdType(getIdType(person, r.getIdType()));
            return vo;
        }).collect(Collectors.toList());
        return exportMgr.buildExportEmptyList(collect, ProfileSocialRelationExportVO.class, "社会关系");
    }

    /**
     * 获取社会关系
     *
     * @param person person
     * @return 社会关系
     */
    private List<ProfilePlatformAccountExportVO> getPlatformAccountList(Person person) throws Exception {
        List<ProfilePlatformAccount> relations = profilePlatformAccountMapper.getByPersonId(person.getId());
        List<ProfilePlatformAccountExportVO> collect = relations.stream().map(PersonConverter.CONVERTER::platformAccountToExportVO).collect(Collectors.toList());
        return exportMgr.buildExportEmptyList(collect, ProfilePlatformAccountExportVO.class, "虚拟身份");
    }

    private void buildMdjf(Person person, Map<String, Object> data) throws Exception {
        List<PersonMdjfVO> personMdjfVos = personMdjfMapper.selectMdjfList(person.getId());
        List<PersonHotlineVO> personHotlineVos = personMdjfMapper.selectHotlineList(person.getId());
        List<PersonXfVO> personXfVos = personMdjfMapper.selectXfList(person.getId());
        personMdjfVos = exportMgr.buildExportEmptyList(personMdjfVos, PersonMdjfVO.class, "矛盾纠纷");
        personHotlineVos = exportMgr.buildExportEmptyList(personHotlineVos, PersonHotlineVO.class, "1  2  3  4  5");
        personXfVos = exportMgr.buildExportEmptyList(personXfVos, PersonXfVO.class, "信访");
        data.put("mdjf", personMdjfVos);
        data.put("hotline", personHotlineVos);
        data.put("xf", personXfVos);
    }

    /**
     * 获取人员轨迹
     *
     * @param person person
     * @return 常控列表
     * @throws Exception Exception
     */
    private List<TrackPointExportVO> getPersonTrackList(Person person) throws Exception {
        ListParamsRequest request = buildNoPageRequest();
        SortParams sortParams = new SortParams();
        sortParams.setSortField("time");
        sortParams.setSortDirection("");
        request.setSortParams(sortParams);
        KeyValueTypeVO keyValueTypeVO = new KeyValueTypeVO();
        keyValueTypeVO.setKey("activeTime");
        keyValueTypeVO.setValue("1");
        keyValueTypeVO.setType("timeParams");
        List<KeyValueTypeVO> typeVOList = new ArrayList<>();
        typeVOList.add(keyValueTypeVO);
        request.setFilterParams(typeVOList);
        PageResult<TrackPointVO> pageResult = personArchiveService.getPersonTrackList(person.getId(), request);
        List<TrackPointVO> items = pageResult.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return exportMgr.buildExportEmptyList(TrackPointExportVO.class, "人员轨迹");
        }
        List<TrackPointExportVO> voList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            TrackPointVO dto = items.get(i);
            TrackPointExportVO vo = new TrackPointExportVO();
            vo.setCellHead("人员轨迹");
            vo.setNum(i + 1);
            BeanUtils.copyProperties(dto, vo);
            vo.setTypeName(exportMgr.dictCodeToName(dto.getType(), "control_warning_source_type"));
            voList.add(vo);
        }
        return voList;
    }

    private void buildFxyp(Person person, Map<String, Object> data) throws Exception {
        GroupWorkRecordService groupWorkRecordService = BeanFactoryHolder.getBean(GroupWorkRecordService.class).get();
        GroupWorkRecordSearchDTO dto = new GroupWorkRecordSearchDTO();
        dto.setPersonId(person.getId());
        List<GroupWorkRecordVo> workRecords = groupWorkRecordService.getWorkRecords(dto);
        List<PersonWorkRecordVO> collect = workRecords.stream().map(PersonConverter.CONVERTER::workRecordToExportVO).collect(Collectors.toList());
        List<PersonWorkRecordVO> exportVos = exportMgr.buildExportEmptyList(collect, PersonWorkRecordVO.class, "风险研判");
        data.put("fxyp", exportVos);
    }

    private void buildPersonDcqkList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonDcqk> list = personDcqkMapper.selectListByPersonIdAndPoliceKind(person.getId(), policeKind);
        List<PersonDcqkExportVO> collect = list.stream().map(PersonConverter.CONVERTER::dcqkToExportVO).collect(Collectors.toList());
        List<PersonDcqkExportVO> exportVos = exportMgr.buildExportEmptyList(collect, PersonDcqkExportVO.class, "打处情况");
        data.put("dcqk", exportVos);
    }

    private void buildPersonGkcsList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonGkcs> list = personGkcsMapper.selectListByPersonIdAndPoliceKind(person.getId(), policeKind);
        List<PersonGkcsExportVO> collect = list.stream().map(PersonConverter.CONVERTER::gkcsToExportVO).collect(Collectors.toList());
        List<PersonGkcsExportVO> exportVos = exportMgr.buildExportEmptyList(collect, PersonGkcsExportVO.class, "管控措施");
        data.put("gkcs", exportVos);
    }

    private void buildPersonGzcsList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonGzcs> list = personGzcsMapper.selectListByPersonIdAndPoliceKind(person.getId(), policeKind);
        List<PersonGzcsExportVO> collect = list.stream().map(PersonConverter.CONVERTER::gzcsToExportVO).collect(Collectors.toList());
        List<PersonGzcsExportVO> exportVos = exportMgr.buildExportEmptyList(collect, PersonGzcsExportVO.class, "工作措施");
        data.put("gzcs", exportVos);
    }

    private void buildPersonXsbxList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonXsbx> list = personXsbxMapper.selectListByPersonIdAndPoliceKind(person.getId(), policeKind);
        List<PersonXsbxExportVO> collect = list.stream().map(PersonConverter.CONVERTER::xsbxToExportVO).collect(Collectors.toList());
        List<PersonXsbxExportVO> exportVos = exportMgr.buildExportEmptyList(collect, PersonXsbxExportVO.class, "现实动向");
        data.put("xsbx", exportVos);
    }

    private void buildPersonXswhList(Person person, Long policeKind, Map<String, Object> data) throws Exception {
        List<PersonXswh> list = personXswhMapper.selectListByPersonIdAndPoliceKind(person.getId(), policeKind);
        List<PersonXswhExportVO> collect = list.stream().map(PersonConverter.CONVERTER::xswhToExportVO).collect(Collectors.toList());
        List<PersonXswhExportVO> exportVos = exportMgr.buildExportEmptyList(collect, PersonXswhExportVO.class, "现实危害");
        data.put("xswh", exportVos);
    }

    private void buildPersonDzControlList(Person person, Map<String, Object> data) {
        PersonDzControl entity = personDzControlMapper.selectByPersonId(person.getId());
        PersonDzControlExportVO vo = PersonConverter.CONVERTER.dzControlToExportVO(entity);
        data.put("dzControl", vo);
    }

    private void buildPersonRiskJzList(Person person, Map<String, Object> data) {
        PersonRiskJz list = personRiskJzMapper.selectByPersonId(person.getId());
        PersonRiskJzExportVO vo = PersonConverter.CONVERTER.riskJzToExportVO(list);
        data.put("risk", vo);
    }

    private void buildPersonWorkRecordList(Person person, Map<String, Object> data) throws Exception {
        List<PersonWorkRecord> list = personWorkRecordMapper.selectListByPersonId(person.getId());
        List<PersonWorkRecordExportVO> collect = list.stream().map(PersonConverter.CONVERTER::personWorkRecordToExportVO).collect(Collectors.toList());
        List<PersonWorkRecordExportVO> exportVos = exportMgr.buildExportEmptyList(collect, PersonWorkRecordExportVO.class, "工作记录");
        data.put("workRecord", exportVos);
    }

    private void buildPersonRiskSdList(Person person, Map<String, Object> data) {
        PersonRiskSd list = personRiskSdMapper.selectByPersonId(person.getId());
        PersonRiskSdExportVO vo = PersonConverter.CONVERTER.riskSdToExportVO(list);
        data.put("risk", vo);
    }

    private void buildPersonRiskZaList(Person person, Map<String, Object> data) {
        PersonRiskZa list = personRiskZaMapper.selectByPersonId(person.getId());
        PersonRiskZaExportVO vo = PersonConverter.CONVERTER.riskZaToExportVO(list);
        data.put("risk", vo);
    }

    private void buildPersonRiskOtherList(Person person, Long policeKind, Map<String, Object> data) {
        PersonRiskOther list = personRiskOtherMapper.selectByPersonIdAndPoliceKind(person.getId(), policeKind);
        PersonRiskOtherExportVO vo = PersonConverter.CONVERTER.riskOtherToExportVO(list);
        data.put("risk", vo);
    }

    private void buildPersonRiskZbList(Person person, Long policeKind, Map<String, Object> data) {
        PersonRiskOther list = personRiskOtherMapper.selectByPersonIdAndPoliceKind(person.getId(), policeKind);
        PersonRiskOtherExportVO vo = PersonConverter.CONVERTER.zbToExportVO(list);
        data.put("risk", vo);
    }
}
