package com.trs.police.profile.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.control.RegularMonitorListVO;
import com.trs.police.common.core.vo.permission.DataPermissionInfo;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.common.core.vo.profile.PersonCaseVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.vo.PersonListVO;
import com.trs.police.profile.debezium.vo.ProfilePersonInfoVO;
import com.trs.police.profile.domain.dto.PersonAberrationDTO;
import com.trs.police.profile.domain.dto.ProfilePersonDto;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.vo.*;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人员档案查询
 *
 * <AUTHOR> yanghy
 * @date : 2022/10/9 18:03
 */
@Mapper
@DS("mysql")
public interface PersonMapper extends BaseMapper<Person> {

    /**
     * 模糊查询人员档案
     *
     * @param certificateNumber 证件号码
     * @param certificateType   证件类型
     * @return {@link PersonVO}
     */
    List<PersonVO> getPersonListFuzzy(@Param("certificateNumber") String certificateNumber,
        @Param("certificateType") String certificateType);

    /**
     * 根据证件号码查询人员档案
     *
     * @param idNumber 证件号码
     * @return {@link PersonVO}
     */
    PersonVO getPersonByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 根据id查询人员档案
     *
     * @param id 人员id
     * @return {@link PersonVO}
     */
    PersonVO getPersonById(@Param("id") Long id);

    /**
     * 根据标识符及类型查询人员信息
     *
     * @param idNumber 证件号码
     * @param identifierNumbers 标识符号码
     * @param identifierType   标识符类型
     * @return 人员信息
     */
    List<PersonVO> findByIdentifier(
            @Param("idNumber") String idNumber,
            @Param("identifierNumbers") String identifierNumbers,
        @Param("identifierType") Integer identifierType);

    /**
     * 模糊查询人员档案
     *
     * @param personId 人员id
     * @return {@link PersonVO}
     */
    PersonVO getPerson(@Param("personId") Long personId);

    /**
     * 查询电话号
     *
     * @param personId 人员id
     * @return {@link String}
     */
    List<String> getPersonMobilePhoneById(@Param("personId") Long personId);

    /**
     * 分页查询人员
     *
     * @param filterParams 动态参数
     * @param searchParams 动态参数
     * @param page         分页参数
     * @return {@link PersonVO}
     */
    Page<PersonVO> selectPageList(@Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams, Page<PersonVO> page);

    /**
     * 分页查询人员
     *
     * @param filterParams 动态参数
     * @param searchParams 动态参数
     * @param page         分页参数
     * @param groupId      群体id
     * @return {@link PersonCardVO}
     */
    Page<PersonCardVO> selectGroupPageList(@Param("groupId") Long groupId,
        @Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams, Page<PersonVO> page);

    /**
     * 群体查询人员列表
     *
     * @param groupId 群体id
     * @return 人员列表
     */
    List<PersonVO> getPersonByGroup(@Param("groupId") Long groupId);


    /**
     * 根据人员id，批量获取人员信息
     *
     * @param ids 人员id数组
     * @return {@link PersonVO}
     */
    List<PersonVO> getByIds(@Param("ids") List<Long> ids);

    /**
     * 根据证件号码和证件类型获取人员
     *
     * @param certificateNumber 证件号码
     * @param type              证件类型
     * @return 人员信息
     */
    @ResultMap("mybatis-plus_Person")
    @Select("select * from t_profile_person where Id_TYPE =#{type} and ID_NUMBER=#{certificateNumber} order by id desc limit 1")
    Person getPersonByCertificateNumberAndType(@Param("certificateNumber") String certificateNumber,
        @Param("type") Integer type);

    /**
     * 根据证件号码和证件类型获取人员 (加上排他锁)
     *
     * @param certificateNumber 证件号码
     * @param type              证件类型
     * @return 人员信息
     */
    @ResultMap("mybatis-plus_Person")
    @Select("select * from t_profile_person where Id_TYPE =#{type} and ID_NUMBER=#{certificateNumber} and deleted=0 FOR UPDATE")
    Person getPersonByCertificateNumberAndTypeForUpdate(@Param("certificateNumber") String certificateNumber,
                                               @Param("type") Integer type);

    /**
     * 根据证件号码和证件类型获取人员
     *
     * @param certificateNumber 证件号码
     * @param type              证件类型
     * @return 人员信息
     */
    @ResultMap("mybatis-plus_Person")
    @Select("select * from t_profile_person where Id_TYPE =#{type} and ID_NUMBER=#{certificateNumber} and deleted =- 0")
    Person getPersonByCertificateNumberAndTypeAndNotDelete(@Param("certificateNumber") String certificateNumber,
        @Param("type") Integer type);

    /**
     * 根据证件号码和证件类型获取人员
     *
     * @param certificateNumber 证件号码
     * @param type              证件类型
     * @param policeKind        警种信息
     * @return 人员信息
     */
    @ResultMap("mybatis-plus_Person")
    @Select("select * from t_profile_person where Id_TYPE =#{type} and ID_NUMBER=#{certificateNumber} and deleted = 0 and JSON_CONTAINS(police_kind, '${policeKind}', '$')")
    Person getPersonByCertificateNumberAndTypeAndPoliceKindAndNotDelete(@Param("certificateNumber") String certificateNumber,
                                                           @Param("type") Integer type,
                                                           @Param("policeKind") Long policeKind);

    /**
     * 更新人员布控状态
     *
     * @param personIds     人员id
     * @param monitorStatus 布控状态
     * @return {@link Integer}
     */
    Integer setPersonMonitorStatus(@Param("personIds") List<Long> personIds,
        @Param("monitorStatus") Integer monitorStatus);

    /**
     * 更新人员常控状态
     *
     * @param personIds     人员id
     * @param controlStatus 布控状态
     * @return {@link Integer}
     */
    Integer setPersonControlStatus(@Param("personIds") List<Long> personIds,
        @Param("controlStatus") Integer controlStatus);

    /**
     * 更新人员常控状态
     *
     * @param idNumbers     人员id
     * @param controlStatus 布控状态
     * @return {@link Integer}
     */
    Integer setPersonMonitorStatusByIdNumbers(@Param("idNumbers") List<String> idNumbers,
        @Param("monitorStatus") Integer controlStatus);

    /**
     * 人员id查询
     *
     * @param filterParams 列表请求参数
     * @param searchParams 模糊查询参数
     * @return 人员Id
     */
    List<Long> selectPersonIds(@Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams);

    /**
     * 根据人员身份证号查询所在群体
     *
     * @param idNumber 身份证号
     * @return 群体id
     */
    @Select("select r.group_id from t_profile_person p left join t_profile_person_group_relation r on p.id = r.person_id where p.id_number = #{idNumber}")
    List<Long> selectGroupIdsByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 预警列表
     *
     * @param personId 人员id
     * @param page     分页
     * @return 分页信息
     */
    Page<PersonArchiveWarningDto> selectWarningListByIdCard(@Param("personId") Long personId,
        Page<PersonArchiveWarningDto> page);

    /**
     * 获取类型
     *
     * @param modelIds 模型id
     * @return 类型id
     */
    List<Long> getModelTypesByIds(@Param("modelIds") List<Long> modelIds);

    /**
     * 查询类型码值
     *
     * @param code 类型
     * @return 码值
     */
    @Select("select type_code from t_control_warning_source where code =#{code}")
    Long findSourceDictCode(@Param("code") String code);

    /**
     * 布控ids
     *
     * @param idNumber 身份证
     * @return 布控ids
     */
    @Deprecated
    @Select(
        "select c.id from t_control_monitor c where c.deleted=0 and  c.id in (select DISTINCT cmtr.monitor_id from t_control_monitor_target_relation cmtr join t_profile_person ctmp on cmtr.target_id = ctmp.id where ctmp.id_number=#{idNumber}"
            + "union all "
            + "select DISTINCT cmgpr.monitor_id from t_control_monitor_group_person_relation cmgpr where cmgpr.person_identifier_number=#{idNumber}"
            + "union all "
            + "select DISTINCT ar.monitor_id from t_control_monitor_target_filter_params  ar where #{idNumber} MEMBER OF(ar.id_numbers))")
    List<Long> selectRelationMonitorId(@Param("idNumber") String idNumber);

    /**
     * 根据人员档案id查询临控数量
     *
     * @param personId 人员id
     * @return 人员对应的临空数量
     */
    List<IdNameCountVO> temporaryControlCount(@Param("personId") List<Long> personId);

    /**
     * 根据人员档案id查询临控数量
     *
     * @param personId 人员id
     * @return 人员对应的常控数量
     */
    List<IdNameCountVO> regularControlCount(@Param("personId") List<Long> personId);

    /**
     * 根据
     *
     * @param idNumbers 人员证件号码
     * @return 人员vo
     */
    List<PersonVO> getPersonByIdNumbers(@Param("idNumbers") List<String> idNumbers);

    /**
     * 列表页查询
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param page         分页
     * @return 分页查询结果
     */
    Page<PersonVO> selectPersonList(
        @Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams,
        Page<PersonVO> page);

    /**
     * 列表页查询
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param page         分页
     * @param sortParams 排序参数
     * @param permissionInfo permissionInfo
     * @param currentUserId 当前登录用户id
     * @param policeKind 归属警种
     * @param joinVO join
     * @return 分页查询结果
     */
    Page<PersonListVO> personListJoinSearch(
            @Param("filterParams") List<KeyValueTypeVO> filterParams,
            @Param("searchParams") SearchParams searchParams,
            @Param("sortParams") SortParams sortParams,
            @Param("permissionInfo") DataPermissionInfo permissionInfo,
            @Param("currentUserId") Long currentUserId,
            @Param("joinVO") PersonProfileJoinVO joinVO,
            @Param("policeKind") Integer policeKind,
            Page<PersonVO> page);

    /**
     * 查询id列表
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param sortParams 排序参数
     * @param permissionInfo permissionInfo
     * @param currentUserId 当前登录用户id
     * @param policeKind 归属警种
     * @param joinVO join
     * @return 分页查询结果
     */
    List<Long> personIdsJoinSearch(
            @Param("filterParams") List<KeyValueTypeVO> filterParams,
            @Param("searchParams") SearchParams searchParams,
            @Param("sortParams") SortParams sortParams,
            @Param("permissionInfo") DataPermissionInfo permissionInfo,
            @Param("currentUserId") Long currentUserId,
            @Param("joinVO") PersonProfileJoinVO joinVO,
            @Param("policeKind") Integer policeKind);

    /**
     * 列表页查询计数
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param sortParams 排序参数
     * @param permissionInfo permissionInfo
     * @param currentUserId 当前登录用户id
     * @param policeKind 归属警种
     * @param joinVO join
     * @return 分页查询结果
     */
    Long personListJoinSearchCount(
            @Param("filterParams") List<KeyValueTypeVO> filterParams,
            @Param("searchParams") SearchParams searchParams,
            @Param("sortParams") SortParams sortParams,
            @Param("permissionInfo") DataPermissionInfo permissionInfo,
            @Param("currentUserId") Long currentUserId,
            @Param("joinVO") PersonProfileJoinVO joinVO,
            @Param("policeKind") Integer policeKind);

    /**
     * 列表页查询
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param page         分页
     * @param sortParams 排序参数
     * @param permissionInfo permissionInfo
     * @param currentUserId 当前登录用户id
     * @param currentDeptId 当前登录部门id
     * @param policeKind 归属警种
     * @param joinVO join
     * @return 分页查询结果
     */
    Page<PersonListVO> personListJoinSearchV2(
            @Param("filterParams") List<KeyValueTypeVO> filterParams,
            @Param("searchParams") SearchParams searchParams,
            @Param("sortParams") SortParams sortParams,
            @Param("permissionInfo") DataPermissionInfo permissionInfo,
            @Param("currentUserId") Long currentUserId,
            @Param("currentDeptId") Long currentDeptId,
            @Param("joinVO") PersonProfileJoinVO joinVO,
            @Param("policeKind") Integer policeKind,
            Page<PersonVO> page);

    /**
     * 查询ids
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param sortParams 排序参数
     * @param permissionInfo permissionInfo
     * @param currentUserId 当前登录用户id
     * @param currentDeptId 当前登录部门id
     * @param policeKind 归属警种
     * @param joinVO join
     * @return 分页查询结果
     */
    List<Long> personIdsJoinSearchV2(
            @Param("filterParams") List<KeyValueTypeVO> filterParams,
            @Param("searchParams") SearchParams searchParams,
            @Param("sortParams") SortParams sortParams,
            @Param("permissionInfo") DataPermissionInfo permissionInfo,
            @Param("currentUserId") Long currentUserId,
            @Param("currentDeptId") Long currentDeptId,
            @Param("joinVO") PersonProfileJoinVO joinVO,
            @Param("policeKind") Integer policeKind);

    /**
     * 查询符合条件的人员证件信息
     *
     * @param filterParams 动态参数
     * @param searchParams 模糊检索参数
     * @return 人员证件号码
     */
    List<String> selectIdNumber(
        @Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams);

    /**
     * 查询类型码值
     *
     * @param typeCode 类型
     * @return 码值
     */
    @Select("select code from t_control_warning_source_type where type_code =#{typeCode}")
    List<String> findSourceCode(@Param("typeCode") Long typeCode);

    /**
     * 查看人员是否存在
     *
     * @param idType   证件号码
     * @param idNumber 证件类型
     * @return 人员id
     */
    @Select("select id from t_profile_person where id_type = #{idType} and id_number = #{idNumber}")
    Long existByIdTypeAndIdNumber(@Param("idType") Integer idType, @Param("idNumber") String idNumber);

    /**
     * 新增常控时档案人员列表页查询
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param myNotMonitor 我未常控的
     * @param page         分页
     * @return 分页查询结果
     */
    Page<Long> selectRegularPageList(
        @Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams,
        @Param("myNotMonitor")String myNotMonitor,
        Page<PersonVO> page);

    /**
     * 查询身份证号列表里存在于档案中的
     *
     * @param idNumbers 列表
     * @return 结果
     */
    List<String> selectExistIdNumbers(@Param("idNumbers") List<String> idNumbers);

    /**
     * 人员相关常控列表页查询
     *
     * @param personId 人员id
     * @param page     分页
     * @return 分页查询结果
     */
    Page<RegularMonitorListVO> selectRegularMonitorList(@Param("personId") Long personId, Page<PersonVO> page);

    /**
     * 新增常控时档案人员id
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param myNotMonitor myNotMonitor
     * @return 人员id
     */
    List<Long> getIdsByRegularParams(@Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams, @Param("myNotMonitor")String myNotMonitor);

    /**
     * 查看人员管控状态
     *
     * @param personId 人员id
     * @return 状态码
     */
    @Select("select p.monitor_status from t_profile_person p where p.id=#{personId}")
    Integer getMonitorStatus(@Param("personId") Long personId);

    /**
     * 获取人员常控状态列表
     *
     * @param personId 人员id
     * @return 状态列表
     */
    @Select("select distinct r.status from t_control_regular_monitor r where r.target_id=#{personId}")
    List<Integer> getRegularMonitorStatus(@Param("personId") Long personId);

    /**
     * 取消人员常控状态
     *
     * @param personId 人员id
     */
    @Select("update t_profile_person p set p.control_status  = 0 "
        + "where id = #{personId} "
        + "and not exists (select * from t_control_regular_monitor t where t.target_id = #{personId} and t.status in (2,3,5))")
    void setPersonUnControl(@Param("personId") Long personId);

    /**
     * 删除人员时更新
     *
     * @param idNumber 人员证件号码
     */
    void updateWhenPersonDeleting(@Param("idNumber") String idNumber);

    /**
     * 新增人员时更新
     *
     * @param vo 人员信息
     */
    void updateWhenPersonCreating(@Param("vo") ProfilePersonInfoVO vo);

    /**
     * 根据人员id获取人员信息
     *
     * @param personId 人员id
     * @return 人员信息
     */
    ProfilePersonInfoVO getPersonInfoById(@Param("personId") Integer personId);

    /**
     * 列表页查询
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索
     * @param page         分页
     * @return 分页查询结果
     */
    Page<PersonCardVO> getPersonCardList(
        @Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams,
        Page<PersonCardVO> page);

    /**
     * 根据证件号码查询人员卡片详情
     *
     * @param idNumber 证件号码
     * @return 卡片信息
     */
    PersonCardVO getCardByIdNumber(@Param("idNumber") String idNumber);


    /**
     * 根据人员id查询人员卡片
     *
     * @param id 人员id
     * @return 卡片信息
     */
    PersonCardVO getCardById(@Param("id") Long id);

    /**
     * 获取所有人员信息
     *
     * @param page 分页
     * @return 人员信息
     */
    List<ProfilePersonInfoVO> getAllPersonList(Page<ProfilePersonInfoVO> page);

    /**
     * 获取人员管控单位id
     *
     * @param identifierNumber 证件号码
     * @param identifierType   证件类型
     * @return 管控派出所id
     */
    Long getPersonControlDeptIdByIdentifier(@Param("identifierNumber") String identifierNumber,
        @Param("identifierType") Integer identifierType);

    /**
     * 根据虚拟身份信息获取人员信息
     *
     * @param tel 电话号码
     * @return 人员信息
     */
    List<Person> getPersonByTel(@Param("tel") String tel);

    /**
     * 根据电话号码获取人员信息
     *
     * @param tel 电话号码
     * @return 人员信息
     */
    List<PersonVO> getPersonVoByTel(@Param("tel") String tel);

    /**
     * 统计人员轨迹类型数量
     *
     * @param personId  人员id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    List<IdNameCountVO> countTrackByWarningType(@Param("personId") Long personId,
        @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询人员群体活跃程度
     *
     * @param personId 人员id
     * @param groupId  人员群体id
     * @return 活跃程度
     */
    CodeNameVO getActivityLevel(@Param("personId") Long personId, @Param("groupId") Long groupId);

    /**
     * 按时间统计轨迹数量
     *
     * @param identifiers 特征值号码
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @param column      统计字段
     * @param functionName 统计函数
     * @return 结果
     */
    List<TrackActivityTime> countTrackByTime(@Param("identifiers") List<String> identifiers,
                                             @Param("beginTime") String beginTime,
                                             @Param("endTime") String endTime,
                                             @Param("column") String column,
                                             @Param("functionName") String functionName);

    /**
     * 按时间统计轨迹数量, 一律按照活动发生时间统计
     *
     * @param identifiers 特征值号码
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @param functionColumn 取格式后的日期字段
     * @return 结果
     */
    List<TrackActivityTime> countTrackByActivityTime(@Param("identifiers") List<String> identifiers,
                                             @Param("beginTime") String beginTime,
                                             @Param("endTime") String endTime,
                                             @Param("functionColumn") String functionColumn);

    /**
     * 分页查询人员轨迹
     *
     * @param personId     人员id
     * @param filterParams 筛选条件
     * @param searchParams 搜索条件
     * @param sortParams   排序条件
     * @param page         分页参数
     * @return 结果
     */
    Page<TrackPointWithWarningIdVO> selectTrackPage(@Param("personId") Long personId,
        @Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams,
        @Param("sortParams") SortParams sortParams,
        Page<TrackPointWithWarningIdVO> page);

    /**
     * 查询content信息
     *
     * @param warningIdList warningId列表
     * @return content信息
     */
    List<ContentVO> selectContent(@Param("warningIdList") List<Long> warningIdList);

    /**
     * 查询轨迹对应预警信息
     *
     * @param trackId 轨迹id
     * @return 预警
     */
    List<WarningCardVO> selectWarningByTrack(@Param("trackId") Long trackId);

    /**
     * 查询部门下的人员id
     *
     * @param deptId          部门id
     * @param level           管控级别
     * @param regularLabelStr 常控标签json
     * @return 结果
     */
    Double selectPersonIdsByDeptId(@Param("deptId") Long deptId, @Param("level") String level,
        @Param("regularLabel") String regularLabelStr);

    /**
     * 查询所有人员id
     *
     * @return id
     */
    @Select("select id from t_profile_person")
    List<Long> selectAllPersonIds();

    /**
     * 更新完整率
     *
     * @param personId 人员id
     * @param rate     完整率
     */
    @Update("update t_profile_person set complete_rate = #{rate} where id = #{personId}")
    void updatePersonCompleteRate(@Param("personId") Long personId, @Param("rate") Double rate);

    /**
     * 获取筛选条件下的数量
     *
     * @param filterParams 筛选条件
     * @param searchParams 模糊检索参数
     * @return 数量
     */
    Long selectRegularPageListCount(@Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams);

    /**
     * 查询用户是否对人员进行了常控
     *
     * @param personId 人员Id
     * @param userId   用户id
     * @return 是否常控
     */
    @Select("select count(*) > 0 from t_control_regular_monitor where target_id=#{personId} and create_user_id=#{userId} and status in (2, 3, 5) and deleted=0")
    Boolean selectIsRegularControlByUser(@Param("personId") Long personId, @Param("userId") Long userId);

    /**
     * 获取导出vo
     *
     * @param id id
     * @return {@link   PersonExportVO}
     */
    PersonExportVO getPersonExport(@Param("id") Long id);

    /**
     * 查询人员总数
     *
     * @param filterParams 动态参数
     * @param searchParams 模糊检索参数
     * @return 数量
     */
    Long getPersonCardListTotal(@Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams);


    /**
     * 分页查询云哨人员轨迹
     *
     * @param listParamsRequest 筛选
     * @param page              分页
     * @param personId          人员id
     * @return 轨迹信息
     */
    Page<TrackPointVO> getTrack(@Param("request") ListParamsRequest listParamsRequest,
        @Param("page") Page<TrackPointVO> page, @Param("personId") Long personId);

    /**
     * 更新FK专题已建档建档状态
     *
     * @param relatedId relatedId
     * @return 更新条数
     */
    @Update("update t_warning_fk_person set on_record=0 where person_profile_id=#{profilePersonId}")
    int changeFkRecordPerson(Long relatedId);

    /**
     * 查询人档列表
     *
     * @param page page
     * @param dto  dto
     * @return 结果
     */
    Page<PersonGroupVO> getProfilePersonList(@Param("page") Page<PersonGroupVO> page,
        @Param("dto") ProfilePersonDto dto);

    /**
     * updateScore<BR>
     *
     * @param riskScore 参数
     * @param rawScore  参数
     * @param riskLevel 参数
     * @param id        参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/22 16:52
     */
    @Update("update t_profile_person set risk_score=#{riskScore},raw_score=#{rawScore},risk_level=#{riskLevel} where id=#{id}")
    public Integer updateScore(
        @Param("riskScore") Double riskScore,
        @Param("rawScore") Double rawScore,
        @Param("riskLevel") String riskLevel,
        @Param("id") Long id
    );

    /**
     * 根据人员身份证信息查询人员信息
     *
     * @param personId 身份证信息
     * @return 人员信息
     */
    @Select("select * from t_profile_person where id_number = #{personId} and deleted = 0 limit 1")
    Person selectByPersonIdNumber(String personId);

    /**
     * 获取人员异常行为
     *
     * @param personAberrationDTO 如擦
     * @param page                分页信息
     * @return 异常行为集合
     */
    Page<PersonArchiveWarningDto> getPersonAberration(
        @Param("personAberrationDTO") PersonAberrationDTO personAberrationDTO,
        Page<PersonArchiveWarningDto> page);

    /**
     * 根据人员id和风险等级获取人员信息
     *
     * @param personId    人员信息
     * @param personLevel 风险等级
     * @return 人员信息
     */
    @Select("select * from t_profile_person where id = #{personId} and risk_level = #{riskLevel}")
    Person selectByIdAndRiskLevel(@Param("personId") Integer personId, @Param("riskLevel") String personLevel);

    /**
     * 涉案人员列表
     *
     * @param filterParams 检索参数
     * @param searchParams 参数
     * @param page 分页
     * @return 人员信息
     */
    Page<PersonCaseVO> getPersonCaseList(@Param("filterParams") List<KeyValueTypeVO> filterParams,
                                         @Param("searchParams") SearchParams searchParams,
                                         Page<PersonVO> page);

    /**
     * 根据人员标签获取人员档案
     *
     * @param personLabel 人员标签
     * @param page 分页
     * @return {@link Page}<{@link Person}>
     */
    Page<Person> getPersonByPersonLabel(@Param("personLabel") String personLabel, Page<Person> page);

    /**
     * 查找全部
     *
     * @return 结果
     */
    List<PersonExportVO> selectAll();

    /**
     * xx
     *
     * @param personIds 人员ids
     * @param personLevel 人员级别
     * @param policeKind 归属警种
     * @return 结果
     */
    List<RelatedPersonVO> selectPersonResultList(@Param("personIds")List<Integer> personIds,@Param("personLevel") String personLevel,
                                        @Param("policeKind") Integer policeKind);

    /**
     * 批量更新人员的关注状态
     *
     * @param personIds 人员ID列表
     * @param isFollowed 是否关注状态：0-未关注，1-已关注
     */
    void updateFollowStatus(@Param("personIds") List<Long> personIds, @Param("isFollowed") Integer isFollowed);

    /**
     * 根据人员id查询任务
     *
     * @param idNumber 人员身份证
     * @return 任务集合
     */
    @Select("select b.* from tb_intelligence_renwu_related_person_mapping m join tb_intelligence_renwu_base_info b on m.task_id = b.data_id and b.is_del = 0 where m.zjhm = #{idNumber} order by b.cr_time desc")
    List<PersonRenwuVO> findRenWuBaseInfoByPersonId(@Param("idNumber") String idNumber);

    /**
     * 根据人员标签获取人员档案
     *
     * @param labelIds 人员标签
     * @return {@link Page}<{@link PersonListVO}>
     */
    List<PersonListVO> selectAllByLabels(@Param("labelIds") List<Long> labelIds);

    /**
     * 批量插入sw人员信息
     *
     * @param fxPersonList 人员信息
     */
    void insertSwPerson(@Param("list") List<com.trs.police.common.core.fx.entity.Person> fxPersonList);

    /**
     * 获取预警人员关联杆体
     *
     * @param personIds 人员id
     * @return 结果
     */
    List<RelatedWarningPersonGtVO> getRelatedWarningPersonGt(@Param("personIds") List<Long> personIds);

    /**
     * 获取预警人员关联群体
     *
     * @param personId 人员id
     * @return 结果
     */
    List<RelatedGtWarningGroupVO> getRelatedGtWarningGroup(@Param("personId") Long personId);

    /**
     * 获取人员家庭关系
     *
     * @param personId 人员id
     * @return 结果
     */
    List<RelatedPersonVO> getRelatedFamilyWithWarnInfo(@Param("personId") Long personId);
}
