package com.trs.police.profile.domain.vo.graph;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 人员节点属性
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PersonNodeProperties extends AbstractNodeProperties {

    private static final long serialVersionUID = 1L;

    /**
     * 证件号码
     */
    private String idNumber;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 人员级别
     */
    private Integer controlLevel;

    /**
     * 是否有预警
     */
    private Integer havingWarning;
}
