package com.trs.police.profile.service.graph.impl;

import com.trs.police.profile.domain.dto.graph.GraphQueryDTO;
import com.trs.police.profile.domain.enums.NodeTypeEnum;
import com.trs.police.profile.domain.enums.RelationTypeEnum;
import com.trs.police.profile.domain.vo.RelatedPersonVO;
import com.trs.police.profile.domain.vo.graph.GraphLinkVO;
import com.trs.police.profile.domain.vo.graph.GraphNodeVO;
import com.trs.police.profile.factory.NodeFactory;
import com.trs.police.profile.factory.RelationFactory;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.service.graph.RelationProvider;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * fk人员到群体关系提供者
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@RequiredArgsConstructor
public class FkPersonToFamilyPersonRelationProvider implements RelationProvider {

    private final PersonMapper personMapper;

    @Override
    public String getSourceNodeType() {
        return NodeTypeEnum.FKPERSON.getCode();
    }

    @Override
    public RelationResult queryRelations(String sourceNodeId, GraphQueryDTO queryDTO) {
        // 检查是否需要过滤关系类型
        if (CollectionUtils.isNotEmpty(queryDTO.getRelationTypes())
                && !queryDTO.getRelationTypes().contains(RelationTypeEnum.PERSON_FAMILY_PERSON.getName())
                && !queryDTO.getRelationTypes().contains(RelationTypeEnum.PERSON_FAMILY_PERSON.getCode())) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        // 查询人员-群体关系
        List<RelatedPersonVO> groupList = personMapper.getRelatedFamilyWithWarnInfo(
                Long.valueOf(sourceNodeId));

        if (CollectionUtils.isEmpty(groupList)) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        List<GraphLinkVO<?>> links = new ArrayList<>();
        List<GraphNodeVO<?>> targetNodes = new ArrayList<>();

        // 构建关系和节点
        for (RelatedPersonVO relatedPersonVO : groupList) {
            // 创建群体节点
            GraphNodeVO<?> groupNode = NodeFactory.createFamilyPersonNode(relatedPersonVO);
            targetNodes.add(groupNode);
            GraphLinkVO<?> link = RelationFactory.createPersonToFamilyPersonRelation(
                    sourceNodeId, relatedPersonVO);
            links.add(link);
        }

        return new RelationResult(links, targetNodes);
    }
}