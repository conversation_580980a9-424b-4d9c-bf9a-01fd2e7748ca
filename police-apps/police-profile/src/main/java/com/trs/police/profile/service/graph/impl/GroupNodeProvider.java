package com.trs.police.profile.service.graph.impl;

import com.trs.police.profile.domain.entity.Group;
import com.trs.police.profile.domain.enums.NodeTypeEnum;
import com.trs.police.profile.domain.vo.graph.GraphNodeVO;
import com.trs.police.profile.factory.NodeFactory;
import com.trs.police.profile.mapper.GroupMapper;
import com.trs.police.profile.service.graph.NodeProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 群体节点提供者
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@RequiredArgsConstructor
public class GroupNodeProvider implements NodeProvider {

    private final GroupMapper groupMapper;

    @Override
    public String getNodeType() {
        return NodeTypeEnum.GROUP.getCode();
    }

    @Override
    public GraphNodeVO<?> getNodeById(String id) {
        Group group = groupMapper.selectById(Long.valueOf(id));
        if (group == null) {
            return null;
        }
        return NodeFactory.createGroupNode(group, NodeTypeEnum.GROUP);
    }
}