package com.trs.police.profile.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@Data
public class FkGroupExportVO {
    /**
     * 群体名称
     */
    @ExcelProperty("群体名称")
    private String groupName;

    /**
     * 群体级别
     */
    @ExcelProperty("级别")
    private String groupLevel;

    /**
     * 领头人务工地址名称
     */
    @ExcelProperty("地址")
    private String leaderWorkAddressName;

    /**
     * 群体人数
     */
    @ExcelProperty("成员数")
    private Integer relationPersonCount;

    /**
     * 责任派出所名称
     */
    @ExcelProperty("责任派出所")
    private String controlBureauName;

    /**
     * 责任民警名称
     */
    @ExcelProperty("责任民警")
    private String controlPersonName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty("新增时间")
    private Date createTime;

    /**
     * 领头人
     */
    @ExcelProperty("领头人")
    private String leaderName;
}
