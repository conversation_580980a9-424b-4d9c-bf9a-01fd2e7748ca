package com.trs.police.profile.domain.enums;

import lombok.Getter;

/**
 * 关系类型枚举
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Getter
public enum RelationTypeEnum {
    /**
     * 人员-群体关系
     */
    PERSON_GROUP("PERSON_GROUP", "相关群体"),

    /**
     * 群体-人员关系
     */
    GROUP_PERSON("GROUP_PERSON", "相关人员"),

    /**
     * 人员-人员关系
     */
    PERSON_PERSON("PERSON_PERSON", "人员-人员关系"),

    /**
     * 群体-群体关系
     */
    GROUP_GROUP("GROUP_GROUP", "群体-群体关系"),

    /**
     * 人员-家庭人员关系
     */
    PERSON_FAMILY_PERSON("PERSON_FAMILY_PERSON", "家庭人员");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    RelationTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static RelationTypeEnum getByCode(String code) {
        for (RelationTypeEnum type : RelationTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
