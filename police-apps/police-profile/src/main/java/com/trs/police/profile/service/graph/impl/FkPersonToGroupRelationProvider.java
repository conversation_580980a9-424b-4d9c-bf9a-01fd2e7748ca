package com.trs.police.profile.service.graph.impl;

import com.trs.police.profile.domain.dto.graph.GraphQueryDTO;
import com.trs.police.profile.domain.entity.Group;
import com.trs.police.profile.domain.entity.PersonGroupRelation;
import com.trs.police.profile.domain.enums.NodeTypeEnum;
import com.trs.police.profile.domain.enums.RelationTypeEnum;
import com.trs.police.profile.domain.vo.graph.GraphLinkVO;
import com.trs.police.profile.domain.vo.graph.GraphNodeVO;
import com.trs.police.profile.factory.NodeFactory;
import com.trs.police.profile.factory.RelationFactory;
import com.trs.police.profile.mapper.PersonGroupRelationMapper;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.service.graph.RelationProvider;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * fk人员到群体关系提供者
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@RequiredArgsConstructor
public class FkPersonToGroupRelationProvider implements RelationProvider {

    private final PersonGroupRelationMapper personGroupRelationMapper;

    private final PersonMapper personMapper;

    @Override
    public String getSourceNodeType() {
        return NodeTypeEnum.FKPERSON.getCode();
    }

    @Override
    public RelationResult queryRelations(String sourceNodeId, GraphQueryDTO queryDTO) {
        // 检查是否需要过滤节点类型
        if (CollectionUtils.isNotEmpty(queryDTO.getNodeTypes())
                && !queryDTO.getNodeTypes().contains(NodeTypeEnum.FKGROUP.getName())
                && !queryDTO.getNodeTypes().contains(NodeTypeEnum.FKGROUP.getCode())) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        // 检查是否需要过滤关系类型
        if (CollectionUtils.isNotEmpty(queryDTO.getRelationTypes())
                && !queryDTO.getRelationTypes().contains(RelationTypeEnum.PERSON_GROUP.getName())
                && !queryDTO.getRelationTypes().contains(RelationTypeEnum.PERSON_GROUP.getCode())) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        // 查询人员-群体关系
        List<Group> groupList = personGroupRelationMapper.getPersonGroupList(
                Long.valueOf(sourceNodeId), queryDTO.getPoliceKind())
                .stream().map(pg->{
                    Group group = new Group();
                    group.setId(pg.getId());
                    group.setName(pg.getName());
                    return group;
                }).collect(Collectors.toList());
        List<Group> relatedGtWarningGroupList = personMapper.getRelatedGtWarningGroup(Long.valueOf(sourceNodeId))
                .stream().map(gt -> {
                    Group group = new Group();
                    group.setId(gt.getId());
                    group.setName(gt.getName());
                    return group;
                }).collect(Collectors.toList());
        groupList.addAll(relatedGtWarningGroupList);

        if (CollectionUtils.isEmpty(groupList)) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        List<GraphLinkVO<?>> links = new ArrayList<>();
        List<GraphNodeVO<?>> targetNodes = new ArrayList<>();

        // 构建关系和节点
        for (Group group : groupList) {
            // 创建群体节点
            GraphNodeVO<?> groupNode = NodeFactory.createGroupNode(group, NodeTypeEnum.FKGROUP);
            targetNodes.add(groupNode);

            // 创建人员-群体关系
            PersonGroupRelation relation = new PersonGroupRelation();
            relation.setPersonId(Long.valueOf(sourceNodeId));
            relation.setGroupId(group.getId());

            GraphLinkVO<?> link = RelationFactory.createPersonToGroupRelation(
                    sourceNodeId, group.getId().toString(), relation);
            links.add(link);
        }

        return new RelationResult(links, targetNodes);
    }
}