<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.LabelMapper">
    <resultMap id="BaseTreeMap" type="com.trs.police.common.core.entity.Label">
        <!--@mbg.generated-->
        <!--@Table t_profile_label-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_dept_id" jdbcType="BIGINT" property="createDeptId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_dept_id" jdbcType="BIGINT" property="updateDeptId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_type" jdbcType="VARCHAR" property="createType"/>
        <result column="module" jdbcType="VARCHAR" property="module"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <collection property="children" column="id" select="findAllByParentId"
            ofType="com.trs.police.common.core.entity.Label"/>
    </resultMap>

    <select id="selectTree" resultMap="BaseTreeMap">
        select *
        from t_profile_label
        where module = #{module}
          and pid is null and status='1'
        order by show_order, id
    </select>

    <select id="findAllByParentId" resultMap="BaseTreeMap">
        select *
        from t_profile_label l
        where l.pid = #{id}
          and l.pid != id and status='1'
        order by show_order
    </select>
    <select id="getFxxl" resultType="java.lang.String">
        SELECT GROUP_CONCAT(t1.name)
        FROM t_profile_label t1
        where  t1.pid in (select t.id from t_profile_label t where t.module=#{module} and t.pid is null) and
        <foreach collection="ids" item="id" separator="or" open="(" close=")">
            (SELECT concat(t2.path,t2.id,'-') from t_profile_label t2 where id =#{id}) like  concat('%',t1.path,t1.id,'-%')
        </foreach>
    </select>
    <select id="getFxlb" resultType="java.lang.String">
        SELECT GROUP_CONCAT(t1.name)
        FROM t_profile_label t1
        where t1.module=#{module} and  t1.pid is null and
        <foreach collection="ids" item="id" separator="or" open="(" close=")">
            (SELECT concat(t2.path,t2.id,'-') from t_profile_label t2 where id =#{id}) like  concat('%',t1.path,t1.id,'-%')
        </foreach>
    </select>

    <select id="selectLabelByNames" resultType="com.trs.police.common.core.entity.Label">
        select * from t_profile_label
        where
            name in
        <foreach collection="names" item="name" separator="," open="(" close=")">
            #{name}
        </foreach>
            and module = #{module}
    </select>
</mapper>