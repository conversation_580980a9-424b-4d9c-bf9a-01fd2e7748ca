<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.GroupMapper">
    <resultMap id="groupVoMap" type="com.trs.police.common.core.vo.profile.GroupVO">
        <id column="id" jdbcType="VARCHAR" property="groupId"/>
        <result column="name" jdbcType="VARCHAR" property="groupName"/>
        <result column="group_label" jdbcType="VARCHAR" property="groupLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="basic_info" jdbcType="VARCHAR" property="basicInfo"/>
        <result column="mainDemand" jdbcType="VARCHAR" property="mainDemand"/>
        <result column="person_count" jdbcType="INTEGER" property="personCount"/>
        <result column="monitorPerson" property="monitorPerson"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <collection property="groupType" ofType="java.lang.String"
                    select="com.trs.police.profile.mapper.GroupMapper.getGroupType" column="{groupId=id}"/>
    </resultMap>

    <resultMap id="groupMap" type="com.trs.police.profile.domain.entity.Group">
        <result column="groupLabel" property="groupLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="policeKind" property="policeKind"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
    </resultMap>

    <resultMap id="groupVoListMap" type="com.trs.police.common.core.vo.profile.GroupVO">
        <id column="id" jdbcType="VARCHAR" property="groupId"/>
        <result column="name" jdbcType="VARCHAR" property="groupName"/>
        <result column="basic_info" jdbcType="VARCHAR" property="basicInfo"/>
        <result column="mainDemand" jdbcType="VARCHAR" property="mainDemand"/>
        <result column="person_count" jdbcType="INTEGER" property="personCount"/>
        <collection property="groupType" ofType="java.lang.String"
                    select="com.trs.police.profile.mapper.GroupMapper.getGroupType" column="{groupId=id}"/>
    </resultMap>

    <resultMap id="groupListMap" type="com.trs.police.common.openfeign.starter.vo.GroupListVO">
        <id column="id" property="id"/>
        <result column="groupName" property="groupName"/>
        <result column="groupType" property="groupType"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="groupLevel" property="groupLevel"/>
        <result column="relationPersonCount" property="relationPersonCount"/>
        <result column="createDeptName" property="createDeptName"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="policeKind" property="policeKind"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="profileStatus" property="profileStatus"/>
        <result column="approval_detail" property="approvalDetailStr"/>
    </resultMap>

    <resultMap id="groupStatisticMap" type="com.trs.police.common.core.vo.profile.GroupStatisticVO">
        <result column="un_control" property="unControl"/>
        <collection property="inControl" ofType="com.trs.police.common.core.vo.profile.GroupInControlStatisticVO">
            <result column="total" property="total"/>
            <result column="missing_info" property="missingInfo"/>
        </collection>
    </resultMap>

    <resultMap id="relatedGtWarningPersonVO" type="com.trs.police.profile.domain.vo.RelatedGtWarningPersonVO">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"></result>
        <result column="id_number" property="idNumber"/>
        <result column="photo" property="photo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="groupListControlMap" type="com.trs.police.common.openfeign.starter.vo.GroupListVO">
        <id column="id" property="id"/>
        <result column="controlPerson" property="controlPersonList"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="controlBureau" property="controlBureau"/>
        <result column="controlPolice" property="controlPolice"/>
    </resultMap>

    <select id="getGroupType" resultType="java.lang.String">
        SELECT l.name
        FROM t_profile_label l
        WHERE l.id MEMBER OF
        ((
        SELECT g.group_label
        FROM t_profile_group g
        WHERE g.id=#{groupId,jdbcType=BIGINT}
        ))
    </select>

    <select id="findById" resultMap="groupVoMap">
        select g.id as id,
        g.name as name,
        g.group_label,
        g.basic_info as basic_info,
        g.MAIN_DEMAND as mainDemand,
        '[]' as monitorPerson,
       -- g.
        (select count(1) from t_profile_person_group_relation r where r.GROUP_ID = g.ID) as person_count
        FROM t_profile_group g
        WHERE g.id=#{groupId,jdbcType=BIGINT}
    </select>

    <select id="findByPage" resultMap="groupVoListMap">
        select g.id as id,
        g.name as name,
        g.basic_info as basic_info,
        g.MAIN_DEMAND as mainDemand,
        (select count(1) from t_profile_person_group_relation r where r.GROUP_ID = g.ID) as person_count
        FROM t_profile_group g
        <where>
            g.deleted = 0
            <if test="params.filterParams.size() > 0">
                <foreach collection="params.filterParams" item="param">
                    <if test="param.key == 'permissionDeptCode'">
                        and g.id in
                        (select c.group_id from t_profile_group_police_control c where c.control_station in
                        <foreach collection="param.value" item="dept" separator="," open="(" close=")">
                            #{dept}
                        </foreach>
                        )
                    </if>
                    <if test="param.key == 'currentUser'">
                        and g.id in (select c.group_id from t_profile_group_police_control c where c.control_person =
                        #{param.value})
                    </if>
                    <if test="param.key == 'createDept'">
                        <bind name="deptPrefix"
                              value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)"/>
                        and g.create_dept_id in (select d.id from t_dept d where d.code like concat(#{deptPrefix},'%'))
                    </if>
                    <if test="param.key == 'groupType'">
                        and JSON_OVERLAPS(g.group_label, (SELECT JSON_ARRAYAGG( l.id ) FROM t_profile_label l WHERE
                        CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', #{param.value}, '-%' )))
                    </if>
                </foreach>
            </if>
            <if test="params.searchParams.searchField == 'groupName'">
                and g.name like concat('%',#{params.searchParams.searchValue},'%')
            </if>
        </where>
    </select>

    <select id="findByIds" resultMap="groupVoMap">
        select g.id as id,
        g.name as name,
        g.basic_info as basic_info,
        g.MAIN_DEMAND as mainDemand,
        (select count(1) from t_profile_person_group_relation r where r.GROUP_ID = g.ID) as person_count
        FROM t_profile_group g
        <where>
            g.ID in
            <foreach collection="groupBatchId" open="(" close=")" separator="," item="groupId">
                #{groupId}
            </foreach>
        </where>
    </select>

    <select id="fuzzyQueryGroupIdByName" resultType="java.lang.Long">
        select g.id
        from t_profile_group g
        where g.name like concat('%',#{groupName},'%')
    </select>

    <select id="countPersonByGroupIds" resultType="java.lang.Integer">
        select count(DISTINCT a.PERSON_ID)
        from t_profile_person_group_relation a
        <where>
            <if test="groupIds!= null and groupIds.size() != 0">
                a.GROUP_ID in
                <foreach collection="groupIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findByPersonId" resultMap="groupVoMap">
        select g.id as id,
        g.name as name,
        g.basic_info as basic_info,
        g.MAIN_DEMAND as mainDemand,
        '[]' as monitorPerson,
        (select count(1) from t_profile_person_group_relation r where r.GROUP_ID = g.ID) as person_count
        FROM t_profile_group g
        join t_profile_person_group_relation r on g.id = r.GROUP_ID
        WHERE r.person_id = #{personId}
    </select>

    <select id="selectByParamsIds" resultType="com.trs.police.profile.domain.vo.GroupLabelVo">
        select g.id as groupId,
        g.group_label as groupLabel
        FROM t_profile_group g
        <where>
            g.ID in
            <foreach collection="ids" open="(" close=")" separator="," item="groupId">
                #{groupId}
            </foreach>
        </where>
    </select>

    <update id="updateBatchByIds">
        <foreach collection="groups" item="group" index="index">
            update t_profile_group set group_label = #{group.groupLabel}
            where id = #{group.groupId};
        </foreach>
    </update>

    <select id="selectByParams" resultType="com.trs.police.profile.domain.vo.GroupLabelVo">
        <bind name="searchParams" value="params.searchParams"/>
        <bind name="filterParams" value="params.filterParams"/>
        select g.id as groupId,
        g.group_label as groupLabel
        from t_profile_group g
        <where>
            <!--筛选条件-->
            <if test="@java.util.Objects@nonNull(filterParams)">
                <foreach collection="filterParams" item="filterParam">
                    <choose>
                        <when test="filterParam.key == 'createTime'">
                            <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                            and g.create_time between #{timeParam.beginTime} and #{timeParam.endTime}
                        </when>
                    </choose>
                    <choose>
                        <when test="filterParam.key == 'updateTime'">
                            <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                            and g.update_time between #{timeParam.beginTime} and #{timeParam.endTime}
                        </when>
                    </choose>
                    <choose>
                        <when test="filterParam.key == 'groupLabel'">
                            <bind name="labels" value="filterParam.getProcessedValue()"/>
                            AND
                            <foreach collection="labels" item="item" open="(" separator="OR" close=")">
                                (JSON_OVERLAPS(g.group_label
                                ,(SELECT JSON_ARRAYAGG( l.id )
                                FROM t_profile_label l
                                WHERE CONCAT(l.path, l.id, '-')
                                LIKE CONCAT('%-', ${item}, '-%' ))
                                )
                                >0)
                            </foreach>
                        </when>
                        <when test="filterParam.key == 'createDept'">
                            <bind name="deptList"
                                  value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(filterParam.getProcessedValue())"/>
                            AND
                            JSON_OVERLAPS(#{deptList}
                            ,(SELECT JSON_ARRAYAGG(d.id)
                            FROM t_dept d
                            WHERE ifnull((select concat(path, d.id, '-') from t_profile_group_police_control t join
                            t_dept
                            d on t.control_station=d.code where t.group_id = g.id), '')
                            LIKE CONCAT('%-', d.id, '-%'))
                            ) > 0
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
    </select>
    <select id="getGroupExport" resultType="com.trs.police.profile.domain.vo.GroupExportVO">
        select p.id,
        (select t.name from t_dept d left join t_district t on d.district_code = t.code where d.code=c.control_bureau)
        as controlBureau,
        p.group_label as groupLabels,
        p.name as name,
        (select name from t_dept d where d.code=c.control_police) as controlPolice,
        p.work_measures as workMeasures,
        g.control_government as controlGovernment,
        g.control_government_person_duty as zrldzw,
        (SELECT GROUP_CONCAT( u.`real_name` ) FROM t_user u WHERE
        EXISTS (
        SELECT 1
        FROM
        t_profile_group_police_control co,
        JSON_TABLE ( co.control_person, '$[*]' COLUMNS ( id INT PATH '$' ) ) jt
        WHERE
        co.id = c.id AND jt.id = u.id )) as gazry,
        (SELECT GROUP_CONCAT( u.`telephone` ) FROM t_user u WHERE
        EXISTS (
        SELECT 1
        FROM
        t_profile_group_police_control co,
        JSON_TABLE ( co.control_person, '$[*]' COLUMNS ( id INT PATH '$' ) ) jt
        WHERE
        co.id = c.id AND jt.id = u.id )) as gazrydh,
        p.main_demand as mainDemand,
        p.petition_info as petitionInfo,
        (select u.real_name from t_user u where u.id = c.control_bureau_leader) as ldxm,
        (select u.telephone from t_user u where u.id = c.control_bureau_leader) as gabalddh,
        (select u.real_name from t_user u where u.id = c.control_bureau_leader) as gazry,
        (select u.telephone from t_user u where u.id = c.control_bureau_leader) as gazrydh,
        (select u.duty from t_user u where u.id = c.control_bureau_leader) as gabaldyw,
        p.realtime_trend as realtimeTrend,
        (select d.name from t_dict d where d.type='profile_work_target' and d.code = p.work_target) as workTarget,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='group' and
        i.profile_target_id=p.id and i.inspector_type=1 ORDER BY create_time DESC LIMIT 1) as zbyxqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='group' and
        i.profile_target_id=p.id and i.inspector_type=2 ORDER BY create_time DESC LIMIT 1) as zrrlsqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='group' and
        i.profile_target_id=p.id and i.inspector_type=3 ORDER BY create_time DESC LIMIT 1) as rywkqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='group' and
        i.profile_target_id=p.id and i.inspector_type=4 ORDER BY create_time DESC LIMIT 1) as hjcxqk,
        (CONCAT_WS('、',g.control_government,g.control_government_person,g.control_government_contact)) as
        governmentControl
        from t_profile_group p
        left join t_profile_group_police_control c on p.id=c.group_id
        left join t_profile_group_government_control g on p.id=g.group_id
        where p.id = #{id}
        group by p.id
    </select>
    <select id="selectAll" resultType="com.trs.police.profile.domain.vo.GroupExportVO">
        select p.id,
        (select t.name from t_dept d left join t_district t on d.district_code = t.code where d.code=c.control_bureau)
        as controlBureau,
        (select name from t_dept d where d.code=c.control_police) as controlPolice,
        p.group_label as groupLabels,
        p.name as name,
        (select name from t_dept d where d.code=c.control_police) as controlPolice,
        p.work_measures as workMeasures,
        g.control_government as controlGovernment,
        p.main_demand as mainDemand,
        p.petition_info as petitionInfo,
        p.realtime_trend as realtimeTrend,
        (select d.name from t_dict d where d.type='profile_work_target' and d.code = p.work_target) as workTarget,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='group' and
        i.profile_target_id=p.id and i.inspector_type=1 ORDER BY create_time DESC LIMIT 1) as zbyxqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='group' and
        i.profile_target_id=p.id and i.inspector_type=2 ORDER BY create_time DESC LIMIT 1) as zrrlsqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='group' and
        i.profile_target_id=p.id and i.inspector_type=3 ORDER BY create_time DESC LIMIT 1) as rywkqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='group' and
        i.profile_target_id=p.id and i.inspector_type=4 ORDER BY create_time DESC LIMIT 1) as hjcxqk,
        (CONCAT_WS('、',g.control_government,g.control_government_person,g.control_government_contact)) as
        governmentControl
        from t_profile_group p
        left join t_profile_group_police_control c on p.id=c.group_id
        left join t_profile_group_government_control g on p.id=g.group_id
        where p.deleted = 0
    </select>
    <select id="groupList" resultMap="groupListMap">
        select
        a.id as id,
        a.name as groupName ,
        a.group_label as groupType,
        (select name from t_dict where type='profile_person_control_level' and code = a.control_level) as groupLevel,
        (select count(1) from t_profile_person_group_relation where group_id = a.id) as relationPersonCount,
        (select short_name from t_dept where id = a.create_dept_id) as createDeptName,
        a.create_time as createTime,
        a.update_time as updateTime,
        a.police_kind as policeKind,
        a.profile_status as profileStatus,
        a.approval_detail
        <if test="policeKind != null and policeKind == 12" >
            ,a.business_category as businessCategory,
            p.name as leaderName,
            p.id_number as leaderIdNumber,
            p.gender as leaderGender,
            p.registered_residence as leaderRegisteredResidence,
            p.basic_investigation_address as leaderBasicInvestigationAddress,
            p.work_address as leaderWorkAddress,
            p.work_category as leaderWorkCategory,
            p.occupation_category as leaderOccupationCategory,
            p.tel as leaderTel,
            p.other_illegal_activities as leaderOtherIllegalActivities,
            p.inflow_time as leaderInflowTime,
            p.specific_analysis as leaderSpecificAnalysis,
            p.is_slrylb as leaderIsSlrylb
        </if>
        from
        t_profile_group as a
        <if test="policeKind != null and policeKind == 12" >
            left join t_profile_person p on a.leader = p.id
        </if>
        <where>
            a.deleted = 0
            <include refid="profileFilterParam"></include>
            <include refid="profileSearchParam"></include>
        </where>
        <include refid="profileSort"></include>
    </select>

    <select id="buildDeptInfo" resultMap="groupListControlMap">
        SELECT group_id as id,
        REPLACE(GROUP_CONCAT(DISTINCT control_station SEPARATOR ','), '],[', ',') AS controlBureau,
        REPLACE(GROUP_CONCAT(DISTINCT control_police SEPARATOR ','), '],[', ',') AS controlPolice,
        REPLACE(GROUP_CONCAT(DISTINCT control_person SEPARATOR ','), '],[', ',') AS controlPerson
        FROM t_profile_group_police_control
        <where>
            1=1
            <if test="groupIdList != null and groupIdList.size() > 0">
                and group_id in
                <foreach item="item" open="(" separator="," close=")" collection="groupIdList">
                    #{item}
                </foreach>
            </if>
            <if test="policeKind != null">
                and police_kind = #{policeKind}
            </if>
        </where>
        group by group_id
    </select>
    <select id="buildZaDeptInfo" resultType="com.trs.police.common.openfeign.starter.vo.GroupListVO">
        SELECT group_id as id,
        REPLACE(GROUP_CONCAT(DISTINCT control_bureau SEPARATOR ','), '],[', ',') AS controlBureau,
        REPLACE(GROUP_CONCAT(DISTINCT control_police SEPARATOR ','), '],[', ',') AS controlPolice
        FROM t_profile_group_police_control
        <where>
            1=1
            <if test="groupIdList != null and groupIdList.size() > 0">
                and group_id in
                <foreach item="item" open="(" separator="," close=")" collection="groupIdList">
                    #{item}
                </foreach>
            </if>
            <if test="policeKind != null">
                and police_kind = #{policeKind}
            </if>
        </where>
        group by group_id
    </select>
    <select id="selectByPoliceKind" resultMap="groupMap">
        select *,group_Label as groupLabel,police_kind as policeKind
        from
        t_profile_group
        <where>
            deleted = 0
            <if test="groupId != null">
                and id = #{groupId}
            </if>
            <if test="policeKind != null">
                and JSON_CONTAINS(police_kind, CAST(#{policeKind} AS JSON), '$')
            </if>
        </where>
    </select>
    <select id="selectByIds" resultMap="groupListMap">
        select
        a.id as id,
        a.name as groupName ,
        a.group_label as groupType,
        (select name from t_dict where type='profile_person_control_level' and code = a.control_level) as groupLevel,
        (select count(1) from t_profile_person_group_relation where group_id = a.id) as relationPersonCount,
        (select short_name from t_dept where id = a.create_dept_id) as createDeptName,
        a.create_time as createTime,
        a.update_time as updateTime,
        a.police_kind as policeKind,
        a.profile_status as profileStatus
        from
        t_profile_group as a
        <where>
            a.deleted = 0
            <if test="ids != null and ids.size() > 0">
                and a.id in
                <foreach item="id" open="(" separator="," close=")" collection="ids">
                    #{id}
                </foreach>
            </if>
        </where>
        <include refid="profileSort"></include>
    </select>


    <sql id="profileSearchParam">
        <!-- 关键词检索 -->
        <bind name="searchParams" value="params.searchParams"/>
        <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="searchField" value="searchParams.searchField"/>
            <bind name="searchValue"
                  value="@com.trs.police.common.core.utils.StringUtil@removeSpecialCharacters(searchParams.searchValue)"/>
            <choose>
                <when test="searchField == 'fullText'">
                    and (
                    a.name like concat('%',#{searchParams.searchValue},'%')
                    )
                </when>
                <when test="searchField == 'name'">
                    and a.name like concat('%',#{searchParams.searchValue},'%')
                </when>
            </choose>
        </if>
    </sql>


    <sql id="profileFilterParam">
        <if test="createDept != null and createDept.size() > 0">
            and a.create_dept_id in
            <foreach collection="createDept" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="groupType != null and groupType.size() > 0">
            and JSON_CONTAINS(a.group_label, JSON_ARRAY(
            <foreach collection="groupType" item="item" separator="," open="" close="">
                #{item}
            </foreach>)
            , '$')
        </if>
        <if test="policeKind != null">
            and JSON_CONTAINS(a.police_kind, CAST(#{policeKind} AS JSON), '$')
            and (
                a.approval_detail is null
                or (
                    JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$."${policeKind}".status'), '3')
                    or JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$."${policeKind}".approvalUserId'), '${currentUserId}')
                    or a.create_dept_id = #{currentDeptId}
                )
            )
        </if>
        <choose>
            <when test="controlBureau != null and controlBureau != ''">
                <if test="policeKind != 4">
                    and EXISTS (
                    SELECT b.control_station
                    FROM t_profile_group_police_control b
                    WHERE b.group_id = a.id
                    AND b.police_kind = #{policeKind}
                    AND b.control_station = #{controlBureau}
                    )
                </if>
                <if test="policeKind == 4">
                    and EXISTS (
                    SELECT b.control_police
                    FROM t_profile_group_police_control b
                    WHERE b.group_id = a.id
                    AND b.police_kind = #{policeKind}
                    AND b.control_police = #{controlBureau}
                    )
                </if>
            </when>
            <when test="controlPolice != null and controlPolice != ''">
                <if test="policeKind != 4">
                    and EXISTS (
                    SELECT b.control_police
                    FROM t_profile_group_police_control b
                    WHERE b.group_id = a.id
                    AND b.police_kind = #{policeKind}
                    AND b.control_police = #{controlPolice}
                    )
                </if>
                <if test="policeKind == 4">
                    and EXISTS (
                    SELECT ga.control_police
                    FROM t_profile_group_police_control b
                    WHERE b.group_id = a.id
                    AND b.police_kind = #{policeKind}
                    AND b.control_police = #{controlPolice}
                    )
                </if>
            </when>
        </choose>
        <if test="params.filterParams != null and params.filterParams.size() > 0">
            <foreach collection="params.filterParams" item="param">
                <choose>
                    <when test="'groupLevel'== param.key">
                        and a.control_level = #{param.value}
                    </when>
                    <when test="'createTime' == param.key">
                        AND (a.create_time >= '${param.value.beginTime}'
                        AND a.create_time &lt;= '${param.value.endTime}')
                    </when>
                    <when test="'updateTime' == param.key">
                        AND (a.update_time >= '${param.value.beginTime}'
                        AND a.update_time &lt;= '${param.value.endTime}')
                    </when>
                    <when test="param.key == 'permissionDeptCode'">
                        and a.id in
                        (select c.group_id from t_profile_group_police_control c where
                        c.police_kind = #{policeKind}
                        and
                        (a.create_dept_id in
                        <foreach collection="param.value" item="dept" separator="," open="(" close=")">
                            #{dept}
                        </foreach>
                        or c.control_bureau in
                        <foreach collection="param.value" item="dept" separator="," open="(" close=")">
                            #{dept}
                        </foreach>
                        or c.control_police in
                        <foreach collection="param.value" item="dept" separator="," open="(" close=")">
                            #{dept}
                        </foreach>
                        or c.control_station in
                        <foreach collection="param.value" item="dept" separator="," open="(" close=")">
                            #{dept}
                        </foreach>
                        )
                        )
                    </when>
                    <when test="param.key == 'controlBureaus'">
                        and EXISTS (
                        SELECT id
                        FROM t_profile_group_police_control b
                        WHERE b.group_id = a.id
                        AND b.police_kind = #{policeKind}
                        <foreach collection="param.getProcessedValue()" item="item" separator="or" open="AND (" close=")">
                            b.control_station = #{item}
                        </foreach>
                        )
                    </when>
                    <when test="param.key == 'controlPerson'">
                        and EXISTS (
                        SELECT id
                        FROM t_profile_group_police_control b
                        WHERE b.group_id = a.id
                        AND b.police_kind = #{policeKind}
                        and JSON_OVERLAPS(control_person, CAST(
                        <foreach collection="param.getProcessedValue()" item="item" separator="," open="'[" close="]'">
                            ${item}
                        </foreach>
                        AS JSON))
                    </when>
                    <when test="param.key == 'ids'">
                        and a.id in
                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </when>
                    <when test="param.key == 'isSlrylb'">
                        <if test="policeKind != null and policeKind == 12" >
                            and p.is_slrylb = #{param.value}
                        </if>
                    </when>
                    <when test="param.key == 'inflowTime'">
                        <if test="policeKind != null and policeKind == 12" >
                            and p.inflow_time <![CDATA[>=]]> #{param.value.beginTime}
                            and p.inflow_time <![CDATA[<=]]> #{param.value.endTime}
                        </if>
                    </when>
                    <when test="param.key == 'registeredResidence'">
                        <if test="policeKind != null and policeKind == 12" >
                            <foreach collection="param.getProcessedValue()" item="item" open="and (" separator="OR"
                                     close=")">
                                p.registered_residence like concat(
                                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                , '%')
                            </foreach>
                        </if>
                    </when>
                    <when test="param.key == 'workAddress'">
                        <if test="policeKind != null and policeKind == 12" >
                            <foreach collection="param.getProcessedValue()" item="item" open="and (" separator="OR"
                                     close=")">
                                p.work_address like concat(
                                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                , '%')
                            </foreach>
                        </if>
                    </when>
                </choose>
            </foreach>
        </if>

    </sql>
    <sql id="profileSort">
        <!-- 排序参数 -->
        <choose>
            <when test="sortParams != null and sortParams.sortField != null">
                <if test="'updateTime'==sortParams.sortField">
                    ORDER BY a.update_time ${sortParams.getProcessedValue()}
                </if>
                <if test="'createTime'==sortParams.sortField">
                    ORDER BY a.create_time ${sortParams.getProcessedValue()}
                </if>
            </when>
        </choose>
    </sql>

    <insert id="insertRelation">
        INSERT INTO `t_profile_person_group_relation` (`create_dept_id`, `create_user_id`, `create_time`,
        `update_user_id`, `update_dept_id`, `update_time`, `group_id`, `person_id`, `activity_level`)
        VALUES ( 1, 1, now(), 1, 1, now(), #{groupId}, #{personId}, null);
    </insert>

    <select id="getRelatedGtWarningPerson"
            resultMap="relatedGtWarningPersonVO">
        SELECT distinct
               p.id,p.gender,p.name,p.id_number,p.photo
        from t_profile_group g
            left join t_warning_fkrxyj w on JSON_CONTAINS(g.related_gt, CAST(concat('"',w.device_code,'"') AS JSON), '$')
            left join t_profile_person p on p.id_number = w.id_card
        where
            p.id is not null
            and g.id = #{groupId}
    </select>
</mapper>