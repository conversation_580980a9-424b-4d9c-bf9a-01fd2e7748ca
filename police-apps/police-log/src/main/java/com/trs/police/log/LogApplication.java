package com.trs.police.log;

import com.trs.police.common.security.starter.annotation.EnablePoliceCloudResourceServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR> 日志中心
 */
@SpringBootApplication
@EnablePoliceCloudResourceServer
@EnableScheduling
@ComponentScan("com.trs.web.builder.util")
@ComponentScan("com.trs.police.log")
public class LogApplication {

    /**
     * main
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        SpringApplication.run(LogApplication.class, args);
    }

}
