package com.trs.police.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.WcspsdkUtils;
import com.trs.police.common.core.vo.log.LoginLogVO;
import com.trs.police.log.domain.dto.LoginLogDTO;
import com.trs.police.log.domain.entity.LoginLogEntity;
import com.trs.police.log.mapper.LoginLogMapper;
import com.trs.police.log.service.LoginLogService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @since 2022/11/9 18:25
 **/
@Service
public class LoginLogServiceImpl implements LoginLogService {

    @Resource
    private LoginLogMapper loginLogMapper;

    @Override
    @Transactional(rollbackFor = TRSException.class)
    public void createLoginLog(LoginLogVO loginLogVO) {
        LoginLogEntity loginLogEntity = new LoginLogEntity();
        BeanUtil.copyPropertiesIgnoreNull(loginLogVO, loginLogEntity);
        loginLogEntity.setMac(WcspsdkUtils.mac(loginLogEntity.toMacString()));
        loginLogMapper.insert(loginLogEntity);
    }

    @Override
    public RestfulResultsV2<LoginLogVO> loginLogList(LoginLogDTO dto) {
        Page<LoginLogEntity> page = Page.of(dto.getPageNum(), dto.getPageSize());
        QueryWrapper<LoginLogEntity> queryWrapper = new QueryWrapper<LoginLogEntity>()
                .eq(StringUtils.isNotEmpty(dto.getUserName()), "user_name", dto.getUserName())
                .ge(StringUtils.isNotEmpty(dto.getStartTime()), "create_time", dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()), "create_time", dto.getEndTime())
                .orderByDesc("create_time");
        Page<LoginLogEntity> loginLogEntityPage = loginLogMapper.selectPage(page, queryWrapper);
        loginLogEntityPage.getRecords().forEach(loginLogEntity -> PreConditionCheck.checkArgument(WcspsdkUtils.verifyMac(loginLogEntity.getMac(), loginLogEntity.toMacString()), loginLogEntity.getId()+"日志被篡改！"));
        List<LoginLogVO> loginLogVOList =  loginLogEntityPage.getRecords().stream().map(loginLogEntity -> {
            LoginLogVO loginLogVO = new LoginLogVO();
            BeanUtil.copyPropertiesIgnoreNull(loginLogEntity, loginLogVO);
            return loginLogVO;
        }).collect(Collectors.toList());
        return RestfulResultsV2.ok(loginLogVOList)
                .addTotalCount(loginLogEntityPage.getTotal())
                .addPageSize((int) loginLogEntityPage.getSize())
                .addPageNum((int) loginLogEntityPage.getCurrent());
    }

    @Override
    public RestfulResultsV2 encryptHistory(LoginLogDTO dto) {
        QueryWrapper<LoginLogEntity> queryWrapper = new QueryWrapper<LoginLogEntity>()
                .eq(StringUtils.isNotEmpty(dto.getUserName()), "user_name", dto.getUserName())
                .ge(StringUtils.isNotEmpty(dto.getStartTime()), "create_time", dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()), "create_time", dto.getEndTime())
                .orderByDesc("create_time");
        List<LoginLogEntity> loginLogList = loginLogMapper.selectList(queryWrapper);
        loginLogList.forEach(loginLogMapper::updateById);
        return RestfulResultsV2.ok();
    }

    @Override
    public RestfulResultsV2 encryptHistoryMac(LoginLogDTO dto) {
        QueryWrapper<LoginLogEntity> queryWrapper = new QueryWrapper<LoginLogEntity>()
                .eq(StringUtils.isNotEmpty(dto.getUserName()), "user_name", dto.getUserName())
                .ge(StringUtils.isNotEmpty(dto.getStartTime()), "create_time", dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()), "create_time", dto.getEndTime())
                .orderByDesc("create_time");
        List<LoginLogEntity> loginLogList = loginLogMapper.selectList(queryWrapper);
        loginLogList.forEach(loginLogEntity -> {
            loginLogEntity.setMac(WcspsdkUtils.mac(loginLogEntity.toMacString()));
            loginLogMapper.updateById(loginLogEntity);
        });
        return RestfulResultsV2.ok();
    }
}
