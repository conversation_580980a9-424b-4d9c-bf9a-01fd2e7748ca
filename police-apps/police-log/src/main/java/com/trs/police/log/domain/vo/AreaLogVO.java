package com.trs.police.log.domain.vo;

import com.trs.police.common.core.vo.approval.ApprovalInfoVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 重点区域日志
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AreaLogVO extends BaseLogVO {

    private String operationInfo;
    /**
     * 审批消息
     */
    private ApprovalInfoVO approvalInfo;

}
