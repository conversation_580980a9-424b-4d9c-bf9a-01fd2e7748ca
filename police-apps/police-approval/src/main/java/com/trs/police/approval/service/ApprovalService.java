package com.trs.police.approval.service;

import com.trs.police.approval.domain.request.ApproveResultRequest;
import com.trs.police.approval.domain.vo.ApprovalVO;
import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.vo.approval.ApprovalResult;
import com.trs.police.common.core.vo.approval.NodeResultEntityVO;
import com.trs.police.common.core.vo.approval.ProcessPreviewVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.common.openfeign.starter.vo.BatchApprovalRequest;

import java.util.List;

/**
 * 审批接口
 *
 * <AUTHOR>
 */
public interface ApprovalService {

    /**
     * 同意审批
     *
     * @param approvalId 审批id
     * @param request    审批意见
     */
    void agreeApproval(Long approvalId, ApproveResultRequest request);

    /**
     * 批量同意审批
     *
     * @param request    审批意见
     * @return 审批详情
     */
    ApprovalVO batchAgreeApproval(ApproveResultRequest request);

    /**
     * 批量驳回审批
     *
     * @param request 请求
     * @return 结果
     */
    ApprovalVO batchReject(ApproveResultRequest request);

    /**
     * 驳回审批
     *
     * @param approvalId 审批id
     * @param request    审批意见
     */
    void rejectApproval(Long approvalId, ApproveResultRequest request);

    /**
     * 发起审批
     *
     * @param approvalRequest 审批参数
     * @return 审批id
     */
    Long startApproval(ApprovalRequest approvalRequest);

    /**
     * 发起审批
     *
     * @param approvalRequest 审批参数
     * @return 审批结果
     */
    ApprovalResult startApprovalV1(ApprovalRequest approvalRequest);

    /**
     * 发起多个审批请求
     *
     * @param approvalRequest 审批请求
     * @return 响应信息
     */
    ResponseMessage startApprovalBatch(BatchApprovalRequest approvalRequest);

    /**
     * 获取首次审批的用户列表
     *
     * @param approvalRequest 审批参数
     * @return 审批用户列表
     */
    List<UserDeptVO> getFistApprover(ApprovalRequest approvalRequest);

    /**
     * 获取审批流程预览
     *
     * @param approvalRequest 审批参数
     * @return 审批流程预览
     */
    List<ProcessPreviewVO> getApprovalProcessPreview(ApprovalRequest approvalRequest);

    /**
     * 审批已读
     *
     * @param approvalId 审批id
     */
    void changeApprovalIsRead(Long approvalId);

    /**
     * 撤回所有相关审批
     *
     * @param serviceCode 关联业务类型code
     * @param serviceId   关联业务id
     * @param action 具体某个操作
     */
    void cancelAllApprovals(String serviceCode, Long serviceId, String action);

    /**
     * 获取发起审批产生的审批人
     *
     * @param approvalRequest 审批参数
     * @return 审批人集合
     */
    UserDeptVO[] getStartApprovers(ApprovalRequest approvalRequest);

    /**
     * 获取审批节点详情
     *
     * @param nodeIds 节点ids
     * @return 审批节点详情
     */
    List<NodeResultEntityVO> getNodesByNodeIds(String nodeIds);

    /**
     * 获取审批模板是否启用
     *
     * @param templateName 流程模板名
     * @return {@link Boolean }
     */
    Boolean findEnableByTemplateName(String templateName);
}
