package com.trs.police.approval.converter;

import com.trs.police.approval.domain.entity.NodeConfigEntity;
import com.trs.police.approval.domain.entity.ProcessConfigEntity;
import com.trs.police.common.openfeign.starter.vo.NodeConfigDetailVO;
import com.trs.police.approval.domain.vo.NodeConfigVO;
import com.trs.police.approval.domain.vo.ProcessConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * @author: dingkeyu
 * @date: 2024/04/18
 * @description:
 */
@Mapper(componentModel = "spring")
public interface ApprovalConfigConverter {


    /**
     * vo -> entity
     *
     * @param vo vo
     * @return {@link ProcessConfigEntity}
     */
    @Mappings({
            @Mapping(target = "scope", expression = "java(ApprovalScopeEnum.codeOf(vo.getScope()))")
        })
    ProcessConfigEntity vo2Entity(ProcessConfigVO vo);

    /**
     * vo -> entity
     *
     * @param vo vo
     * @return {@link NodeConfigEntity}
     */
    @Mappings({
            @Mapping(target = "approvalUsers", expression = "java(com.alibaba.fastjson.JSON.parseObject(vo.getApprovalUsers(), com.trs.police.approval.domain.vo.ApprovalUserDeptVO[].class))"),
            @Mapping(target = "approvalRoles", expression = "java(com.alibaba.fastjson.JSON.parseObject(vo.getApprovalRoles(), com.trs.police.approval.domain.vo.RoleLevelVOV2[].class))"),
            @Mapping(target = "method", expression = "java(ApproveMethodEnum.codeOf(vo.getMethod()))"),
            @Mapping(target = "rejectType", expression = "java(RejectTypeEnum.codeOf(vo.getRejectType()))"),
    })
    NodeConfigEntity vo2Entity(NodeConfigVO vo);

    /**
     * entity -> vo
     *
     * @param entity entity
     * @return {@link ProcessConfigVO}
     */
    @Mappings({
            @Mapping(target = "scope", source = "scope.code")
    })
    ProcessConfigVO entity2VO(ProcessConfigEntity entity);

    /**
     * entity -> vo
     *
     * @param entity entity
     * @return {@link NodeConfigVO}
     */
    @Mappings({
            @Mapping(target = "approvalUsers", expression = "java(com.alibaba.fastjson.JSONObject.toJSONString(entity.getApprovalUsers()))"),
            @Mapping(target = "approvalRoles", expression = "java(com.alibaba.fastjson.JSONObject.toJSONString(entity.getApprovalRoles()))"),
            @Mapping(target = "method", source = "method.code"),
            @Mapping(target = "rejectType", source = "rejectType.code"),
    })
    NodeConfigVO entity2VO(NodeConfigEntity entity);

    /**
     * 配置转换成细览vo
     *
     * @param entity 配置
     * @return 细览vo
     */
    @Mappings({})
    NodeConfigDetailVO entityToEntityDetail(NodeConfigEntity entity);

}
