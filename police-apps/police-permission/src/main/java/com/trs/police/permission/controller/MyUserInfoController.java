package com.trs.police.permission.controller;

import com.trs.police.common.core.dto.UserDto;
import com.trs.police.permission.service.MyUserInfoService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 我的信息接口
 */
@RestController
@RequestMapping("/myUserInfoController")
public class MyUserInfoController {

    @Autowired
    private MyUserInfoService myUserInfoService;


    /**
     * 更新
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/update")
    public RestfulResultsV2<String> update(@RequestBody UserDto dto){
        return myUserInfoService.update(dto);
    }

}
