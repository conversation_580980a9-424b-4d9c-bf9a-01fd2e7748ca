package com.trs.police.permission.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-06-12 09:59
 */
@Data
public class ConversationDTO {
    /**
     *  新开一个会话需要传-1，如果继续在这个会话里面问答的话传递这个会话的会话id
     */
    @NotNull(message = "conversationId不能为空")
    private String conversationId = "-1";

    /**
     * 会话场景，目前支持：权限中心：permission  人员档案：profilePerson
     */
    private String sceneType;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     *  提问的问题
     */
    private String question;
}
