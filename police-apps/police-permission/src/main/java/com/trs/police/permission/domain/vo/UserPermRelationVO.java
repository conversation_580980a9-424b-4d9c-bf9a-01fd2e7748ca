package com.trs.police.permission.domain.vo;

import com.trs.police.permission.domain.entity.UserPermissionRelation;
import com.trs.police.permission.domain.entity.UserRoleRelation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户权限关系VO
 *
 * <AUTHOR> yanghy
 * @date : 2022/7/26 10:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserPermRelationVO {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 部门Id
     */
    private Long deptId;
    /**
     * 权限Id
     */
    private Long permissionId;

    /**
     * 转实体类
     *
     * @return {@link UserRoleRelation}
     */
    public UserRoleRelation toUserRoleRelation() {
        return new UserRoleRelation(this.getUserId(), this.getDeptId(), this.getPermissionId());
    }

    /**
     * 转实体
     *
     * @return {@link UserPermissionRelation}
     */
    public UserPermissionRelation toUserPermissionRelation() {
        return new UserPermissionRelation(this.getUserId(), this.getDeptId(), this.getPermissionId());
    }
}
