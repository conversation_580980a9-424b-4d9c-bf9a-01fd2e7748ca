package com.trs.police.permission.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_user_data_permission_relation")
@AllArgsConstructor
@NoArgsConstructor
public class UserPermissionRelation extends AbstractBaseEntity {

    private static final long serialVersionUID = 3694589056159802550L;

    /**
     * 用户主键
     */
    private Long userId;

    /**
     * 部门主键
     */
    private Long deptId;

    /**
     * 数据权限主键
     */
    private Long permissionId;
}

