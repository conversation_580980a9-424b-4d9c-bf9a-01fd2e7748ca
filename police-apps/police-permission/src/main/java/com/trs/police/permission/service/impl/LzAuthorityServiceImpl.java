package com.trs.police.permission.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.constant.enums.ApprovalStatusEnum;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.approval.ProcessPreviewVO;
import com.trs.police.common.core.vo.permission.UserCardVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.common.openfeign.starter.service.OperationLogService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalDetailVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalListVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.permission.domain.dto.PlatformAuthDto;
import com.trs.police.permission.domain.dto.lzauth.LzAuthorityApprovalDTO;
import com.trs.police.permission.domain.dto.lzauth.LzAuthorityMyRequestDTO;
import com.trs.police.permission.domain.entity.Dept;
import com.trs.police.permission.domain.entity.PlatformAuthUserRelationEntity;
import com.trs.police.permission.domain.entity.PlatformEntity;
import com.trs.police.permission.domain.vo.lzauth.AuthCenterLogDetailVO;
import com.trs.police.permission.domain.vo.lzauth.LzAuthApprovalListDetail;
import com.trs.police.permission.domain.vo.lzauth.LzAuthApprovalListVO;
import com.trs.police.permission.mapper.PlatformAuthUserRelationMapper;
import com.trs.police.permission.mapper.PlatformMapper;
import com.trs.police.permission.service.DeptService;
import com.trs.police.permission.service.LzAuthorityService;
import com.trs.police.permission.service.PlatformService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.police.common.core.constant.enums.ApprovalStatusEnum.IN_PROCESS;
import static com.trs.police.common.core.constant.enums.ApprovalStatusEnum.PASSED;
import static com.trs.police.common.core.constant.log.OperateModule.PERMISSION_CENTER;

/**
 * 泸州权限中心服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LzAuthorityServiceImpl implements LzAuthorityService {

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private PlatformMapper platformMapper;

    @Autowired
    private PlatformService platformService;

    @Autowired
    private PlatformAuthUserRelationMapper platformAuthUserRelationMapper;

    @Autowired
    private DeptService deptService;

    @Autowired
    private PermissionService permissionService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approval(LzAuthorityApprovalDTO dto) {
        final PlatformEntity entity = validate(dto.getPlatId());

        CurrentUser user = AuthHelper.getCurrentUser();
        // 查询状态，如果已经审批通过了 直接舍弃
        PlatformAuthUserRelationEntity p = platformService.getPlatformAuthUserRelationEntity(user.getId(), user.getDeptId(), dto.getPlatId());
        if (Objects.nonNull(p)
                && Objects.nonNull(p.getApprovalStatus())
                && !ApprovalStatusEnum.REJECTED.getCode().equals(p.getApprovalStatus())) {
            throw new TRSException("已经申请过当前权限");
        }

        // 添加用户和平台的关联关系
        PlatformAuthDto pad = new PlatformAuthDto();
        pad.setPlatId(dto.getPlatId());
        pad.setAuthType(dto.getAuthType());
        pad.setUserInfos(List.of(UserDeptVO.of(user)));
        pad.setApprovalStatus(ApprovalStatusEnum.WAITING.getCode());
        platformService.addAuthUserIfNotPresent(pad, AuthHelper.getCurrentUser());

        // 构造平台信息
        AuthCenterLogDetailVO detail = new AuthCenterLogDetailVO();
        detail.setPlatId(entity.getId());
        detail.setTel(AuthHelper.getCurrentUser().getMobile());
        detail.setPlatformName(entity.getName());
        detail.setPurposes(dto.getPurposes());
        detail.setApprovalTitle(String.format("%s(%s)提交的关于%s， 申请权限类型：%s", user.getRealName(), user.getDept().getName(), entity.getName(), dto.getAuthType()));

        // 发起请求
        p = platformService.getPlatformAuthUserRelationEntity(user.getId(), user.getDeptId(), dto.getPlatId());

        ApprovalRequest request = buildRequest(dto, p.getId());
        request.getApprovalActionVO().setStartFormContent(JSON.toJSONString(detail));
        String approvalResult = approvalService.startApprovalWithResult(request);
        if (!approvalResult.contains("200")) {
            throw new TRSException("发起审批失败");
        }

        // 更新状态
        p.setApprovalStatus(ApprovalStatusEnum.WAITING.getCode());
        platformAuthUserRelationMapper.updateById(p);

        // 记录日志
        String ip = null;
        try {
            HttpServletRequest r = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            ip = IpUtil.getRemoteAddress(r);
        } catch (Exception e) {
            log.error("获取ip失败", e);
            ip = "127.0.0.1";
        }
        operationLogService.createOperationLog(
                dto.getPlatId(), ip, PERMISSION_CENTER, Operation.APPROVAL_REQUEST, null, JSON.toJSONString(detail)
        );
    }

    @Override
    public List<ProcessPreviewVO> approvalProcessPreview(LzAuthorityApprovalDTO dto) {
        ApprovalRequest request = buildRequest(dto, -1L);
        String response = approvalService.getApprovalProcessPreview(request);
        JSONObject jsonObject = JSON.parseObject(response);
        if (jsonObject.getInteger("code").equals(200)) {
            return jsonObject.getJSONArray("data").toJavaList(ProcessPreviewVO.class);
        } else {
            throw new TRSException(jsonObject.getString("message"));
        }
    }

    @Override
    public RestfulResultsV2<LzAuthApprovalListVO> myRequest(LzAuthorityMyRequestDTO dto, PageParams params) {
        // 获取到审批列表
        ListParamsRequest request = buildParams(params.getPageNumber(), params.getPageSize());
        PageResult<ApprovalListVO> pending = approvalService.getApprovalList(request, "initiated");
        // 转换成列表vo
        List<LzAuthApprovalListVO> vos = pending.getItems()
                .stream()
                .map(LzAuthApprovalListVO::new)
                .collect(Collectors.toList());
        // 添加属性
        initVo(vos);
        RestfulResultsV2<LzAuthApprovalListVO> ok = RestfulResultsV2.ok(vos);
        ok.addTotalCount(pending.getTotal());
        return ok;
    }

    @Override
    public RestfulResultsV2<LzAuthApprovalListVO> myApproval(LzAuthorityMyRequestDTO dto, PageParams params) {
        // 获取到审批列表
        ListParamsRequest request = buildParams(params.getPageNumber(), params.getPageSize());
        PageResult<ApprovalListVO> pending = approvalService.getApprovalList(request, "pending");
        // 转换成列表vo
        List<LzAuthApprovalListVO> vos = pending.getItems()
                .stream()
                .map(LzAuthApprovalListVO::new)
                .collect(Collectors.toList());
        // 添加属性
        initVo(vos);
        RestfulResultsV2<LzAuthApprovalListVO> ok = RestfulResultsV2.ok(vos);
        ok.addTotalCount(pending.getTotal());
        return ok;
    }

    @Override
    public void acceptApprovalMsg(ApprovalActionVO action) {
        PlatformAuthUserRelationEntity relation = platformAuthUserRelationMapper.selectById(action.getId());
        if (relation == null) {
            return;
        }
        final PlatformEntity entity = validate(relation.getPlatformId());
        String startFormContent = action.getStartFormContent();
        AuthCenterLogDetailVO detail = JSON.parseObject(startFormContent, AuthCenterLogDetailVO.class);
        detail.setApprovalUserId(relation.getUserId());
        detail.setApprovalDeptId(relation.getDeptId());
        CurrentUser createUser = permissionService.findCurrentUser(relation.getUserId(), relation.getDeptId());
        CurrentUser operateUser = permissionService.findCurrentUser(action.getOperateUserId(), action.getOperateUserDeptId());
        String op = Arrays.asList(PASSED, IN_PROCESS).contains(action.getApprovalStatus()) ? "同意" : "驳回";
        String title = String.format(
                "%s（%s）%s了%s（%s）提交的关于%s，“%s”的权限申请",
                operateUser.getRealName(),
                operateUser.getDept().getName(),
                op,
                createUser.getRealName(),
                createUser.getDept().getName(),
                entity.getName(),
                relation.getAuthName()

        );
        detail.setApprovalTitle(title);
        switch (action.getApprovalStatus()) {
            case WAITING:
                break;
            case IN_PROCESS:
                // 记录审批日志
                detail.setApprovalResult(PASSED.getName());
                operationLogService.publicCreateOperationLog(
                        detail.getPlatId(), "127.0.0.1", PERMISSION_CENTER, Operation.APPROVAL_RESPONSE, null,
                        JSON.toJSONString(detail), action.getOperateUserId(), action.getOperateUserDeptId()
                );
                break;
            case PASSED:
                // 记录审批日志
                detail.setApprovalResult(PASSED.getName());
                operationLogService.publicCreateOperationLog(
                        detail.getPlatId(), "127.0.0.1", PERMISSION_CENTER, Operation.APPROVAL_RESPONSE, null,
                        JSON.toJSONString(detail), action.getOperateUserId(), action.getOperateUserDeptId()
                );
                // 修改状态
                relation.setApprovalStatus(PASSED.getCode());
                platformAuthUserRelationMapper.updateById(relation);
                break;
            case REJECTED:
                // 记录审批日志
                detail.setApprovalResult(ApprovalStatusEnum.REJECTED.getName());
                operationLogService.publicCreateOperationLog(
                        detail.getPlatId(), "127.0.0.1", PERMISSION_CENTER, Operation.APPROVAL_RESPONSE,
                        null, JSON.toJSONString(detail), action.getOperateUserId(), action.getOperateUserDeptId()
                );
                // 修改状态
                relation.setApprovalStatus(ApprovalStatusEnum.REJECTED.getCode());
                platformAuthUserRelationMapper.updateById(relation);
                break;
            default:
                break;
        }
    }

    private ApprovalRequest buildRequest(LzAuthorityApprovalDTO dto, Long id) {
        final PlatformEntity entity = validate(dto.getPlatId());
        ApprovalRequest request = new ApprovalRequest();
        request.setApprovalConfigName(getConfigName());
        ApprovalActionVO action = new ApprovalActionVO();
        action.setService(PERMISSION_CENTER);
        action.setId(id);
        action.setTitle(entity.getName());
        action.setAction(Operation.ADD);
        request.setApprovalActionVO(action);
        request.setUser(AuthHelper.getCurrentUser().toUserDeptVO());
        return request;
    }

    private String getConfigName() {
        CurrentUser user = AuthHelper.getCurrentUser();
        AreaUtils.Level level = AreaUtils.level(user.getDept().getDistrictCode());
        if (AreaUtils.Level.COUNTY.equals(level)) {
            return "PERMISSION_CENTER_QX";
        }
        return "PERMISSION_CENTER_SJ";
    }

    private ListParamsRequest buildParams(Integer pageNum, Integer pageSize) {
        ListParamsRequest params = new ListParamsRequest();
        // 检索参数
        KeyValueTypeVO module = new KeyValueTypeVO("operateModule", PERMISSION_CENTER.getCode());
        ArrayList<KeyValueTypeVO> filterParams = new ArrayList<>();
        filterParams.add(module);
        params.setFilterParams(filterParams);
        // 关键字
        SearchParams searchParams = new SearchParams();
        searchParams.setSearchField("fullText");
        searchParams.setSearchValue("");
        params.setSearchParams(searchParams);
        // 排序
        SortParams sortParams = new SortParams();
        sortParams.setSortField("");
        sortParams.setSortDirection("descending");
        params.setSortParams(sortParams);
        // 分页参数
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(pageNum);
        pageParams.setPageSize(pageSize);
        params.setPageParams(pageParams);
        return params;
    }

    private void initVo(List<LzAuthApprovalListVO> vos) {
        // 时间字段调整
        for (LzAuthApprovalListVO vo : vos) {
            vo.getOverview().setCreateTime(vo.getOverview().getOriginCreateTime());
        }
        // 构造节点列表
        String ids = vos
                .stream()
                .map(LzAuthApprovalListVO::getOverview)
                .map(ApprovalListVO::getApprovalId)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        List<ApprovalDetailVO> approvalBatch = approvalService.getApprovalBatch(ids);
        VoParameterConstructor.of(vos)
                .singleValueMatcher(vs -> approvalBatch, (vo, detail) -> vo.getOverview().getApprovalId().equals(detail.getId()))
                .consumer((vo, detail) -> {
                    if (CollectionUtils.isNotEmpty(detail.getNodes())) {
                        detail.getNodes().remove(0);
                    }
                    List<LzAuthApprovalListVO.Node> nds = detail.getNodes().stream()
                            .map(nd -> {
                                LzAuthApprovalListVO.Node n = new LzAuthApprovalListVO.Node();
                                BeanUtils.copyProperties(nd, n);
                                return n;
                            })
                            .collect(Collectors.toList());
                    vo.setNodes(nds);
                })
                .build();
        // 添加列表详情
        vos.forEach(vo -> {
            LzAuthApprovalListDetail detail = new LzAuthApprovalListDetail();
            detail.setInitiator(vo.getOverview().getInitiator());
            detail.setInitiateDept(vo.getOverview().getInitiateDept());
            detail.setCreateTime(TimeUtil.localDateTimeToString(vo.getOverview().getOriginCreateTime(), null));
            String startFormContent = vo.getOverview().getStartFormContent();
            if (!StringUtil.isEmpty(startFormContent)) {
                AuthCenterLogDetailVO approvalForm = JSON.parseObject(startFormContent, AuthCenterLogDetailVO.class);
                detail.setPlatformName(approvalForm.getPlatformName());
                detail.setPurposes(approvalForm.getPurposes());
                detail.setTel(approvalForm.getTel());
                detail.setApprovalTitle(approvalForm.getApprovalTitle());
            }
            vo.setListDetail(detail);
            vo.getNodes().forEach(node -> {
                List<Long> did = node.getCommentDetails().stream()
                        .map(ApprovalDetailVO.ApproverCommentDetail::getApprover)
                        .filter(Objects::nonNull)
                        .map(UserCardVO::getDeptId)
                        .distinct()
                        .collect(Collectors.toList());
                List<Dept> deptByIds = CollectionUtils.isNotEmpty(did) ? deptService.getDeptByIds(did) : new ArrayList<>();
                node.setDeptNames(deptByIds.stream().map(Dept::getName).collect(Collectors.toList()));
            });
        });

    }

    private PlatformEntity validate(Long id) {
        PlatformEntity platform = platformMapper.selectById(id);
        if (Objects.isNull(platform)) {
            throw new TRSException("不存在的平台");
        }
        return platform;
    }
}
