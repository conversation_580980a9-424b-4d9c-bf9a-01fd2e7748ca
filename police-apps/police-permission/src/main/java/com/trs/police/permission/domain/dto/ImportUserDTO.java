package com.trs.police.permission.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-01-11 17:33
 */
@Data
@EqualsAndHashCode
public class ImportUserDTO {

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String name;

    /**
     * 身份证号
     */
    @ExcelProperty("身份证号")
    private String idNumber;

    /**
     *  密码
     */
    @ExcelProperty("密码")
    private String userPassword;

    /**
     * 性别
     */
    @ExcelProperty("性别")
    private String gender;

    /**
     * 部门code,多个以逗号进行分割
     */
    @ExcelProperty("所属组织")
    private String dept;

    /**
     * 所属角色（非必填）
     */
    @ExcelProperty("所属角色（非必填）")
    private String roleNames;

    /**
     * 数据权限ids（非必填）
     */
    @ExcelProperty("数据权限（非必填）")
    private String dataNames;

    /**
     * 警号
     */
    @ExcelProperty("警号（非必填）")
    private String policeCode;

    /**
     * 手机号
     */
    @ExcelProperty("手机号码（非必填）")
    private String tel;

    /**
     * 职务
     */
    @ExcelProperty("职务（非必填）")
    private String duty;

    /**
     * 岗位
     */
    @ExcelProperty("岗位（非必填）")
    private String postName;
}
