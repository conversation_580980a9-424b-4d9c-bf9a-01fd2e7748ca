package com.trs.police.spacetime.collision.repository;

import com.trs.db.sdk.repository.AbsBeanRepository;
import com.trs.police.common.core.entity.ThemeGjxxbEntity;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Repository;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/11/12 15:30
 * @since 1.0
 */
@Repository
@ConditionalOnProperty(value = "collision.gjxxb.db.type", havingValue = "ES")
public class ThemeGjxxbRepository extends AbsBeanRepository<ThemeGjxxbEntity> {
}
