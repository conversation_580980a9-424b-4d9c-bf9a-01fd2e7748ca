package com.trs.police.spacetime.collision.domain.request;

import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.utils.GeoUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.spacetime.collision.constant.enums.HitLogicEnum;
import com.trs.police.spacetime.collision.constant.enums.SourceTypeEnum;
import com.trs.police.spacetime.collision.domain.entity.CollisionAreaEntity;
import com.trs.police.spacetime.collision.domain.entity.CollisionEntity;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;

/**
 * 碰撞任务发起request
 *
 * <AUTHOR>
 */
@Data
@Slf4j
public class CollisionCreateRequest {

    /**
     * 任务id
     */
    private Long jobId;
    /**
     * 任务名称
     */
    private String jobName;
    /**
     * 为0时需要命中所有条件，否则就是对应次数
     */
    private Integer threshold;
    /**
     * 条件词组
     */
    private List<Condition> conditions;
    /**
     * 新输出结构
     */
    private Boolean newOutModel = true;
    /**
     * 感知源分组 （人车电网像）
     */
    private List<String> gzyfz;

    /**
     * 条件
     */
    @Data
    public static class Condition {
        /**
         * 条件类型
         */
        private String conditionType;
        /**
         * 区域名称
         */
        private String name;
        /**
         * wkt 字符串
         */
        private String area;
        /**
         * 数量为2的倍数，用于多个开始跟结束时间
         * 格式：yyyyMMddHHmmss
         */
        private List<String> timeRange;
        /**
         * 感知源类型列表，可以为空List
         */
        private List<String> gzylx;
        /**
         * 1:出现
         * 0:不出现
         */
        private Integer dataCountType;
    }

    /**
     * toRequest
     *
     * @param entity 碰撞
     * @param areaEntities 时空碰撞区域
     * @return CollisionCreateRequest
     */
    public static CollisionCreateRequest toRequest(CollisionEntity entity, List<CollisionAreaEntity> areaEntities) {
        CollisionCreateRequest request = new CollisionCreateRequest();
        request.setJobName(entity.getName());
        request.setThreshold(entity.getHitLogic().equals(HitLogicEnum.NUMBER.getCode())
            ? entity.getSpacetimeNumber() : 0);

        List<String> gzylx = entity.getSourceType().stream()
                .map(code -> {
                    SourceTypeEnum typeEnum = SourceTypeEnum.codeOf(code);
                    if (typeEnum == null || typeEnum.equals(SourceTypeEnum.ALL)) {
                        return null;
                    } else {
                        return typeEnum.getName();
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        request.setGzyfz(gzylx);
        List<Condition> conditions = areaEntities.stream().map(area -> {
            Condition c = new Condition();
            c.setConditionType("area");
            c.setName(area.getName());
            c.setArea(geometryVoToString(area.getGeometry()));
            c.setTimeRange(getTimeStrList(JsonUtil.parseArray(area.getTimeRanges(), TimeParams.class)));
            c.setDataCountType(area.getDataCountType());
            return c;
        }).collect(Collectors.toList());
        request.setConditions(conditions);
        return request;
    }

    private static String geometryVoToString(GeometryVO vo) {
        if (vo.getType().equals("line")) {
            return toLineRectangles(vo);
        } else {
            List<String> geometries = vo.toMoyeString();
            return geometries != null && geometries.size() == 1 ? geometries.get(0) : "";
        }
    }

    private static List<String> getTimeStrList(List<TimeParams> timeParamsList) {
        return timeParamsList.stream()
            .flatMap(timeParams -> Stream.of(
                TimeUtil.getSubscribeTime(timeParams.getBeginTime()),
                TimeUtil.getSubscribeTime(timeParams.getEndTime())))
            .collect(Collectors.toList());
    }

    /**
     * 特殊处理带宽度的线段，整理成矩形数组
     *
     * @param vo GeometryVO
     * @return wkt list
     */
    private static String toLineRectangles(GeometryVO vo) {
        try {
            List<Polygon> polygons = GeoUtils.lineVoToPolygonList(vo);
            return GeoUtils.toMultiPolygon(polygons).toText();
        } catch (Exception e) {
            log.error("线段转换矩形失败！", e);
        }
        return null;
    }
}
