package com.trs.police.spacetime.collision.domain.vo;

import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.spacetime.collision.constant.enums.CollisionStatusEnum;
import com.trs.police.spacetime.collision.constant.enums.HitLogicEnum;
import com.trs.police.spacetime.collision.domain.entity.CollisionAreaEntity;
import com.trs.police.spacetime.collision.domain.entity.CollisionEntity;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 碰撞创建vo
 *
 * <AUTHOR>
 */
@Data
public class CollisionCreateVO {

    /**
     * 碰撞名称
     */
    private String name;
    /**
     * 区域
     */
    private List<CollisionArea> areas;
    /**
     * 命中逻辑
     */
    private HitLogic hitLogic;
    /**
     * 感知源类型
     */
    private List<Long> sourceType;
    /**
     * 是否为草稿
     */
    private Boolean isDraft;

    /**
     * 通知电话
     */
    private String notifyTel;

    /**
     * 碰撞区域
     */
    @Data
    public static class CollisionArea {

        /**
         * 区域名称
         */
        private String areaName;
        /**
         * 区域wkt
         */
        private GeometryVO geometry;
        /**
         * 时间范围
         */
        private List<TimeParams> timeRanges;
        /**
         * 命中逻辑（1:出现 0:不出现）
         */
        private Integer hitLogic;

        /**
         * toEntity
         *
         * @param collisionId 碰撞id
         * @return entity
         */
        public CollisionAreaEntity toEntity(Long collisionId) {
            CollisionAreaEntity entity = new CollisionAreaEntity();
            entity.setCollisionId(collisionId);
            entity.setName(areaName);
            entity.setGeometry(geometry);
            entity.setTimeRanges(JsonUtil.toJsonString(timeRanges));
            entity.setDataCountType(hitLogic);
            return entity;
        }
    }

    /**
     * 命中逻辑
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HitLogic {

        /**
         * 命中逻辑类型
         */
        private HitLogicEnum type;
        /**
         * 区域数量
         */
        private Integer spacetimeNumber;
    }

    /**
     * toEntity
     *
     * @return entity
     */
    public CollisionEntity toEntity() {
        CollisionEntity entity = new CollisionEntity();
        entity.setStatus(Boolean.TRUE.equals(isDraft) ? CollisionStatusEnum.DRAFT : CollisionStatusEnum.RUNNING);
        entity.setName(name);
        entity.setHitLogic(hitLogic.type.getCode());
        entity.setSpacetimeNumber(hitLogic.spacetimeNumber);
        //若包括0（全部），则传空数组
        List<Long> sourceTypes = sourceType.contains(0L) ? List.of() : sourceType;
        entity.setSourceType(sourceTypes);
        // 通知电话
        entity.setNotifyTel(notifyTel);
        return entity;
    }
}
