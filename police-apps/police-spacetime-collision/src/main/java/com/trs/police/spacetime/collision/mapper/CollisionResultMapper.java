package com.trs.police.spacetime.collision.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.spacetime.collision.domain.entity.CollisionResultEntity;
import com.trs.police.spacetime.collision.domain.vo.CollisionDetailPersonVO;
import com.trs.police.spacetime.collision.domain.vo.CollisionResultDetail;
import com.trs.police.spacetime.collision.domain.vo.CollisionTrackVO;
import com.trs.police.spacetime.collision.domain.vo.GroupVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/13 10:34
 */
@Mapper
public interface CollisionResultMapper extends BaseMapper<CollisionResultEntity> {


    /**
     * 根据碰撞id获取碰撞结果
     *
     * @param jobId 碰撞任务id
     * @return {@link CollisionResultEntity}
     */
    @Select("select * from t_spacetime_collision_result where job_id = #{jobId}")
    List<CollisionResultEntity> getResultByJobId(@Param("jobId") Long jobId);

    /**
     * 根据碰撞id获取碰撞结果
     *
     * @param jobId       碰撞任务id
     * @param displayType 类型
     * @return {@link CollisionResultEntity}
     */
    @Select("select * from t_spacetime_collision_result where job_id = #{jobId} and display_type = #{displayType}")
    List<CollisionResultEntity> getResultByJobIdAndDisplayType(@Param("jobId") Long jobId, @Param("displayType") Integer displayType);

    /**
     * 碰撞详情-人员
     *
     * @param personId 人员id
     * @return 人员卡片
     */
    CollisionDetailPersonVO getCollisionDetailPerson(@Param("personId") Long personId);

    /**
     * 根据数据碰撞结果获取轨迹数据
     *
     * @param vo       数据碰撞结果
     * @param areaName 区域名称
     * @param jobId    任务id
     * @return {@link  CollisionTrackVO}
     */
    List<CollisionTrackVO> getTrackByCollisionResult(@Param("vo") CollisionResultDetail vo, @Param("areaName") String areaName, @Param("jobId") Long jobId);

    /**
     * 根据特征值查询人员id
     *
     * @param idNumber 身份证号
     * @return 结果
     */
    @Select("select id from t_profile_person where id_number = #{idNumber} limit 1")
    Long getPersonIdByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 根据特征值查询人员id
     *
     * @param tel 电话号码
     * @return 结果
     */
    @Select("select id from t_profile_person where json_contains(tel, #{tel}) limit 1")
    Long getPersonIdByPhoneNumber(@Param("tel") String tel);

    /**
     * 根据特征值查询人员id
     *
     * @param virtualNumber 虚拟身份
     * @return 结果
     */
    @Select("select p.id from t_profile_person p left join t_profile_virtual_identity v on json_contains(p.virtual_identity_ids, convert(v.id, char)) where v.virtual_number = #{virtualNumber} limit 1")
    Long getPersonIdByVirtualIdentity(@Param("virtualNumber") String virtualNumber);

    /**
     * 根据特征值查询人员id
     *
     * @param carNumber 车牌号
     * @return 结果
     */
    @Select("select p.id from t_profile_person p left join t_profile_vehicle v on json_contains(p.vehicle_ids, convert(v.id, char)) where v.car_number = #{carNumber} limit 1")
    Long getPersonIdByCarNumber(@Param("carNumber") String carNumber);

    /**
     * 根据人员id获取人员姓名
     *
     * @param id id
     * @return 姓名
     */
    @Select("select name from t_profile_person where id = #{id}")
    String getNameByPersonId(@Param("id") Long id);

    /**
     * 统计结果数量
     *
     * @param jobId 任务id
     * @return 结果
     */
    List<KeyValueVO> countResultByDisplayType(@Param("jobId") Long jobId);

    /**
     * 统计结果数量
     *
     * @param jobId       任务id
     * @param displayType 显示类型
     * @return 结果
     */
    List<KeyValueVO> countResultByObjectValue(@Param("jobId") Long jobId, @Param("displayType") Integer displayType);

    /**
     * 分页查询结果
     *
     * @param jobId        任务id
     * @param displayType  类型
     * @param searchParams 搜索参数
     * @param page         分页
     * @return 结果
     */
    Page<CollisionResultDetail> getResultPageByJobIdAndDisplayType(@Param("jobId") Long jobId,
                                                                   @Param("displayType") Integer displayType,
                                                                   @Param("searchParams") SearchParams searchParams,
                                                                   Page<CollisionResultDetail> page);

    /**
     * 分页查询，带人员信息
     *
     * @param jobId 任务id
     * @param displayType 类型
     * @param searchParams 搜索参数
     * @param page 分页
     * @return {@link Page }<{@link CollisionResultDetail }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-14 15:09:36
     */
    Page<CollisionResultDetail> getResultPageByJobIdAndDisplayTypeWithPersonInfo(@Param("jobId") Long jobId,
                                                                   @Param("displayType") Integer displayType,
                                                                   @Param("searchParams") SearchParams searchParams,
                                                                   Page<CollisionResultDetail> page);

    /**
     * 查询是否有出租车数据
     *
     * @param jobId 任务id
     * @return 结果
     */
    @Select("select count(id) from t_spacetime_collision_result where job_id = #{jobId} and display_type = 5 and gzylx = '出租车GPS'")
    Boolean countTaxiTrack(@Param("jobId") Long jobId);

    /**
     * 判断车辆是否是出租车
     *
     * @param jobId    任务id
     * @param objValue 车牌号
     * @return 结果
     */
    @Select("select count(1) from t_spacetime_collision_result where job_id = #{jobId} and object_value = #{objValue} and display_type = 5 and gzylx = '出租车GPS'")
    Boolean judgePlateNoIsTaxi(@Param("jobId") Long jobId, @Param("objValue") String objValue);

    /**
     * 查询当前任务的所有出租车信息
     *
     * @param jobId 任务id
     * @return {@link Map }<{@link String },{@link Long }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-10-31 10:57:49
     */
    List<GroupVo> getAllPlateTaxi(@Param("jobId") Long jobId);
}
