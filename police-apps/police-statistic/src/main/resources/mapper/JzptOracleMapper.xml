<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.statistic.mapper.download.JzptOracleMapper">
    <select id="selectXsaj" resultType="com.trs.police.statistic.domain.entity.Xsaj">
        select t.*,
        t.asjfsdd_dqjd as JD,
        t.asjfsdd_dqwd as WD
        from ${tableName} t
        <where>
            t.ajywztdm is not null
              and t.xt_zxbz = '0'
            <choose>
                <when test="dateTime == null">
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date('20200101', 'yyyymmdd')
                </when>
                <otherwise>
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date(#{dateTime},'yyyy-mm-dd hh24:mi:ss')
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectZaaj" resultType="com.trs.police.statistic.domain.entity.Zaaj">
        select t.*,
        t.xxzjbh       as zj,
        t.asjfsdd_dqjd as JD,
        t.asjfsdd_dqwd as WD
        from ${tableName} t
        <where>
            t.xt_zxbz = '0'
              and t.ajlbdm is not null
            <choose>
                <when test="dateTime == null">
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date('20200101', 'yyyymmdd')
                </when>
                <otherwise>
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date(#{dateTime},'yyyy-mm-dd hh24:mi:ss')
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectJqxx" resultType="com.trs.police.statistic.domain.entity.Jqxx">
        select t.jqbh                                                                                  as JQBH,
               t.jjdbh                                                                                 as JJDBH,
               t.cjdw_gajgjgdm                                                                         AS CJDW_GAJGJGDM,
               t.bjsj_rqsj                                                                             as bjsj,
               t.jqlbdm                                                                                AS JQLBDM,
               t.cjdw_gajgmc                                                                           AS cjdw_gajgmc,
               t.jyaq                                                                                  as jyaq,
               t.sfbzdz_dqjd                                                                           as JD,
               t.sfbzdz_dqwd                                                                           as WD,
               t.sfbzdz_dzmc                                                                           as FSDDMC,
               t.jjdw_gajgjgdm                                                                         AS jjdwdm,
               t.jjdw_gajgmc                                                                           AS jjdwmc
        from ${tableName} t
        <where>
            t.cjdw_gajgjgdm is not null
              and t.jqztdm not in ('04', '05')
            <choose>
                <when test="dateTime == null">
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date('20200101', 'yyyymmdd')
                </when>
                <otherwise>
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date(#{dateTime},'yyyy-mm-dd hh24:mi:ss')
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectXsajXyr" resultType="com.trs.police.statistic.domain.entity.Xyr">
        select t.*,
               t.fzxyr_xm        AS XM,
               t.fzxyr_cyzj_zjhm as ZJHM,
               t.asjxgrybh       AS XYRBH,
               t.xt_lrsj         AS LRSJ,
               t.fzxyr_rsqzcsdm  as rsqzcs,
               t.XXZJBH          as zj
        from ${tableName} t
        <where>
            <choose>
                <when test="dateTime == null">
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date('20200101', 'yyyymmdd')
                </when>
                <otherwise>
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date(#{dateTime},'yyyy-mm-dd hh24:mi:ss')
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectZaajXyr" resultType="com.trs.police.statistic.domain.entity.Xyr">
        select t.*,
               t.cyzj_zjhm as ZJHM,
               t.asjxgrybh AS XYRBH,
               t.xt_lrsj   AS LRSJ,
               t.XXZJBH    as zj
        from ${tableName} t
        <where>
            <choose>
                <when test="dateTime == null">
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date('20200101', 'yyyymmdd')
                </when>
                <otherwise>
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date(#{dateTime},'yyyy-mm-dd hh24:mi:ss')
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectXsajAjxgry" resultType="com.trs.police.statistic.domain.entity.Ajxgry">
        select t.*,
        t.cyzj_zjhm       as ZJHM,
        t.asjxgrybh       AS xgrybh,
        t.xt_lrsj         AS LRSJ,
        t.XXZJBH          as zj,
        t.ASJXGRYJSDM     as role
        from ${tableName} t
        <where>
            <choose>
                <when test="dateTime == null">
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date('20200101', 'yyyymmdd')
                </when>
                <otherwise>
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date(#{dateTime},'yyyy-mm-dd hh24:mi:ss')
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectZaajAjxgry" resultType="com.trs.police.statistic.domain.entity.Ajxgry">
        select  t.*,
        t.cyzj_zjhm       as ZJHM,
        t.asjxgrybh       AS xgrybh,
        t.xt_lrsj         AS LRSJ,
        t.XXZJBH          as zj
        from ${tableName} t
        <where>
            <choose>
                <when test="dateTime == null">
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date('20200101', 'yyyymmdd')
                </when>
                <otherwise>
                    and to_date(t.xt_zhxgsj,'yyyy-mm-dd hh24:mi:ss') > to_date(#{dateTime},'yyyy-mm-dd hh24:mi:ss')
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>