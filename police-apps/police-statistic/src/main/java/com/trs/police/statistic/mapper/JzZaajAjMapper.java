package com.trs.police.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.statistic.domain.entity.Zaaj;
import com.trs.police.statistic.domain.vo.page.PageListVO;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * jz_xzaj_aj 表数据库查询语句
 *
 * <AUTHOR>
 */
@DS("mysql")
@Mapper
public interface JzZaajAjMapper extends AbstractMapper {

    /**
     * 计数查询
     *
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return 案件计数
     */

    @Override
    Integer selectCount(@Param("subCaseTypes") List<String> subCaseTypes, @Param("deptCode") String deptCode,
        @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询
     *
     * @param page         分页参数
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return {@link PageListVO}
     */
    @Override
    Page<PageListVO> selectPage(Page<PageListVO> page,
        @Param("subCaseTypes") List<String> subCaseTypes,
        @Param("deptCode") String deptCode,
        @Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询列表
     *
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划前缀
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return {@link PageListVO}
     */
    @Override
    List<PageListVO> selectList(
        @Param("subCaseTypes") List<String> subCaseTypes,
        @Param("deptCode") String deptCode,
        @Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);


    /**
     * 插入数据
     *
     * @param zaaj 案事件编号
     */
    void insert(@Param("zaaj") Zaaj zaaj);
}
