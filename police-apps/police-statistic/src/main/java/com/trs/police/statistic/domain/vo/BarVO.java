package com.trs.police.statistic.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 柱VO
 *
 * <AUTHOR>
 * @date 2023/05/06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BarVO extends AbstractDataVO {

    private static final long serialVersionUID = 8123171558679598161L;

    public BarVO(String code, String name, AbstractDataVO dataVO) {
        super(dataVO);
        this.code = code;
        this.name = name;
    }

    /**
     * 代码
     */
    private String code;

    /**
     * 名称
     */
    private String name;
}
