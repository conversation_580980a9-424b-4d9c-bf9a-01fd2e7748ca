package com.trs.police.statistic.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chenhaiyang.plugin.mybatis.sensitive.annotation.EncryptField;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToFileInfoHandler;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import com.trs.police.common.core.handler.typehandler.JsonToStringListHandler;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 人员档案表(ProfilePerson)数据访问类
 *
 * <AUTHOR>
 * @since 2022-11-15 11:38:14
 */
//@SensitiveEncryptEnabled
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_profile_person", autoResultMap = true)
public class Person extends AbstractBaseEntity {

    private static final long serialVersionUID = 113659934363509167L;

    /**
     * 证件号码
     */
    @EncryptField
    private String idNumber;

    /**
     * 证件类型
     */
    private Integer idType;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 曾用名
     */
    private String formerName;

    /**
     * 绰号
     */
    private String nickName;

    /**
     * 民族
     */
    private Integer nation;

    /**
     * 政治面貌
     */
    private Integer politicalStatus;

    /**
     * 宗教信仰
     */
    private String religiousBelief;

    /**
     * 婚姻状况
     */
    private Integer martialStatus;

    /**
     * 现职业
     */
    private String currentJob;

    /**
     * 目前所在地
     */
    private Integer currentPosition;

    /**
     * 户籍地区域代码
     */
    private String registeredResidence;

    /**
     * 户籍地详细地址
     */
    private String registeredResidenceDetail;

    /**
     * 现住址区域代码
     */
    private String currentResidence;

    /**
     * 现住址详细地址
     */
    private String currentResidenceDetail;

    /**
     * 主要诉求
     */
    private String mainDemand;

    /**
     * 工作措施
     */
    private String workMeasures;

    /**
     * 上访情况
     */
    private String petitionInfo;

    /**
     * 被打击处理情况
     */
    private String punishInfo;

    /**
     * 人员标签
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> personLabel;

    /**
     * 照片
     */
    @TableField(typeHandler = JsonToFileInfoHandler.class)
    private List<FileInfoVO> photo;

    /**
     * 关联虚拟身份id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> virtualIdentityIds;

    /**
     * 关联车辆id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> vehicleIds;

    /**
     * 电话号码
     */
    @EncryptField
    @TableField(typeHandler = JsonToStringListHandler.class)
    private List<String> tel;

    /**
     * 家庭关系id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> familyRelationIds;

    /**
     * 社会关系id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> socialRelationIds;

    /**
     * 布控状态
     */
    private Integer monitorStatus;

    /**
     * 管控状态
     */
    private Integer controlStatus;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 完整度统计
     */
    private Double completeRate;

    /**
     * 人员档案类型，普通档案为0(默认值)，FK人员档案：1
     */
    private Integer personType;

    /**
     *  工作目标
     */
    private Integer workTarget;

    /**
     *  人员级别
     */
    private Integer controlLevel;

    /**
     *  人员风险评分
     */
    private Double riskScore;

    /**
     *  累计分
     */
    private Double rawScore;

    /**
     * 风险等级
     */
    private String riskLevel;

    public Person(Xyr xyr, Map<String, Long> roleAndLabelIdMap){
        buildPersonLabel(roleAndLabelIdMap, "00");
        this.idNumber = xyr.getZjhm();
        this.name = xyr.getXm();
        if(StringUtils.isNotEmpty(xyr.getFzxyrXbdm())){
            this.gender = Try.of(()->Integer.valueOf(xyr.getFzxyrXbdm())).getOrNull();
        }
        this.formerName = xyr.getFzxyrCym();
        this.nickName = xyr.getFzxyrBmch();
        if(StringUtils.isNotEmpty(xyr.getFzxyrMzdm())){
            this.nation = Try.of(()->Integer.valueOf(xyr.getFzxyrMzdm())).getOrNull();
        }
        if(StringUtils.isNotEmpty(xyr.getFzxyrZzmmdm())){
            this.politicalStatus = Try.of(()->Integer.valueOf(xyr.getFzxyrZzmmdm())).getOrNull();
        }
        this.religiousBelief = xyr.getFzxyrZjxydm();
        if(StringUtils.isNotEmpty(xyr.getFzxyrJyzkdm())){
            this.martialStatus = Try.of(()->Integer.valueOf(xyr.getFzxyrJyzkdm())).getOrNull();
        }
        this.currentJob = xyr.getFzxyrZylbdm();
        if(StringUtils.isNotEmpty(xyr.getFzxyrXzzXzqhdm())){
            this.currentPosition = Try.of(()->Integer.valueOf(xyr.getFzxyrXzzXzqhdm())).getOrNull();
        }
        if(StringUtils.isNotEmpty(xyr.getFzxyrHjdzXzqhdm())){
            this.registeredResidence = xyr.getFzxyrHjdzXzqhdm();
        }
        this.registeredResidenceDetail = xyr.getFzxyrHjdzDzmc();
        this.currentResidence = xyr.getFzxyrXzzXzqhdm();
        this.currentResidenceDetail = xyr.getFzxyrXzzDzmc();
        if(StringUtils.isNotEmpty(xyr.getFzxyrLxdh())){
            this.tel = Arrays.asList(xyr.getFzxyrLxdh().split(","));
        }
        this.deleted = false;
        this.idType = IdentifierTypeEnum.ID_NUMBER.getCode();
    }

    public Person(Ajxgry xgry, Map<String, Long> roleAndLabelIdMap){
        buildPersonLabel(roleAndLabelIdMap, xgry.getRole());
        this.idNumber = xgry.getZjhm();
        this.name = xgry.getXm();
        if(StringUtils.isNotEmpty(xgry.getXbdm())){
            this.gender = Try.of(()->Integer.valueOf(xgry.getXbdm())).getOrNull();
        }
        this.formerName = xgry.getCym();
        this.nickName = xgry.getBmch();
        if(StringUtils.isNotEmpty(xgry.getMzdm())){
            if(xgry.getMzdm().contains(",")){
                this.nation = Try.of(()->Integer.valueOf(xgry.getMzdm().split(",")[0])).getOrNull();
            }else {
                this.nation = Try.of(()->Integer.valueOf(xgry.getMzdm())).getOrNull();
            }
        }
        if(StringUtils.isNotEmpty(xgry.getHyzkdm())){
            this.martialStatus = Try.of(()->Integer.valueOf(xgry.getHyzkdm())).getOrNull();
        }
        this.currentJob = xgry.getZylbdm();
        if(StringUtils.isNotEmpty(xgry.getXzzXzqhdm())){
            this.currentPosition = Try.of(()->Integer.valueOf(xgry.getXzzXzqhdm())).getOrNull();
        }
        if(StringUtils.isNotEmpty(xgry.getHjdzXzqhdm())){
            this.registeredResidence = xgry.getHjdzXzqhdm();
        }
        this.registeredResidenceDetail = xgry.getHjdzDzmc();
        this.currentResidence = xgry.getXzzXzqhdm();
        this.currentResidenceDetail = xgry.getXzzDzmc();
        if(StringUtils.isNotEmpty(xgry.getBarLxdh())){
            this.tel = Arrays.asList(xgry.getBarLxdh().split(","));
        }
        this.deleted = false;
        this.idType = IdentifierTypeEnum.ID_NUMBER.getCode();
    }

    private void buildPersonLabel(Map<String, Long> roleAndLabelIdMap, String role){
        Long labelId = roleAndLabelIdMap.get(role);
        if(labelId!=null){
            personLabel= Arrays.asList(labelId);
        }
    }
}