package com.trs.police.modelstatistics.service;

import com.trs.police.modelstatistics.domain.dto.UserModelDTO;
import com.trs.police.modelstatistics.domain.dto.UserModelQueryDTO;
import com.trs.police.modelstatistics.domain.dto.UserModelReviewDTO;
import com.trs.police.modelstatistics.domain.vo.UserModelVO;
import com.trs.web.builder.base.RestfulResultsV2;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户模型Service
 *
 * @author: AI
 * @create: 2024-07-31
 */
public interface UserModelService {

    /**
     * 添加模型
     *
     * @param dto 模型信息
     * @return 添加结果
     */
    RestfulResultsV2<String> addUserModel(UserModelDTO dto);

    /**
     * 修改模型
     *
     * @param dto 模型信息
     * @return 修改结果
     */
    RestfulResultsV2<String> updateUserModel(UserModelDTO dto);

    /**
     * 删除模型
     *
     * @param ids 模型ID
     * @return 删除结果
     */
    RestfulResultsV2<Boolean> deleteUserModel(List<Long> ids);

    /**
     * 分页查询模型
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    RestfulResultsV2<UserModelVO> queryUserModelPage(UserModelQueryDTO dto);

    /**
     * 查询模型详情
     *
     * @param id 模型ID
     * @return 模型详情
     */
    RestfulResultsV2<UserModelVO> getUserModelDetail(Long id);

    /**
     * 审核模型
     *
     * @param dto 审核信息
     * @return 审核结果
     */
    RestfulResultsV2<Boolean> reviewUserModel(UserModelReviewDTO dto);

    /**
     * 批量导出
     *
     * @param dto 查询条件
     * @param response 响应
     */
    void export(UserModelQueryDTO dto, HttpServletResponse response);
}
