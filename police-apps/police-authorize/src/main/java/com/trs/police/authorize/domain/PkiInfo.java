package com.trs.police.authorize.domain;

import java.util.Date;
import lombok.Data;

/**
 * pki信息
 *
 * <AUTHOR>
 */
@Data
public class PkiInfo {

    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号码
     */
    private String idcard;
    /**
     * 组织机构代码
     */
    private String deptCode;
    /**
     * 有效期开始时间
     */
    private Date notBefore;
    /**
     * 有效期结束时间
     */
    private Date notAfter;
    /**
     * 是否有效
     */
    private Boolean onService;

}
