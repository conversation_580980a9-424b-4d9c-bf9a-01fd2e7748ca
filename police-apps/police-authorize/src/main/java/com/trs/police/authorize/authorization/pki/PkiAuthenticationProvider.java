package com.trs.police.authorize.authorization.pki;

import com.trs.police.authorize.service.UserService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * pki认证
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class PkiAuthenticationProvider implements AuthenticationProvider {

    private UserService userDetailsService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {

        PkiAuthenticationToken token = (PkiAuthenticationToken) authentication;

        UserDetails user = userDetailsService.loadUserByIdentity((String) token.getPrincipal());
        if (user == null) {
            throw new InternalAuthenticationServiceException("用户未注册");
        }

        PkiAuthenticationToken authenticationToken = new PkiAuthenticationToken(user, user.getAuthorities(),
            token.getDefaultUnitId());

        authenticationToken.setDetails(token.getDetails());

        return authenticationToken;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return PkiAuthenticationToken.class.isAssignableFrom(authentication);
    }

}
