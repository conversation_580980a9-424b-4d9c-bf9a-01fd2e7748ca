package com.trs.police.authorize.authorization.base64;

import com.trs.police.authorize.authorization.LoginSuccessAndFailureHandler;
import com.trs.police.authorize.service.DeptService;
import com.trs.police.authorize.service.UserService;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: luoxu
 * @date: 2019/2/1 16:35
 */
@Component
public class Base64AuthenticationSecurityConfig
    extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    @Resource
    private LoginSuccessAndFailureHandler successAndFailureHandler;

    @Resource
    private UserService userService;
    @Resource
    private DeptService deptService;

    @Override
    public void configure(HttpSecurity http) {

        Base64AuthenticationFilter filter = new Base64AuthenticationFilter();

        filter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));

        filter.setAuthenticationSuccessHandler(successAndFailureHandler);
        filter.setAuthenticationFailureHandler(successAndFailureHandler);


        Base64AuthenticationProvider base64AuthenticationProvider = new Base64AuthenticationProvider();
        base64AuthenticationProvider.setUserService(userService);
        base64AuthenticationProvider.setDeptService(deptService);
        http.authenticationProvider(base64AuthenticationProvider)
            .addFilterAfter(filter, UsernamePasswordAuthenticationFilter.class);

    }

}
