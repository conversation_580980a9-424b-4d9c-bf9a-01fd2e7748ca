package com.trs.police.common.core.entity.message;

import com.trs.police.common.core.vo.message.Channel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.concurrent.ConcurrentHashMap;

/**
 * UserSession 用于管理用户的所有链接
 *
 * <AUTHOR>
 * @date 2022/03/30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserSession extends ConcurrentHashMap<Channel, ChannelSession> {

    private static final long serialVersionUID = -8903835665655630345L;


}
