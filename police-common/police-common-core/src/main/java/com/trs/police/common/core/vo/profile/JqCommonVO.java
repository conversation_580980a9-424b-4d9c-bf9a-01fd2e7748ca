package com.trs.police.common.core.vo.profile;

import com.trs.police.common.core.vo.CodeNameVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 警情vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class JqCommonVO implements Serializable {

    private Long id;

    /**
     * 接警员姓名
     */
    private String jjyxm;

    /**
     * 接警单编号
     */
    private String jjdbh;

    /**
     * 警情状态
     */
    private CodeNameVO status;

    /**
     * 警情等级
     */
    private CodeNameVO level;
    /**
     * 警情类别
     */
    private List<String> category;

    /**
     * 警情标签
     */
    private List<String> labels;

    /**
     * 智能打标
     */
    private Object intelligentMarking;
    /**
     * 相关风险
     */
    private Long relatedRisk = 0L;
    /**
     * 风险分值
     */
    private String score;

    /**
     * 联系电话
     */
    private String tel;
    /**
     * 警情来源
     */
    private String source;

    /**
     * 接警类型
     */
    private String jjlx;
    /**
     * 来话类型
     */
    private String lhlx;
    /**
     * 报警内容
     */
    private String content;

    /**
     * 所属区县-行政区划
     */
    private String districtName;

    /**
     * 管辖单位
     */
    private String handleDept;

    /**
     * 报警时间
     */
    private LocalDateTime alarmTime;

    /**
     * 时间
     */
    private String time;

    /**
     * 处警时间
     */
    private LocalDateTime handleTime;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 经度
     */
    private Double jd;

    /**
     * 纬度
     */
    private Double wd;

    /**
     * 报警电话
     */
    private String bjdh;

    /**
     * 警情地址
     */
    private String jqdz;

    /**
     * 报警人名称
     */
    private String bjrmc;

    /**
     * 接警单位名称
     */
    private String jjdwmc;

    /**
     * 警情类别名称
     */
    private String jqlbmc;

    /**
     * 警情类型代码
     */
    private String jqlxdm;

    /**
     * 警情类型名称
     */
    private String jqlxmc;

    /**
     * 处置单位 管辖单位 名称
     */
    private String gxdwmc;

    /**
     * 行政区划代码
     */
    private String xzqhdm;

    /**
     * 管辖单位代码
     */
    private String gxdwdm;

    /**
     * 联系民警姓名
     */
    private String lxmjxm;

    /**
     * 报警人电话
     */
    private String bjrdh;

    /**
     * 报警人身份证
     */
    private String bjrsfz;

    /**
     * 报警人性别
     */
    private String bjrxb;

    /**
     * 警情等级代码
     */
    private String jqdjdm;
}
