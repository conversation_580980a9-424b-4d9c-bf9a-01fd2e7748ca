package com.trs.police.common.core.vo.profile;

import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/9 16:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonVO {

    private Long id;

    private String name;

    private String certificateNumber;

    private Integer certificateType;

    private String registeredResidence;

    private List<FileInfoVO> imgs;

    private List<Long> targetType;

    private List<String> carNumber;

    private List<PersonVirtualIdentityVO> virtualIdentity;

    private CodeNameVO activityLevel;

    private Long targetId;
    /**
     * 布控数量
     */
    private Long monitorCount;
    /**
     * 常控数量
     */
    private Long regularCount;

    /**
     * 发起人员布控回显是否可以编辑
     */
    private Boolean canEdit;

    private List<String> tel;

    private List<Long> vehicleIds;

    private List<Long> virtualIdentityIds;

    private String approvalDetail;

    private List<RelatedJqVO> relatedJqs;

    /**
     * 责任派出所
     */
    private String dutyPoliceStation;

    /**
     * 性别
     */
    private Integer xb;

    /**
     * 管控级别
     */
    private Integer controlLevel;

    /**
     * 标签
     */
    private List<Long> personLabel;

    /**
     * 转换为人员卡片
     *
     * @param labelNameMap 标签名称
     * @return 人员卡片
     */
    public PersonCardVO toCardVo(Map<String, String> labelNameMap) {
        PersonCardVO card = new PersonCardVO();
        card.setId(this.getId());
        card.setName(this.getName());
        card.setIdNumber(this.getCertificateNumber());
        card.setImgs(this.getImgs());

        List<String> personLabelName = this.getTargetType()
                .stream()
                .map(labelId -> labelNameMap.get(String.valueOf(labelId)))
                .collect(Collectors.toList());
//        CommonMapper commonMapper = BeanUtil.getBean(CommonMapper.class);
//        List<String> personLabelName = commonMapper.getPersonLabelName();
        card.setPersonLabel(personLabelName);

        card.setCarNumber(this.getCarNumber());
        card.setVirtualIdentity(this.getVirtualIdentity());
        card.setActiveLevel(this.getActivityLevel());
        card.setMonitorCount(this.getMonitorCount());
        card.setRegularCount(this.getRegularCount());
        card.setTel(this.getTel());

        return card;
    }
}
