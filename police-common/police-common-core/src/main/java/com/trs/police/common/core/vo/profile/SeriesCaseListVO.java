package com.trs.police.common.core.vo.profile;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.json.serializer.SimpleTimeSerializer;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.trs.police.common.core.vo.CaseTagVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/2 10:56
 */
@Data
public class SeriesCaseListVO {
    private Long id;
    /**
     * 案事件编号
     */
    private String asjbh;
    /**
     * 案件名稱
     */
    private String caseName;
    /**
     * 案件等级
     */
    private String caseLevel;
    /**
     * 案件状态
     */
    private String caseStatus;
    /**
     * 案件状态明文
     */
    private String caseStatusName;
    /**
     * 案件大类代码
     */
    private String ajlbdm;
    /**
     * 案件大类
     */
    private String caseTopType;
    /**
     * 案件标签
     */
    private List<CaseTagVO> caseTag;
    /**
     * 案件细类代码
     */
    private String ajxldm;
    /**
     * 案件细类
     */
    private String caseFineType;
    /**
     * 主办人
     */
    private String sponsor;
    /**
     * 主办单位
     */
    private String organizer;
    /**
     * 发生时间
     */
    @JsonSerialize(using = SimpleTimeSerializer.class,nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime occurTime;

    /**
     * 作战id
     */
    private Long compositeId;

    /**
     * 受案时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date acceptTime;

    /**
     * 简要案情
     */
    private String caseAbstract;

    /**
     * 主办人证件号码
     */
    private String zbrGmsfhm;

    /**
     * 主办单位关联机构代码
     */
    private String badwGajgjgdm;

    /**
     * 发生地点代码
     */
    private String asjfsddXzqhdm;
}
