package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.vo.DistrictVO;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 行政区划实体类
 *
 * <AUTHOR>
 * @date 2022/09/08
 */
@Data
@TableName("t_district")
public class District implements Serializable {

    private static final long serialVersionUID = -6841426245352943159L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 码值
     */
    @TableField(value = "code")
    private String code;
    /**
     * 显示名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 简称
     */
    @TableField("short_name")
    private String shortName;

    /**
     * 主要名称
     */
    @TableField("main_name")
    private String mainName;

    /**
     * 上级代码(同一类型码表本身码值的上下关系)
     */
    @TableField(value = "p_code")
    private String pCode;
    /**
     * 级别
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 中心点
     */
    private String center;

    /**
     * 轮廓
     */
    private String contour;

    /**
     * 检索路径
     */
    private String path;

    @TableField(exist = false)
    private List<District> children = new ArrayList<>();

    @TableField(exist = false)
    private Boolean isLeaf;

    /**
     * dto
     *
     * @return {@link DistrictDto}
     */
    public DistrictDto toDto() {
        return new DistrictDto(
                this.getCode(),
                this.getPCode(),
                this.getName(),
                this.getShortName(),
                this.getIsLeaf(),
                this.getMainName(),
                this.getCenter(),
                this.getChildren()
                        .stream()
                        .map(District::toDto)
                        .collect(Collectors.toList()));
    }

    /**
     * getIsLeaf
     *
     * @return {@link Boolean}
     */
    public Boolean getIsLeaf() {
        return CollectionUtils.isEmpty(children);
    }

    /**
     * 转换成VO对象
     *
     * @return 结果
     */
    public DistrictVO toVo() {
        DistrictVO vo = new DistrictVO();
        vo.setId(this.getId());
        vo.setCode(this.getCode());
        vo.setName(this.getName());
        vo.setPCode(this.getPCode());
        vo.setLevel(this.getLevel());
        vo.setCenter(this.getCenter());
        vo.setShortName(this.getShortName());
        vo.setMainName(this.getMainName());
        return vo;
    }

}
