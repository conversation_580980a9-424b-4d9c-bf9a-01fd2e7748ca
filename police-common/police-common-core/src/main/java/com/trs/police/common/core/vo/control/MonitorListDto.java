package com.trs.police.common.core.vo.control;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * dto
 *
 * <AUTHOR>
 * @since 2022/12/2 11:17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonitorListDto {
    private Long monitorId;
    /**
     * 布控级别
     */
    private String monitorLevel;
    /**
     * 布控标题
     */
    private String monitorTitle;
    /**
     * 预警模型
     */
    private List<String> warningModel;
    /**
     * 最近预警时间
     */
    private String lastWarningTime;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 布控状态
     */
    private String monitorStatus;

    /**
     * 预警信息
     */
    private Integer warningInfo;

    /**
     * 过期时间
     */
    private Long deadLine;

    /**
     * 创建人
     */
    private String initiator;
    /**
     * 部门
     */
    private String monitorDept;
    /**
     * 布控类型
     */
    private String monitorType;
    /**
     * 过期时间
     */
    @JsonIgnore
    private LocalDateTime expirationDate;
}
