package com.trs.police.common.core.process;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 数据处理中心
 *
 * @param <T> T
 *
 * <AUTHOR>
 * @date on 2019/7/8
 */
public class DataProcessCenter<T> {

    private List<IProcessBehavior> processBehaviorList = new ArrayList<>();

    /**
     * 构建一个流程中心
     *
     * @return DataProcessCenter
     */
    public static DataProcessCenter build() {
        return new DataProcessCenter();
    }

    /**
     * 设置执行流程，并按照顺序执行
     *
     * @param process 操作行为
     * @return DataProcessCenter
     */
    public DataProcessCenter addProcess(final IProcessBehavior... process) {
        //1.验空
        if (process != null && process.length > 0) {
            processBehaviorList.addAll(Arrays.asList(process));
        }
        return this;
    }

    /**
     * 设置执行流程，并按照顺序执行
     *
     * @param processList 操作行为
     * @return DataProcessCenter
     */
    public DataProcessCenter addProcess(List<IProcessBehavior> processList) {
        //1.验空
        if (!CollectionUtils.isEmpty(processList)) {
            //2.按照顺序添加process
            processBehaviorList.addAll(processList);
        }
        return this;
    }

    /**
     * 按照顺序执行流程，开启事务，发生异常进行事务回滚
     *
     * @param start start
     * @return 结果
     */
    public Object start(List<T> start) throws Throwable {
        if (!CollectionUtils.isEmpty(processBehaviorList)) {
            return processBehaviorList.stream()
                    .reduce(IProcessBehavior::andThen)
                    .orElseGet(IProcessBehavior::identity)
                    .apply(start);
        }
        return null;
    }

}
