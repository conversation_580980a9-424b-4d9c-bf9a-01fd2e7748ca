package com.trs.police.common.core.dto;

import com.trs.common.pojo.BaseDTO;
import com.trs.common.pojo.OrderDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * baseListDTO
 *
 * <AUTHOR>
 * @date 2024/4/23
 */
@Data
public abstract class BaseListDTO extends BaseDTO {
    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 搜索字段
     */
    private String searchField;

    /**
     * 搜索值
     */
    private String searchValue;

    /**
     * 排序字段
     */
    private String orderField;

    /**
     * 排序类型
     */
    private String orderType;

    /**
     * 排序列表
     */
    private List<OrderDTO> orderList;

    public BaseListDTO() {
        this.pageNum = 1;
        this.pageSize = 10;
        this.orderList = new ArrayList<>(0);
    }
}
