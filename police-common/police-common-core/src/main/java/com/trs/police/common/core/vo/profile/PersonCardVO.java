package com.trs.police.common.core.vo.profile;

import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import java.util.List;
import lombok.Data;

/**
 * 人员卡片VO
 *
 * <AUTHOR>
 * @date 2023/2/1 19:01
 */
@Data
public class PersonCardVO {

    /**
     * id
     */
    private Long id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号
     */
    private String idNumber;
    /**
     * 标签
     */
    private List<String> personLabel;
    /**
     * 车牌号
     */
    private List<String> carNumber;
    /**
     * 电话号码
     */
    private List<String> tel;
    /**
     * 虚拟身份
     */
    private List<PersonVirtualIdentityVO> virtualIdentity;
    /**
     * 常控级别
     */
    private CodeNameVO regularLevel;
    /**
     * 常控状态
     */
    private CodeNameVO regularStatus;
    /**
     * 临控数量
     */
    private Long monitorCount;
    /**
     * 常控数量
     */
    private Long regularCount;
    /**
     * 照片
     */
    private List<FileInfoVO> imgs;
    /**
     * 责任派出所
     */
    private String dept;
    /**
     * 备注
     */
    private String remark;
    /**
     * 活跃程度
     */
    private CodeNameVO activeLevel;
    /**
     * 风险人员类型
     */
    private CodeNameVO riskPersonType;
    /**
     * 是否被当前用户常控
     */
    private Boolean regularByCurrent;
    /**
     * 布控人
     */
    private String monitorUserName;
    /**
     * 布控单位
     */
    private String monitorDeptName;


    private String personLabelStr;

    private String virtualIdentityStr;

    private String carNumberStr;

    private List<String> deptCode;

    private List<Long> policeKind;

    private List<String> policeKindName;

    /**
     * 责任pcs
     */
    private String dutyPoliceStation;

    /**
     * 性别 1 男 2 女
     */
    private Integer xb;
}
