package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.vo.profile.LabelVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签实体类
 *
 * <AUTHOR>
 * @since 2021/7/27 14:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_profile_label")
public class Label extends AbstractBaseEntity {

    private static final long serialVersionUID = -5752123125490863925L;
    /**
     * 标签名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建方式 0-手动创建，1-大数据平台推送
     */
    private String createType;
    /**
     * 模块 (person, group, event, clue)
     */
    private String module;
    /**
     * 上级标签id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED, jdbcType = JdbcType.BIGINT)
    private Long pid;
    /**
     * 是否启用状态
     */
    private String status;
    /**
     * 显示顺序
     */
    private Integer showOrder;

    /**
     * 儿子
     */
    @TableField(exist = false)
    private List<Label> children = new ArrayList<>();

    /**
     * 检索参数
     */
    private String path;

    /**
     * jz code
     */
    private String code;

    /**
     * 转换vo
     *
     * @return vo
     */
    public LabelVO toVO() {
        LabelVO vo = new LabelVO();
        vo.setId(this.getId());
        vo.setName(name);
        vo.setPid(pid);
        vo.setCode(code);
        vo.setShowOrder(showOrder);
        vo.setChildren(this.children.stream().map(Label::toVO).collect(Collectors.toList()));
        return vo;
    }
}

