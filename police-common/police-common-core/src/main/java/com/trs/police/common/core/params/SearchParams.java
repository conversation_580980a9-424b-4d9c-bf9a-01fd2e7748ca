package com.trs.police.common.core.params;

import com.trs.police.common.core.utils.StringUtil;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/2/17 16:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchParams implements Serializable {

    private static final long serialVersionUID = 8236486566499352274L;

    /**
     * 获取搜索值
     *
     * @return String string字符
     */
    public String getSearchValue() {
        if (StringUtils.isNotBlank(searchValue)) {
            return StringUtil.removeSpecialCharacters(searchValue.trim());
        }
        return "";
    }

    /**
     * 检索字段
     */
    private String searchField;

    /**
     * 检索关键词
     */
    private String searchValue;


}
