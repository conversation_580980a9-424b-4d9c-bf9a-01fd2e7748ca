package com.trs.police.common.core.handler;

import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.excpetion.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Priority;
import javax.validation.ValidationException;

/**
 * 该类用于校验请求参数
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@Slf4j
@RestControllerAdvice(basePackages = {"com.trs"})
@Priority(1)
public class CustomExceptionHandler {

    public static final String SYSTEM_EXCEPTION_MESSAGE = "系统错误";

    public static final String AUTHORIZATION_ERROR = "权限认证失败";

    public static final String AUTHORIZATION_DEFICIENCY = "暂无操作权限";

    public static final String NOT_FOUND_RESOURCE = "资源未找到";

    public static final String PARAM_VALID_ERROR = "参数校验失败";

    public static final String INTERVAL_SYSTEM_ERROR = "服务器内部异常";

    public static final String SERVICE_UNAVAILABLE = "服务暂不可访问";

    public static final String OPERATION_FAILED = "操作失败";


    /**
     * 参数校验异常
     *
     * @param e 方法参数校验异常
     * @return 响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        return ResponseMessage.errorWithStatus(HttpStatus.BAD_REQUEST.value(),
            e.getBindingResult().getAllErrors().get(0).getDefaultMessage());
    }


    /**
     * 参数校验异常
     *
     * @param e 方法参数校验异常
     * @return 响应
     */
    @ExceptionHandler(ValidationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleMethodArgumentNotValidException(ValidationException e) {
        return ResponseMessage.errorWithStatus(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }


    /**
     * 暂停服务
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
    public ResponseMessage handleServiceException(ServiceException ex) {
        return ResponseMessage.errorWithStatus(HttpStatus.SERVICE_UNAVAILABLE.value(), SERVICE_UNAVAILABLE);
    }

    /**
     * 系统内部异常
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(SystemException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseMessage handleSystemException(SystemException ex) {
        return ResponseMessage.errorWithStatus(HttpStatus.INTERNAL_SERVER_ERROR.value(),
            INTERVAL_SYSTEM_ERROR.concat(":").concat(ex.getMessage()));
    }

    /**
     * 参数校验异常
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(ParamValidationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleParmaValidationException(ParamValidationException ex) {
        return ResponseMessage.errorWithStatus(HttpStatus.BAD_REQUEST.value(),
            PARAM_VALID_ERROR.concat(":").concat(ex.getMessage()));
    }

    /**
     * 权限拒绝
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(PermissionDeniedException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ResponseMessage handlePermissionDeniedException(PermissionDeniedException ex) {
        return ResponseMessage.errorWithStatus(HttpStatus.UNAUTHORIZED.value(), AUTHORIZATION_DEFICIENCY);
    }

    /**
     * 权限禁止
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(PermissionForbiddenException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseMessage handlePermissionForbiddenException(PermissionForbiddenException ex) {
        return ResponseMessage.errorWithStatus(HttpStatus.FORBIDDEN.value(), AUTHORIZATION_ERROR);
    }

    /**
     * 资源未找到异常处理
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseMessage handleResourceNotFoundException(ResourceNotFoundException ex) {
        return ResponseMessage.errorWithStatus(HttpStatus.NOT_FOUND.value(), NOT_FOUND_RESOURCE);
    }

    /**
     * 用户名未找到异常处理
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(UsernameNotFoundException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseMessage handleResourceNotFoundException(UsernameNotFoundException ex) {
        return ResponseMessage.errorWithStatus(HttpStatus.NOT_FOUND.value(), ex.getMessage());
    }

    /**
     * 普通异常处理
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseMessage handleCommonException(Exception ex) {
        logException(ex);
        return ResponseMessage.error(null);
    }

    /**
     * 逻辑层异常处理
     *
     * @param ex 异常
     * @return com.trs.police.common.core.entity.ResponseMessage 响应结果
     **/
    @ExceptionHandler(TRSException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResponseMessage handleTRSException(TRSException ex) {
        logException(ex);
        String message = ex.getMessage();
        return ResponseMessage.bizError(StringUtils.isBlank(message) ? OPERATION_FAILED : message);
    }

    /**
     * 打印异常
     *
     * @param ex 异常
     */
    private void logException(Exception ex) {
        log.error("", ex);
    }
}
