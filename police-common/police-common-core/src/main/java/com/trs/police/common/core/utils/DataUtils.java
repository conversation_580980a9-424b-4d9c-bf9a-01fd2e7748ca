package com.trs.police.common.core.utils;

import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 数据处理工具类
 */
public class DataUtils {

    /**
     * 过滤已经存在的数据
     * 如果targetList中的某个元素在sourceList中已经存在,则将它剔除掉,
     *
     * @param targetList   目标集合对象
     * @param sourceList   比较的集合对象
     * @param uniqueKeyGet 比较的唯一值获取方式
     * @param <T> 泛型参数
     * @param <K> 泛型参数
     * @return 在sourceList中不存在的数据集合
     */
    public static <T, K extends Serializable> List<T> filterExistData(List<T> targetList,
                                                                      List<T> sourceList,
                                                                      Function<T, K> uniqueKeyGet) {
        Tuple2<List<T>, List<T>> newDataAndExistData = separateExistData(targetList, sourceList, uniqueKeyGet);
        return newDataAndExistData._1();
    }

    /**
     * 提取出已经存在的数据
     * 如果targetList中的某个元素在sourceList中已经存在,则将它剔除掉,
     *
     * @param targetList   目标集合对象
     * @param sourceList   比较的集合对象
     * @param uniqueKeyGet 比较的唯一值获取方式
     * @param <T> 泛型参数
     * @param <K> 泛型参数
     * @return 在sourceList中不存在的数据集合
     */
    public static <T, K extends Serializable> List<T> extractExistData(List<T> targetList,
                                                                       List<T> sourceList,
                                                                       Function<T, K> uniqueKeyGet) {
        Tuple2<List<T>, List<T>> newDataAndExistData = separateExistData(targetList, sourceList, uniqueKeyGet);
        return newDataAndExistData._2();
    }

    /**
     * 分隔已经存在的数据
     * 如果targetList中的某个元素在sourceList中已经存在,则将它剔除来形成一个新的集合
     *
     * @param targetList   目标集合对象
     * @param sourceList   比较的集合对象
     * @param uniqueKeyGet 比较的唯一值获取方式
     * @param <T> 泛型参数
     * @param <K> 泛型参数
     * @return Tuple2  tuple._1 在sourceList中不存在的数据, tuple._2 在sourceList中存在的数据
     */
    public static <T, K> Tuple2<List<T>, List<T>> separateExistData(List<T> targetList,
                                                                                         List<T> sourceList,
                                                                                         Function<T, K> uniqueKeyGet) {
        return separateExistData(targetList, sourceList, uniqueKeyGet, null);
    }

    /**
     * 分隔已经存在的数据
     * 如果targetList中的某个元素在sourceList中已经存在,则将它剔除来形成一个新的集合
     *
     * @param targetList           目标集合对象
     * @param sourceList           比较的集合对象
     * @param uniqueKeyGet         比较的唯一值获取方式
     * @param doSomeThingWhenExist 当判断已经存在时的操作行为
     * @param <T> 泛型参数
     * @param <K> 泛型参数
     * @return Tuple2  tuple._1 在sourceList中不存在的数据, tuple._2 在sourceList中存在的数据
     */
    public static <T, K> Tuple2<List<T>, List<T>> separateExistData(List<T> targetList,
                                                                                         List<T> sourceList,
                                                                                         Function<T, K> uniqueKeyGet,
                                                                                         BiConsumer<T, T> doSomeThingWhenExist) {

        if (CollectionUtils.isEmpty(sourceList)) {
            return Tuple.of(targetList, sourceList);
        }
        if (CollectionUtils.isEmpty(targetList)) {
            return Tuple.of(targetList, targetList);
        }

        List<T> newDataList = new ArrayList<>();
        List<T> existDataList = new ArrayList<>();
        //根据两个对象的唯一值进行分组
        Map<K, List<T>> keyListMap = sourceList.stream().collect(Collectors.groupingBy(uniqueKeyGet));
        for (T target : targetList) {
            List<T> list = keyListMap.get(uniqueKeyGet.apply(target).toString());
            //当在sourceList中已经存在时,则执行相应的行为
            if (!CollectionUtils.isEmpty(list)) {
                //执行一些操作处理
                if (doSomeThingWhenExist != null) {
                    doSomeThingWhenExist.accept(target, list.get(0));
                }
                existDataList.add(target);
                continue;
            }
            newDataList.add(target);
        }
        return Tuple.of(newDataList, existDataList);
    }

    /**
     * 获取到没保存的数据
     * 总是返回data的过滤结果
     *
     * @param data data
     * @param savedData saveData
     * @param getKey getKey
     * @param <T> 泛型参数
     * @param <R> 泛型参数
     * @return {@link List}<{@link T}>
     */
    public static <T, R> List<T> getNotExistData(List<T> data, List<T> savedData, Function<T, R> getKey) {
        return dataFilter(data, savedData, getKey, saved -> saved ? false : true);
    }

    /**
     * 获取到保存的数据
     * 总是返回data
     *
     * @param data data
     * @param savedData savedData
     * @param getKey getKey
     * @param <T> 泛型参数
     * @param <R> 泛型参数
     * @return {@link List}<{@link T}>
     */
    public static <T, R> List<T> getExistData(List<T> data, List<T> savedData, Function<T, R> getKey) {
        return dataFilter(data, savedData, getKey, saved -> saved ? true : false);
    }

    /**
     * 通过比较两份数据 对数据过滤
     *
     * @param data            需要过滤的数据
     * @param savedData       比较的数据
     * @param getKey          通过数据构造唯一值
     * @param applySavedExist 如果数据在比较的数据中存在的返回 true 不过滤 false 过滤
     * @param <T> 泛型参数
     * @param <R> 泛型参数
     * @return 结果
     */
    public static <T, R> List<T> dataFilter(List<T> data, List<T> savedData, Function<T, R> getKey, Function<Boolean, Boolean> applySavedExist) {
        if (null == data) {
            return null;
        }
        if (null == savedData) {
            savedData = new ArrayList<>();
        }
        Objects.requireNonNull(getKey);
        // 存入数据库的主键
        Set<R> savedKey = savedData.stream()
                .map(getKey)
                .collect(Collectors.toSet());
        // 根据主键是否存入来保存数据
        List<T> result = data.stream()
                .filter(d -> {
                    if (Objects.isNull(d)) {
                        return false;
                    }
                    R key = getKey.apply(d);
                    Boolean saved = savedKey.contains(key);
                    return applySavedExist.apply(saved);
                })
                .collect(Collectors.toList());
        return result;
    }

    /**
     * 从目标数据集合匹配到目标对象 然后消费目标对象
     *
     * @param data     待处理对象集合
     * @param toMatch  目标对象集合
     * @param keyT     待处理对象构造唯一值的方式
     * @param consumer 消费方式
     * @param <T>      泛型参数
     * @param <KEY>    泛型参数
     */
    public static <T, KEY> void matchDataThen(List<T> data, Collection<T> toMatch, Function<T, KEY> keyT, BiConsumer<T, T> consumer) {
        matchListDataThen(data, toMatch, keyT, keyT, (d, list) -> {
            for (T r : list) {
                consumer.accept(d, r);
            }
        });
    }

    /**
     * 从目标数据集合匹配到目标对象 然后消费目标对象
     *
     * @param data     待处理对象集合
     * @param toMatch  目标对象集合
     * @param keyT     待处理对象构造唯一值的方式
     * @param keyR     目标对象构造唯一值的方式
     * @param consumer 消费方式
     * @param <T>      泛型参数
     * @param <R>      泛型参数
     * @param <KEY>    泛型参数
     */
    public static <T, R, KEY> void matchDataThen(List<T> data, Collection<R> toMatch, Function<T, KEY> keyT, Function<R, KEY> keyR, BiConsumer<T, R> consumer) {
        matchListDataThen(data, toMatch, keyT, keyR, (d, list) -> {
            for (R r : list) {
                consumer.accept(d, r);
            }
        });
    }

    /**
     * @param data data
     * @param toMatch toMatch
     * @param keyT keyT
     * @param keyR keyR
     * @param consumer consumer
     * @param <T> 泛型参数
     * @param <R> 泛型参数
     * @param <KEY> 泛型参数
     */
    public static <T, R, KEY> void matchListDataThen(List<T> data, Collection<R> toMatch, Function<T, KEY> keyT, Function<R, KEY> keyR, BiConsumer<T, List<R>> consumer) {
        // 为空则跳出
        if (null == data || data.isEmpty()) {
            return;
        }
        if (null == toMatch || toMatch.isEmpty()) {
            return;
        }
        // 根据id分组
        List<R> toMatchNoNull = toMatch.stream().filter(r -> Objects.nonNull(keyR.apply(r))).collect(Collectors.toList());
        Map<KEY, List<R>> keyListMap = toMatchNoNull.stream().collect(Collectors.groupingBy(keyR));
        // 遍历待匹配对象
        for (T d : data) {
            // 消费匹配到的对象集合
            List<R> r = keyListMap.get(keyT.apply(d));
            if (Objects.nonNull(r)) {
                consumer.accept(d, r);
            }
        }
    }

    /**
     * isJsonData
     *
     * @param bytes bytes
     * @return {@link Boolean}
     */
    public static Boolean isJsonData(byte[] bytes) {
        try {
            if (Objects.isNull(bytes) || bytes.length < "{}".getBytes(StandardCharsets.UTF_8).length) {
                return false;
            }
            byte[] startByte = Arrays.copyOfRange(bytes, 0, "{".getBytes(StandardCharsets.UTF_8).length);
            byte[] endByte = Arrays.copyOfRange(bytes, bytes.length - "}".getBytes(StandardCharsets.UTF_8).length, bytes.length);
            String str = new StringBuilder()
                    .append(new String(startByte, StandardCharsets.UTF_8))
                    .append(new String(endByte, StandardCharsets.UTF_8)).toString();
            return "{}".equals(str);
        } catch (Exception e) {
            // 不记录日志
        }
        return false;
    }

    /**
     * 对两份数据做比较 得到 删除列表 更新列表 删除列表
     *
     * @param oldData     旧的数据（数据库已经保存）
     * @param newData     新分析得到的数据
     * @param uuidBuilder 构造唯一值的方式
     * @param update      更新旧数据的方法 p1 oldData p2 newData
     * @param <DATA>      泛型参数
     * @return            结果
     */
    public static <DATA> DataWrapper<DATA> compareData(List<DATA> oldData, List<DATA> newData, Function<DATA, String> uuidBuilder, BiConsumer<DATA, DATA> update) {
        if (null == oldData) {
            oldData = new ArrayList<>();
        }
        if (null == newData) {
            newData = new ArrayList<>();
        }

        // 缓存唯一值和对象的关系
        Map<String, DATA> uuidOldData = oldData.stream().collect(Collectors.toMap(uuidBuilder, d -> d));
        Map<String, DATA> uuidNewData = newData.stream().collect(Collectors.toMap(uuidBuilder, d -> d));

        // 得到uuid列表
        List<String> uuidList = new ArrayList<>();
        uuidList.addAll(uuidOldData.keySet());
        uuidList.addAll(uuidNewData.keySet());
        List<String> uuidFinalList = uuidList.stream().distinct().collect(Collectors.toList());

        DataWrapper<DATA> result = new DataWrapper(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());

        // 根据数据情况把它分类到对应集合中去
        for (String uuid : uuidFinalList) {
            DATA old = uuidOldData.get(uuid);
            DATA insert = uuidNewData.get(uuid);
            if (Objects.isNull(old)) {
                // 新增
                result.newDataList.add(insert);
            } else if (Objects.isNull(insert)) {
                // 删除
                result.deleteDataList.add(old);
            } else {
                // 更新
                update.accept(old, insert);
                result.updateDataList.add(old);
            }
        }
        return result;
    }

    /**
     * @param <DATA> 泛型参数
     */
    public static class DataWrapper<DATA> {

        /**
         * 新数据
         */
        @Getter
        private List<DATA> newDataList;

        /**
         * 更新后的数据
         */
        @Getter
        private List<DATA> updateDataList;

        /**
         * 需要删除的数据
         */
        @Getter
        private List<DATA> deleteDataList;

        public DataWrapper(List<DATA> newDataList, List<DATA> updateDataList, List<DATA> deleteDataList) {
            this.newDataList = newDataList;
            this.updateDataList = updateDataList;
            this.deleteDataList = deleteDataList;
        }
    }

    /**
     * 从目标集合中匹配相同的数据
     *
     * @param data 待匹配数据
     * @param findData 根据待匹配数据查询目标数据的方法
     * @param equal 相等函数
     * @param id 主键构造方式
     * @param <T> 待匹配数据
     * @param <R> 目标数据
     * @param <ID> id
     * @return 匹配函数
     */
    public static  <T, R, ID> Function<T, Optional<R>> findSingleByT(List<T> data, Function<List<T>, List<R>> findData, BiPredicate<T, R> equal, Function<T, ID> id) {
        Function<T, List<R>> findList = findByT(data, findData, equal, id);
        return t -> findList.apply(t).stream().findAny();
    }

    /**
     * 从目标集合中匹配相同的数据
     *
     * @param data 待匹配数据
     * @param findData 根据待匹配数据查询目标数据的方法
     * @param equal 相等函数
     * @param id 主键构造方式
     * @param <T> 待匹配数据
     * @param <R> 目标数据
     * @param <ID> id
     * @return 匹配函数
     */
    public static  <T, R, ID> Function<T, List<R>> findByT(List<T> data, Function<List<T>, List<R>> findData, BiPredicate<T, R> equal, Function<T, ID> id) {
        Map<ID, List<R>> result = new HashMap<>();
        List<R> list = (Objects.isNull(data) || data.isEmpty()) ? new ArrayList<>() : findData.apply(data);
        for (T datum : data) {
            result.put(id.apply(datum), new ArrayList<>());
            for (R r : list) {
                if (Boolean.TRUE.equals(equal.test(datum, r))) {
                    result.get(id.apply(datum)).add(r);
                }
            }
        }
        return t -> result.get(id.apply(t));
    }

    /**
     * 数据根据key去重
     *
     * @param data 数据
     * @param keyExtractor 唯一标识
     * @param <T> 数据格式
     * @param <R> 唯一标识
     * @return 去重后的列表
     */
    public static <T, R> List<T> distinctByKey(Collection<T> data, Function<T, R> keyExtractor) {
        if (null == data || data.isEmpty()) {
            return new ArrayList<>();
        }
        List<T> distinctByKey = data.stream()
                .collect(Collectors.groupingBy(keyExtractor))
                .entrySet()
                .stream()
                .map(en -> en.getValue().get(0))
                .collect(Collectors.toList());
        return distinctByKey;
    }
}
