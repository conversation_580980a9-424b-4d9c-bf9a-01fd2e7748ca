package com.trs.police.common.openfeign.starter.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 群体VO
 *
 * <AUTHOR> tang.shuai
 * @date : 2024/12/02 15:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupListVO {

    /**
     * 群体id
     */
    private Long id;
    /**
     * 群体名称
     */
    private String groupName;
    /**
     * 群体人数
     */
    private Integer relationPersonCount;
    /**
     * 群体类别id
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> groupType;

    /**
     * 群体类别名称
     */
    private List<String> groupLabel;

    /**
     * 群体级别
     */
    private String groupLevel;


    /**
     * 创建单位名称
     */
    private String createDeptName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 主责民警code
     */
    private String controlPolice;

    /**
     * 主责警种名称
     */
    private List<String> controlPoliceName;

    /**
     * 责任民警code
     */
    private List<Long> controlPersonList;

    /**
     * 责任民警名称
     */
    private String controlPersonName;

    /**
     * 归属警种
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> policeKind;
    /**
     * 归属警种名称
     */
    private List<String> policeKindName;

    /**
     * 主责派出所code
     */
    private String controlBureau;
    /**
     * 责任派出所名称
     */
    private List<String> controlBureauName;

    /**
     * 档案状态
     */
    private Integer profileStatus;

    /**
     * 档案状态名称
     */
    private String profileStatusName;

    /**
     * 审批详情
     */
    private String approvalDetailStr;

    /**
     * 审批详情
     */
    private ProfileApprovalDetail approvalDetail;

    /**
     * 状态名称
     */
    private String approvalStatusName;

    /**
     * 审批id
     */
    private Long approvalId;

    /**
     * 审批标题
     */
    private String approvalTitle;

    /**
     * 经营类别
     */
    private String businessCategory;

    /**
     * 领头人
     */
    private String leaderName;

    /**
     * 领头人性别
     */
    private Long leaderGender;

    /**
     * 领头人性别名称
     */
    private String leaderGenderName;

    /**
     * 领头人身份证号码
     */
    private String leaderIdNumber;

    /**
     * 领头人户籍地
     */
    private String leaderRegisteredResidence;

    /**
     * 领头人户籍地名称
     */
    private String leaderRegisteredResidenceName;

    /**
     * 领头人基础摸排地址
     */
    private String leaderBasicInvestigationAddress;

    /**
     * 领头人基础摸排地址名称
     */
    private String leaderBasicInvestigationAddressName;

    /**
     * 领头人务工地址
     */
    private String leaderWorkAddress;

    /**
     * 领头人务工地址名称
     */
    private String leaderWorkAddressName;

    /**
     * 领头人工作类别
     */
    private String leaderWorkCategory;

    /**
     * 领头人职业类别
     */
    private String leaderOccupationCategory;

    /**
     * 领头人电话
     */
    private List<String> leaderTel;

    /**
     * 领头人其他违法犯罪情况
     */
    private String leaderOtherIllegalActivities;

    /**
     * 领头人流入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaderInflowTime;

    /**
     * 领头人具体研判情况
     */
    private String leaderSpecificAnalysis;

    /**
     * 领头人是否十类人员
     */
    private Long leaderIsSlrylb;

    /**
     * 领头人是否十类人员名称
     */
    private String leaderIsSlrylbName;

    /**
     * 设置审批详情
     *
     * @param approvalDetail 审批详情
     */
    public void setApprovalDetail(ProfileApprovalDetail approvalDetail) {
        this.approvalDetail = approvalDetail;
        if(approvalDetail != null){
            this.approvalStatusName = approvalDetail.getStatusName();
            this.approvalId = approvalDetail.getApprovalId();
            this.approvalTitle = approvalDetail.getTitle();
        }
    }
}
