package com.trs.police.common.openfeign.starter.service;

import com.trs.police.common.core.constant.PoliceMicroserviceNameConstant;
import com.trs.police.common.core.dto.NodeCreateStrategyDTO;
import com.trs.police.common.core.dto.UpdateSharedDTO;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.approval.NodeCreateStrategyResult;
import com.trs.police.common.core.vo.message.SystemMessage;
import com.trs.police.common.core.vo.message.WebsocketMessageVO;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 合成作战消息处理接口
 *
 * <AUTHOR>
 */
@FeignClient(name = PoliceMicroserviceNameConstant.SERVICE_NAME_FIGHT)
public interface FightService {

    /**
     * 接收用户消息并处理
     *
     * @param message {@link WebsocketMessageVO}
     */
    @PostMapping(value = "/message/receive/user", consumes = MediaType.APPLICATION_JSON_VALUE)
    void receiveUserMessage(@RequestBody WebsocketMessageVO message);

    /**
     * 接收系统消息并处理
     *
     * @param message {@link SystemMessage}
     */
    @PostMapping(value = "/message/receive/system", consumes = MediaType.APPLICATION_JSON_VALUE)
    void receiveSystemMessage(@RequestBody SystemMessage message);

    /**
     * 查询我的协作
     *
     * @param r 参数
     * @return 结果
     */
    @PostMapping(value = "/dashboard/todo/my-demand", consumes = MediaType.APPLICATION_JSON_VALUE)
    PageResult<TodoTaskVO> getMyDemand(@RequestBody PageParams r);

    /**
     * 查询我的合成
     *
     * @param r 参数
     * @return 结果
     */
    @PostMapping(value = "/dashboard/todo/my-record", consumes = MediaType.APPLICATION_JSON_VALUE)
    PageResult<TodoTaskVO> getMyRecord(@RequestBody PageParams r);

    /**
     * 查询我的指令
     *
     * @param r 参数
     * @return 结果
     */
    @PostMapping(value = "/dashboard/todo/my-command", consumes = MediaType.APPLICATION_JSON_VALUE)
    PageResult<TodoTaskVO> getMyCommand(@RequestBody PageParams r);

    /**
     * 根据id获取协作
     *
     * @param collaborationId 协作id
     * @return {@link Collaboration}
     */
    @GetMapping("/public/collaboration/getCollaborationById")
    Collaboration getCollaborationById(@RequestParam("collaborationId") Long collaborationId);

    /**
     * 根据id获取协作
     *
     * @param code 协作id
     * @return {@link Long}
     */
    @GetMapping("/public/plan/getPlanNum")
    Long getPlanNum(@RequestParam("code") Long code);

    /**
     * 根据id获取合成
     *
     * @param compositeId 合成id
     * @return {@link FightComposite}
     */
    @GetMapping("/public/composite/getFightCompositeById")
    FightComposite getFightCompositeById(@RequestParam("compositeId") Long compositeId);

    /**
     * 作战的审批策略
     *
     * @param dto dto
     * @return 策略
     */
    @PostMapping("/public/nodeCreateStrategyResult")
    NodeCreateStrategyResult nodeCreateStrategyResult(@RequestBody NodeCreateStrategyDTO dto);

    /**
     * cluePoolUpdateShared<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/6 11:59
     */
    @PostMapping("/public/cluePool/updateShared")
    RestfulResultsV2<String> cluePoolUpdateShared(@RequestBody UpdateSharedDTO dto);

}
