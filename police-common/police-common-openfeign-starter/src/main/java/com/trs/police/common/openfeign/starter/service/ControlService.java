package com.trs.police.common.openfeign.starter.service;

import com.trs.police.common.core.constant.PoliceMicroserviceNameConstant;
import com.trs.police.common.core.dto.Source;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.AreaStatisticsVO;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.control.*;
import com.trs.police.common.core.vo.log.SourceBasicVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.configure.OpenFeignAutoConfigure;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.AreaListVO;
import com.trs.police.common.openfeign.starter.vo.PersonListVO;
import com.trs.police.common.openfeign.starter.vo.RegularMonitorInitiateVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 管控中心接口
 *
 * <AUTHOR>
 */
@FeignClient(name = PoliceMicroserviceNameConstant.SERVICE_NAME_CONTROL, configuration = OpenFeignAutoConfigure.class, url = "http://localhost:9701")
public interface ControlService {

    /**
     * 发起常控
     *
     * @param initiateVO initiateVO
     * @return b
     */
    @PostMapping("/public/regular/initiate")
    Boolean regularInitiate(@RequestBody RegularMonitorInitiateVO initiateVO);

    /**
     * 撤销常控
     *
     * @param id id
     * @param reason 原因
     * @param usePersonId 使用的是人员id
     */
    @GetMapping("/public/regular/revoke")
    void revoke(@RequestParam("id") Long id, @RequestParam("reason") String reason, @Nullable @RequestParam("usePersonId") Integer usePersonId);

    /**
     * 撤控 (不会审批)
     *
     * @param ids 主键
     * @param reason 原因
     * @param usePersonId 使用人员id
     */
    @GetMapping("/public/regular/batchRevoke")
    void batchRevoke(@RequestParam("id") String ids, @RequestParam("reason") String reason,  @Nullable @RequestParam("usePersonId") Integer usePersonId);

    /**
     * 获取布控信息
     *
     * @param id 布控id
     * @return {@link MonitorInfoVO}
     */
    @GetMapping("/monitor/detail/{id}/info")
    MonitorInfoVO getMonitorInfo(@PathVariable("id") @NotNull Long id);

    /**
     * 获取感知源信息
     *
     * @param sourceId 感知源id
     * @return {@link SourceBasicVO}
     */
    @GetMapping("/basic/source/{sourceId}")
    SourceBasicVO getSourceInfo(@PathVariable("sourceId") Long sourceId);

    /**
     * 统计区域内感知源信息 http://192.168.200.192:3001/project/4974/interface/api/140823
     *
     * @param geometryList 区域wkt
     * @return 数量
     */
    @PostMapping("/basic/area/source/points/count")
    Integer getAreaOfSourceCount(@Valid @RequestBody List<GeometryVO> geometryList);

    /**
     * 根据感知源名称搜索id
     *
     * @param name 名称
     * @return id
     */
    @GetMapping("/basic/source/id/{name}")
    Long getSourceIdByName(@PathVariable("name") String name);

    /**
     * 获取区域信息
     *
     * @param areaId 区域id
     * @return {@link AreaDetailVO}
     */
    @GetMapping("/basic/area/{areaId}")
    AreaDetailVO getAreaDetail(@PathVariable("areaId") Long areaId);

    /**
     * 更新操作记录审批id
     *
     * @param processId 审批id
     * @param actionVO  审批操作vo
     */
    @PutMapping("/public/log/update/{processId}")
    void updateLogRelatedProcessId(@PathVariable("processId") Long processId, @RequestBody ApprovalActionVO actionVO);

    /**
     * 布控列表
     *
     * @param monitorListByIdVO 分页
     * @return 分页信息
     */
    @PostMapping("/monitor/monitor-list")
    PageResult<MonitorListDto> getMonitorList(@RequestBody MonitorListByIdVO monitorListByIdVO);

    /**
     * 预警模型查询（树状）
     *
     * @param type 模型类型
     * @return {@link ModelTreeVO}
     * @see <a href="http://192.168.200.192:3001/project/4974/interface/api/139051"></a>
     */
    @GetMapping("/monitor/warning-model/tree")
    List<ModelTreeVO> getWarningModelTree(@RequestParam("monitorType") Long type);

    /**
     * 查询待办列表
     *
     * @param pageParams 分页
     * @return 结果
     */
    @PostMapping("/warning/todo")
    PageResult<TodoTaskVO> getWarningTodo(@RequestBody PageParams pageParams);

    /**
     * 同步感知源
     *
     * @param source 感知源
     */
    @PostMapping("/public/basic/source/save-not-exist")
    void saveSourceIfNotExists(@RequestBody List<Source> source);

    /**
     * 感知源类型树
     *
     * @return 感知源类型树
     */
    @GetMapping("/public/basic/source-type/tree")
    List<ModelTreeVO> getSourceTypeTree();

    /**
     * 获取人员详情
     *
     * @param certificateNumber 证件号码
     * @param certificateType   证件类型
     * @return 人员详情
     */
    @GetMapping("/monitor/person/backfill")
    List<PersonVO> getPersonBackfill(@RequestParam("certificateNumber") String certificateNumber,
                                     @RequestParam("certificateType") Long certificateType);

    /**
     * 管控区域统计
     *
     * @param request request
     * @return {@link List}<{@link AreaStatisticsVO}>
     */
    @PostMapping("/public/basic/area/statistics")
    List<AreaStatisticsVO> areaStatistics(@RequestBody ListParamsRequest request);

    /**
     * 触发关注监控工作
     *
     * @param careMonitorVO careMonitorVO
     * @return 结果
     */
    @PostMapping("/care-monitor/initiateSingle")
    Boolean initiateCareMonitor(@RequestBody CareMonitorVO careMonitorVO);

    /**
     * 触发关注监控工作
     *
     * @param careMonitorVO careMonitorVO
     * @return 结果
     */
    @PostMapping("/public/care-monitor/initiateSingle")
    Boolean initiateCareMonitorNoAuth(@RequestBody CareMonitorVO careMonitorVO);

    /**
     * 重点区域列表查询 http://192.168.200.192:3001/project/4974/interface/api/138970
     *
     * @param request 列表查询参数
     * @return 区域列表
     */
    @PostMapping("/public/basic/area/list")
    PageResult<AreaListVO> getAreaList(@RequestBody ListParamsRequest request);

    /**
     * 批量查询AreaVO
     *
     * @param areaIds areaIds
     * @return {@link AreaVO}
     */
    @PostMapping("/public/basic/area/batch-ids")
    List<AreaVO> getAreaByIds(@RequestBody List<Long> areaIds);

    /**
     * 根据enName获取warningSourceType
     *
     * @return WarningSourceTypeEntity
     */
    @GetMapping("/public/basic/source/all")
    List<WarningSourceTypeEntity> getCnNameByEnName();

    /**
     * 获取到常控标签
     *
     * @param personIds 人员id
     * @return 常控标签信息
     */
    @PostMapping("/public/regular/getRegularLabel")
    List<PersonListVO> getRegularLabel(@RequestBody List<Long> personIds);

    /**
     * 获取手机的三码信息
     *
     * @param tels 手机号码数组
     * @return 结果
     */
    @PostMapping("/public/virtual/info")
    List<VirtualInfoVO> getVirtualInfoList(@RequestBody List<String> tels);

    /**
     * 530获取手机信息
     *
     * @param imsiList imsi
     * @return 手机号
     */
    @PostMapping("/virtual/getPhoneByImsi")
    Map<String, String> getImsiPhoneMap(@RequestBody List<String> imsiList);

    /**
     * 根据人员id和人员级别名称修改常控级别 <br> 理论上人员级别名称和常控级别名称一样 如果不一样 则不更新
     *
     * @param personProfileId 人员档案id
     * @param personLevelName 人员界别名称
     */
    @GetMapping("/public/regular/editLevelByPersonProfile")
    void editLevelByPersonProfile(@RequestParam("personProfileId") Long personProfileId, @RequestParam("personLevelName") String personLevelName);

    /**
     * 根据状态获取重点区域
     *
     * @param status status
     * @return {@link AreaVO}
     */
    @GetMapping("/public/basic/area/findAreaByStatus/{status}")
    List<AreaVO> findAreaByStatus(@PathVariable("status") Integer status);


    /**
     * 无审批常控
     *
     * @param initiateVO initiateVO
     * @return b
     */
    @PostMapping("/public/regular/initiateNoApproval")
    Boolean initiateNoApproval(@RequestBody RegularMonitorInitiateVO initiateVO);

    /**
     * 添加工作记录
     *
     * @param workRecordDTO workRecordDTO
     * @return
     */
    @PostMapping("/public/regular/addWorkRecord")
    void addWorkRecord(@RequestBody WorkRecordDTO workRecordDTO);

    /**
     * 根据人员id添加fk人员
     *
     * @param personId 人员id
     */
    @PostMapping("/public/warning/addFkryByPersonId")
    void addFkryByPersonId(@RequestParam("personId") Long personId);
}
