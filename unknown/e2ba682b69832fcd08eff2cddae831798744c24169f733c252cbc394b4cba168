package com.trs.police.control.service.warning;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.mapper.PersonRelateToFkMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 同杆徘徊
 *
 * <AUTHOR>
 */
@Component
public class TgphWarningService implements WarningService {

    @Autowired
    private ProfileService profileService;

    @Autowired
    private PersonRelateToFkMapper personRelateToFkMapper;

    @Override
    public MonitorLevelEnum getWarningLevel(WarningFkrxyjEntity entity) {
        PersonRelateToFkEntity fkEntity = personRelateToFkMapper.selectOne(
                Wrappers.lambdaQuery(PersonRelateToFkEntity.class)
                        .eq(PersonRelateToFkEntity::getIdCard, entity.getIdCard())
        );
        if (Objects.isNull(fkEntity)) {
            return MonitorLevelEnum.ORANGE;
        }
        Long level = BeanFactoryHolder.getEnv().getProperty("control.fkry.tgph.control.level", Long.class, 1L);
        return level.equals(fkEntity.getControlLevel()) ? MonitorLevelEnum.YELLOW : MonitorLevelEnum.ORANGE;
    }
}
