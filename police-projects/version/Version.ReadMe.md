# police-projects
# 17.4
- XMKFB-8544[XMKFB-8530] 后 - 排查警务资源回流

# 17.3
- XMKFB-8414[XMKFB-8021] 后 - udp服务开通多个端口支持
- XMKFB-8416[XMKFB-8021] 后 - 优化对应设备列表查询
- XMKFB-8432[XMKFB-8021] 后 - 提供切换的州市列表跟获取对应融合通讯人员列表

## 部署前置
```properties
# 20250519 - 张阳 - 设备信息UPD服务接口配置（项目定制）
com.trs.bigscreen.device.upd.port.config=[{"port":9876,"districtCode":"510500"}]
# 20250523 - 肖豪 - 增加省厅融合通讯相关账号配置（省厅）
projects.qz.rh.personList=[{"districtCode":"510000","districtName":"四川省","userId":19,"userName":"叶桓荣","idCard":"510302198809140514","deptId":2,"deptName":"厅情报指挥中心"},{"districtCode":"510700","districtName":"绵阳市","userId":7612,"userName":"admin1","idCard":"511002119204295649","deptId":523,"deptName":"绵阳市公安局"}]
```

# 17.2
- XMKFB-8328[XMKFB-8185] 后-【省厅情指】- 完成值班系统接口改造
- XMKFB-8330[XMKFB-8185] 后-【省厅情指】- 后端相关接口提供

# 16.5
-XMKFB-8169【泸州】大数据权限中心，将新增的技战法、典型案例状态默认为已选用

# 16.4
- XMKFB-7906[XMKFB-7815] 后-【广安】- 线索、警务资源、值班相关接口提供
- XMKFB-8059[XMKFB-7925]【泸州】技战法，典型示例详情接口支持

# 16.3
- XMKFB-7904【泸州】数据权限中心，通知通报、技战法、典型案例编辑页面里的内容上传图片并提交以后报错

# v16.2
-XMKFB-7588[XMKFB-7567] 后-【泸州】- 提供后台管理相关接口

# v15.4
- XMKFB-7306 南充大屏问题集中处理

## RC20250305
- XMKFB-7148[XMKFB-7042] 后-【省厅情指】设备撒点支持按照地域检索

# v14.4
- XMKFB-7062 后 - UDP设备回写数据存储

# v14.3
- XMKFB-6869[XMKFB-6374] 后 - 增加UDP接口接受第三方回写的数据并提供接口给前端

# RC20250210
- XMKFB-6401 【省厅情指】狼烟大屏，值班接警情列表需支持按照领导批示指示内容进行搜索

# v14.2
- XMKFB-6729 【省厅情指】狼烟平台，值班表格里显示的市级、自治州值班数据有误

# v14.1
- XMKFB-6688[XMKFB-6673] 【省厅情指】收发文，表单保存、编辑接口支持

# v13.4
- XMKFB-6458 【省厅情指】狼烟大屏，查询内容里未显示流转情况数据
- XMKFB-6473 【省厅情指】狼烟大屏，交接班关注导出文档里线索人员核查稳控调度内容格式的优化
- XMKFB-6568 【省厅情指】-后-行政区域筛选“值班计划”列表逻辑修改
- 

## v13.3
- XMKFB-6261 后-【省厅情指】-导出指挥日志模板调整
- XMKFB-6401 【省厅情指】狼烟大屏，值班接警情列表需支持按照领导批示指示内容进行搜索
- XMKFB-6472 省厅GA-指挥日志导出的日期问题

## v13.2

- XMKFB-6230 【自贡】矛盾纠纷大屏，劳动调节仲裁数据详情表中的处置状态显示为了数字
- XMKFB-6264 合-【自贡】- 矛盾纠纷化解大屏增加数据种类

## v13.1

- XMKFB-5745 省厅GA-狼烟迭代一版更新
- XMKFB-5968[XMKFB-5745] 后-【省厅情指】- 指挥日志-值班表相关优化
- XMKFB-6215 自贡-矛盾大屏右侧新增外部数据展示

## RC20250102

- XMKFB-6128 后-【自贡】- 从业人员数据同步到mysql
- XMKFB-6124 自贡GA-人员档案详情页底部增加矛盾纠纷记录功能

## v12.4

- XMKFB-5725 后 - 出入境短信调整香港、台湾、澳门籍护照的比对逻辑
- XMKFB-5737[XMKFB-5658] 后 - 完善警务站，快反点，巡防点，检查站数据同步
- XMKFB-5738[XMKFB-5658] 后 - 巡防线，巡防区域，圈层同步

## v12.2

- XMKFB-5591 【南充】专刊，一个派出所一张表导出文件的文件名称应作修改

# 历史配置分割线 ---------------------------------------------------

## v1.5 发版日志

- feat: 三清日清排序优化

## 6.1

- XMKFB-1766 【泸州】出入境jw人员住宿登记比对
- XMKFB-1971[XMKFB-1725] 后 - 工作日志功能开发

### 新增配置项

```yaml
# 泸州环境增加出入境相关数据源的配置
spring:
  datasource:
    dynamic:
      datasource:
        crj:
          username: jdbc:postgresql://ip:port/数据库名
          password: 数据库密码
          url: 数据库链接
          # 泸州市PG
          driver-class-name: org.postgresql.Driver
          druid:
            max-wait: 10000
            validation-query: select 1
            validation-query-timeout: 200
            initial-size: 5
            max-active: 5
            min-idle: 5

```

## 6.3

- XMKFB-2130 【泸州】出入境境外人员住宿登记优化
-

### 新增配置项

```properties
# projects-crj.properties中增加如下配置
# 当错误的对象为空时，是否跳过短信发送，按照XMKFB-2130要求为空的也需要发送短信
com.trs.crj.system.skipOnErrorEmpty=false
# 以下国家或地区中姓名顺序跟中文一致，姓前名后
# XMKFB-2130 英文姓名的先后关系和国家有关系，
# 包括：朝鲜、韩国、越南、日本、蒙古、阿富汗、新加坡、匈牙利，以上国家的英文姓名是 姓在前名在后。
# 除以上国家之外的国家英文姓名是 名在前姓在后。
com.trs.crj.system.xm.style.like.cn=PRK,KOR,VNM,JPN,MNG,AFG,SGP,HUN
```

## 6.4

- XMKFB-2304[XMKFB-1725] 今日重点关注-重点案事件，线索事件的工作日志导出失败
- # 20240627发版

## 7.1

- XMKFB-2426 后 - 优化出入境英文姓名拼接
-

### 部署前缀

```properties
# projects-crj.properties中修改如下配置
com.trs.crj.system.xm.style.like.cn=.*
```

## v8.2 发版日志

- XMKFB-2915 【南充】情报专刊开发
- XMKFB-2988 后-【南充】情报专刊开发

## 8.3

- XMKFB-3052[XMKFB-3049] 后-【广安】-指挥大屏后端优化
-

### 新增配置

```yaml
# projects.yaml
ys:
  projects:
    bigScreen:
      duty:
        nature: 值班领导,指挥长,值班长
        level: 主班,副班
```

## 8.4

- XMKFB-3329 【南充】发布专刊-日报后，在专刊库中出现多条已发布的数据
- XMKFB-3337 【南充】专刊-写专刊中显示的逾期时间不正确
- XMKFB-3304 【南充】专刊-收专刊，日报中显示的未提交单位取值不正确
- XMKFB-3314 【南充】专刊-写、收专刊，列表中展示的时间不正确
- XMKFB-3348 【南充】专刊库详情中，类似专刊查询的数据不准确
- XMKFB-3367 后-针对每个模块的催办记住状态

## 8.5

- XMKFB-3484 合-【广安】-广安大屏中增加警员和警车图层，为了一期验收
- XMKFB-3470 后 - 出入境境外人员住宿登记短信推送
- XMKFB-3471 后 - 出入境境外人员住宿登记优化
-

### 新增配置

```properties
# projects-crj.properties 泸州环境增加配置
# XMKFB-3470 20240828 褚川宝 需要推送的指定国家/地区的代码，多个逗号分割
com.trs.crj.rzxx.system.guoji=TWN
# XMKFB-3470 是否发送短信
com.trs.crj.rzxx.system.send.sms=false
# XMKFB-3470 短信接收方（手机号），多个逗号分割
com.trs.crj.rzxx.system.phone=
# XMKFB-3470 定时任务
com.trs.crj.rzxx.system.task.enable=false
com.trs.crj.rzxx.system.task.cron=22 2/20 * * * ?
```

## 9.1

- XMKFB-3661 后 -完善广安大屏中警务资源的同步行为
- feat(crj): 优化短信内容跟旅店名称设置
- XMKFB-3359 【南充】非主管、填报单位和可见范围的单位，就算拥有专刊库的权限，也不应该能查看到数据
- XMKFB-3338 【南充】对应单位填写的内容存在评论，但在写专刊的列表页未展示评论标识
-

### 新增配置

```properties
# projects-crj.properties
# 哪个项目用这个功能，广安为ga,自贡为zg
com.trs.bigscreen.system.area=
com.trs.bigscreen.sync.task.enable=true
com.trs.bigscreen.sync.task.cron=30 2/5 * * * ?
# 警务资源中查询上报时间在n分钟内的数据（负数），大于等于0时该配置项不生效
com.trs.bigscreen.jwzy.search.time=0
```

## 9.2

- XMKFB-3853[XMKFB-3385] 后-【南充】-对接值班信息
- XMKFB-3860[XMKFB-3385] 后-【南充】-完成工作指令对接

### 新增配置

```properties
# 20240912 - 褚川宝 - 南充的值班接口URL
com.trs.bigscreen.sync.duty.nc.url=
# 20240912 - 褚川宝 - 南充的值班接口多长时间内可以同步一次（只控制是否同步，不是同步周期，周期为：com.trs.bigscreen.sync.task.cron），单位：分钟（为0时关闭即每次都同步）
com.trs.bigscreen.sync.duty.nc.redis.timeout=120
```

## v9.3

- XMKFB-3910 【南充】专刊的红头内容展示不正确

## 9.4

- XMKFB-3861 后-【广安】-对接重庆警员警车数据
- XMKFB-2258[XMKFB-1725] 合 - 自贡大屏对接第三方勤务报备系统

## 10.2

- XMKFB-4253[XMKFB-4243] 后-【南充】-工作指令优化
- XMKFB-4314 后 - 南充大屏值班表详情优化
- XMKFB-4315[XMKFB-4289] 后-【广安】-警务资源增加 警员-重庆，警车-重庆
- XMKFB-4076 广安GA-预案功能
- XMKFB-4354[XMKFB-4289] 后-【广安】-警员、警车支持按照经纬度范围检索
- XMKFB-4470 后-【广安】-重庆警员警车更换topic
-

### 新增配置

```properties
# 20241008 - 褚川宝 - 手动配置的需要同步的值班人员信息
com.trs.bigscreen.sync.duty.nc.custom.dutyUser=[]
# 20241008 - 褚川宝 - 需要同步地域编码的值班人员信息，多个逗号分割（以其开头或等于其的数据）
com.trs.bigscreen.sync.duty.nc.supportCode=5113

```

## 10.3

- XMKFB-4481[XMKFB-4410] 后-值班信息录入和编辑接口
- XMKFB-4366 后-【南充】-情报研判专刊bug
- XMKFB-3915 【南充】专刊-写专刊、收专刊列表中，周报名称显示具体的时间范围
- XMKFB-4773[XMKFB-4768] 后 - 大屏值班表接口支持手动传入地域编码

## 10.4

- XMKFB-4603[XMKFB-4326] 后-【南充】情报专刊开发

## 11.2

- XMKFB-4728 广安-预案相关调整
- XMKFB-5042 【南充】节假日专刊，展示的开始时间和结束时间不正确

## v11.3 发版日志

- XMKFB-5174 南充-大屏对接海能达数据

```yaml
# 南充环境增海能达相关数据源的配置
spring:
  datasource:
    dynamic:
      datasource:
        hnd:
          username: jdbc:postgresql://ip:port/数据库名
          password: 数据库密码
          url: 数据库链接
          # 泸州市PG
          driver-class-name: org.postgresql.Driver
          druid:
            max-wait: 10000
            validation-query: select 1
            validation-query-timeout: 200
            initial-size: 5
            max-active: 5
            min-idle: 5
```

## 11.4

- XMKFB-5208[XMKFB-5174] 后-【南充】-对接海能达警务资源数据
- XMKFB-5228 南充GA-新增“派出所一张表”模板
- XMKFB-5364[XMKFB-5319] 后-【省厅情指】- 提供值班人员相关接口
- XMKFB-4662 南充GA-专刊新增统计页面
- XMKFB-5228 南充GA-新增“派出所一张表”模板
- XMKFB-5265[XMKFB-4662] 南充GA-专刊统计页面单位填写情况的设计和开发
- XMKFB-5339 后-【德阳】-北新机械厂-群体档案-上传附件bug
- XMKFB-5377[XMKFB-5319] 后-【省厅情指】- 门户-指挥日志相关接口提供
- XMKFB-5466 后-【泸州】- 三日三清 sql优化

## 12.1

- XMKFB-5474 后 -【南充】- 大屏优化
- XMKFB-5501[XMKFB-5319] 后-【省厅情指】- 门户指挥日志剩余接口提供

### 新增配置

```properties
# 20241202 - 褚川宝 - XMKFB-5474 - 南充根据地域编码映射对应的单位名，key为地域编码，value为单位名
com.trs.bigscreen.sync.duty.nc.code.unitName.mapping={"511300":"南充市公安局"}
```

## 13.2

- XMKFB-6246[XMKFB-6202] 后-【省厅情指】- 指挥日志-值班人员新增警保岗
- XMKFB-6450 【省厅情指】狼烟大屏，值班计划列表按照组合筛选无效

## 13.4

- XMKFB-6479 【省厅情指】狼烟大屏，值班人员批量导入模板的优化
- XMKFB-6457 【省厅情指】狼烟大屏，屏蔽值班计划导出Excel里指挥调度专班的副班

## 15.4
- XMKFB-7404[XMKFB-7140] 后 - 完善广安数据同步

### 新增配置

```properties
# 20250326 - 张阳 - 安巡数据同步方式配置[视图：view（广安）  API：api（自贡）]
com.trs.bigscreen.system.qinwu.utils.key=
# 20250326 - 张阳 - 安巡数据源相关配置
spring.datasource.dynamic.datasource.ga-jcgl.lazy=true
spring.datasource.dynamic.datasource.ga-jcgl.username=账户
spring.datasource.dynamic.datasource.ga-jcgl.password=密码
spring.datasource.dynamic.datasource.ga-jcgl.url=********************************
spring.datasource.dynamic.datasource.ga-jcgl.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ga-jcgl.druid.max-wait=10000
spring.datasource.dynamic.datasource.ga-jcgl.druid.validation-query=select 1
spring.datasource.dynamic.datasource.ga-jcgl.druid.validation-query-timeout=200
spring.datasource.dynamic.datasource.ga-jcgl.druid.initial-size=5
spring.datasource.dynamic.datasource.ga-jcgl.druid.max-active=5
spring.datasource.dynamic.datasource.ga-jcgl.druid.min-idle=5
spring.datasource.dynamic.datasource.ga-qwgl.lazy=true
spring.datasource.dynamic.datasource.ga-qwgl.username=账户
spring.datasource.dynamic.datasource.ga-qwgl.password=密码
spring.datasource.dynamic.datasource.ga-qwgl.url=********************************
spring.datasource.dynamic.datasource.ga-qwgl.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ga-qwgl.druid.max-wait=10000
spring.datasource.dynamic.datasource.ga-qwgl.druid.validation-query=select 1
spring.datasource.dynamic.datasource.ga-qwgl.druid.validation-query-timeout=200
spring.datasource.dynamic.datasource.ga-qwgl.druid.initial-size=5
spring.datasource.dynamic.datasource.ga-qwgl.druid.max-active=5
spring.datasource.dynamic.datasource.ga-qwgl.druid.min-idle=5
```