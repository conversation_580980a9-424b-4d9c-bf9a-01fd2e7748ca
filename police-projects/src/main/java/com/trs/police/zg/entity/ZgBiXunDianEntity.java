package com.trs.police.zg.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.datasource.starter.typehandler.GeometryTypeHandler;
import com.trs.police.zg.anno.ZiGongField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

/**
 * @ClassName BigScreenBiXunDianEntity
 * @Description 自贡大屏 - 必巡点实体
 * <AUTHOR>
 * @Date 2024/12/24 10:04
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "tb_projects_bigscreen_bixundian", autoResultMap = true)
public class ZgBiXunDianEntity extends BaseZgJingWuSourceEntity {

    private String id;

    private String bxdMc;

    /**
     * 必巡点 类型
     * 1：一般区域
     * 2：重点区域
     * 3：核心区域
     * 4：人员密集区域
     */
    private String bxdLx;

    /**
     * 有效期
     */
    @ZiGongField(clazz = Date.class)
    private Date yxq;

    @ZiGongField(clazz = Double.class)
    private Double jd;

    @ZiGongField(clazz = Double.class)
    private Double wd;

    @TableField(jdbcType = JdbcType.OTHER, typeHandler = GeometryTypeHandler.class)
    private String zb;

    private String zbhash;

    private String xxdz;

    private String bxdTp;

    private String xzqhdm;

    /**
     * 来源代码
     * 01：经验必巡点
     * 02：登记必巡点
     * 03：区域分析必巡点
     */
    private String bxdlydm;

    /**
     * 巡逻频次
     */
    private String xlpc;

}
