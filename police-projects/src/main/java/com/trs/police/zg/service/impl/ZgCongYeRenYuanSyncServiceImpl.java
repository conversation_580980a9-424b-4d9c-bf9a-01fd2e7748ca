package com.trs.police.zg.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.bigscreen.service.BaseSyncService;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.zg.entity.ZgCongYeRenYuanEntity;
import com.trs.police.zg.mapper.ZgCongYeRenYuanMapper;
import com.trs.police.zg.utils.QinWuUtils;
import com.trs.police.zg.vo.ZgCongYeRenYuanVO;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 从业人员同步服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "zg")
public class ZgCongYeRenYuanSyncServiceImpl extends BaseSyncService<ZgCongYeRenYuanVO, ZgCongYeRenYuanEntity> {

    private ZgCongYeRenYuanMapper zgCongYeRenYuanMapper;

    public ZgCongYeRenYuanSyncServiceImpl(
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate,
            ZgCongYeRenYuanMapper zgCongYeRenYuanMapper) {
        super(syncTaskMapper, redisTemplate);
        this.zgCongYeRenYuanMapper = zgCongYeRenYuanMapper;
    }

    @Override
    public Tuple2<List<ZgCongYeRenYuanVO>, String> findInData(String startTime, String endTime) {
        try {
            var url = BeanFactoryHolder.getEnv().getProperty("com.trs.bigscreen.sync.zg.qwbb.cyryxx");
            String sql = String.format("dep_action_time <= '%s' and dep_action_time >= '%s'", endTime, startTime);
            List<ZgCongYeRenYuanVO> allData = QinWuUtils.getAllData(url,"510300380000-0100-00010", QinWuUtils.PAGE_SIZE, sql, ZgCongYeRenYuanVO.class);
            return new Tuple2<>(allData, sql);
        } catch (Exception e) {
            log.error("同步从业人员失败", e);
            return new Tuple2<>(new ArrayList<>(), "");
        }
    }

    @Override
    public ZgCongYeRenYuanEntity convert(ZgCongYeRenYuanVO vo) {
        ZgCongYeRenYuanEntity entity = new ZgCongYeRenYuanEntity();
        BeanUtils.copyProperties(vo, entity);
        return entity;
    }

    @Override
    public void save(ZgCongYeRenYuanEntity entity) {
        ZgCongYeRenYuanEntity saved = zgCongYeRenYuanMapper.selectOne(
                Wrappers.lambdaQuery(ZgCongYeRenYuanEntity.class)
                        .eq(ZgCongYeRenYuanEntity::getXxzjbh, entity.getXxzjbh())
        );
        if (Objects.isNull(saved)) {
            zgCongYeRenYuanMapper.insert(entity);
        } else {
            // 不做操作
        }

    }

    @Override
    public String key() {
        return "zg_cyryxx";
    }

    @Override
    public String desc() {
        return "zg从业人员";
    }
}
