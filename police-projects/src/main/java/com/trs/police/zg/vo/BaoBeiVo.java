package com.trs.police.zg.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @ClassName BaoBeiVo
 * @Description 报备数据vo
 * <AUTHOR>
 * @Date 2024/12/26 17:29
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class BaoBeiVo extends BaseJingwuSourceVo {

    private String id;

    private String bcId;

    private String bcMc;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private Date bcKssj;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private Date bcJssj;

    /**
     * 班次跨天数
     * 01：今日
     * 02：明日
     * 03：2天后
     * 04：3天后
     * 05：4天后
     * 06：5天后
     * 07：6天后
     * 08：7天后
     */
    private String bcKtsDict;

    private Integer bcKts;

    private String xfqyId;

    private String xfqyMc;

    private String jzId;

    private String jzMc;

    /**
     * 巡逻方式
     * 0：车巡
     * 1：步巡
     */
    private String xfxlfs;

    /**
     * 武装类型
     * 0：武装
     * 1：非武装
     */
    private String xfwzlx;

    /**
     * 着装类型
     * 0：制服
     * 1：便衣
     */
    private String xfzzlx;

    /**
     * 值班频道名称
     */
    private String zbpdmc;

    /**
     * 值班频道号
     */
    private String zbpdh;

    /**
     * 值班频道呼号
     */
    private String zbpdhh;

    private String zbdh;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date bbSjBbrq;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bbSjKssj;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bbSjJssj;

    /**
     * 报备状态
     * 01：排班中
     * 02：报备中
     * 03：报备结束
     */
    private String bbZt;

    /**
     * 审核人姓名
     */
    private String shrXm;

    private String shrSfzh;

    private String shrLxfs;

    private String shrSsbm;

    private String shrSsbmdm;

    /**
     * 01：未审核
     * 02：审核中
     * 03：审核通过
     * 04：审核不通过
     */
    private String shZt;

    private String xxdz;

    private JingZuVo jingzu;

    private List<XunFangBaoBeiCheLiangVo> cheliang;

    private List<XunFangBaoBeiQiXieVo>  qixie;

    private List<XunFangBaoBeiRenYuanVo> renyuan;

    private List<XunFangBaoBeiZhongDuanVo> zhongduan;

}
