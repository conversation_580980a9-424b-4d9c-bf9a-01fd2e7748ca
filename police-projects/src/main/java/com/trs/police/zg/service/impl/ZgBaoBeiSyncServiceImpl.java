package com.trs.police.zg.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.zg.entity.ZgBaoBeiEntity;
import com.trs.police.zg.mapper.ZgBaoBeiMapper;
import com.trs.police.zg.service.BaseZgJingWuSourceService;
import com.trs.police.zg.utils.JingWuUtils;
import com.trs.police.zg.utils.QinWuUtils;
import com.trs.police.zg.vo.*;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.trs.common.utils.TimeUtils.getCurrentDate;

/**
 * @ClassName ZgBaoBeiSyncServiceImpl
 * @Description 报备业务类
 * <AUTHOR>
 * @Date 2024/12/26 17:30
 **/
@Slf4j
@Service
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "zg")
public class ZgBaoBeiSyncServiceImpl extends BaseZgJingWuSourceService<ZgBaoBeiEntity, ZgBaoBeiEntity, BaoBeiVo> {

    private final ZgBaoBeiMapper mapper;

    public ZgBaoBeiSyncServiceImpl(SyncTaskMapper syncTaskMapper, RedisTemplate<String, Object> redisTemplate, ZgBaoBeiMapper mapper) {
        super(syncTaskMapper, redisTemplate, mapper);
        this.mapper = mapper;
    }

    @Override
    public ZgBaoBeiEntity convert(ZgBaoBeiEntity zgBaoBeiEntity) {
        zgBaoBeiEntity.setUpdateTime(new Date());
        return zgBaoBeiEntity;
    }

    @Override
    public void save(ZgBaoBeiEntity entity) {
        final Optional<ZgBaoBeiEntity> opt = new LambdaQueryChainWrapper<>(mapper).eq(ZgBaoBeiEntity::getId, entity.getId()).oneOpt();
        if (opt.isPresent()) {
            entity.setDataId(opt.get().getDataId());
            entity.setCrTime(opt.get().getCrTime());
            mapper.updateById(entity);
        } else {
            entity.setCrTime(new Date());
            mapper.insert(entity);
        }
    }

    @Override
    protected Tuple2<Long, List<ZgBaoBeiEntity>> getDataFromThird(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        final Tuple2<Long, List<ZgBaoBeiEntity>> data = qinWuUtils.getBb(pageNum, pageSize, where);
        final List<ZgBaoBeiEntity> list = data._2();
        for (ZgBaoBeiEntity entity : list) {
            final Optional<SheBeiLiShiWeiZhiVo> lastSheBei = findLastSheBei(entity.getJzId());
            if (lastSheBei.isPresent()) {
                final SheBeiLiShiWeiZhiVo sheBeiLiShiWeiZhiVo = lastSheBei.get();
                entity.setJd(sheBeiLiShiWeiZhiVo.getJd());
                entity.setWd(sheBeiLiShiWeiZhiVo.getWd());
                entity.setXxdz(sheBeiLiShiWeiZhiVo.getDzjc());
            }
            if (Objects.nonNull(entity.getJd()) && Objects.nonNull(entity.getWd())) {
                entity.setZb(String.format("POINT(%s %s)", entity.getJd(), entity.getWd()));
            }
            final List<XunFangBaoBeiZhongDuanVo> zdList = findQwglXfbbZd(entity.getId());
            entity.setZhongduan(zdList);
            final List<XunFangBaoBeiCheLiangVo> qwglXfbbCl = findQwglXfbbCl(entity.getId());
            entity.setCheliang(qwglXfbbCl);
            final List<XunFangBaoBeiRenYuanVo> qwglXfbbRy = findQwglXfbbRy(entity.getId());
            entity.setRenyuan(qwglXfbbRy);
            final List<XunFangBaoBeiQiXieVo> qwglXfbbQx = findQwglXfbbQx(entity.getId());
            entity.setQixie(qwglXfbbQx);
            final JingZuVo qwglJingZu = findQwglJingZu(entity.getJzId());
            entity.setJingzu(qwglJingZu);
        }
        return data;
    }

    private List<XunFangBaoBeiZhongDuanVo> findQwglXfbbZd(String xfbbId) {
        if (StringUtils.isEmpty(xfbbId)) {
            return List.of();
        }
        try {
            int pageNum = 1;
            final var sql = String.format(" xt_scbz = '0' and xfbb_id = '%s' ", xfbbId);
            Tuple2<Long, List<XunFangBaoBeiZhongDuanVo>> t = qinWuUtils.qwglXfbbZd(pageNum, QinWuUtils.PAGE_SIZE, sql);
            final int size = t._1.intValue();
            List<XunFangBaoBeiZhongDuanVo> d = new ArrayList<>(size);
            d.addAll(t._2);
            while (size > QinWuUtils.PAGE_SIZE * pageNum) {
                pageNum += 1;
                d.addAll(qinWuUtils.qwglXfbbZd(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
            }
            return d;
        } catch (Exception e) {
            log.error("根据[{}]查询终端异常", xfbbId, e);
            return List.of();
        }
    }

    private List<XunFangBaoBeiCheLiangVo> findQwglXfbbCl(String xfbbId) {
        if (StringUtils.isEmpty(xfbbId)) {
            return List.of();
        }
        try {
            int pageNum = 1;
            final var sql = String.format(" xt_scbz = '0' and xfbb_id = '%s' ", xfbbId);
            Tuple2<Long, List<XunFangBaoBeiCheLiangVo>> t = qinWuUtils.qwglXfbbCl(pageNum, QinWuUtils.PAGE_SIZE, sql);
            final int size = t._1.intValue();
            List<XunFangBaoBeiCheLiangVo> d = new ArrayList<>(size);
            d.addAll(t._2);
            while (size > QinWuUtils.PAGE_SIZE * pageNum) {
                pageNum += 1;
                d.addAll(qinWuUtils.qwglXfbbCl(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
            }
            return d;
        } catch (Exception e) {
            log.error("根据[{}]查询车辆异常", xfbbId, e);
            return List.of();
        }
    }

    private List<XunFangBaoBeiRenYuanVo> findQwglXfbbRy(String xfbbId) {
        if (StringUtils.isEmpty(xfbbId)) {
            return List.of();
        }
        try {
            int pageNum = 1;
            final var sql = String.format(" xt_scbz = '0' and xfbb_id = '%s' ", xfbbId);
            Tuple2<Long, List<XunFangBaoBeiRenYuanVo>> t = qinWuUtils.qwglXfbbRy(pageNum, QinWuUtils.PAGE_SIZE, sql);
            final int size = t._1.intValue();
            List<XunFangBaoBeiRenYuanVo> d = new ArrayList<>(size);
            d.addAll(t._2);
            while (size > QinWuUtils.PAGE_SIZE * pageNum) {
                pageNum += 1;
                d.addAll(qinWuUtils.qwglXfbbRy(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
            }
            return d;
        } catch (Exception e) {
            log.error("根据[{}]查询车辆异常", xfbbId, e);
            return List.of();
        }
    }

    private List<XunFangBaoBeiQiXieVo> findQwglXfbbQx(String xfbbId) {
        if (StringUtils.isEmpty(xfbbId)) {
            return List.of();
        }
        try {
            int pageNum = 1;
            final var sql = String.format(" xt_scbz = '0' and xfbb_id = '%s' ", xfbbId);
            Tuple2<Long, List<XunFangBaoBeiQiXieVo>> t = qinWuUtils.qwglXfbbQx(pageNum, QinWuUtils.PAGE_SIZE, sql);
            final int size = t._1.intValue();
            List<XunFangBaoBeiQiXieVo> d = new ArrayList<>(size);
            d.addAll(t._2);
            while (size > QinWuUtils.PAGE_SIZE * pageNum) {
                pageNum += 1;
                d.addAll(qinWuUtils.qwglXfbbQx(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
            }
            return d;
        } catch (Exception e) {
            log.error("根据[{}]查询人员异常", xfbbId, e);
            return List.of();
        }
    }

    private JingZuVo findQwglJingZu(String jzId) {
        if (StringUtils.isEmpty(jzId)) {
            return null;
        }
        try {
            int pageNum = 1;
            final var sql = String.format(" xt_scbz = '0' and id = '%s' ", jzId);
            Tuple2<Long, List<JingZuVo>> t = qinWuUtils.qwglJingZu(pageNum, QinWuUtils.PAGE_SIZE, sql);
            final int size = t._1.intValue();
            List<JingZuVo> d = new ArrayList<>(size);
            d.addAll(t._2);
            while (size > QinWuUtils.PAGE_SIZE * pageNum) {
                pageNum += 1;
                d.addAll(qinWuUtils.qwglJingZu(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
            }
            if (CollectionUtils.isEmpty(d)) {
                return null;
            }
            return d.get(0);
        } catch (Exception e) {
            log.error("根据[{}]查询警组异常", jzId, e);
            return null;
        }
    }

    private Optional<SheBeiLiShiWeiZhiVo> findLastSheBei(String jzId) {
        if (StringUtils.isEmpty(jzId)) {
            return Optional.empty();
        }
        List<SheBeiLiShiWeiZhiVo> d = new ArrayList<>(0);
        Try.run(() -> {
            final var time = getCurrentDate("yyyy-MM-dd 00:00:00");
            final var sql = String.format(" xt_cjsj >= '%s' and xt_scbz = '0' and jz_id = '%s' ", time, jzId);
            int pageNum = 1;
            Tuple2<Long, List<SheBeiLiShiWeiZhiVo>> t = qinWuUtils.wzSb(pageNum, QinWuUtils.PAGE_SIZE, sql);
            final int size = t._1.intValue();
            d.addAll(t._2);
            while (size > QinWuUtils.PAGE_SIZE * pageNum) {
                pageNum += 1;
                d.addAll(qinWuUtils.wzSb(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
            }
        }).onFailure(e -> log.error("根据警组[{}]查询最后设备异常", jzId, e));
        return d.stream().max(Comparator.comparing(BaseQingWuVo::getXtCjsj));
    }

    @Override
    protected BaoBeiVo toVO(ZgBaoBeiEntity entity) {
        BaoBeiVo vo = new BaoBeiVo();
        BeanUtil.copyPropertiesIgnoreNull(entity, vo);
        vo.setName(entity.getBcMc());
        vo.setGeometries(JingWuUtils.makeGeometries("point", entity.getZb()));
        return vo;
    }

    @Override
    public String key() {
        return "ZgBaoBei";
    }

    @Override
    public String desc() {
        return "自贡巡防报备信息同步";
    }
}
