package com.trs.police.nc.monograph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.nc.monograph.dto.MonographInfoDTO;
import com.trs.police.nc.monograph.entity.MonographDeptRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonographDeptRelationMapper extends BaseMapper<MonographDeptRelation> {
    /**
     * 获取催办MonographDeptRelation
     *
     * @param monographInfoDTO 参数
     * @return 结果
     */
    List<MonographDeptRelation> selectByDeptIds(@Param("dto") MonographInfoDTO monographInfoDTO);

    /**
     * 设置催办
     *
     * @param list 催办列表
     */
    void updateList(List<MonographDeptRelation> list);
}
