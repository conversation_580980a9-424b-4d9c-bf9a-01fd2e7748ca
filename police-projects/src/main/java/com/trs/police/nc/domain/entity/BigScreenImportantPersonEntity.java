package com.trs.police.nc.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 重点人员
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Data
@TableName("tb_common_bigscreen_important_person")
public class BigScreenImportantPersonEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField
    private Date crTime = new Date();

    @TableField
    private Date updateTime = new Date();

    /**
     * 人员编号
     */
    @TableField
    private String personCode;

    /**
     * 身份号码
     */
    @TableField
    private String idCard;

    /**
     * 户籍地代码
     */
    @TableField
    private String domicilePlaceCode;

    /**
     * 出生日期
     */
    @TableField
    private String birthday;

    /**
     * 姓名
     */
    @TableField
    private String name;

    /**
     * 姓名拼音
     */
    @TableField
    private String namePinyin;

    /**
     * 民族代码
     */
    @TableField
    private String nationCode;

    /**
     * 民族名称
     */
    @TableField
    private String nationName;

    /**
     * 性别代码
     */
    @TableField
    private String genderCode;

    /**
     * 性别名称
     */
    @TableField
    private String genderName;

    /**
     * 手机号
     */
    @TableField
    private String mobilePhone;

    /**
     * 户籍地址
     */
    @TableField
    private String domicilePlaceAddress;

    /**
     * 现居地址
     */
    @TableField
    private String currentPlaceAddress;

    /**
     * 管控等级代码
     */
    @TableField
    private String controlLevelCode;

    /**
     * 管控等级名称
     */
    @TableField
    private String controlLevelName;

    /**
     * 管控状态代码
     */
    @TableField
    private String controlStatusCode;

    /**
     * 管控状态名称
     */
    @TableField
    private String controlStatusName;

    /**
     * 人员类别代码
     */
    @TableField
    private String personCategoryCode;

    /**
     * 人员类别名称
     */
    @TableField
    private String personCategoryName;

    /**
     * 人员类别代码树，方便检索，格式-PGAOHM-jtk8uj-WPFDII-
     */
    @TableField
    private String personCategoryTree;

    /**
     * 责任派出所代码
     */
    @TableField
    private String policeOrgCode;

    /**
     * 责任派出所名称
     */
    @TableField
    private String policeOrgName;

    /**
     * 责任派出所标识符
     */
    @TableField
    private String policeOrgIdentifier;

    /**
     * 责任民警ID
     */
    @TableField
    private String policeResponsibleStaffId;

    /**
     * 责任民警证件ID
     */
    @TableField
    private String policeResponsibleStaffCardId;

    /**
     * 责任民警职位名称
     */
    @TableField
    private String policeResponsibleStaffPositionName;

    /**
     * 责任民警电话
     */
    @TableField
    private String policeResponsibleStaffPhone;

    /**
     * 政府地址代码
     */
    @TableField
    private String govAddressCode;

    /**
     * 政府责任人姓名
     */
    @TableField
    private String govResponsibleStaffName;

    /**
     * 政府责任人职位
     */
    @TableField
    private String govResponsibleStaffPosition;

    /**
     * 政府责任人电话
     */
    @TableField
    private String govResponsibleStaffPhone;

    /**
     * 人员标签代码
     */
    @TableField
    private String personnelTagsCode;

    /**
     * 人员标签名称
     */
    @TableField
    private String personnelTagsName;

    /**
     * 创建用户ID
     */
    @TableField
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField
    private String createUserName;

    /**
     * 创建终端类型
     */
    @TableField
    private Integer createTerminalType;

    /**
     * 创建终端标识
     */
    @TableField
    private String createTerminalIdentify;

    /**
     * 创建用户组织代码
     */
    @TableField
    private String createUserOrgCode;

    /**
     * 创建用户组织名称
     */
    @TableField
    private String createUserOrgName;

    /**
     * 创建时间
     */
    @TableField
    private Date createTime;

    /**
     * 修改用户ID
     */
    @TableField
    private String modifyUserId;

    /**
     * 修改用户名
     */
    @TableField
    private String modifyUserName;

    /**
     * 修改终端类型
     */
    @TableField
    private Integer modifyTerminalType;

    /**
     * 修改终端标识
     */
    @TableField
    private String modifyTerminalIdentify;

    /**
     * 修改用户组织代码
     */
    @TableField
    private String modifyUserOrgCode;

    /**
     * 修改用户组织名称
     */
    @TableField
    private String modifyUserOrgName;

    /**
     * 修改时间
     */
    @TableField
    private Date modifyTime;

    /**
     * 是否有效
     */
    @TableField
    private Integer valid;

    /**
     * 责任民警姓名
     */
    @TableField
    private String policeResponsibleStaffName;

    /**
     * 证件类型代码
     */
    @TableField
    private String idTypeCode;

    /**
     * 证件类型名称
     */
    @TableField
    private String idTypeName;

    /**
     * 户籍地名称
     */
    @TableField
    private String domicilePlaceName;

    /**
     * 政府地址名称
     */
    @TableField
    private String govAddressName;

    /**
     * 最后一次失控时间
     */
    @TableField
    private String lastUncontrolledTime;

    /**
     * 警种代码
     */
    @TableField
    private String policeCategoryCode;

    /**
     * 警种名称
     */
    @TableField
    private String policeCategoryName;

    /**
     * 车牌号
     */
    @TableField
    private String licensePlateNumber;

    /**
     * 所属区域名称
     */
    @TableField
    private String belongAreaName;

    /**
     * 所属区域代码
     */
    @TableField
    private String belongAreaCode;

    /**
     * 人员积分
     */
    @TableField
    private Integer score;

    /**
     * 最近发生地点
     */
    @TableField
    private String recentOccurrenceLocation;

    /**
     * 根据地
     */
    @TableField
    private String foothold;

    /**
     * 责任区县代码
     */
    @TableField
    private String dutyCountyCode;

    /**
     * 责任区县名称
     */
    @TableField
    private String dutyCountyName;

    /**
     * 职能
     */
    @TableField
    private String functionary;

    /**
     * 公司名称
     */
    @TableField
    private String companyName;

    /**
     * 警种类别组织代码
     */
    @TableField
    private String policeCategoryOrgCode;

    /**
     * 警种类别组织标识符
     */
    @TableField
    private String policeCategoryOrgIdentifier;

    /**
     * 警种类别组织名称
     */
    @TableField
    private String policeCategoryOrgName;

    /**
     * 通知管控
     */
    @TableField
    private Integer noticeControl;

    /**
     * 人员id
     */
    @TableField
    private String personId;

    /**
     * 文件id
     */
    @TableField
    private String fileId;

    /**
     * 文件名称
     */
    @TableField
    private String fileName;

    /**
     * 文件类型，AVATAR：证件照片， ATTACHMENT：其他照片
     */
    @TableField
    private String businessFiledName;
}
