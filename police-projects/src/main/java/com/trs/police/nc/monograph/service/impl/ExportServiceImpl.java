package com.trs.police.nc.monograph.service.impl;

import com.alibaba.fastjson.JSON;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.DataUtils;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.nc.monograph.bean.MonographInitContext;
import com.trs.police.nc.monograph.bean.TimeInfo;
import com.trs.police.nc.monograph.constant.FillMode;
import com.trs.police.nc.monograph.entity.Monograph;
import com.trs.police.nc.monograph.entity.MonographDirectory;
import com.trs.police.nc.monograph.entity.MonographDirectoryDeptRelation;
import com.trs.police.nc.monograph.entity.MonographTemplate;
import com.trs.police.nc.monograph.html.tag.HtmlToTextRenderPolicy;
import com.trs.police.nc.monograph.html.tag.MyHtmlRenderPolicy;
import com.trs.police.nc.monograph.mpservice.MonographDirectoryDeptRelationMpService;
import com.trs.police.nc.monograph.mpservice.MonographDirectoryMpService;
import com.trs.police.nc.monograph.mpservice.MonographMpService;
import com.trs.police.nc.monograph.mpservice.MonographTemplateMpService;
import com.trs.police.nc.monograph.service.ExportService;
import com.trs.police.nc.monograph.service.FileService;
import com.trs.police.nc.monograph.service.TemplateService;
import com.trs.police.nc.monograph.vo.DirectoryConfig;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导出的实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ExportServiceImpl implements ExportService {

    @Autowired
    private MonographDirectoryMpService monographDirectoryMpService;

    @Autowired
    private MonographMpService monographMpService;

    @Autowired
    private MonographDirectoryDeptRelationMpService directoryDeptRelationMpService;

    @Autowired
    private MonographTemplateMpService monographTemplateMpService;

    @Autowired
    private List<FileService> fileServiceList;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private TemplateService templateService;

    @Override
    public void export(Long monographId) {
        Monograph monograph = monographMpService.getById(monographId);
        // 构造导出的参数列表
        Map<String, String> paramsMap = buildExportParamsMap(monograph);
        // 上下文参数
        MonographInitContext context = new MonographInitContext(
                monograph.getCycleStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                monograph.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                monograph.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                monographTemplateMpService.getById(monograph.getTemplateId()),
                permissionService.findCurrentUser(monograph.getCreateUserId(), monograph.getCreateDeptId()),
                monograph.getCurrentPeriod()
        );
        context.setMonograph(monograph);
        Date pubTime = Optional.ofNullable(monograph.getPublishTime()).orElse(monograph.getEndTime());
        context.setPublishTimeInfo(new TimeInfo(pubTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()));
        context.convertToParams().forEach(paramsMap::put);
        // 表头html模板参数
        templateService.templateValue(context.getMonographTemplate().getHeaderParamsMap(), context)
                .forEach((key, value) -> {
                    paramsMap.put(key.replaceAll("[{}]", ""), value);
                });
        // 导出
        doExport(monograph, paramsMap);
    }

    @Override
    public Boolean accept(Monograph monograph) {
        MonographTemplate byId = monographTemplateMpService.getById(monograph.getTemplateId());
        return FillMode.COLLABORATION.equals(FillMode.ofCode(byId.getFillMode()).orElse(FillMode.COLLABORATION));
    }

    private Map<String, String> buildExportParamsMap(Monograph monograph) {
        // 查询专刊目录
        List<MonographDirectory> direct = monographDirectoryMpService.lambdaQuery()
                .eq(MonographDirectory::getTemplateId, monograph.getTemplateId())
                .list();
        // 查询目录的填写情况
        List<Long> directId = direct.stream().map(MonographDirectory::getId).collect(Collectors.toList());
        List<MonographDirectoryDeptRelation> deptDirect = CollectionUtils.isEmpty(direct)
                ? new ArrayList<>()
                : directoryDeptRelationMpService.lambdaQuery()
                    .in(MonographDirectoryDeptRelation::getDirectoryId, directId)
                    .eq(MonographDirectoryDeptRelation::getMonographId, monograph.getId())
                    .list();
        Function<MonographDirectory, Optional<MonographDirectoryDeptRelation>> findDeptRelation = DataUtils.findSingleByT(
                direct,
                d -> deptDirect,
                (di, de) -> di.getId().equals(de.getDirectoryId()),
                MonographDirectory::getId
        );
        // 生成目录模板参数值map
        Map<String, String> map = new HashMap<>();
        for (MonographDirectory directory : direct) {
            if (Objects.nonNull(directory.getTemplateKey())) {
                map.put(directory.getTemplateKey(), findDeptRelation.apply(directory).map(MonographDirectoryDeptRelation::getDirectoryContent).orElse(""));
            }
        }
        return map;
    }

    private void doExport(Monograph monograph, Map<String, String> paramsMap) {
        // 查询专刊目录
        List<MonographDirectory> direct = monographDirectoryMpService.lambdaQuery()
                .eq(MonographDirectory::getTemplateId, monograph.getTemplateId())
                .list();
        // 构造导出的配置
        MyHtmlRenderPolicy htmlRenderPolicy = new MyHtmlRenderPolicy();
        ConfigureBuilder builder = Configure.builder();
        direct.forEach(d -> {
            DirectoryConfig config = StringUtils.isEmpty(d.getDirectoryConfig()) ? new DirectoryConfig() : JSON.parseObject(d.getDirectoryConfig(), DirectoryConfig.class);
            if (!Boolean.TRUE.equals(config.getNormalText())) {
                builder.bind(d.getTemplateKey(), htmlRenderPolicy);
            } else {
                builder.bind(d.getTemplateKey(), new HtmlToTextRenderPolicy());
            }
        });
        // 获取导出模板
        MonographTemplate template = monographTemplateMpService.getById(monograph.getTemplateId());
        FileService fileService = fileServiceList.stream()
                .filter(fs -> Objects.equals(fs.type(), template.getTemplateDocPathType()))
                .findAny()
                .get();
        // 导出
        try (
                InputStream inputStream = fileService.getFile(template.getTemplateDocPath());
                XWPFTemplate exportTemplate = XWPFTemplate.compile(inputStream, builder.build()).render(paramsMap);
        ) {
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            ServletOutputStream outputStream = null;
            try {
                response.reset();
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode(monograph.getName(), "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
                outputStream = response.getOutputStream();
                exportTemplate.writeAndClose(outputStream);
            } catch (Exception e) {
                Try.run(() -> {
                    response.reset();
                    response.setContentType("application/json");
                    response.setCharacterEncoding("utf-8");
                    response.getWriter().println(JSON.toJSONString(RestfulResults.error(e.getMessage())));
                }).onFailure(throwable -> log.error("重置返回异常", throwable));
                log.error("导出异常", e);
            } finally {
                if (null != outputStream) {
                    outputStream.close();
                }
            }
        } catch (Exception e) {
            log.error("导出失败", e);
        }
    }
}
