package com.trs.police.guangan.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.common.pojo.BaseVO;
import lombok.Data;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/18 18:47
 * @since 1.0
 */
@Data
public class ChongQingDataRespVo extends BaseVO {

    @JsonProperty("ResponseParam")
    private ResponseParam responseParam;

    @JsonProperty("MessageStatus")
    private String messageStatus;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("MessageSequence")
    private String messageSequence;

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2024</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2024/9/18 19:05
     * @since 1.0
     */
    @Data
    public static class ResponseParam {

        @JsonProperty("ResourceInfos")
        private List<ResourceInfos> resourceInfos;
    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2024</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2024/9/18 19:05
     * @since 1.0
     */
    @Data
    public static class ResourceInfos {

        @JsonProperty("ResourceName")
        private String resourceName;

        @JsonProperty("DataInfo")
        private List<List<String>> dataInfo;

        @JsonProperty("DataItems")
        private List<DataItems> dataItems;

    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2024</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2024/9/18 19:06
     * @since 1.0
     */
    @Data
    public static class DataItems {

        @JsonProperty("Name")
        private String name;
    }
}
