package com.trs.police.guangan.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/18 18:06
 * @since 1.0
 */
@Data
@TableName("tb_projects_guangan_chong_qing_data_entity")
public class ChongQingDataEntity implements Serializable {

    @TableField
    private String fhxtsjwybsid;

    @TableField
    private String sjcjlyxt;

    @TableField
    private String sjcjlyxtbz;

    @TableField
    private String sjcjlybm;

    @TableField
    private String sjcjlybm1;

    @TableField
    private String sjcjlyd;

    @TableField
    private String cjsj;

    @TableField
    private String ysxtjrzj;

    @TableField
    private String gxsj;

    @TableField
    private String xxscPdbz;

    @TableField
    private String sjmgjbbm;

    @TableField
    private String sjkhsbsf;

    @TableField
    private String ywbqbs;

    @TableField
    private String xwbqbs;

    @TableField
    private String zhujiXxid;

    @TableField
    private String swyjXxbh;

    @TableField
    private String dwmc;

    @TableField
    private String jdchphm;

    @TableField
    private String yotXxdm02;

    @TableField
    private String yotMc;

    @TableField
    private String clPpxh;

    @TableField
    private String clysMc;

    @TableField
    private String lxdh;

    @TableField
    private String sbeWybs;

    @TableField
    private String sbeLb01Xxdm02;

    @TableField
    private String sbeXh;

    @TableField
    private String sbePpXhXxid;

    @TableField
    private String sbeGlReyGmsfhm;

    @TableField
    private String sbeGlReyJh;

    @TableField
    private String sbeLb01Mc;

    @TableField
    private String lxrMc;

    @TableField
    private String cphXxbh;

    @TableField
    private String sbeSsChasMc;

    @TableField
    private String sbeSsChasXxid;

    @TableField
    private String jdccllxdm;

    @TableField
    private String cllxMc;

    @TableField
    private String sbeGlr1JihaXxbh;

    @TableField
    private Date crTime = new Date();
}
