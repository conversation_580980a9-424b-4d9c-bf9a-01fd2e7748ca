package com.trs.police.guangan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.guangan.domain.entity.ChongQingDataEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/18 18:11
 * @since 1.0
 */
@Mapper
public interface ChongQingDataMapper extends BaseMapper<ChongQingDataEntity> {

    /**
     * deleteByClassAndCode<BR>
     *
     * @param deviceClass 参数
     * @param deviceCode  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/18 19:51
     */
    @Delete("delete from tb_projects_guangan_chong_qing_data_entity where sbe_lb01_xxdm02=#{deviceClass} and sbe_wybs=#{deviceCode}")
    Integer deleteByClassAndCode(
            @Param("deviceClass") Integer deviceClass,
            @Param("deviceCode") String deviceCode
    );

    /**
     * addData<BR>
     *
     * @param keys   参数
     * @param values 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/18 19:55
     */
    Integer addData(
            @Param("keys") List<String> keys,
            @Param("values") List<String> values
    );
}
