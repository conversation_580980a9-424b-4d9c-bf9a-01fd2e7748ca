package com.trs.police.ga.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.zg.vo.XunFangBaoBeiZhongDuanVo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 勤务管理-巡防报备-终端
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2025/4/1 17:27
 * @version 1.0
 * @since 1.0
 */
@Data
@TableName("v_tb_qwgl_xfbb_zd")
public class GaXunFangBaoBeiZhongDuanEntity implements Serializable {

    @TableId
    private String id;

    @TableField
    private String xfbbId;

    /**
     * 设备编号
     */
    @TableField
    private String sbbh;

    @TableField
    private String sbmc;

    @TableField
    private String sblx;

    @TableField
    private String sblb;

    @TableField
    private String glgpsid;

    @TableField
    private String xtSjly;

    @TableField
    private String xtSjzt;

    @TableField
    private String xtScbz;

    @TableField
    private String xtCjip;

    @TableField
    private Timestamp xtCjsj;

    @TableField
    private String xtCjrId;

    @TableField
    private String xtCjr;

    @TableField
    private String xtCjbmdm;

    @TableField
    private String xtCjbmmc;

    @TableField
    private String xtZhgxip;

    @TableField
    private Timestamp xtZhgxsj;

    @TableField
    private String xtZhgxrid;

    @TableField
    private String xtZhgxr;

    @TableField
    private String xtZhgxbmdm;

    @TableField
    private String xtZhgxbm;

    @TableField
    private String bz;

    /**
     * toVo<Br/>
     *
     * @param entity 参数
     * @return 结果
     */
    public static XunFangBaoBeiZhongDuanVo toVo(GaXunFangBaoBeiZhongDuanEntity entity) {
        XunFangBaoBeiZhongDuanVo vo = new XunFangBaoBeiZhongDuanVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
