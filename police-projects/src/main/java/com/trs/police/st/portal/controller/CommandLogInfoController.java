package com.trs.police.st.portal.controller;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.st.portal.domain.dto.request.AddCommandLogInfoRequest;
import com.trs.police.st.portal.domain.dto.response.GetCommandLogInfoListResponse;
import com.trs.police.st.portal.domain.dto.response.GetCommandLogInfoResponse;
import com.trs.police.st.portal.service.CommandLogInfoService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 新的指挥日志接口
 */

@RequestMapping("/command/log")
@RestController
public class CommandLogInfoController {

    @Resource
    private CommandLogInfoService commandLogInfoService;

    /**
     * 保存指挥日志
     *
     * @param request 请求参数
     */
    @PostMapping("")
    public void createCommandLogInfo(@RequestBody AddCommandLogInfoRequest request) {
        commandLogInfoService.createCommandLogInfo(request);
    }

    /**
     * 获取指挥日志详情
     *
     * @param id   id
     * @param date 日期
     * @return 指挥日志详情
     */
    @GetMapping("")
    public RestfulResultsV2<GetCommandLogInfoResponse> getCommandLogInfo(
            @RequestParam("id") Long id,
            @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return commandLogInfoService.getCommandLogInfo(id, date);
    }

    /**
     * 获取指挥日志列表
     *
     * @param pageParams 分页参数
     * @param date 日期
     * @return 指挥日志列表
     */
    @GetMapping("/list")
    public RestfulResultsV2<GetCommandLogInfoListResponse> getCommandLogInfoList(
            PageParams pageParams,
            @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return commandLogInfoService.getCommandLogInfoList(pageParams, date);
    }

    /**
     * 导入上篇内容
     *
     * @param date 本篇日期
     * @param type 内容类型
     * @return 上篇内容
     */
    @GetMapping("/content/last")
    public RestfulResultsV2<String> getLastContent(
            @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date,
            @RequestParam("type") Integer type) {
        return commandLogInfoService.getLastContent(date, type);
    }

    /**
     * 获取已提交指挥日志的日期
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 返回列表
     */
    @GetMapping("/exists/of")
    public RestfulResultsV2<LocalDate> getLogExists(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return commandLogInfoService.getLogExists(startDate, endDate);
    }

    /**
     * 获取接警情况处置情况/备注历史记录
     *
     * @param type       类型，disposal：处置情况 remark：备注
     * @param pageParams 分页参数
     * @param date       日期
     * @return 处置情况历史记录列表
     */
    @GetMapping("/incident/{type}/history")
    public RestfulResultsV2<String> getIncidentDisposalHistory(
            @PathVariable("type") String type,
            PageParams pageParams,
            @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return commandLogInfoService.getIncidentDisposalHistory(type, pageParams, date);
    }
}
