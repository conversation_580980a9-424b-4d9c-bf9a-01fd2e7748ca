package com.trs.police.st.portal.service;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.st.portal.domain.dto.request.AddCommandLogInfoRequest;
import com.trs.police.st.portal.domain.dto.response.GetCommandLogInfoListResponse;
import com.trs.police.st.portal.domain.dto.response.GetCommandLogInfoResponse;
import com.trs.web.builder.base.RestfulResultsV2;

import java.time.LocalDate;

/**
 * 新的指挥日志service
 */
public interface CommandLogInfoService {

    /**
     * 保存指挥日志
     *
     * @param request 请求参数
     */
    void createCommandLogInfo(AddCommandLogInfoRequest request);

    /**
     * 获取指挥日志详情
     *
     * @param id   id
     * @param date 日期
     * @return 指挥日志详情
     */
    RestfulResultsV2<GetCommandLogInfoResponse> getCommandLogInfo(Long id, LocalDate date);

    /**
     * 获取指挥日志列表
     *
     * @param pageParams 分页参数
     * @param date       日期
     * @return 指挥日志列表
     */
    RestfulResultsV2<GetCommandLogInfoListResponse> getCommandLogInfoList(PageParams pageParams, LocalDate date);

    /**
     * 导入上篇内容
     *
     * @param date 本篇日期
     * @param type 内容类型
     * @return 上篇内容
     */
    RestfulResultsV2<String> getLastContent(LocalDate date, Integer type);

    /**
     * 获取指挥日志是否存在
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 返回列表
     */
    RestfulResultsV2<LocalDate> getLogExists(LocalDate startDate, LocalDate endDate);

    /**
     * 获取接警情况处置情况历史记录
     *
     * @param type       类型，disposal：处置情况 remark：备注
     * @param pageParams 分页参数
     * @param date       日期
     * @return 处置情况历史记录列表
     */
    RestfulResultsV2<String> getIncidentDisposalHistory(String type, PageParams pageParams, LocalDate date);

    /**
     * 获取接警情况备注历史记录
     *
     * @param pageParams 分页参数
     * @param date       日期
     * @return 处置情况历史记录列表
     */
    RestfulResultsV2<String> getIncidentRemarkHistory(PageParams pageParams, LocalDate date);
}
