package com.trs.police.st.portal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.police.st.portal.domain.entity.CommandLogReceiveIncidentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;

/**
 * 指挥日志接警情况mapper
 */

@Mapper
public interface CommandLogReceiveIncidentMapper extends BaseMapper<CommandLogReceiveIncidentEntity> {

    /**
     * 查询处置情况历史记录
     *
     * @param page 分页参数
     * @param type 类型
     * @param date 日期
     * @return 处置情况
     */
    @Select("select ri.#{type} from t_command_log_receive_incident ri join t_command_log_info i on ri.info_id = i.id\n" +
            "where i.date < #{date} order by i.date desc, ri.receive_time desc")
    IPage<String> getDisposalHistory(IPage<CommandLogReceiveIncidentEntity> page, @Param("type") String type, @Param("date") LocalDate date);

    /**
     * 查询备注历史记录
     *
     * @param page 分页参数
     * @param date 日期
     * @return 处置情况
     */
    @Select("select ri.remark from t_command_log_receive_incident ri join t_command_log_info i on ri.info_id = i.id\n" +
            "where i.date < #{date} order by i.date desc, ri.receive_time desc")
    IPage<String> getRemarkHistory(IPage<CommandLogReceiveIncidentEntity> page, @Param("date") LocalDate date);

}
