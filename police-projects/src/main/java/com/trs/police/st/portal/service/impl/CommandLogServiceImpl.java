package com.trs.police.st.portal.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.TimeUtils;
import com.trs.police.bigscreen.service.impl.BigScreenServiceImpl;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.DateUtil;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.utils.ThreadPoolExecutorServiceUtil;
import com.trs.police.st.portal.domain.dto.WritingLogDTO;
import com.trs.police.st.portal.domain.dto.WritingLogSearchDTO;
import com.trs.police.st.portal.domain.entity.CommandLogDispatchDocEntity;
import com.trs.police.st.portal.domain.entity.CommandLogYyqEntity;
import com.trs.police.st.portal.domain.vo.CommandLogDetailVO;
import com.trs.police.st.portal.helper.LogDetailExportHelper;
import com.trs.police.st.portal.service.*;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @author: dingkeyu
 * @date: 2024/11/27
 * @description:
 */
@Slf4j
@Service
public class CommandLogServiceImpl implements CommandLogService {

    @Autowired
    private CommandLogRelatedInfoService commandLogRelatedInfoService;

    @Autowired
    private CommandLogDispatchDocService commandLogDispatchDocService;

    @Autowired
    private CommandLogYyqService commandLogYyqService;

    @Autowired
    private List<AbsCommandLogAnalysis> commandLogAnalyses;

    @Autowired
    private BigScreenServiceImpl bigScreenService;

    @Autowired
    private LogDetailExportHelper logDetailExportHelper;

    private Map<String, AbsCommandLogAnalysis> commandLogMap = new HashMap<>();

    /**
     * init
     */
    @PostConstruct
    public void init() {
        for (AbsCommandLogAnalysis logAnalysis : commandLogAnalyses) {
            commandLogMap.put(logAnalysis.key(), logAnalysis);
        }
    }

    @Override
    public void writingLog(WritingLogDTO dto) {
        AbsCommandLogAnalysis logAnalysis = commandLogMap.get(dto.getOperateType());
        PreConditionCheck.checkArgument(Objects.nonNull(logAnalysis), "未知的操作类型");
        try {
            logAnalysis.writingLog(dto);
        } catch (Exception e) {
            log.error(String.format("【%s】写日志操作失败：", logAnalysis.desc()), e);
            throw new TRSException(String.format("【%s】写日志操作失败", logAnalysis.desc()));
        }
    }


    @Override
    public RestfulResultsV2 getWritingLog(WritingLogSearchDTO dto) {
        AbsCommandLogAnalysis logAnalysis = commandLogMap.get(dto.getOperateType());
        PreConditionCheck.checkArgument(Objects.nonNull(logAnalysis), "未知的操作类型");
        RestfulResultsV2<String> results = RestfulResultsV2.ok("");
        try {
            results =logAnalysis.getWritingLog(dto);
        } catch (Exception e) {
            log.error(String.format("【%s】获取日志内容失败：", logAnalysis.desc()), e);
            throw new TRSException(String.format("【%s】获取日志内容失败", logAnalysis.desc()));
        }
        return results;
    }

    @Override
    public void commandLogDelete(WritingLogSearchDTO dto) {
        AbsCommandLogAnalysis logAnalysis = commandLogMap.get(dto.getOperateType());
        PreConditionCheck.checkArgument(Objects.nonNull(logAnalysis), "未知的操作类型");
        logAnalysis.delete(dto.getId());
    }

    @Override
    public void doSwitch(WritingLogDTO dto) {
        PreConditionCheck.checkArgument(Objects.nonNull(dto.getId()), "id不能为空");
        PreConditionCheck.checkArgument(Objects.nonNull(dto.getOperateType()), "操作类型不能为空");
        AbsCommandLogAnalysis logAnalysis = commandLogMap.get(dto.getOperateType());
        PreConditionCheck.checkArgument(Objects.nonNull(logAnalysis), "未知的操作类型");
        String sql = String.format("%s = NOT %s", StringUtil.convertToUnderscore(dto.getSwitchType()), StringUtil.convertToUnderscore(dto.getSwitchType()));
        switch (logAnalysis.key()) {
            case "dutyDispatchDoc":
                commandLogDispatchDocService.lambdaUpdate()
                        .setSql(sql)
                        .eq(CommandLogDispatchDocEntity::getId, dto.getId())
                        .update();
                return;
            case "dutyYyq":
                commandLogYyqService.lambdaUpdate()
                        .setSql(sql)
                        .eq(CommandLogYyqEntity::getId, dto.getId())
                        .update();
                return;
            default:
                return;
        }
    }

    @Override
    public RestfulResultsV2<CommandLogDetailVO> commandLogDetail(WritingLogSearchDTO dto) {
        CommandLogDetailVO detailVo = new CommandLogDetailVO();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (AbsCommandLogAnalysis commandLog: commandLogAnalyses) {
            futures.add(CompletableFuture.runAsync(() ->{
                try {
                    commandLog.commandLogDetail(dto, detailVo);
                }catch (Exception e) {
                    log.error(String.format("获取【%s】内容失败：", commandLog.desc()), e);
                }
            }, ThreadPoolExecutorServiceUtil.getInstance()));
        }
        //等待全部完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return RestfulResultsV2.ok(detailVo);
    }

    @Override
    public void exportDetail(HttpServletResponse response, WritingLogSearchDTO dto) {
        //String fileName = dto.getDutyTime() + "值班日志.docx";
        String fileName = TimeUtils.stringToString(dto.getDutyTime(), "MM月dd日") + "交接班关注.docx";
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/x-download");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dutyTime", TimeUtils.stringToString(dto.getDutyTime(), "MM月dd日"));
        dataMap.put("dutyTimeV1", TimeUtils.dateBefOrAft(dto.getDutyTime(), 1,"MM月dd日"));
        dataMap.put("week", DateUtil.getDayOfWeek(dto.getDutyTime()));
        try {
            XWPFDocument document = logDetailExportHelper.getDocumentV2(dataMap, dto);
            document.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出{}指挥日志失败：", dto.getDutyTime(), e);
        }

    }

    @Override
    public void export(HttpServletResponse response, WritingLogSearchDTO dto) {
        AbsCommandLogAnalysis logAnalysis = commandLogMap.get(dto.getOperateType());
        PreConditionCheck.checkArgument(Objects.nonNull(logAnalysis), "未知的操作类型");
        logAnalysis.export(response, dto);
    }
}
