package com.trs.police.st.portal.controller;

import com.trs.police.st.portal.domain.dto.WritingLogDTO;
import com.trs.police.st.portal.domain.dto.WritingLogSearchDTO;
import com.trs.police.st.portal.domain.vo.CommandLogDetailVO;
import com.trs.police.st.portal.service.CommandLogService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: dingkeyu
 * @date: 2024/11/27
 * @description: 指挥日志controller
 */
@RestController
@RequestMapping(value = {"/commandLog", "/public/commandLog"})
public class CommandLogController {

    @Autowired
    private CommandLogService commandLogService;

    /**
     * 写日志
     *
     * @param dto dto
     */
    @PostMapping("/writingLog")
    public void writingLog(@RequestBody WritingLogDTO dto){
        commandLogService.writingLog(dto);
    }

    /**
     * 获取日志内容
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}
     */
    @GetMapping("/getWritingLog")
    public RestfulResultsV2 getWritingLog(WritingLogSearchDTO dto) {
        return commandLogService.getWritingLog(dto);
    }

    /**
     * 删除收发文信息
     *
     * @param dto dto
     */
    @PostMapping("/delete")
    public void commandLogDelete(@RequestBody WritingLogSearchDTO dto) {
        commandLogService.commandLogDelete(dto);
    }

    /**
     * 日志开关操作
     *
     * @param dto dto
     */
    @PostMapping("/doSwitch")
    public void doSwitch(@RequestBody WritingLogDTO dto) {
        commandLogService.doSwitch(dto);
    }

    /**
     * 指挥日志详情
     *
     * @param dto dto
     * @return {@link RestfulResultsV2}<{@link CommandLogDetailVO}>
     */
    @GetMapping("/detail")
    public RestfulResultsV2<CommandLogDetailVO> commandLogDetail(WritingLogSearchDTO dto) {
        return commandLogService.commandLogDetail(dto);
    }

    /**
     * 指挥日志详情导出
     *
     * @param response response
     * @param dto dto
     */
    @GetMapping("/detail/export")
    public void exportDetail(HttpServletResponse response, WritingLogSearchDTO dto) {
        commandLogService.exportDetail(response, dto);
    }

    /**
     * 指挥日志单项导出
     *
     * @param response response
     * @param dto dto
     */
    @GetMapping("/export")
    public void export(HttpServletResponse response, WritingLogSearchDTO dto) {
        commandLogService.export(response, dto);
    }
}
