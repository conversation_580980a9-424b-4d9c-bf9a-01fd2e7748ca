package com.trs.police.lz.authCenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.lz.authCenter.domain.dto.AuthCenterDto;
import com.trs.police.lz.authCenter.domain.entity.AuthCenterBackendManage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/04/01
 */
@Mapper
public interface AuthCenterBackendManageMapper extends BaseMapper<AuthCenterBackendManage> {

    /**
     * 后台管理查询
     *
     * @param dto dto
     * @param page 分页
     * @return AuthCenterVO vo
     */
    Page<AuthCenterBackendManage> queryList(@Param("dto") AuthCenterDto dto, Page<Objects> page);

    /**
     * 查询当前id前一条数据
     *
     * @param id id
     * @param type type
     * @return AuthCenterVO vo
     */
    AuthCenterBackendManage selectPrevious(@Param("id") Integer id,@Param("type") Integer type);

    /**
     * 查询当前id后一条数据
     *
     * @param id id
     * @param type type
     * @return AuthCenterVO vo
     */
    AuthCenterBackendManage selectNext(@Param("id") Integer id,@Param("type") Integer type);
}
