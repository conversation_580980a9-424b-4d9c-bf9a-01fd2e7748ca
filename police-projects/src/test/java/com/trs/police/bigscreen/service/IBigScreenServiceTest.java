package com.trs.police.bigscreen.service;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.TimeUtils;
import com.trs.police.bigscreen.domain.dto.AreaSearchDTO;
import com.trs.police.bigscreen.domain.dto.ModifyDutyUsersDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
class IBigScreenServiceTest {

    @Resource
    private IBigScreenService service;

    @Test
    void modifyDutyUsers() throws ServiceException {
        ModifyDutyUsersDTO dto = ModifyDutyUsersDTO.builder()
                .dutyTime(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD))
                .unitName("TRS")
                .districtCode("510000")
                .force(true)
                .data(List.of(
                        ModifyDutyUsersDTO.DutyUserDataDTO.builder()
                                .policeKind(0)
                                .name("TRS1")
                                .jh("TRS001")
                                .dh("13412341234")
                                .nature("值班领导")
                                .level("主班")
                                .build(),
                        ModifyDutyUsersDTO.DutyUserDataDTO.builder()
                                .policeKind(0)
                                .name("TRS2")
                                .nature("值班领导")
                                .level("副班")
                                .build(),
                        ModifyDutyUsersDTO.DutyUserDataDTO.builder()
                                .policeKind(0)
                                .name("TRS3")
                                .nature("值班领导")
                                .level("主班")
                                .build(),
                        ModifyDutyUsersDTO.DutyUserDataDTO.builder()
                                .policeKind(0)
                                .name("TRS5")
                                .nature("指挥长")
                                .level("副班")
                                .build()
                )).build();
        service.modifyDutyUsers(dto);

        dto.setForce(false);
        dto.setData(
                List.of(
                        ModifyDutyUsersDTO.DutyUserDataDTO.builder()
                                .policeKind(0)
                                .name("TRS4")
                                .nature("指挥长")
                                .level("主班")
                                .build()
                )
        );
        service.modifyDutyUsers(dto);
    }

    @Test
    void test(){
        AreaSearchDTO dto = new AreaSearchDTO();
        dto.setDistrictCode("511300");
        service.areaList(dto);
    }
}