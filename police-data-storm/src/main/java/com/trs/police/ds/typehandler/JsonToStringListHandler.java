package com.trs.police.ds.typehandler;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.ds.utils.JsonUtil;
import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * json转List Long
 *
 * <AUTHOR>
 * @date 2022/6/22 10:00
 */
public class JsonToStringListHandler extends JacksonTypeHandler {

    @Override
    protected List<String> parse(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        return JsonUtil.parseArray(json, String.class);
    }

    public JsonToStringListHandler(Class<?> type) {
        super(type);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return rs.getString(columnName) == null ? List.of() : parse(rs.getString(columnName));
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return rs.getString(columnIndex) == null ? List.of() : parse(rs.getString(columnIndex));
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return cs.getString(columnIndex) == null ? List.of() : parse(cs.getString(columnIndex));
    }
}
