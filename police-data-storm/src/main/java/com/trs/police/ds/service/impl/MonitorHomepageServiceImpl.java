package com.trs.police.ds.service.impl;

import com.trs.police.ds.domain.request.KeyValueTypeVO;
import com.trs.police.ds.domain.request.MonitorRequest;
import com.trs.police.ds.domain.request.TimeParams;
import com.trs.police.ds.domain.vo.homepage.MonitorTotalVO;
import com.trs.police.ds.domain.vo.homepage.TrackVO;
import com.trs.police.ds.mapper.MonitorHomepageMapper;
import com.trs.police.ds.service.MonitorHomepageService;
import com.trs.police.ds.utils.JsonUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 布控大屏
 *
 * <AUTHOR>
 */
@Service
public class MonitorHomepageServiceImpl implements MonitorHomepageService {

    @Resource
    private MonitorHomepageMapper monitorHomepageMapper;


    @Override
    public List<KeyValueTypeVO> countLevel(TimeParams filterParams) {
        return monitorHomepageMapper.countMonitorLevel(filterParams);
    }

    @Override
    public List<KeyValueTypeVO> countPersonLabel(MonitorRequest filterParams) {
        List<Long> labelIds = monitorHomepageMapper.getPersonLabels("person");
        return monitorHomepageMapper.countMonitorLabel(labelIds, filterParams.getTimeParams(), filterParams.getLevel());
    }

    @Override
    public List<KeyValueTypeVO> countGroupLabel(MonitorRequest filterParams) {
        List<Long> labelIds = monitorHomepageMapper.getPersonLabels("group");
        return monitorHomepageMapper.countMonitorLabel(labelIds, filterParams.getTimeParams(), filterParams.getLevel());
    }

    @Override
    public List<KeyValueTypeVO> countArea(MonitorRequest filterParams) {
        List<String> districtCodes = monitorHomepageMapper.getDistrictCode();
        List<KeyValueTypeVO> result =
            monitorHomepageMapper.countMonitorByArea(districtCodes, filterParams.getTimeParams(), filterParams.getLabel());
        result.forEach(vo -> vo.setKey(vo.getKey().replace("德阳市", "")));
        return result;
    }

    @Override
    public List<KeyValueTypeVO> countModel(MonitorRequest filterParams) {
        List<Long> modelIds = monitorHomepageMapper.getPersonModelIds();
        return monitorHomepageMapper.countMonitorByModel(modelIds, filterParams.getTimeParams(), filterParams.getLabel());
    }

    @Override
    public List<TrackVO> countHaveTrack(MonitorRequest filterParams) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime beginTime = endTime.minusDays(2).toLocalDate().atStartOfDay();
        List<Long> trackIds = monitorHomepageMapper.getHaveTrackPerson(filterParams.getLevel(), beginTime, endTime)
            .stream().filter(Objects::nonNull).collect(Collectors.toList());
        return monitorHomepageMapper.getPersonTracks(trackIds);
    }

    @Override
    public List<TrackVO> countNoTrack(MonitorRequest filterParams) {
        LocalDateTime beginTime = LocalDateTime.now().minusDays(2).toLocalDate().atStartOfDay();
        List<Long> trackIds = monitorHomepageMapper.getNoTrackPerson(filterParams.getLevel(), beginTime)
            .stream().filter(Objects::nonNull).collect(Collectors.toList());
        return monitorHomepageMapper.getPersonTracks(trackIds);
    }

    @Override
    public List<KeyValueTypeVO> countWarningHandle(MonitorRequest filterParams) {
        final Integer level = filterParams.getLevel();
        final TimeParams timeParams = filterParams.getTimeParams();
        List<KeyValueTypeVO> voList = new ArrayList<>();
        KeyValueTypeVO sign = new KeyValueTypeVO();
        sign.setKey("签收");
        sign.setValue(monitorHomepageMapper.countWarningByStatus("sign", level, timeParams));
        voList.add(sign);
        KeyValueTypeVO doing = new KeyValueTypeVO();
        doing.setKey("在办");
        doing.setValue(monitorHomepageMapper.countWarningByStatus("doing", level, timeParams));
        voList.add(doing);
        KeyValueTypeVO done = new KeyValueTypeVO();
        done.setKey("办结");
        done.setValue(monitorHomepageMapper.countWarningByStatus("done", level, timeParams));
        voList.add(done);
        return voList;
    }

    @Override
    public MonitorTotalVO countTotal(TimeParams timeParams) {
        MonitorTotalVO vo = new MonitorTotalVO();
        vo.setTotal(monitorHomepageMapper.countArchivePerson(timeParams));
        vo.setImportant(monitorHomepageMapper.countArchiveZdry(timeParams));
        vo.setEscape(monitorHomepageMapper.countArchiveZtry(timeParams));
        Long monitorCount = monitorHomepageMapper.countArchiveInControl(timeParams)
                .stream().flatMap(json -> JsonUtil.parseArray(json, Long.class).stream()).distinct().count();
        vo.setControl(monitorCount);
        return vo;
    }

    @Override
    public List<KeyValueTypeVO> countPersonByArea(TimeParams timeParams) {
        List<String> districtCodes = monitorHomepageMapper.getDistrictCode();
        List<KeyValueTypeVO> result = monitorHomepageMapper.countPersonByArea(districtCodes, timeParams);
        result.forEach(vo -> vo.setKey(vo.getKey().replace("德阳市", "")));
        return result;
    }
}
