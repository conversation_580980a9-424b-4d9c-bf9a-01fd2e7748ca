#!/bin/bash
SOURCE="$0"
while [ -h "$SOURCE"  ]; do # resolve $SOURCE until the file is no longer a symlink
    DIR="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"
    SOURCE="$(readlink "$SOURCE")"
    [[ $SOURCE != /*  ]] && SOURCE="$DIR/$SOURCE" # if $SOURCE was a relative symlink, we need to resolve it relative to the path where the symlink file was located
done
DIR="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"

## 初始化nacos命名空间,添加police 命名空间
curl 'http://127.0.0.1:8848/nacos/v1/console/namespaces?&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTY0MDk4MDUxMX0.v4SQexLjIZn4eJTDlxdhuMk5zMug3Y3MdLtl5Hw0EFo' \
  -H 'Authorization: {"accessToken":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTY0MDk4MDUxMX0.v4SQexLjIZn4eJTDlxdhuMk5zMug3Y3MdLtl5Hw0EFo","tokenTtl":18000,"globalAdmin":true,"username":"nacos"}' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  --data-raw 'customNamespaceId=police&namespaceName=police&namespaceDesc=police&namespaceId=' \
  --compressed


curl 'http://127.0.0.1:8848/nacos/v1/cs/configs?import=true&namespace=police&accessToken=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTY0MDk4MDUxMX0.v4SQexLjIZn4eJTDlxdhuMk5zMug3Y3MdLtl5Hw0EFo&username=nacos' \
  -H 'accessToken: eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTY0MDk4MDUxMX0.v4SQexLjIZn4eJTDlxdhuMk5zMug3Y3MdLtl5Hw0EFo' \
  -H 'Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryg53TB6DzeaMma9AM' \
  --form `'file=@"${DIR}/nacos_config_export_20211122234222.zip"'` \
  --form 'policy="ABORT"'
  --compressed